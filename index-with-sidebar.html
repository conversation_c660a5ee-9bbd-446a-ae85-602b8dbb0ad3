<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة السفر والسياحة - قيمة الوعد</title>
    
    <!-- الخطوط -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    
    <!-- ملفات CSS المخصصة -->
    <link href="styles/main.css" rel="stylesheet">
    <link href="src/css/sidebar-navigation.css" rel="stylesheet">
    <link href="src/css/sales-enhanced.css" rel="stylesheet">
    <link href="src/css/sales-window-complete.css" rel="stylesheet">
    <link href="src/css/purchases.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
            margin: 0;
            padding: 0;
        }
        
        /* تخطيط الصفحة الرئيسي */
        .main-layout {
            display: flex;
            min-height: 100vh;
        }
        
        /* المحتوى الرئيسي */
        .main-content {
            flex: 1;
            margin-right: var(--sidebar-width);
            transition: margin-right var(--transition-speed) ease;
            min-height: 100vh;
            background: #f8f9fa;
        }
        
        .sidebar.collapsed + .main-content {
            margin-right: var(--sidebar-collapsed-width);
        }
        
        /* شريط التنقل العلوي */
        .top-navbar {
            background: white;
            padding: 15px 30px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
        }
        
        .top-navbar-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .user-menu {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 15px;
            background: #f8f9fa;
            border-radius: 25px;
            text-decoration: none;
            color: #2c3e50;
            transition: all 0.3s ease;
            border: none;
        }
        
        .user-menu:hover {
            background: #e9ecef;
            color: #2c3e50;
        }
        
        /* محتوى الصفحة */
        .page-content {
            padding: 30px;
        }
        
        /* بطاقات لوحة التحكم */
        .dashboard-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: none;
        }
        
        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
        }
        
        .dashboard-card .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 15px;
        }
        
        .dashboard-card h5 {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .dashboard-card p {
            color: #6c757d;
            margin: 0;
        }
        
        /* ألوان البطاقات */
        .card-primary .card-icon { background: linear-gradient(45deg, #007bff, #0056b3); }
        .card-success .card-icon { background: linear-gradient(45deg, #28a745, #1e7e34); }
        .card-info .card-icon { background: linear-gradient(45deg, #17a2b8, #138496); }
        .card-warning .card-icon { background: linear-gradient(45deg, #ffc107, #e0a800); }
        .card-danger .card-icon { background: linear-gradient(45deg, #dc3545, #c82333); }
        
        /* التجاوب */
        @media (max-width: 768px) {
            .main-content {
                margin-right: 0;
            }
            
            .top-navbar {
                padding: 10px 15px;
            }
            
            .page-content {
                padding: 15px;
            }
            
            .page-title {
                font-size: 1.2rem;
            }
        }
        
        /* تحسينات إضافية */
        .welcome-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            border-radius: 20px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .welcome-section h2 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .welcome-section p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .quick-actions {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .quick-actions h5 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .action-btn {
            display: inline-block;
            padding: 10px 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            margin: 5px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            color: white;
        }
    </style>
</head>
<body>
    <div class="main-layout">
        <!-- سيتم إدراج اللوحة الجانبية هنا بواسطة JavaScript -->
        
        <!-- المحتوى الرئيسي -->
        <div class="main-content">
            <!-- شريط التنقل العلوي -->
            <div class="top-navbar">
                <h1 class="page-title">لوحة التحكم الرئيسية</h1>
                <div class="top-navbar-actions">
                    <button class="btn btn-outline-primary btn-sm" onclick="sidebarNav.showSidebar()">
                        <i class="fas fa-bars me-1"></i>
                        <span class="d-none d-md-inline">القائمة</span>
                    </button>
                    
                    <div class="dropdown">
                        <button class="user-menu dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle"></i>
                            <span class="d-none d-md-inline">مدير النظام</span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#" onclick="showUserProfile()">
                                <i class="fas fa-user me-2"></i>الملف الشخصي
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showSettings()">
                                <i class="fas fa-cog me-2"></i>الإعدادات
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="#" onclick="logout()">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- محتوى الصفحة -->
            <div class="page-content">
                <div id="main-content">
                    <!-- قسم الترحيب -->
                    <div class="welcome-section">
                        <h2>مرحباً بك في نظام إدارة السفر والسياحة</h2>
                        <p>نظام متكامل لإدارة جميع عمليات شركة قيمة الوعد للسفر والسياحة</p>
                    </div>
                    
                    <!-- إحصائيات سريعة -->
                    <div class="stats-grid">
                        <div class="dashboard-card card-primary">
                            <div class="card-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <h5>إجمالي العملاء</h5>
                            <p>1,234 عميل مسجل</p>
                        </div>
                        
                        <div class="dashboard-card card-success">
                            <div class="card-icon">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <h5>الحجوزات النشطة</h5>
                            <p>89 حجز قيد التنفيذ</p>
                        </div>
                        
                        <div class="dashboard-card card-info">
                            <div class="card-icon">
                                <i class="fas fa-file-invoice"></i>
                            </div>
                            <h5>الفواتير الشهرية</h5>
                            <p>567 فاتورة هذا الشهر</p>
                        </div>
                        
                        <div class="dashboard-card card-warning">
                            <div class="card-icon">
                                <i class="fas fa-handshake"></i>
                            </div>
                            <h5>الوكلاء النشطين</h5>
                            <p>45 وكيل معتمد</p>
                        </div>
                    </div>
                    
                    <!-- الإجراءات السريعة -->
                    <div class="quick-actions">
                        <h5><i class="fas fa-bolt me-2"></i>الإجراءات السريعة</h5>
                        <div class="d-flex flex-wrap gap-2">
                            <button class="action-btn" onclick="showAddBooking()">
                                <i class="fas fa-plus me-1"></i>حجز جديد
                            </button>
                            <button class="action-btn" onclick="showAddCustomer()">
                                <i class="fas fa-user-plus me-1"></i>عميل جديد
                            </button>
                            <button class="action-btn" onclick="showNewInvoice()">
                                <i class="fas fa-file-invoice me-1"></i>فاتورة جديدة
                            </button>
                            <button class="action-btn" onclick="showSalesDashboard()">
                                <i class="fas fa-shopping-cart me-1"></i>نظام المبيعات
                            </button>
                            <button class="action-btn" onclick="showPurchasesDashboard()">
                                <i class="fas fa-shopping-basket me-1"></i>نظام المشتريات
                            </button>
                            <button class="action-btn" onclick="showAccountingDashboard()">
                                <i class="fas fa-calculator me-1"></i>النظام المحاسبي
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- تحميل ملفات JavaScript -->
    <script src="src/js/sidebar-navigation.js"></script>
    <script src="src/js/components/sales.js"></script>
    <script src="src/js/components/purchases.js"></script>
    <script src="sales_window_enhancements.js"></script>
    <script src="invoice_summary_enhancements.js"></script>
    <script src="invoice_items_summary_fix.js"></script>
    
    <script>
        // وظائف النظام الأساسية
        function showDashboard() {
            sidebarNav.setActivePage('dashboard');
            sidebarNav.updatePageTitle('لوحة التحكم الرئيسية');
            
            document.getElementById('main-content').innerHTML = `
                <!-- قسم الترحيب -->
                <div class="welcome-section">
                    <h2>مرحباً بك في نظام إدارة السفر والسياحة</h2>
                    <p>نظام متكامل لإدارة جميع عمليات شركة قيمة الوعد للسفر والسياحة</p>
                </div>
                
                <!-- إحصائيات سريعة -->
                <div class="stats-grid">
                    <div class="dashboard-card card-primary">
                        <div class="card-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h5>إجمالي العملاء</h5>
                        <p>1,234 عميل مسجل</p>
                    </div>
                    
                    <div class="dashboard-card card-success">
                        <div class="card-icon">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <h5>الحجوزات النشطة</h5>
                        <p>89 حجز قيد التنفيذ</p>
                    </div>
                    
                    <div class="dashboard-card card-info">
                        <div class="card-icon">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                        <h5>الفواتير الشهرية</h5>
                        <p>567 فاتورة هذا الشهر</p>
                    </div>
                    
                    <div class="dashboard-card card-warning">
                        <div class="card-icon">
                            <i class="fas fa-handshake"></i>
                        </div>
                        <h5>الوكلاء النشطين</h5>
                        <p>45 وكيل معتمد</p>
                    </div>
                </div>
                
                <!-- الإجراءات السريعة -->
                <div class="quick-actions">
                    <h5><i class="fas fa-bolt me-2"></i>الإجراءات السريعة</h5>
                    <div class="d-flex flex-wrap gap-2">
                        <button class="action-btn" onclick="showAddBooking()">
                            <i class="fas fa-plus me-1"></i>حجز جديد
                        </button>
                        <button class="action-btn" onclick="showAddCustomer()">
                            <i class="fas fa-user-plus me-1"></i>عميل جديد
                        </button>
                        <button class="action-btn" onclick="showNewInvoice()">
                            <i class="fas fa-file-invoice me-1"></i>فاتورة جديدة
                        </button>
                        <button class="action-btn" onclick="showSalesDashboard()">
                            <i class="fas fa-shopping-cart me-1"></i>نظام المبيعات
                        </button>
                        <button class="action-btn" onclick="showPurchasesDashboard()">
                            <i class="fas fa-shopping-basket me-1"></i>نظام المشتريات
                        </button>
                        <button class="action-btn" onclick="showAccountingDashboard()">
                            <i class="fas fa-calculator me-1"></i>النظام المحاسبي
                        </button>
                    </div>
                </div>
            `;
        }
        
        // وظائف مؤقتة للصفحات الأخرى
        function showCustomers() {
            sidebarNav.setActivePage('showCustomers');
            sidebarNav.updatePageTitle('إدارة العملاء');
            showPlaceholderPage('العملاء', 'fas fa-users', 'إدارة قاعدة بيانات العملاء');
        }
        
        function showAddCustomer() {
            sidebarNav.setActivePage('showAddCustomer');
            sidebarNav.updatePageTitle('إضافة عميل جديد');
            showPlaceholderPage('إضافة عميل جديد', 'fas fa-user-plus', 'نموذج إضافة عميل جديد');
        }
        
        function showAgents() {
            sidebarNav.setActivePage('showAgents');
            sidebarNav.updatePageTitle('إدارة الوكلاء');
            showPlaceholderPage('الوكلاء', 'fas fa-handshake', 'إدارة شبكة الوكلاء المعتمدين');
        }
        
        function showAddAgent() {
            sidebarNav.setActivePage('showAddAgent');
            sidebarNav.updatePageTitle('إضافة وكيل جديد');
            showPlaceholderPage('إضافة وكيل جديد', 'fas fa-user-plus', 'نموذج إضافة وكيل جديد');
        }
        
        function showSuppliers() {
            sidebarNav.setActivePage('showSuppliers');
            sidebarNav.updatePageTitle('إدارة الموردين');
            showPlaceholderPage('الموردين', 'fas fa-truck', 'إدارة قاعدة بيانات الموردين');
        }
        
        function showAddSupplier() {
            sidebarNav.setActivePage('showAddSupplier');
            sidebarNav.updatePageTitle('إضافة مورد جديد');
            showPlaceholderPage('إضافة مورد جديد', 'fas fa-plus', 'نموذج إضافة مورد جديد');
        }
        
        function showBookings() {
            sidebarNav.setActivePage('showBookings');
            sidebarNav.updatePageTitle('إدارة الحجوزات');
            showPlaceholderPage('الحجوزات', 'fas fa-calendar-check', 'إدارة جميع الحجوزات والرحلات');
        }
        
        function showAddBooking() {
            sidebarNav.setActivePage('showAddBooking');
            sidebarNav.updatePageTitle('حجز جديد');
            showPlaceholderPage('حجز جديد', 'fas fa-plus', 'نموذج إنشاء حجز جديد');
        }
        
        function showHajjUmrah() {
            sidebarNav.setActivePage('showHajjUmrah');
            sidebarNav.updatePageTitle('حج وعمرة');
            showPlaceholderPage('حج وعمرة', 'fas fa-kaaba', 'إدارة رحلات الحج والعمرة');
        }
        
        // وظائف نظام المبيعات
        function showSalesDashboard() {
            sidebarNav.setActivePage('showSalesDashboard');
            sidebarNav.updatePageTitle('لوحة تحكم المبيعات');
            
            try {
                if (!window.SalesComponent) {
                    throw new Error('مكون المبيعات غير محمل');
                }
                
                const container = document.getElementById('main-content');
                if (container) {
                    container.innerHTML = '<div id="sales-container"></div>';
                    window.SalesComponent.init();
                    console.log('✅ تم عرض لوحة تحكم المبيعات');
                    return true;
                }
                
                throw new Error('عنصر العرض الرئيسي غير موجود');
                
            } catch (error) {
                console.error('❌ خطأ في عرض لوحة تحكم المبيعات:', error);
                showPlaceholderPage('نظام المبيعات', 'fas fa-shopping-cart', 'خطأ في تحميل نظام المبيعات: ' + error.message);
                return false;
            }
        }
        
        function showInvoices() {
            sidebarNav.setActivePage('showInvoices');
            sidebarNav.updatePageTitle('إدارة الفواتير');
            showPlaceholderPage('الفواتير', 'fas fa-file-invoice', 'إدارة فواتير المبيعات');
        }
        
        function showNewInvoice() {
            sidebarNav.setActivePage('showNewInvoice');
            sidebarNav.updatePageTitle('فاتورة جديدة');
            showPlaceholderPage('فاتورة جديدة', 'fas fa-plus', 'نموذج إنشاء فاتورة جديدة');
        }
        
        // وظائف نظام المشتريات
        function showPurchasesDashboard() {
            sidebarNav.setActivePage('showPurchasesDashboard');
            sidebarNav.updatePageTitle('لوحة تحكم المشتريات');
            
            try {
                if (!window.PurchasesComponent) {
                    throw new Error('مكون المشتريات غير محمل');
                }
                
                const container = document.getElementById('main-content');
                if (container) {
                    container.innerHTML = '<div id="purchases-container"></div>';
                    window.PurchasesComponent.init();
                    console.log('✅ تم عرض لوحة تحكم المشتريات');
                    return true;
                }
                
                throw new Error('عنصر العرض الرئيسي غير موجود');
                
            } catch (error) {
                console.error('❌ خطأ في عرض لوحة تحكم المشتريات:', error);
                showPlaceholderPage('نظام المشتريات', 'fas fa-shopping-basket', 'خطأ في تحميل نظام المشتريات: ' + error.message);
                return false;
            }
        }
        
        function showPurchaseInvoices() {
            sidebarNav.setActivePage('showPurchaseInvoices');
            sidebarNav.updatePageTitle('فواتير المشتريات');
            showPlaceholderPage('فواتير المشتريات', 'fas fa-file-invoice', 'إدارة فواتير المشتريات');
        }
        
        function showPurchaseOrders() {
            sidebarNav.setActivePage('showPurchaseOrders');
            sidebarNav.updatePageTitle('أوامر الشراء');
            showPlaceholderPage('أوامر الشراء', 'fas fa-clipboard-list', 'إدارة أوامر الشراء');
        }
        
        function openPurchasesWindow() {
            window.open('purchases.html', 'PurchasesWindow', 'width=1400,height=900,scrollbars=yes,resizable=yes');
        }
        
        // وظائف النظام المحاسبي
        function showAccountingDashboard() {
            sidebarNav.setActivePage('showAccountingDashboard');
            sidebarNav.updatePageTitle('لوحة التحكم المحاسبية');
            showPlaceholderPage('النظام المحاسبي', 'fas fa-calculator', 'لوحة التحكم المحاسبية الشاملة');
        }
        
        // وظائف أخرى
        function showUserProfile() {
            showPlaceholderPage('الملف الشخصي', 'fas fa-user', 'إدارة الملف الشخصي للمستخدم');
        }
        
        function showSettings() {
            showPlaceholderPage('الإعدادات', 'fas fa-cog', 'إعدادات النظام العامة');
        }
        
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                alert('تم تسجيل الخروج بنجاح');
                location.reload();
            }
        }
        
        // وظيفة عرض صفحة مؤقتة
        function showPlaceholderPage(title, icon, description) {
            document.getElementById('main-content').innerHTML = `
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="${icon} fa-5x text-muted"></i>
                    </div>
                    <h3 class="text-muted">${title}</h3>
                    <p class="text-muted fs-5">${description}</p>
                    <p class="text-muted">هذه الصفحة قيد التطوير...</p>
                    <button class="btn btn-primary" onclick="showDashboard()">
                        <i class="fas fa-arrow-left me-1"></i>العودة للرئيسية
                    </button>
                </div>
            `;
        }
    </script>
</body>
</html>
