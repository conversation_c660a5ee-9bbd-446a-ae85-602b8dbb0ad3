// Script لإصلاح استدعاءات AccountingComponent
const fs = require('fs');
const path = require('path');

try {
    // قراءة الملف
    const filePath = path.join(__dirname, 'src', 'js', 'components', 'accounting.js');
    let content = fs.readFileSync(filePath, 'utf8');

    // عد الاستدعاءات قبل الإصلاح
    const beforeCount = (content.match(/onclick="AccountingComponent\./g) || []).length;
    console.log(`عدد الاستدعاءات قبل الإصلاح: ${beforeCount}`);

    // إصلاح جميع الاستدعاءات
    content = content.replace(/onclick="AccountingComponent\./g, 'onclick="window.AccountingComponent.');

    // عد الاستدعاءات بعد الإصلاح
    const afterCount = (content.match(/onclick="AccountingComponent\./g) || []).length;
    const fixedCount = (content.match(/onclick="window\.AccountingComponent\./g) || []).length;

    // كتابة الملف المحدث
    fs.writeFileSync(filePath, content);

    console.log(`عدد الاستدعاءات بعد الإصلاح: ${afterCount}`);
    console.log(`عدد الاستدعاءات المصححة: ${fixedCount}`);
    console.log('✅ تم إصلاح جميع استدعاءات AccountingComponent بنجاح!');

} catch (error) {
    console.error('❌ خطأ في إصلاح الاستدعاءات:', error.message);
}
