<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SHARAUB SOSFT_TRAVEL SYSTEM </title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="src/css/accounting.css">
    <link rel="stylesheet" href="src/css/sales.css">
    <link rel="stylesheet" href="src/css/sales-enhanced.css">
    <link rel="stylesheet" href="src/css/sales-window-complete.css">
</head>
<body>
    <!-- شريط التنقل العلوي المحسن -->
    <nav class="navbar navbar-expand-xl navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#" onclick="showDashboard()">
                <i class="fas fa-plane me-2"></i>
                قمة الوعد للسفريات
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#" onclick="showDashboard()">
                            <i class="fas fa-home"></i>
                            <span class="nav-text">الرئيسية</span>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-users"></i>
                            <span class="nav-text">العملاء</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="showCustomers()">
                                <i class="fas fa-list me-2"></i>قائمة العملاء
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showAddCustomer()">
                                <i class="fas fa-user-plus me-2"></i>إضافة عميل جديد
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-truck"></i>
                            <span class="nav-text">الموردين</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="showSuppliers()">
                                <i class="fas fa-list me-2"></i>قائمة الموردين
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showAddSupplier()">
                                <i class="fas fa-plus me-2"></i>إضافة مورد جديد
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-handshake"></i>
                            <span class="nav-text">الوكلاء</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="showAgents()">
                                <i class="fas fa-list me-2"></i>قائمة الوكلاء
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showAddAgent()">
                                <i class="fas fa-user-plus me-2"></i>إضافة وكيل جديد
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-calendar-check"></i>
                            <span class="nav-text">الحجوزات</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="showBookings()">
                                <i class="fas fa-list me-2"></i>قائمة الحجوزات
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showAddBooking()">
                                <i class="fas fa-plus me-2"></i>حجز جديد
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showHajjUmrah()">
                                <i class="fas fa-kaaba me-2"></i>حج وعمرة
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="nav-text">المبيعات</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="showSalesDashboard()">
                                <i class="fas fa-tachometer-alt me-2"></i>لوحة تحكم المبيعات
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showInvoices()">
                                <i class="fas fa-file-invoice me-2"></i>الفواتير
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showSalesCustomers()">
                                <i class="fas fa-users me-2"></i>عملاء المبيعات
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showSalesProducts()">
                                <i class="fas fa-box me-2"></i>المنتجات والخدمات
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showCreditNotes()">
                                <i class="fas fa-file-invoice-dollar me-2"></i>الإشعارات الدائنة
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showSalesReports()">
                                <i class="fas fa-chart-bar me-2"></i>تقارير المبيعات
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="showNewInvoice()">
                                <i class="fas fa-plus me-2"></i>فاتورة جديدة
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showNewCreditNote()">
                                <i class="fas fa-plus me-2"></i>إشعار دائن جديد
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="testSalesSystem()">
                                <i class="fas fa-vial me-2"></i>اختبار النظام
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-boxes"></i>
                            <span class="nav-text">المخزون</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="showVisaInventory()">
                                <i class="fas fa-passport me-2"></i>مخزون التأشيرات
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showInventory()">
                                <i class="fas fa-warehouse me-2"></i>المخزون العام
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showProducts()">
                                <i class="fas fa-tags me-2"></i>المنتجات والخدمات
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-calculator"></i>
                            <span class="nav-text">الحسابات</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="showAccountingDashboard()">
                                <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم المحاسبية
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showAccounts()">
                                <i class="fas fa-book me-2"></i>دليل الحسابات
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showJournalEntries()">
                                <i class="fas fa-edit me-2"></i>القيود المحاسبية
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showAccountingReports()">
                                <i class="fas fa-chart-bar me-2"></i>التقارير المالية
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showPayments()">
                                <i class="fas fa-money-bill-wave me-2"></i>المدفوعات والمقبوضات
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showAgentsAndSuppliersAccounts()">
                                <i class="fas fa-users-cog me-2"></i>حسابات الوكلاء والموردين
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="showAccountingTools()">
                                <i class="fas fa-tools me-2"></i>أدوات النظام المحاسبي
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-chart-bar"></i>
                            <span class="nav-text">التقارير</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="showCustomerReports()">
                                <i class="fas fa-users me-2"></i>تقارير العملاء
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showAgentReports()">
                                <i class="fas fa-handshake me-2"></i>تقارير الوكلاء
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showTransactionReports()">
                                <i class="fas fa-exchange-alt me-2"></i>تقارير المعاملات
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showFinancialReports()">
                                <i class="fas fa-chart-line me-2"></i>التقارير المالية
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog"></i>
                            <span class="nav-text">الإعدادات</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="showUserManagement()">
                                <i class="fas fa-users-cog me-2"></i>إدارة المستخدمين
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showBackupSettings()">
                                <i class="fas fa-database me-2"></i>النسخ الاحتياطية
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showSystemSettings()">
                                <i class="fas fa-sliders-h me-2"></i>الإعدادات العامة
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showTransportCompanies()">
                                <i class="fas fa-bus me-2"></i>شركات النقل
                            </a></li>
                        </ul>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle user-menu" href="#" role="button" data-bs-toggle="dropdown">
                            <span id="user-info">
                                <i class="fas fa-user-circle me-2"></i>مدير النظام
                            </span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#" onclick="showUserProfile()">
                                <i class="fas fa-user me-2"></i>الملف الشخصي
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showSettings()">
                                <i class="fas fa-cog me-2"></i>الإعدادات
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="testSalesSystem()">
                                <i class="fas fa-vial me-2"></i>اختبار نظام المبيعات
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="#" onclick="logout()">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container-fluid main-content">
        <div id="main-content" class="dashboard-container">
            <!-- لوحة التحكم الرئيسية -->
            <div id="dashboard" class="content-section active">
                <div class="row">
                    <div class="col-12">
                        <div class="page-header text-center mb-5">
                            <h1 class="display-4 mb-3">
                                <i class="fas fa-tachometer-alt me-3 text-primary"></i>
                                لوحة التحكم الرئيسية
                            </h1>
                            <p class="lead text-muted">نظام إدارة وكالة السفر المتكامل</p>
                        </div>
                    </div>
                </div>

                <!-- الوحدات بتصميم مربعات عمودية -->
                <div class="vertical-modules-container">
                    <!-- العنوان الرئيسي -->
                    <div class="page-title-section">
                        <div class="title-icon">
                            <i class="fas fa-plane"></i>
                        </div>
                        <div class="title-content">
                            <h1>قمة الوعد للسفريات</h1>
                            <p>نظام إدارة شامل ومتطور</p>
                        </div>
                    </div>

                    <!-- شبكة الوحدات ثلاثية الأعمدة -->
                    <div class="vertical-grid">
                        <!-- الصف الأول -->
                        <!-- إدارة العملاء -->
                        <div class="vertical-card primary" onclick="showCustomers()">
                            <div class="card-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="card-content">
                                <h3>إدارة العملاء</h3>
                                <p>إدارة بيانات العملاء والمعاملات والتأشيرات</p>
                                <div class="card-stats">
                                    <span class="stat-number">1,234</span>
                                    <span class="stat-label">عميل</span>
                                </div>
                            </div>
                        </div>

                        <!-- إدارة الموردين -->
                        <div class="vertical-card success" onclick="showSuppliers()">
                            <div class="card-icon">
                                <i class="fas fa-truck"></i>
                            </div>
                            <div class="card-content">
                                <h3>إدارة الموردين</h3>
                                <p>إدارة الموردين والشركاء وأنواع الخدمات</p>
                                <div class="card-stats">
                                    <span class="stat-number">45</span>
                                    <span class="stat-label">مورد</span>
                                </div>
                            </div>
                        </div>

                        <!-- إدارة الوكلاء -->
                        <div class="vertical-card info" onclick="showAgents()">
                            <div class="card-icon">
                                <i class="fas fa-handshake"></i>
                            </div>
                            <div class="card-content">
                                <h3>إدارة الوكلاء</h3>
                                <p>إدارة الوكلاء والمندوبين والعمولات</p>
                                <div class="card-stats">
                                    <span class="stat-number">28</span>
                                    <span class="stat-label">وكيل</span>
                                </div>
                            </div>
                        </div>

                        <!-- الصف الثاني -->
                        <!-- الحجوزات -->
                        <div class="vertical-card warning" onclick="showBookings()">
                            <div class="card-icon">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <div class="card-content">
                                <h3>الحجوزات</h3>
                                <p>إدارة الحجوزات والرحلات والفنادق</p>
                                <div class="card-stats">
                                    <span class="stat-number">567</span>
                                    <span class="stat-label">حجز</span>
                                </div>
                            </div>
                        </div>

                        <!-- مخزون التأشيرات -->
                        <div class="vertical-card purple" onclick="showVisaInventory()">
                            <div class="card-icon">
                                <i class="fas fa-passport"></i>
                            </div>
                            <div class="card-content">
                                <h3>مخزون التأشيرات</h3>
                                <p>إدارة التأشيرات والوثائق الرسمية</p>
                                <div class="card-stats">
                                    <span class="stat-number">156</span>
                                    <span class="stat-label">تأشيرة</span>
                                </div>
                            </div>
                        </div>

                        <!-- المخزون العام -->
                        <div class="vertical-card secondary" onclick="showInventory()">
                            <div class="card-icon">
                                <i class="fas fa-boxes"></i>
                            </div>
                            <div class="card-content">
                                <h3>المخزون العام</h3>
                                <p>إدارة المخزون والمنتجات والمواد</p>
                                <div class="card-stats">
                                    <span class="stat-number">89</span>
                                    <span class="stat-label">منتج</span>
                                </div>
                            </div>
                        </div>

                        <!-- الصف الثالث -->
                        <!-- الحسابات -->
                        <div class="vertical-card dark" onclick="showAccounts()">
                            <div class="card-icon">
                                <i class="fas fa-calculator"></i>
                            </div>
                            <div class="card-content">
                                <h3>الحسابات</h3>
                                <p>النظام المحاسبي والمالي والتقارير</p>
                                <div class="card-stats">
                                    <span class="stat-number">₹ 89K</span>
                                    <span class="stat-label">رصيد</span>
                                </div>
                            </div>
                        </div>

                        <!-- التقارير -->
                        <div class="vertical-card danger" onclick="showCustomerReports()">
                            <div class="card-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div class="card-content">
                                <h3>التقارير</h3>
                                <p>التقارير والإحصائيات والتحليلات</p>
                                <div class="card-stats">
                                    <span class="stat-number">12</span>
                                    <span class="stat-label">تقرير</span>
                                </div>
                            </div>
                        </div>

                        <!-- المنتجات والخدمات -->
                        <div class="vertical-card indigo" onclick="showProducts()">
                            <div class="card-icon">
                                <i class="fas fa-tags"></i>
                            </div>
                            <div class="card-content">
                                <h3>المنتجات والخدمات</h3>
                                <p>إدارة المنتجات والخدمات والعروض</p>
                                <div class="card-stats">
                                    <span class="stat-number">25</span>
                                    <span class="stat-label">خدمة</span>
                                </div>
                            </div>
                        </div>

                        <!-- الصف الرابع -->
                        <!-- إدارة المستخدمين -->
                        <div class="vertical-card teal" onclick="showUserManagement()">
                            <div class="card-icon">
                                <i class="fas fa-users-cog"></i>
                            </div>
                            <div class="card-content">
                                <h3>إدارة المستخدمين</h3>
                                <p>إدارة المستخدمين والصلاحيات والأدوار</p>
                                <div class="card-stats">
                                    <span class="stat-number">8</span>
                                    <span class="stat-label">مستخدم</span>
                                </div>
                            </div>
                        </div>

                        <!-- النسخ الاحتياطية -->
                        <div class="vertical-card orange" onclick="showBackupSettings()">
                            <div class="card-icon">
                                <i class="fas fa-database"></i>
                            </div>
                            <div class="card-content">
                                <h3>النسخ الاحتياطية</h3>
                                <p>إدارة النسخ الاحتياطية والاستعادة</p>
                                <div class="card-stats">
                                    <span class="stat-number">3</span>
                                    <span class="stat-label">نسخة</span>
                                </div>
                            </div>
                        </div>

                        <!-- الإعدادات -->
                        <div class="vertical-card gray" onclick="showSystemSettings()">
                            <div class="card-icon">
                                <i class="fas fa-cog"></i>
                            </div>
                            <div class="card-content">
                                <h3>الإعدادات</h3>
                                <p>إعدادات النظام العامة والتخصيص</p>
                                <div class="card-stats">
                                    <span class="stat-number">15</span>
                                    <span class="stat-label">إعداد</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الإجراءات السريعة -->
                <div class="row mb-5">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-gradient text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-bolt me-2"></i>
                                    الإجراءات السريعة
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                                        <button class="btn btn-outline-primary w-100 quick-action-btn" onclick="showAddCustomer()">
                                            <i class="fas fa-user-plus fa-2x mb-2"></i>
                                            <div>إضافة عميل</div>
                                        </button>
                                    </div>
                                    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                                        <button class="btn btn-outline-success w-100 quick-action-btn" onclick="showAddBooking()">
                                            <i class="fas fa-calendar-plus fa-2x mb-2"></i>
                                            <div>حجز جديد</div>
                                        </button>
                                    </div>
                                    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                                        <button class="btn btn-outline-info w-100 quick-action-btn" onclick="showAddSupplier()">
                                            <i class="fas fa-truck fa-2x mb-2"></i>
                                            <div>إضافة مورد</div>
                                        </button>
                                    </div>
                                    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                                        <button class="btn btn-outline-warning w-100 quick-action-btn" onclick="showAddAgent()">
                                            <i class="fas fa-handshake fa-2x mb-2"></i>
                                            <div>إضافة وكيل</div>
                                        </button>
                                    </div>
                                    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                                        <button class="btn btn-outline-purple w-100 quick-action-btn" onclick="showVisaInventory()">
                                            <i class="fas fa-passport fa-2x mb-2"></i>
                                            <div>إدارة التأشيرات</div>
                                        </button>
                                    </div>
                                    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                                        <button class="btn btn-outline-dark w-100 quick-action-btn" onclick="showJournalEntries()">
                                            <i class="fas fa-edit fa-2x mb-2"></i>
                                            <div>قيد محاسبي</div>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الإحصائيات السريعة -->
                <div class="row">
                    <div class="col-lg-8 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-chart-area me-2"></i>
                                    نظرة عامة على النشاط
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-md-3 mb-3">
                                        <div class="stat-item">
                                            <h3 class="text-primary">1,234</h3>
                                            <small class="text-muted">إجمالي العملاء</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="stat-item">
                                            <h3 class="text-success">567</h3>
                                            <small class="text-muted">الحجوزات النشطة</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="stat-item">
                                            <h3 class="text-warning">₹ 89,000</h3>
                                            <small class="text-muted">الإيرادات الشهرية</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="stat-item">
                                            <h3 class="text-info">156</h3>
                                            <small class="text-muted">التأشيرات المتاحة</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-clock me-2"></i>
                                    النشاط الأخير
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="activity-list">
                                    <div class="activity-item">
                                        <i class="fas fa-user-plus text-primary"></i>
                                        <div>
                                            <strong>عميل جديد</strong>
                                            <small class="text-muted d-block">منذ 5 دقائق</small>
                                        </div>
                                    </div>
                                    <div class="activity-item">
                                        <i class="fas fa-calendar-check text-success"></i>
                                        <div>
                                            <strong>حجز جديد</strong>
                                            <small class="text-muted d-block">منذ 15 دقيقة</small>
                                        </div>
                                    </div>
                                    <div class="activity-item">
                                        <i class="fas fa-passport text-purple"></i>
                                        <div>
                                            <strong>تأشيرة مؤشرة</strong>
                                            <small class="text-muted d-block">منذ 30 دقيقة</small>
                                        </div>
                                    </div>
                                    <div class="activity-item">
                                        <i class="fas fa-money-bill text-warning"></i>
                                        <div>
                                            <strong>دفعة جديدة</strong>
                                            <small class="text-muted d-block">منذ ساعة</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نافذة إدارة العملاء -->
            <div id="customers" class="content-section">
                <!-- العنوان والأدوات -->
                <div class="page-header-section">
                    <div class="page-title">
                        <div class="title-icon primary">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="title-text">
                            <h2>إدارة العملاء</h2>
                            <p>إدارة بيانات العملاء والمعاملات والتأشيرات</p>
                        </div>
                    </div>
                    <div class="page-actions">
                        <button class="action-button primary" onclick="showAddCustomerSidebar()">
                            <i class="fas fa-user-plus"></i>
                            <span>إضافة عميل</span>
                        </button>
                        <button class="action-button secondary" onclick="exportCustomers()">
                            <i class="fas fa-download"></i>
                            <span>تصدير</span>
                        </button>
                    </div>
                </div>

                <!-- أدوات البحث والفلترة -->
                <div class="filters-section">
                    <div class="filters-grid">
                        <div class="filter-item">
                            <label>البحث بالاسم</label>
                            <div class="input-group">
                                <i class="fas fa-search"></i>
                                <input type="text" id="customerNameFilter" placeholder="ابحث عن عميل...">
                            </div>
                        </div>

                        <div class="filter-item">
                            <label>الوكيل</label>
                            <div class="input-group">
                                <i class="fas fa-user-tie"></i>
                                <select id="customerAgentFilter">
                                    <option value="">جميع الوكلاء</option>
                                </select>
                            </div>
                        </div>

                        <div class="filter-item">
                            <label>حالة المعاملة</label>
                            <div class="input-group">
                                <i class="fas fa-tasks"></i>
                                <select id="customerStatusFilter">
                                    <option value="">جميع الحالات</option>
                                    <option value="قيد المعاملة">قيد المعاملة</option>
                                    <option value="مسلم للمكتب">مسلم للمكتب</option>
                                    <option value="قيد الترحيل للسفارة">قيد الترحيل للسفارة</option>
                                    <option value="مؤشر في السفارة">مؤشر في السفارة</option>
                                    <option value="مؤشر في المكتب">مؤشر في المكتب</option>
                                    <option value="مسلم للعميل مؤشر">مسلم للعميل مؤشر</option>
                                    <option value="مسلم للعميل غير مؤشر">مسلم للعميل غير مؤشر</option>
                                </select>
                            </div>
                        </div>

                        <div class="filter-actions">
                            <button class="filter-btn primary" onclick="filterCustomers()">
                                <i class="fas fa-filter"></i>
                                تطبيق الفلتر
                            </button>
                            <button class="filter-btn secondary" onclick="clearCustomerFilters()">
                                <i class="fas fa-eraser"></i>
                                مسح
                            </button>
                        </div>
                    </div>
                </div>

                <!-- الإحصائيات -->
                <div class="stats-section">
                    <div class="stats-row">
                        <div class="stat-box primary">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-number">1,234</span>
                                <span class="stat-label">إجمالي العملاء</span>
                            </div>
                        </div>

                        <div class="stat-box warning">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-number">45</span>
                                <span class="stat-label">قيد المعاملة</span>
                            </div>
                        </div>

                        <div class="stat-box success">
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-number">1,189</span>
                                <span class="stat-label">مكتملة</span>
                            </div>
                        </div>

                        <div class="stat-box info">
                            <div class="stat-icon">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-number">23</span>
                                <span class="stat-label">جديد اليوم</span>
                            </div>
                        </div>

                        <div class="stat-box danger">
                            <div class="stat-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-number">8</span>
                                <span class="stat-label">عاجل</span>
                            </div>
                        </div>

                        <div class="stat-box teal">
                            <div class="stat-icon">
                                <i class="fas fa-shipping-fast"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-number">156</span>
                                <span class="stat-label">تم التسليم</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الجدول -->
                <div class="table-section">
                    <div class="table-header">
                        <h3>قائمة العملاء</h3>
                        <div class="table-controls">
                            <button class="control-btn success">
                                <i class="fas fa-file-excel"></i>
                                تصدير Excel
                            </button>
                            <button class="control-btn info">
                                <i class="fas fa-print"></i>
                                طباعة
                            </button>
                        </div>
                    </div>

                    <div class="table-wrapper">
                        <table class="data-table" id="customersTable">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-hashtag"></i> م</th>
                                    <th><i class="fas fa-file-alt"></i> رقم الوثيقة</th>
                                    <th><i class="fas fa-user"></i> الاسم</th>
                                    <th><i class="fas fa-phone"></i> الجوال</th>
                                    <th><i class="fas fa-passport"></i> رقم الجواز</th>
                                    <th><i class="fas fa-user-tie"></i> الوكيل</th>
                                    <th><i class="fas fa-stamp"></i> نوع التأشيرة</th>
                                    <th><i class="fas fa-tasks"></i> الحالة</th>
                                    <th><i class="fas fa-cogs"></i> الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم ملء البيانات بواسطة JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- نافذة إدارة الموردين -->
            <div id="suppliers" class="content-section">
                <!-- هيدر النافذة المحدث -->
                <div class="modern-window-header">
                    <div class="header-content">
                        <div class="header-icon success">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="header-info">
                            <h2>إدارة الموردين</h2>
                            <p>إدارة الموردين والشركاء وأنواع الخدمات</p>
                        </div>
                        <div class="header-actions">
                            <button class="modern-btn success" onclick="showAddSupplierSidebar()">
                                <i class="fas fa-plus"></i>
                                <span>إضافة مورد</span>
                            </button>
                            <button class="modern-btn secondary" onclick="exportSuppliers()">
                                <i class="fas fa-download"></i>
                                <span>تصدير</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- شريط الأدوات المحدث -->
                <div class="modern-toolbar">
                    <div class="toolbar-grid">
                        <!-- أداة البحث -->
                        <div class="tool-card">
                            <div class="tool-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <div class="tool-content">
                                <label>البحث بالاسم</label>
                                <input type="text" class="modern-input" id="supplierNameFilter" placeholder="ابحث عن مورد...">
                            </div>
                        </div>

                        <!-- فلتر نوع الخدمة -->
                        <div class="tool-card">
                            <div class="tool-icon">
                                <i class="fas fa-tags"></i>
                            </div>
                            <div class="tool-content">
                                <label>نوع الخدمة</label>
                                <select class="modern-select" id="supplierServiceFilter">
                                    <option value="">جميع الخدمات</option>
                                    <option value="تأشيرات">تأشيرات</option>
                                    <option value="طيران">طيران</option>
                                    <option value="فنادق">فنادق</option>
                                    <option value="نقل">نقل</option>
                                    <option value="حج وعمرة">حج وعمرة</option>
                                </select>
                            </div>
                        </div>

                        <!-- فلتر الحالة -->
                        <div class="tool-card">
                            <div class="tool-icon">
                                <i class="fas fa-toggle-on"></i>
                            </div>
                            <div class="tool-content">
                                <label>حالة المورد</label>
                                <select class="modern-select" id="supplierStatusFilter">
                                    <option value="">جميع الحالات</option>
                                    <option value="نشط">نشط</option>
                                    <option value="غير نشط">غير نشط</option>
                                    <option value="معلق">معلق</option>
                                </select>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="tool-actions">
                            <button class="modern-btn primary" onclick="filterSuppliers()">
                                <i class="fas fa-filter"></i>
                                <span>تطبيق الفلتر</span>
                            </button>
                            <button class="modern-btn secondary" onclick="clearSupplierFilters()">
                                <i class="fas fa-eraser"></i>
                                <span>مسح الفلتر</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- منطقة الإحصائيات المحدثة -->
                <div class="modern-stats-section">
                    <div class="stats-grid">
                        <div class="stat-card success">
                            <div class="stat-icon">
                                <i class="fas fa-truck"></i>
                            </div>
                            <div class="stat-content">
                                <h3>45</h3>
                                <p>إجمالي الموردين</p>
                                <span class="stat-trend up">
                                    <i class="fas fa-arrow-up"></i> 8%
                                </span>
                            </div>
                        </div>

                        <div class="stat-card primary">
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-content">
                                <h3>38</h3>
                                <p>نشط</p>
                                <span class="stat-trend up">
                                    <i class="fas fa-arrow-up"></i> 5%
                                </span>
                            </div>
                        </div>

                        <div class="stat-card warning">
                            <div class="stat-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="stat-content">
                                <h3>12</h3>
                                <p>مميز</p>
                                <span class="stat-trend up">
                                    <i class="fas fa-arrow-up"></i> 20%
                                </span>
                            </div>
                        </div>

                        <div class="stat-card info">
                            <div class="stat-icon">
                                <i class="fas fa-handshake"></i>
                            </div>
                            <div class="stat-content">
                                <h3>156</h3>
                                <p>إجمالي الصفقات</p>
                                <span class="stat-trend up">
                                    <i class="fas fa-arrow-up"></i> 12%
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- منطقة الجدول المحدثة -->
                <div class="modern-table-section">
                    <div class="table-header">
                        <h3>قائمة الموردين</h3>
                        <div class="table-actions">
                            <button class="modern-btn outline-success">
                                <i class="fas fa-file-excel"></i>
                                تصدير Excel
                            </button>
                            <button class="modern-btn outline-info">
                                <i class="fas fa-print"></i>
                                طباعة
                            </button>
                        </div>
                    </div>

                    <div class="table-container">
                        <table class="modern-data-table" id="suppliersTable">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-hashtag"></i> م</th>
                                    <th><i class="fas fa-building"></i> اسم المورد</th>
                                    <th><i class="fas fa-tags"></i> نوع الخدمة</th>
                                    <th><i class="fas fa-phone"></i> الهاتف</th>
                                    <th><i class="fas fa-envelope"></i> البريد الإلكتروني</th>
                                    <th><i class="fas fa-map-marker-alt"></i> العنوان</th>
                                    <th><i class="fas fa-toggle-on"></i> الحالة</th>
                                    <th><i class="fas fa-cogs"></i> الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم ملء البيانات بواسطة JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- نافذة إدارة الوكلاء -->
            <div id="agents" class="content-section">
                <!-- هيدر النافذة -->
                <div class="window-header">
                    <div class="window-header-content">
                        <div class="window-icon-container">
                            <div class="window-icon bg-info">
                                <i class="fas fa-handshake"></i>
                            </div>
                        </div>
                        <div class="window-title-section">
                            <h2 class="window-title">إدارة الوكلاء</h2>
                            <p class="window-subtitle">إدارة الوكلاء والمندوبين والعمولات</p>
                        </div>
                        <div class="window-actions">
                            <button class="action-btn info" onclick="showAddAgentSidebar()">
                                <i class="fas fa-user-plus"></i>
                                <span>إضافة وكيل</span>
                            </button>
                            <button class="action-btn secondary" onclick="exportAgents()">
                                <i class="fas fa-download"></i>
                                <span>تصدير</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- شريط الأدوات -->
                <div class="toolbar-container">
                    <div class="toolbar-section">
                        <div class="tool-group">
                            <div class="tool-item">
                                <i class="fas fa-search"></i>
                                <input type="text" class="tool-input" id="agentNameFilter" placeholder="البحث بالاسم...">
                            </div>
                            <div class="tool-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <select class="tool-select" id="agentAreaFilter">
                                    <option value="">جميع المناطق</option>
                                    <option value="صنعاء">صنعاء</option>
                                    <option value="عدن">عدن</option>
                                    <option value="تعز">تعز</option>
                                    <option value="الحديدة">الحديدة</option>
                                </select>
                            </div>
                            <div class="tool-item">
                                <i class="fas fa-toggle-on"></i>
                                <select class="tool-select" id="agentStatusFilter">
                                    <option value="">جميع الحالات</option>
                                    <option value="نشط">نشط</option>
                                    <option value="غير نشط">غير نشط</option>
                                    <option value="معلق">معلق</option>
                                </select>
                            </div>
                        </div>
                        <div class="tool-actions">
                            <button class="tool-btn primary" onclick="filterAgents()">
                                <i class="fas fa-filter"></i>
                                تطبيق الفلتر
                            </button>
                            <button class="tool-btn secondary" onclick="clearAgentFilters()">
                                <i class="fas fa-eraser"></i>
                                مسح
                            </button>
                        </div>
                    </div>
                </div>

                <!-- منطقة المحتوى -->
                <div class="content-area">
                    <div class="data-container">
                        <div class="data-header">
                            <div class="data-stats">
                                <div class="stat-item">
                                    <i class="fas fa-handshake text-info"></i>
                                    <span class="stat-number">28</span>
                                    <span class="stat-label">إجمالي الوكلاء</span>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-check-circle text-success"></i>
                                    <span class="stat-number">24</span>
                                    <span class="stat-label">نشط</span>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-percentage text-warning"></i>
                                    <span class="stat-number">15%</span>
                                    <span class="stat-label">متوسط العمولة</span>
                                </div>
                            </div>
                        </div>

                        <div class="data-table-container">
                            <table class="modern-table" id="agentsTable">
                                <thead>
                                    <tr>
                                        <th><i class="fas fa-hashtag"></i> م</th>
                                        <th><i class="fas fa-user"></i> اسم الوكيل</th>
                                        <th><i class="fas fa-phone"></i> الهاتف</th>
                                        <th><i class="fas fa-envelope"></i> البريد الإلكتروني</th>
                                        <th><i class="fas fa-percentage"></i> العمولة %</th>
                                        <th><i class="fas fa-users"></i> عدد العملاء</th>
                                        <th><i class="fas fa-toggle-on"></i> الحالة</th>
                                        <th><i class="fas fa-cogs"></i> الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- سيتم ملء البيانات بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- النوافذ الجانبية -->

    <!-- نافذة إضافة/تعديل عميل -->
    <div class="sidebar-overlay" id="customerSidebarOverlay"></div>
    <div class="sidebar" id="customerSidebar">
        <div class="sidebar-header">
            <h5 class="sidebar-title">
                <i class="fas fa-user-plus me-2"></i>
                <span id="customerSidebarTitle">إضافة عميل جديد</span>
            </h5>
            <button class="btn-close-sidebar" onclick="hideCustomerSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="sidebar-content">
            <form id="customerForm">
                <!-- معلومات أساسية -->
                <div class="form-section">
                    <h6 class="form-section-title">
                        <i class="fas fa-user me-2"></i>المعلومات الأساسية
                    </h6>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label required">رقم الوثيقة</label>
                            <input type="text" class="form-control" id="customerDocNumber" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label required">الاسم الكامل</label>
                            <input type="text" class="form-control" id="customerName" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label required">رقم الجوال</label>
                            <input type="tel" class="form-control" id="customerMobile" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label required">رقم الجواز</label>
                            <input type="text" class="form-control" id="customerPassport" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">المهنة</label>
                            <input type="text" class="form-control" id="customerProfession">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label required">اسم الوكيل</label>
                            <select class="form-select" id="customerAgent" required>
                                <option value="">اختر الوكيل</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- معلومات التأشيرة -->
                <div class="form-section">
                    <h6 class="form-section-title">
                        <i class="fas fa-passport me-2"></i>معلومات التأشيرة
                    </h6>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">مكتب التخويل</label>
                            <input type="text" class="form-control" id="customerAuthOffice">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">رقم الطلب</label>
                            <input type="text" class="form-control" id="customerRequestNumber">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">نوع التأشيرة</label>
                            <select class="form-select" id="customerVisaType">
                                <option value="">اختر نوع التأشيرة</option>
                                <option value="شركة 3 شهور">شركة 3 شهور</option>
                                <option value="فردية سنة">فردية سنة</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">رقم الإصدار</label>
                            <input type="text" class="form-control" id="customerIssueNumber">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">رقم السجل</label>
                            <input type="text" class="form-control" id="customerRegistryNumber">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">اسم الشركة</label>
                            <input type="text" class="form-control" id="customerCompanyName">
                        </div>
                    </div>
                </div>

                <!-- التواريخ -->
                <div class="form-section">
                    <h6 class="form-section-title">
                        <i class="fas fa-calendar me-2"></i>التواريخ المهمة
                    </h6>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">تاريخ التسليم</label>
                            <input type="date" class="form-control" id="customerDeliveryDate">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">تاريخ الترحيل</label>
                            <input type="date" class="form-control" id="customerDeportationDate">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">تاريخ إصدار التأشيرة</label>
                            <input type="date" class="form-control" id="customerVisaIssueDate">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">تاريخ وصول السفارة</label>
                            <input type="date" class="form-control" id="customerEmbassyArrivalDate">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">تاريخ تسليم العميل</label>
                            <input type="date" class="form-control" id="customerClientDeliveryDate">
                        </div>
                    </div>
                </div>

                <!-- المعلومات المالية -->
                <div class="form-section">
                    <h6 class="form-section-title">
                        <i class="fas fa-money-bill me-2"></i>المعلومات المالية
                    </h6>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">رسوم المعاملة المدفوعة</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="customerTransactionFeesPaid" step="0.01">
                                <select class="form-select currency-select" id="customerTransactionFeesPaymentCurrency">
                                    <option value="YER">ريال يمني</option>
                                    <option value="SAR">ريال سعودي</option>
                                    <option value="USD">دولار أمريكي</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">رسوم المعاملة المتبقية</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="customerRemainingTransactionFees" step="0.01">
                                <select class="form-select currency-select" id="customerRemainingTransactionFeesCurrency">
                                    <option value="YER">ريال يمني</option>
                                    <option value="SAR">ريال سعودي</option>
                                    <option value="USD">دولار أمريكي</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">رسوم التأشيرة المدفوعة</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="customerVisaFeesPaid" step="0.01">
                                <select class="form-select currency-select" id="customerVisaFeesPaymentCurrency">
                                    <option value="YER">ريال يمني</option>
                                    <option value="SAR">ريال سعودي</option>
                                    <option value="USD">دولار أمريكي</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">رسوم التأشيرة المتبقية</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="customerRemainingVisaFees" step="0.01">
                                <select class="form-select currency-select" id="customerRemainingVisaFeesCurrency">
                                    <option value="YER">ريال يمني</option>
                                    <option value="SAR">ريال سعودي</option>
                                    <option value="USD">دولار أمريكي</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الحالة والملاحظات -->
                <div class="form-section">
                    <h6 class="form-section-title">
                        <i class="fas fa-info-circle me-2"></i>الحالة والملاحظات
                    </h6>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">حالة المعاملة</label>
                            <select class="form-select" id="customerTransactionStatus">
                                <option value="قيد المعاملة">قيد المعاملة</option>
                                <option value="مسلم للمكتب">مسلم للمكتب</option>
                                <option value="قيد الترحيل للسفارة">قيد الترحيل للسفارة</option>
                                <option value="مؤشر في السفارة">مؤشر في السفارة</option>
                                <option value="مؤشر في المكتب">مؤشر في المكتب</option>
                                <option value="مسلم للعميل مؤشر">مسلم للعميل مؤشر</option>
                                <option value="مسلم للعميل غير مؤشر">مسلم للعميل غير مؤشر</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">حالة الدفع لمكتب الترحيل</label>
                            <select class="form-select" id="customerPaymentStatusDeportation">
                                <option value="غير مدفوع">غير مدفوع</option>
                                <option value="مدفوع">مدفوع</option>
                                <option value="مدفوع جزئياً">مدفوع جزئياً</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">مكان التسليم</label>
                            <input type="text" class="form-control" id="customerDeliveryLocation">
                        </div>
                        <div class="col-12">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="customerNotes" rows="3"></textarea>
                        </div>
                    </div>
                </div>

                <!-- المرفقات -->
                <div class="form-section">
                    <h6 class="form-section-title">
                        <i class="fas fa-paperclip me-2"></i>المرفقات
                    </h6>

                    <div class="file-upload-area" onclick="document.getElementById('customerAttachments').click()">
                        <i class="fas fa-cloud-upload-alt fa-3x mb-3 text-muted"></i>
                        <p class="mb-2">اضغط لرفع الملفات أو اسحب الملفات هنا</p>
                        <small class="text-muted">يمكن رفع ملفات PDF, DOC, JPG, PNG (حد أقصى 10 ميجا)</small>
                        <input type="file" id="customerAttachments" multiple accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" style="display: none;">
                    </div>

                    <div id="customerAttachmentsList" class="mt-3"></div>
                </div>
            </form>
        </div>

        <div class="sidebar-footer">
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-secondary flex-fill" onclick="hideCustomerSidebar()">
                    <i class="fas fa-times me-2"></i>إلغاء
                </button>
                <button type="button" class="btn btn-primary flex-fill" onclick="saveCustomer()">
                    <i class="fas fa-save me-2"></i>حفظ
                </button>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة/تعديل مورد -->
    <div class="sidebar-overlay" id="supplierSidebarOverlay"></div>
    <div class="sidebar" id="supplierSidebar">
        <div class="sidebar-header">
            <h5 class="sidebar-title">
                <i class="fas fa-truck me-2"></i>
                <span id="supplierSidebarTitle">إضافة مورد جديد</span>
            </h5>
            <button class="btn-close-sidebar" onclick="hideSupplierSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="sidebar-content">
            <form id="supplierForm">
                <!-- معلومات أساسية -->
                <div class="form-section">
                    <h6 class="form-section-title">
                        <i class="fas fa-building me-2"></i>معلومات المورد
                    </h6>

                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label required">اسم المورد</label>
                            <input type="text" class="form-control" id="supplierName" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label required">نوع الخدمة</label>
                            <select class="form-select" id="supplierServiceType" required>
                                <option value="">اختر نوع الخدمة</option>
                                <option value="تأشيرات">تأشيرات</option>
                                <option value="طيران">طيران</option>
                                <option value="فنادق">فنادق</option>
                                <option value="نقل">نقل</option>
                                <option value="حج وعمرة">حج وعمرة</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="supplierPhone">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="supplierEmail">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">الموقع الإلكتروني</label>
                            <input type="url" class="form-control" id="supplierWebsite">
                        </div>
                        <div class="col-12">
                            <label class="form-label">العنوان</label>
                            <textarea class="form-control" id="supplierAddress" rows="2"></textarea>
                        </div>
                    </div>
                </div>

                <!-- معلومات الاتصال -->
                <div class="form-section">
                    <h6 class="form-section-title">
                        <i class="fas fa-user-tie me-2"></i>معلومات الاتصال
                    </h6>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">اسم الشخص المسؤول</label>
                            <input type="text" class="form-control" id="supplierContactPerson">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">منصب الشخص المسؤول</label>
                            <input type="text" class="form-control" id="supplierContactPosition">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">هاتف الشخص المسؤول</label>
                            <input type="tel" class="form-control" id="supplierContactPhone">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">بريد الشخص المسؤول</label>
                            <input type="email" class="form-control" id="supplierContactEmail">
                        </div>
                    </div>
                </div>

                <!-- الشروط المالية -->
                <div class="form-section">
                    <h6 class="form-section-title">
                        <i class="fas fa-money-check me-2"></i>الشروط المالية
                    </h6>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">شروط الدفع</label>
                            <select class="form-select" id="supplierPaymentTerms">
                                <option value="">اختر شروط الدفع</option>
                                <option value="فوري">فوري</option>
                                <option value="30 يوم">30 يوم</option>
                                <option value="60 يوم">60 يوم</option>
                                <option value="90 يوم">90 يوم</option>
                                <option value="حسب الاتفاق">حسب الاتفاق</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">العملة المفضلة</label>
                            <select class="form-select" id="supplierPreferredCurrency">
                                <option value="YER">ريال يمني</option>
                                <option value="SAR">ريال سعودي</option>
                                <option value="USD">دولار أمريكي</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">الحد الائتماني</label>
                            <input type="number" class="form-control" id="supplierCreditLimit" step="0.01">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">نسبة الخصم %</label>
                            <input type="number" class="form-control" id="supplierDiscountRate" step="0.01" min="0" max="100">
                        </div>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="form-section">
                    <h6 class="form-section-title">
                        <i class="fas fa-info-circle me-2"></i>معلومات إضافية
                    </h6>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">تقييم المورد</label>
                            <select class="form-select" id="supplierRating">
                                <option value="">اختر التقييم</option>
                                <option value="ممتاز">ممتاز</option>
                                <option value="جيد جداً">جيد جداً</option>
                                <option value="جيد">جيد</option>
                                <option value="مقبول">مقبول</option>
                                <option value="ضعيف">ضعيف</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">حالة المورد</label>
                            <select class="form-select" id="supplierStatus">
                                <option value="نشط">نشط</option>
                                <option value="غير نشط">غير نشط</option>
                                <option value="معلق">معلق</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="supplierNotes" rows="3"></textarea>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <div class="sidebar-footer">
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-secondary flex-fill" onclick="hideSupplierSidebar()">
                    <i class="fas fa-times me-2"></i>إلغاء
                </button>
                <button type="button" class="btn btn-success flex-fill" onclick="saveSupplier()">
                    <i class="fas fa-save me-2"></i>حفظ
                </button>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة/تعديل وكيل -->
    <div class="sidebar-overlay" id="agentSidebarOverlay"></div>
    <div class="sidebar" id="agentSidebar">
        <div class="sidebar-header">
            <h5 class="sidebar-title">
                <i class="fas fa-handshake me-2"></i>
                <span id="agentSidebarTitle">إضافة وكيل جديد</span>
            </h5>
            <button class="btn-close-sidebar" onclick="hideAgentSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="sidebar-content">
            <form id="agentForm">
                <!-- معلومات أساسية -->
                <div class="form-section">
                    <h6 class="form-section-title">
                        <i class="fas fa-user me-2"></i>معلومات الوكيل
                    </h6>

                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label required">اسم الوكيل</label>
                            <input type="text" class="form-control" id="agentName" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="agentPhone">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="agentEmail">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">رقم الهوية</label>
                            <input type="text" class="form-control" id="agentIdNumber">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">تاريخ الميلاد</label>
                            <input type="date" class="form-control" id="agentBirthDate">
                        </div>
                        <div class="col-12">
                            <label class="form-label">العنوان</label>
                            <textarea class="form-control" id="agentAddress" rows="2"></textarea>
                        </div>
                    </div>
                </div>

                <!-- معلومات العمل -->
                <div class="form-section">
                    <h6 class="form-section-title">
                        <i class="fas fa-briefcase me-2"></i>معلومات العمل
                    </h6>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">نسبة العمولة %</label>
                            <input type="number" class="form-control" id="agentCommissionRate" step="0.01" min="0" max="100">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">تاريخ بداية العمل</label>
                            <input type="date" class="form-control" id="agentStartDate">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">المنطقة المسؤول عنها</label>
                            <input type="text" class="form-control" id="agentArea">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">حالة الوكيل</label>
                            <select class="form-select" id="agentStatus">
                                <option value="نشط">نشط</option>
                                <option value="غير نشط">غير نشط</option>
                                <option value="معلق">معلق</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="agentNotes" rows="3"></textarea>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <div class="sidebar-footer">
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-secondary flex-fill" onclick="hideAgentSidebar()">
                    <i class="fas fa-times me-2"></i>إلغاء
                </button>
                <button type="button" class="btn btn-info flex-fill" onclick="saveAgent()">
                    <i class="fas fa-save me-2"></i>حفظ
                </button>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

    <!-- Core Systems -->
    <script src="src/js/core/state.js"></script>
    <script src="src/js/core/database.js"></script>
    <script src="src/js/core/auth.js"></script>
    <script src="src/js/core/router.js"></script>
    <script src="src/js/core/accounting.js"></script>

    <!-- Utilities -->
    <script src="src/js/utils/helpers.js"></script>
    <script src="src/js/utils/validation.js"></script>
    <script src="src/js/utils/performance.js"></script>
    <script src="src/js/utils/cache.js"></script>
    <script src="src/js/utils/security.js"></script>
    <script src="src/js/utils/ui.js"></script>
    <script src="src/js/utils/modals.js"></script>
    <script src="src/js/utils/navigation.js"></script>
    <script src="src/js/utils/search.js"></script>
    <script src="src/js/utils/export.js"></script>
    <script src="src/js/utils/reports.js"></script>
    <script src="src/js/utils/testing.js"></script>
    <script src="src/js/utils/notifications.js"></script>

    <!-- Components -->
    <script src="src/js/components/customers.js"></script>
    <script src="src/js/components/suppliers.js"></script>
    <script src="src/js/components/agents.js"></script>
    <script src="src/js/components/bookings.js"></script>
    <script src="src/js/components/sales.js"></script>
    <script src="src/js/components/accounting.js"></script>

    <!-- Sales System Fix -->
    <script src="fix_sales.js"></script>

    <!-- UI Enhancements -->
    <script src="src/js/sales-ui-enhancements.js"></script>

    <!-- Legacy Files (for backward compatibility) -->
    <script src="js/database.js"></script>
    <script src="js/main.js"></script>

    <!-- Main App -->
    <script src="src/js/core/app.js"></script>

    <script>
        // التحقق من تسجيل الدخول
        document.addEventListener('DOMContentLoaded', function() {
            if (localStorage.getItem('isLoggedIn') !== 'true') {
                window.location.href = 'login.html';
                return;
            }

            // عرض معلومات المستخدم
            const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
            const userInfo = document.getElementById('user-info');
            if (userInfo && currentUser.fullName) {
                userInfo.innerHTML = `
                    <i class="fas fa-user-circle me-2"></i>
                    ${currentUser.fullName}
                `;
            }

            // تهيئة الأنظمة
            if (window.Accounting) {
                window.Accounting.init();
            }
            if (window.AccountingComponent) {
                window.AccountingComponent.init();
            }
            if (window.SalesComponent) {
                try {
                    window.SalesComponent.init();
                    console.log('✅ تم تهيئة مكون المبيعات بنجاح');
                } catch (error) {
                    console.error('❌ خطأ في تهيئة مكون المبيعات:', error);
                }
            } else {
                console.error('❌ مكون المبيعات غير محمل');
            }
        });

        // وظائف النظام المحاسبي
        function showAccountingDashboard() {
            const content = document.getElementById('main-content');
            content.innerHTML = window.AccountingComponent.render({ view: 'dashboard' });
        }

        function showAccounts() {
            const content = document.getElementById('main-content');
            content.innerHTML = window.AccountingComponent.render({ view: 'accounts' });
        }

        function showJournalEntries() {
            const content = document.getElementById('main-content');
            content.innerHTML = window.AccountingComponent.render({ view: 'entries' });
        }

        function showAccountingReports() {
            const content = document.getElementById('main-content');
            content.innerHTML = window.AccountingComponent.render({ view: 'reports' });
        }

        function showPayments() {
            const content = document.getElementById('main-content');
            content.innerHTML = window.AccountingComponent.render({ view: 'payments' });
        }

        function showAgentsAndSuppliersAccounts() {
            // عرض تقرير خاص بحسابات الوكلاء والموردين
            const content = document.getElementById('main-content');
            content.innerHTML = `
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-handshake me-2"></i>حسابات الوكلاء</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>الوكيل</th>
                                                    <th>رمز الحساب</th>
                                                    <th>الرصيد</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody id="agentsAccountsTable">
                                                <!-- سيتم ملؤها ديناميكياً -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-truck me-2"></i>حسابات الموردين</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>المورد</th>
                                                    <th>رمز الحساب</th>
                                                    <th>الرصيد</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody id="suppliersAccountsTable">
                                                <!-- سيتم ملؤها ديناميكياً -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // تحميل بيانات الوكلاء والموردين
            loadAgentsAndSuppliersAccounts();
        }

        function loadAgentsAndSuppliersAccounts() {
            // تحميل حسابات الوكلاء
            const agentsTable = document.getElementById('agentsAccountsTable');
            if (agentsTable && window.Database) {
                const agents = window.Database.findAll('agents');
                agentsTable.innerHTML = agents.map(agent => {
                    const accountCode = `5230${agent.id.slice(-3)}`;
                    const balance = window.Accounting.getAccountBalance(accountCode);
                    return `
                        <tr>
                            <td>${agent.name}</td>
                            <td>${accountCode}</td>
                            <td class="${balance > 0 ? 'text-danger' : balance < 0 ? 'text-success' : ''}">
                                ${window.Accounting.formatAmount(Math.abs(balance))}
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="viewAccountDetails('${accountCode}')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                }).join('');
            }

            // تحميل حسابات الموردين
            const suppliersTable = document.getElementById('suppliersAccountsTable');
            if (suppliersTable && window.Database) {
                const suppliers = window.Database.findAll('suppliers');
                suppliersTable.innerHTML = suppliers.map(supplier => {
                    const accountCode = `2110${supplier.id.slice(-3)}`;
                    const balance = window.Accounting.getAccountBalance(accountCode);
                    return `
                        <tr>
                            <td>${supplier.name}</td>
                            <td>${accountCode}</td>
                            <td class="${balance > 0 ? 'text-danger' : balance < 0 ? 'text-success' : ''}">
                                ${window.Accounting.formatAmount(Math.abs(balance))}
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="viewAccountDetails('${accountCode}')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                }).join('');
            }
        }

        function viewAccountDetails(accountCode) {
            // عرض تفاصيل الحساب
            if (window.AccountingComponent) {
                window.AccountingComponent.viewAccountDetails(accountCode);
            }
        }

        // وظائف إضافية للنظام المحاسبي
        function auditAccountingSystem() {
            if (window.Accounting) {
                const audit = window.Accounting.auditSystem();

                let message = 'نتائج فحص النظام المحاسبي:\n\n';

                if (audit.errors.length > 0) {
                    message += '❌ الأخطاء:\n';
                    audit.errors.forEach(error => message += `• ${error}\n`);
                    message += '\n';
                }

                if (audit.warnings.length > 0) {
                    message += '⚠️ التحذيرات:\n';
                    audit.warnings.forEach(warning => message += `• ${warning}\n`);
                    message += '\n';
                }

                if (audit.info.length > 0) {
                    message += 'ℹ️ المعلومات:\n';
                    audit.info.forEach(info => message += `• ${info}\n`);
                }

                alert(message);

                if (!audit.isValid) {
                    if (confirm('هل تريد محاولة الإصلاح التلقائي؟')) {
                        const fixResults = window.Accounting.autoFix();
                        let fixMessage = 'نتائج الإصلاح التلقائي:\n\n';

                        if (fixResults.fixed.length > 0) {
                            fixMessage += '✅ تم إصلاحه:\n';
                            fixResults.fixed.forEach(fix => fixMessage += `• ${fix}\n`);
                        }

                        if (fixResults.skipped.length > 0) {
                            fixMessage += '\n❌ لم يتم إصلاحه:\n';
                            fixResults.skipped.forEach(skip => fixMessage += `• ${skip}\n`);
                        }

                        alert(fixMessage);
                    }
                }
            }
        }

        function createAccountingBackup() {
            if (window.Accounting) {
                const success = window.Accounting.createBackup();
                if (success) {
                    alert('تم إنشاء النسخة الاحتياطية بنجاح');
                } else {
                    alert('فشل في إنشاء النسخة الاحتياطية');
                }
            }
        }

        function restoreAccountingBackup() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';

            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file && window.Accounting) {
                    window.Accounting.restoreFromBackup(file)
                        .then(result => {
                            alert(result.message);
                            if (result.success) {
                                location.reload(); // إعادة تحميل الصفحة لتطبيق البيانات الجديدة
                            }
                        })
                        .catch(error => {
                            alert(error.message);
                        });
                }
            };

            input.click();
        }

        // إضافة قائمة أدوات النظام المحاسبي
        function showAccountingTools() {
            const toolsHTML = `
                <div class="modal fade" id="accountingToolsModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">أدوات النظام المحاسبي</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="list-group">
                                    <button type="button" class="list-group-item list-group-item-action" onclick="auditAccountingSystem(); bootstrap.Modal.getInstance(document.getElementById('accountingToolsModal')).hide();">
                                        <i class="fas fa-search text-primary me-2"></i>
                                        <strong>فحص النظام المحاسبي</strong>
                                        <small class="d-block text-muted">فحص شامل للنظام واكتشاف المشاكل</small>
                                    </button>

                                    <button type="button" class="list-group-item list-group-item-action" onclick="createAccountingBackup(); bootstrap.Modal.getInstance(document.getElementById('accountingToolsModal')).hide();">
                                        <i class="fas fa-download text-success me-2"></i>
                                        <strong>إنشاء نسخة احتياطية</strong>
                                        <small class="d-block text-muted">حفظ نسخة احتياطية من البيانات المحاسبية</small>
                                    </button>

                                    <button type="button" class="list-group-item list-group-item-action" onclick="restoreAccountingBackup(); bootstrap.Modal.getInstance(document.getElementById('accountingToolsModal')).hide();">
                                        <i class="fas fa-upload text-warning me-2"></i>
                                        <strong>استعادة نسخة احتياطية</strong>
                                        <small class="d-block text-muted">استعادة البيانات من نسخة احتياطية</small>
                                    </button>

                                    <button type="button" class="list-group-item list-group-item-action" onclick="window.Accounting && window.Accounting.updateBalances(); alert('تم تحديث جميع الأرصدة'); bootstrap.Modal.getInstance(document.getElementById('accountingToolsModal')).hide();">
                                        <i class="fas fa-sync text-info me-2"></i>
                                        <strong>تحديث الأرصدة</strong>
                                        <small class="d-block text-muted">إعادة حساب جميع أرصدة الحسابات</small>
                                    </button>

                                    <button type="button" class="list-group-item list-group-item-action" onclick="if(window.Accounting) { const audit = window.Accounting.auditSystem(); if(audit.isValid) alert('النظام المحاسبي سليم ✓'); else alert('يوجد مشاكل في النظام ✗'); } bootstrap.Modal.getInstance(document.getElementById('accountingToolsModal')).hide();">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        <strong>فحص سريع</strong>
                                        <small class="d-block text-muted">فحص سريع لحالة النظام</small>
                                    </button>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // إضافة النافذة للصفحة
            document.body.insertAdjacentHTML('beforeend', toolsHTML);

            // عرض النافذة
            const modal = new bootstrap.Modal(document.getElementById('accountingToolsModal'));
            modal.show();

            // إزالة النافذة عند الإغلاق
            document.getElementById('accountingToolsModal').addEventListener('hidden.bs.modal', function() {
                this.remove();
            });
        }

        // وظائف نظام المبيعات - محسنة مع معالجة أخطاء شاملة
        function showSalesDashboard() {
            console.log('🎯 طلب عرض لوحة تحكم المبيعات...');

            // التحقق من وجود الوظيفة المحسنة من fix_sales.js
            if (typeof window.showSalesDashboard === 'function' && window.showSalesDashboard !== showSalesDashboard) {
                return window.showSalesDashboard();
            }

            // الطريقة التقليدية مع تحسينات
            return executeSalesView('dashboard', 'لوحة التحكم');
        }

        function showInvoices() {
            console.log('🧾 طلب عرض الفواتير...');

            if (typeof window.showInvoices === 'function' && window.showInvoices !== showInvoices) {
                return window.showInvoices();
            }

            return executeSalesView('invoices', 'الفواتير');
        }

        function showSalesCustomers() {
            console.log('👥 طلب عرض عملاء المبيعات...');

            if (typeof window.showSalesCustomers === 'function' && window.showSalesCustomers !== showSalesCustomers) {
                return window.showSalesCustomers();
            }

            return executeSalesView('customers', 'العملاء');
        }

        function showSalesProducts() {
            console.log('📦 طلب عرض المنتجات...');

            if (typeof window.showSalesProducts === 'function' && window.showSalesProducts !== showSalesProducts) {
                return window.showSalesProducts();
            }

            return executeSalesView('products', 'المنتجات');
        }

        function showSalesReports() {
            console.log('📊 طلب عرض تقارير المبيعات...');

            if (typeof window.showSalesReports === 'function' && window.showSalesReports !== showSalesReports) {
                return window.showSalesReports();
            }

            return executeSalesView('reports', 'التقارير');
        }

        function showCreditNotes() {
            console.log('💳 طلب عرض الإشعارات الدائنة...');

            if (typeof window.showCreditNotes === 'function' && window.showCreditNotes !== showCreditNotes) {
                return window.showCreditNotes();
            }

            return executeSalesView('creditNotes', 'الإشعارات الدائنة');
        }

        function showNewCreditNote() {
            console.log('➕ طلب إنشاء إشعار دائن جديد...');

            if (typeof window.showNewCreditNote === 'function' && window.showNewCreditNote !== showNewCreditNote) {
                return window.showNewCreditNote();
            }

            // الطريقة التقليدية
            try {
                if (!window.SalesComponent) {
                    throw new Error('مكون المبيعات غير محمل');
                }

                // التأكد من تهيئة المكون
                if (typeof window.SalesComponent.init === 'function') {
                    window.SalesComponent.init();
                }

                if (typeof window.SalesComponent.showCreateCreditNoteModal === 'function') {
                    window.SalesComponent.showCreateCreditNoteModal();
                    console.log('✅ تم فتح نافذة الإشعار الدائن الجديد');
                    return true;
                } else {
                    // عرض صفحة الإشعارات الدائنة كبديل
                    return executeSalesView('creditNotes', 'الإشعارات الدائنة');
                }
            } catch (error) {
                console.error('❌ خطأ في إنشاء إشعار دائن جديد:', error);
                showSalesError('فشل في فتح نافذة الإشعار الدائن الجديد: ' + error.message);
                return false;
            }
        }

        function showNewInvoice() {
            console.log('➕ طلب إنشاء فاتورة جديدة...');

            if (typeof window.showNewInvoice === 'function' && window.showNewInvoice !== showNewInvoice) {
                return window.showNewInvoice();
            }

            // الطريقة التقليدية
            try {
                if (!window.SalesComponent) {
                    throw new Error('مكون المبيعات غير محمل');
                }

                // التأكد من تهيئة المكون
                if (typeof window.SalesComponent.init === 'function') {
                    window.SalesComponent.init();
                }

                if (typeof window.SalesComponent.showCreateInvoiceModal === 'function') {
                    window.SalesComponent.showCreateInvoiceModal();
                    console.log('✅ تم فتح نافذة الفاتورة الجديدة');
                    return true;
                } else {
                    // عرض صفحة الفواتير كبديل
                    return executeSalesView('invoices', 'الفواتير');
                }
            } catch (error) {
                console.error('❌ خطأ في إنشاء فاتورة جديدة:', error);
                showSalesError('فشل في فتح نافذة الفاتورة الجديدة: ' + error.message);
                return false;
            }
        }

        // وظيفة مساعدة لتنفيذ عرض صفحات المبيعات
        function executeSalesView(view, viewName) {
            try {
                if (!window.SalesComponent) {
                    throw new Error('مكون المبيعات غير محمل');
                }

                const container = document.getElementById('main-content');
                if (!container) {
                    throw new Error('عنصر العرض الرئيسي غير موجود');
                }

                // تهيئة المكون إذا لم يكن مهيئاً
                if (typeof window.SalesComponent.init === 'function') {
                    window.SalesComponent.init();
                }

                // عرض الصفحة المطلوبة
                window.SalesComponent.render({ view: view });
                console.log(`✅ تم عرض ${viewName} بنجاح`);
                return true;

            } catch (error) {
                console.error(`❌ خطأ في عرض ${viewName}:`, error);
                showSalesError(`فشل في عرض ${viewName}: ${error.message}`);
                return false;
            }
        }

        // وظيفة عرض أخطاء المبيعات
        function showSalesError(message) {
            console.error('❌ خطأ في نظام المبيعات:', message);

            // إظهار رسالة للمستخدم
            alert('خطأ في نظام المبيعات: ' + message);

            // إظهار رسالة في الواجهة
            const container = document.getElementById('main-content');
            if (container) {
                container.innerHTML = `
                    <div class="container-fluid">
                        <div class="alert alert-danger" role="alert">
                            <h4 class="alert-heading">
                                <i class="fas fa-exclamation-triangle me-2"></i>خطأ في نظام المبيعات
                            </h4>
                            <p>${message}</p>
                            <hr>
                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-primary" onclick="location.reload()">
                                    <i class="fas fa-refresh me-1"></i>تحديث الصفحة
                                </button>
                                <button class="btn btn-outline-info" onclick="testSalesSystem()">
                                    <i class="fas fa-vial me-1"></i>اختبار النظام
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        // وظيفة تسجيل الخروج
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('currentUser');
                window.location.href = 'login.html';
            }
        }

        // وظيفة اختبار نظام المبيعات
        function testSalesSystem() {
            console.log('🧪 اختبار نظام المبيعات...');

            if (typeof window.SalesComponent === 'undefined') {
                console.error('❌ مكون المبيعات غير محمل');
                alert('خطأ: مكون المبيعات غير محمل');
                return false;
            }

            console.log('✅ مكون المبيعات محمل');

            try {
                // اختبار التهيئة
                window.SalesComponent.init();
                console.log('✅ تم تهيئة المكون');

                // اختبار التشخيص
                const diagnosis = window.SalesComponent.diagnose();
                console.log('📊 تشخيص النظام:', diagnosis);

                // اختبار عرض لوحة التحكم
                window.SalesComponent.render({ view: 'dashboard' });
                console.log('✅ تم عرض لوحة التحكم');

                alert('✅ نظام المبيعات يعمل بشكل صحيح!');
                return true;

            } catch (error) {
                console.error('❌ خطأ في اختبار النظام:', error);
                alert('❌ خطأ في نظام المبيعات: ' + error.message);
                return false;
            }
        }

        // إضافة اختصار لوحة المفاتيح للاختبار (Ctrl+Shift+T)
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.shiftKey && e.key === 'T') {
                testSalesSystem();
            }
        });
    </script>

    <!-- تحميل تحسينات نافذة المبيعات -->
    <script src="sales_window_enhancements.js"></script>
    <!-- تحميل تحسينات ملخص الفواتير -->
    <script src="invoice_summary_enhancements.js"></script>
</body>
</html>
