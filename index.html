<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SHARAUB SOSFT_TRAVEL SYSTEM </title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="src/css/accounting.css">
    <link rel="stylesheet" href="src/css/sales.css">
    <link rel="stylesheet" href="src/css/sales-enhanced.css">
    <link rel="stylesheet" href="src/css/sales-window-complete.css">
    <link rel="stylesheet" href="src/css/sidebar-navigation.css">
    <link rel="stylesheet" href="src/css/purchases.css">

    <style>
        /* تنسيقات إضافية للوحة الجانبية المدمجة */
        #sidebarToggleBtn {
            border-radius: 8px;
            padding: 8px 12px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        #sidebarToggleBtn:hover {
            background-color: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            transform: scale(1.05);
        }

        #sidebarToggleBtn:active {
            transform: scale(0.95);
        }

        /* تحسين مظهر اللوحة الجانبية */
        .sidebar {
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .sidebar-header {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .sidebar-logo {
            text-decoration: none;
            color: inherit;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            padding: 10px;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .sidebar-logo:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #3498db;
        }

        .sidebar-logo i {
            font-size: 2rem;
            color: #3498db;
        }

        .sidebar-logo-text {
            font-size: 1.2rem;
            font-weight: 600;
            color: #ecf0f1;
        }

        /* تحسين شارات الإشعارات */
        .nav-badge {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            font-size: 0.7rem;
            padding: 3px 7px;
            border-radius: 12px;
            margin-right: auto;
            min-width: 20px;
            text-align: center;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        /* تحسين القوائم الفرعية */
        .nav-item.has-submenu > .nav-link::after {
            content: '\f107';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            margin-right: auto;
            transition: transform 0.3s ease;
            font-size: 0.8rem;
        }

        .nav-item.has-submenu.open > .nav-link::after {
            transform: rotate(180deg);
        }

        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 0 0 8px 8px;
            margin-top: 2px;
        }

        .nav-item.has-submenu.open .submenu {
            max-height: 500px;
        }

        .submenu .nav-link {
            padding: 12px 20px 12px 55px;
            font-size: 0.9rem;
            border-right: none;
            border-radius: 0;
        }

        .submenu .nav-link:hover {
            background: rgba(52, 152, 219, 0.2);
            border-right: 3px solid #3498db;
        }

        /* تحسين تذييل اللوحة */
        .sidebar-footer {
            padding: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
            margin-top: auto;
        }

        .version-info {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.6);
            font-weight: 500;
        }

        /* فواصل القوائم الفرعية */
        .submenu-divider {
            height: 1px;
            background: rgba(255, 255, 255, 0.1);
            margin: 8px 20px;
        }

        /* تأثيرات الحركة */
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .sidebar.show {
            animation: slideInRight 0.3s ease-out;
        }

        /* تحسين التجاوب */
        @media (max-width: 768px) {
            #sidebarToggleBtn {
                padding: 6px 10px;
                font-size: 14px;
            }

            .navbar-brand {
                font-size: 1rem;
            }

            .navbar-brand .fas {
                font-size: 1.2rem;
            }
        }

        /* تحسين شريط التنقل العلوي */
        .navbar {
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .navbar-brand {
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .navbar-brand:hover {
            color: #fff !important;
            transform: scale(1.02);
        }

        .navbar-brand .fas {
            color: #ffd700;
            font-size: 1.3rem;
        }

        /* تحسين قائمة المستخدم */
        .dropdown-menu {
            border: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
            border-radius: 10px;
            padding: 10px 0;
        }

        .dropdown-item {
            padding: 10px 20px;
            transition: all 0.3s ease;
            border-radius: 0;
        }

        .dropdown-item:hover {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            transform: translateX(5px);
        }

        .dropdown-item.text-danger:hover {
            background: linear-gradient(45deg, #dc3545, #c82333);
            color: white;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي المبسط -->
    <nav class="navbar navbar-dark bg-primary shadow-sm">
        <div class="container-fluid">
            <!-- زر فتح اللوحة الجانبية -->
            <button class="btn btn-outline-light me-3" id="sidebarToggleBtn" onclick="toggleSidebar()" title="فتح/إغلاق اللوحة الجانبية">
                <i class="fas fa-bars"></i>
            </button>

            <!-- شعار الشركة -->
            <a class="navbar-brand flex-grow-1" href="#" onclick="showDashboard()">
                <i class="fas fa-plane me-2"></i>
                <span class="fw-bold">قيمة الوعد للسفر والسياحة</span>
            </a>

            <!-- معلومات المستخدم -->
            <div class="dropdown">
                <button class="btn btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user-circle me-1"></i>
                    <span class="d-none d-md-inline">مدير النظام</span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="#" onclick="showUserProfile(); closeSidebar();">
                        <i class="fas fa-user me-2"></i>الملف الشخصي
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="showSettings(); closeSidebar();">
                        <i class="fas fa-cog me-2"></i>الإعدادات
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="testSalesSystem(); closeSidebar();">
                        <i class="fas fa-vial me-2"></i>اختبار نظام المبيعات
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item text-danger" href="#" onclick="logout()">
                        <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                    </a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- اللوحة الجانبية -->
    <div class="sidebar" id="sidebar" style="display: none;">
        <!-- رأس اللوحة -->
        <div class="sidebar-header">
            <a href="#" class="sidebar-logo" onclick="showDashboard()">
                <i class="fas fa-plane"></i>
                <span class="sidebar-logo-text">قيمة الوعد للسفر</span>
            </a>
            <button class="sidebar-close" onclick="toggleSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <!-- قائمة التنقل -->
        <nav class="sidebar-nav">
            <!-- القسم الرئيسي -->
            <div class="nav-section">
                <div class="nav-section-title">القسم الرئيسي</div>
                <div class="nav-item">
                    <a href="#" class="nav-link active" onclick="showDashboard(); closeSidebar();">
                        <i class="fas fa-home"></i>
                        <span class="nav-link-text">الرئيسية</span>
                    </a>
                </div>
            </div>

            <!-- إدارة العملاء والوكلاء -->
            <div class="nav-section">
                <div class="nav-section-title">إدارة العملاء والوكلاء</div>

                <div class="nav-item has-submenu">
                    <a href="#" class="nav-link">
                        <i class="fas fa-users"></i>
                        <span class="nav-link-text">العملاء</span>
                    </a>
                    <div class="submenu">
                        <a href="#" class="nav-link" onclick="showCustomers(); closeSidebar();">
                            <i class="fas fa-list"></i>
                            <span class="nav-link-text">قائمة العملاء</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showAddCustomer(); closeSidebar();">
                            <i class="fas fa-user-plus"></i>
                            <span class="nav-link-text">إضافة عميل جديد</span>
                        </a>
                    </div>
                </div>

                <div class="nav-item has-submenu">
                    <a href="#" class="nav-link">
                        <i class="fas fa-handshake"></i>
                        <span class="nav-link-text">الوكلاء</span>
                    </a>
                    <div class="submenu">
                        <a href="#" class="nav-link" onclick="showAgents(); closeSidebar();">
                            <i class="fas fa-list"></i>
                            <span class="nav-link-text">قائمة الوكلاء</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showAddAgent(); closeSidebar();">
                            <i class="fas fa-user-plus"></i>
                            <span class="nav-link-text">إضافة وكيل جديد</span>
                        </a>
                    </div>
                </div>

                <div class="nav-item has-submenu">
                    <a href="#" class="nav-link">
                        <i class="fas fa-truck"></i>
                        <span class="nav-link-text">الموردين</span>
                    </a>
                    <div class="submenu">
                        <a href="#" class="nav-link" onclick="showSuppliers(); closeSidebar();">
                            <i class="fas fa-list"></i>
                            <span class="nav-link-text">قائمة الموردين</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showAddSupplier(); closeSidebar();">
                            <i class="fas fa-plus"></i>
                            <span class="nav-link-text">إضافة مورد جديد</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- الحجوزات والخدمات -->
            <div class="nav-section">
                <div class="nav-section-title">الحجوزات والخدمات</div>

                <div class="nav-item has-submenu">
                    <a href="#" class="nav-link">
                        <i class="fas fa-calendar-check"></i>
                        <span class="nav-link-text">الحجوزات</span>
                        <span class="nav-badge">5</span>
                    </a>
                    <div class="submenu">
                        <a href="#" class="nav-link" onclick="showBookings(); closeSidebar();">
                            <i class="fas fa-list"></i>
                            <span class="nav-link-text">قائمة الحجوزات</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showAddBooking(); closeSidebar();">
                            <i class="fas fa-plus"></i>
                            <span class="nav-link-text">حجز جديد</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showHajjUmrah(); closeSidebar();">
                            <i class="fas fa-kaaba"></i>
                            <span class="nav-link-text">حج وعمرة</span>
                        </a>
                    </div>
                </div>

                <div class="nav-item has-submenu">
                    <a href="#" class="nav-link">
                        <i class="fas fa-boxes"></i>
                        <span class="nav-link-text">المخزون</span>
                    </a>
                    <div class="submenu">
                        <a href="#" class="nav-link" onclick="showVisaInventory(); closeSidebar();">
                            <i class="fas fa-passport"></i>
                            <span class="nav-link-text">مخزون التأشيرات</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showInventory(); closeSidebar();">
                            <i class="fas fa-warehouse"></i>
                            <span class="nav-link-text">المخزون العام</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showProducts(); closeSidebar();">
                            <i class="fas fa-tags"></i>
                            <span class="nav-link-text">المنتجات والخدمات</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- المبيعات والمشتريات -->
            <div class="nav-section">
                <div class="nav-section-title">المبيعات والمشتريات</div>

                <div class="nav-item has-submenu">
                    <a href="#" class="nav-link">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="nav-link-text">المبيعات</span>
                        <span class="nav-badge">12</span>
                    </a>
                    <div class="submenu">
                        <a href="#" class="nav-link" onclick="showSalesDashboard(); closeSidebar();">
                            <i class="fas fa-tachometer-alt"></i>
                            <span class="nav-link-text">لوحة تحكم المبيعات</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showInvoices(); closeSidebar();">
                            <i class="fas fa-file-invoice"></i>
                            <span class="nav-link-text">الفواتير</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showSalesCustomers(); closeSidebar();">
                            <i class="fas fa-users"></i>
                            <span class="nav-link-text">عملاء المبيعات</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showSalesProducts(); closeSidebar();">
                            <i class="fas fa-box"></i>
                            <span class="nav-link-text">المنتجات والخدمات</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showCreditNotes(); closeSidebar();">
                            <i class="fas fa-file-invoice-dollar"></i>
                            <span class="nav-link-text">الإشعارات الدائنة</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showSalesReports(); closeSidebar();">
                            <i class="fas fa-chart-bar"></i>
                            <span class="nav-link-text">تقارير المبيعات</span>
                        </a>
                        <div class="submenu-divider"></div>
                        <a href="#" class="nav-link" onclick="showNewInvoice(); closeSidebar();">
                            <i class="fas fa-plus"></i>
                            <span class="nav-link-text">فاتورة جديدة</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showNewCreditNote(); closeSidebar();">
                            <i class="fas fa-plus"></i>
                            <span class="nav-link-text">إشعار دائن جديد</span>
                        </a>
                        <a href="#" class="nav-link" onclick="testSalesSystem(); closeSidebar();">
                            <i class="fas fa-vial"></i>
                            <span class="nav-link-text">اختبار النظام</span>
                        </a>
                    </div>
                </div>

                <div class="nav-item has-submenu">
                    <a href="#" class="nav-link">
                        <i class="fas fa-shopping-basket"></i>
                        <span class="nav-link-text">المشتريات</span>
                        <span class="nav-badge">3</span>
                    </a>
                    <div class="submenu">
                        <a href="#" class="nav-link" onclick="showPurchasesDashboard(); closeSidebar();">
                            <i class="fas fa-tachometer-alt"></i>
                            <span class="nav-link-text">لوحة تحكم المشتريات</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showPurchaseInvoices(); closeSidebar();">
                            <i class="fas fa-file-invoice"></i>
                            <span class="nav-link-text">فواتير المشتريات</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showPurchaseOrders(); closeSidebar();">
                            <i class="fas fa-clipboard-list"></i>
                            <span class="nav-link-text">أوامر الشراء</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showPurchaseQuotes(); closeSidebar();">
                            <i class="fas fa-file-contract"></i>
                            <span class="nav-link-text">عروض أسعار المشتريات</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showPurchasePayments(); closeSidebar();">
                            <i class="fas fa-credit-card"></i>
                            <span class="nav-link-text">مدفوعات المشتريات</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showReceivedGoods(); closeSidebar();">
                            <i class="fas fa-boxes"></i>
                            <span class="nav-link-text">البضائع المستلمة</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showPurchaseReturns(); closeSidebar();">
                            <i class="fas fa-undo"></i>
                            <span class="nav-link-text">مرتجعات المشتريات</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showPurchaseReports(); closeSidebar();">
                            <i class="fas fa-chart-bar"></i>
                            <span class="nav-link-text">تقارير المشتريات</span>
                        </a>
                        <div class="submenu-divider"></div>
                        <a href="#" class="nav-link" onclick="showNewPurchaseOrder(); closeSidebar();">
                            <i class="fas fa-plus"></i>
                            <span class="nav-link-text">أمر شراء جديد</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showNewPurchaseInvoice(); closeSidebar();">
                            <i class="fas fa-file-invoice"></i>
                            <span class="nav-link-text">فاتورة شراء جديدة</span>
                        </a>
                        <a href="#" class="nav-link" onclick="openPurchasesWindow(); closeSidebar();">
                            <i class="fas fa-external-link-alt"></i>
                            <span class="nav-link-text">فتح نافذة المشتريات</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- المحاسبة والتقارير -->
            <div class="nav-section">
                <div class="nav-section-title">المحاسبة والتقارير</div>

                <div class="nav-item has-submenu">
                    <a href="#" class="nav-link">
                        <i class="fas fa-calculator"></i>
                        <span class="nav-link-text">الحسابات</span>
                    </a>
                    <div class="submenu">
                        <a href="#" class="nav-link" onclick="showAccountingDashboard(); closeSidebar();">
                            <i class="fas fa-tachometer-alt"></i>
                            <span class="nav-link-text">لوحة التحكم المحاسبية</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showAccounts(); closeSidebar();">
                            <i class="fas fa-book"></i>
                            <span class="nav-link-text">دليل الحسابات</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showJournalEntries(); closeSidebar();">
                            <i class="fas fa-edit"></i>
                            <span class="nav-link-text">القيود المحاسبية</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showPayments(); closeSidebar();">
                            <i class="fas fa-money-bill-wave"></i>
                            <span class="nav-link-text">المدفوعات والمقبوضات</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showAccountingReports(); closeSidebar();">
                            <i class="fas fa-chart-bar"></i>
                            <span class="nav-link-text">التقارير المالية</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showAgentsAndSuppliersAccounts(); closeSidebar();">
                            <i class="fas fa-users-cog"></i>
                            <span class="nav-link-text">حسابات الوكلاء والموردين</span>
                        </a>
                        <div class="submenu-divider"></div>
                        <a href="#" class="nav-link" onclick="showAccountingTools(); closeSidebar();">
                            <i class="fas fa-tools"></i>
                            <span class="nav-link-text">أدوات النظام المحاسبي</span>
                        </a>
                    </div>
                </div>

                <div class="nav-item has-submenu">
                    <a href="#" class="nav-link">
                        <i class="fas fa-chart-line"></i>
                        <span class="nav-link-text">التقارير</span>
                    </a>
                    <div class="submenu">
                        <a href="#" class="nav-link" onclick="showCustomerReports(); closeSidebar();">
                            <i class="fas fa-users"></i>
                            <span class="nav-link-text">تقارير العملاء</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showAgentReports(); closeSidebar();">
                            <i class="fas fa-handshake"></i>
                            <span class="nav-link-text">تقارير الوكلاء</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showTransactionReports(); closeSidebar();">
                            <i class="fas fa-exchange-alt"></i>
                            <span class="nav-link-text">تقارير المعاملات</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showFinancialReports(); closeSidebar();">
                            <i class="fas fa-chart-line"></i>
                            <span class="nav-link-text">التقارير المالية</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- الإعدادات والإدارة -->
            <div class="nav-section">
                <div class="nav-section-title">الإعدادات والإدارة</div>

                <div class="nav-item has-submenu">
                    <a href="#" class="nav-link">
                        <i class="fas fa-cog"></i>
                        <span class="nav-link-text">الإعدادات</span>
                    </a>
                    <div class="submenu">
                        <a href="#" class="nav-link" onclick="showUserManagement(); closeSidebar();">
                            <i class="fas fa-users-cog"></i>
                            <span class="nav-link-text">إدارة المستخدمين</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showBackupSettings(); closeSidebar();">
                            <i class="fas fa-database"></i>
                            <span class="nav-link-text">النسخ الاحتياطية</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showSystemSettings(); closeSidebar();">
                            <i class="fas fa-sliders-h"></i>
                            <span class="nav-link-text">الإعدادات العامة</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showTransportCompanies(); closeSidebar();">
                            <i class="fas fa-bus"></i>
                            <span class="nav-link-text">شركات النقل</span>
                        </a>
                    </div>
                </div>
            </div>
        </nav>

        <!-- تذييل اللوحة -->
        <div class="sidebar-footer">
            <div class="version-info">
                الإصدار 2.0.0
            </div>
        </div>
    </div>

    <!-- طبقة التغطية -->
    <div class="sidebar-overlay" id="sidebarOverlay" onclick="closeSidebar()"></div>

    <!-- المحتوى الرئيسي -->
    <div class="container-fluid main-content">
        <div id="main-content" class="dashboard-container">
            <!-- لوحة التحكم الرئيسية -->
            <div id="dashboard" class="content-section active">
                <div class="row">
                    <div class="col-12">
                        <div class="page-header text-center mb-5">
                            <h1 class="display-4 mb-3">
                                <i class="fas fa-tachometer-alt me-3 text-primary"></i>
                                لوحة التحكم الرئيسية
                            </h1>
                            <p class="lead text-muted">نظام إدارة وكالة السفر المتكامل</p>
                        </div>
                    </div>
                </div>

                <!-- الوحدات بتصميم مربعات عمودية -->
                <div class="vertical-modules-container">
                    <!-- العنوان الرئيسي -->
                    <div class="page-title-section">
                        <div class="title-icon">
                            <i class="fas fa-plane"></i>
                        </div>
                        <div class="title-content">
                            <h1>قمة الوعد للسفريات</h1>
                            <p>نظام إدارة شامل ومتطور</p>
                        </div>
                    </div>

                    <!-- شبكة الوحدات ثلاثية الأعمدة -->
                    <div class="vertical-grid">
                        <!-- الصف الأول -->
                        <!-- إدارة العملاء -->
                        <div class="vertical-card primary" onclick="showCustomers()">
                            <div class="card-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="card-content">
                                <h3>إدارة العملاء</h3>
                                <p>إدارة بيانات العملاء والمعاملات والتأشيرات</p>
                                <div class="card-stats">
                                    <span class="stat-number">1,234</span>
                                    <span class="stat-label">عميل</span>
                                </div>
                            </div>
                        </div>

                        <!-- إدارة الموردين -->
                        <div class="vertical-card success" onclick="showSuppliers()">
                            <div class="card-icon">
                                <i class="fas fa-truck"></i>
                            </div>
                            <div class="card-content">
                                <h3>إدارة الموردين</h3>
                                <p>إدارة الموردين والشركاء وأنواع الخدمات</p>
                                <div class="card-stats">
                                    <span class="stat-number">45</span>
                                    <span class="stat-label">مورد</span>
                                </div>
                            </div>
                        </div>

                        <!-- إدارة الوكلاء -->
                        <div class="vertical-card info" onclick="showAgents()">
                            <div class="card-icon">
                                <i class="fas fa-handshake"></i>
                            </div>
                            <div class="card-content">
                                <h3>إدارة الوكلاء</h3>
                                <p>إدارة الوكلاء والمندوبين والعمولات</p>
                                <div class="card-stats">
                                    <span class="stat-number">28</span>
                                    <span class="stat-label">وكيل</span>
                                </div>
                            </div>
                        </div>

                        <!-- الصف الثاني -->
                        <!-- الحجوزات -->
                        <div class="vertical-card warning" onclick="showBookings()">
                            <div class="card-icon">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <div class="card-content">
                                <h3>الحجوزات</h3>
                                <p>إدارة الحجوزات والرحلات والفنادق</p>
                                <div class="card-stats">
                                    <span class="stat-number">567</span>
                                    <span class="stat-label">حجز</span>
                                </div>
                            </div>
                        </div>

                        <!-- مخزون التأشيرات -->
                        <div class="vertical-card purple" onclick="showVisaInventory()">
                            <div class="card-icon">
                                <i class="fas fa-passport"></i>
                            </div>
                            <div class="card-content">
                                <h3>مخزون التأشيرات</h3>
                                <p>إدارة التأشيرات والوثائق الرسمية</p>
                                <div class="card-stats">
                                    <span class="stat-number">156</span>
                                    <span class="stat-label">تأشيرة</span>
                                </div>
                            </div>
                        </div>

                        <!-- المخزون العام -->
                        <div class="vertical-card secondary" onclick="showInventory()">
                            <div class="card-icon">
                                <i class="fas fa-boxes"></i>
                            </div>
                            <div class="card-content">
                                <h3>المخزون العام</h3>
                                <p>إدارة المخزون والمنتجات والمواد</p>
                                <div class="card-stats">
                                    <span class="stat-number">89</span>
                                    <span class="stat-label">منتج</span>
                                </div>
                            </div>
                        </div>

                        <!-- الصف الثالث -->
                        <!-- الحسابات -->
                        <div class="vertical-card dark" onclick="showAccounts()">
                            <div class="card-icon">
                                <i class="fas fa-calculator"></i>
                            </div>
                            <div class="card-content">
                                <h3>الحسابات</h3>
                                <p>النظام المحاسبي والمالي والتقارير</p>
                                <div class="card-stats">
                                    <span class="stat-number">₹ 89K</span>
                                    <span class="stat-label">رصيد</span>
                                </div>
                            </div>
                        </div>

                        <!-- التقارير -->
                        <div class="vertical-card danger" onclick="showCustomerReports()">
                            <div class="card-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div class="card-content">
                                <h3>التقارير</h3>
                                <p>التقارير والإحصائيات والتحليلات</p>
                                <div class="card-stats">
                                    <span class="stat-number">12</span>
                                    <span class="stat-label">تقرير</span>
                                </div>
                            </div>
                        </div>

                        <!-- المنتجات والخدمات -->
                        <div class="vertical-card indigo" onclick="showProducts()">
                            <div class="card-icon">
                                <i class="fas fa-tags"></i>
                            </div>
                            <div class="card-content">
                                <h3>المنتجات والخدمات</h3>
                                <p>إدارة المنتجات والخدمات والعروض</p>
                                <div class="card-stats">
                                    <span class="stat-number">25</span>
                                    <span class="stat-label">خدمة</span>
                                </div>
                            </div>
                        </div>

                        <!-- الصف الرابع -->
                        <!-- إدارة المستخدمين -->
                        <div class="vertical-card teal" onclick="showUserManagement()">
                            <div class="card-icon">
                                <i class="fas fa-users-cog"></i>
                            </div>
                            <div class="card-content">
                                <h3>إدارة المستخدمين</h3>
                                <p>إدارة المستخدمين والصلاحيات والأدوار</p>
                                <div class="card-stats">
                                    <span class="stat-number">8</span>
                                    <span class="stat-label">مستخدم</span>
                                </div>
                            </div>
                        </div>

                        <!-- النسخ الاحتياطية -->
                        <div class="vertical-card orange" onclick="showBackupSettings()">
                            <div class="card-icon">
                                <i class="fas fa-database"></i>
                            </div>
                            <div class="card-content">
                                <h3>النسخ الاحتياطية</h3>
                                <p>إدارة النسخ الاحتياطية والاستعادة</p>
                                <div class="card-stats">
                                    <span class="stat-number">3</span>
                                    <span class="stat-label">نسخة</span>
                                </div>
                            </div>
                        </div>

                        <!-- الإعدادات -->
                        <div class="vertical-card gray" onclick="showSystemSettings()">
                            <div class="card-icon">
                                <i class="fas fa-cog"></i>
                            </div>
                            <div class="card-content">
                                <h3>الإعدادات</h3>
                                <p>إعدادات النظام العامة والتخصيص</p>
                                <div class="card-stats">
                                    <span class="stat-number">15</span>
                                    <span class="stat-label">إعداد</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الإجراءات السريعة -->
                <div class="row mb-5">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-gradient text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-bolt me-2"></i>
                                    الإجراءات السريعة
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                                        <button class="btn btn-outline-primary w-100 quick-action-btn" onclick="showAddCustomer()">
                                            <i class="fas fa-user-plus fa-2x mb-2"></i>
                                            <div>إضافة عميل</div>
                                        </button>
                                    </div>
                                    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                                        <button class="btn btn-outline-success w-100 quick-action-btn" onclick="showAddBooking()">
                                            <i class="fas fa-calendar-plus fa-2x mb-2"></i>
                                            <div>حجز جديد</div>
                                        </button>
                                    </div>
                                    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                                        <button class="btn btn-outline-info w-100 quick-action-btn" onclick="showAddSupplier()">
                                            <i class="fas fa-truck fa-2x mb-2"></i>
                                            <div>إضافة مورد</div>
                                        </button>
                                    </div>
                                    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                                        <button class="btn btn-outline-warning w-100 quick-action-btn" onclick="showAddAgent()">
                                            <i class="fas fa-handshake fa-2x mb-2"></i>
                                            <div>إضافة وكيل</div>
                                        </button>
                                    </div>
                                    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                                        <button class="btn btn-outline-purple w-100 quick-action-btn" onclick="showVisaInventory()">
                                            <i class="fas fa-passport fa-2x mb-2"></i>
                                            <div>إدارة التأشيرات</div>
                                        </button>
                                    </div>
                                    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                                        <button class="btn btn-outline-dark w-100 quick-action-btn" onclick="showJournalEntries()">
                                            <i class="fas fa-edit fa-2x mb-2"></i>
                                            <div>قيد محاسبي</div>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الإحصائيات السريعة -->
                <div class="row">
                    <div class="col-lg-8 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-chart-area me-2"></i>
                                    نظرة عامة على النشاط
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-md-3 mb-3">
                                        <div class="stat-item">
                                            <h3 class="text-primary">1,234</h3>
                                            <small class="text-muted">إجمالي العملاء</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="stat-item">
                                            <h3 class="text-success">567</h3>
                                            <small class="text-muted">الحجوزات النشطة</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="stat-item">
                                            <h3 class="text-warning">₹ 89,000</h3>
                                            <small class="text-muted">الإيرادات الشهرية</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="stat-item">
                                            <h3 class="text-info">156</h3>
                                            <small class="text-muted">التأشيرات المتاحة</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-clock me-2"></i>
                                    النشاط الأخير
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="activity-list">
                                    <div class="activity-item">
                                        <i class="fas fa-user-plus text-primary"></i>
                                        <div>
                                            <strong>عميل جديد</strong>
                                            <small class="text-muted d-block">منذ 5 دقائق</small>
                                        </div>
                                    </div>
                                    <div class="activity-item">
                                        <i class="fas fa-calendar-check text-success"></i>
                                        <div>
                                            <strong>حجز جديد</strong>
                                            <small class="text-muted d-block">منذ 15 دقيقة</small>
                                        </div>
                                    </div>
                                    <div class="activity-item">
                                        <i class="fas fa-passport text-purple"></i>
                                        <div>
                                            <strong>تأشيرة مؤشرة</strong>
                                            <small class="text-muted d-block">منذ 30 دقيقة</small>
                                        </div>
                                    </div>
                                    <div class="activity-item">
                                        <i class="fas fa-money-bill text-warning"></i>
                                        <div>
                                            <strong>دفعة جديدة</strong>
                                            <small class="text-muted d-block">منذ ساعة</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نافذة إدارة العملاء -->
            <div id="customers" class="content-section">
                <!-- العنوان والأدوات -->
                <div class="page-header-section">
                    <div class="page-title">
                        <div class="title-icon primary">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="title-text">
                            <h2>إدارة العملاء</h2>
                            <p>إدارة بيانات العملاء والمعاملات والتأشيرات</p>
                        </div>
                    </div>
                    <div class="page-actions">
                        <button class="action-button primary" onclick="showAddCustomerSidebar()">
                            <i class="fas fa-user-plus"></i>
                            <span>إضافة عميل</span>
                        </button>
                        <button class="action-button secondary" onclick="exportCustomers()">
                            <i class="fas fa-download"></i>
                            <span>تصدير</span>
                        </button>
                    </div>
                </div>

                <!-- أدوات البحث والفلترة -->
                <div class="filters-section">
                    <div class="filters-grid">
                        <div class="filter-item">
                            <label>البحث بالاسم</label>
                            <div class="input-group">
                                <i class="fas fa-search"></i>
                                <input type="text" id="customerNameFilter" placeholder="ابحث عن عميل...">
                            </div>
                        </div>

                        <div class="filter-item">
                            <label>الوكيل</label>
                            <div class="input-group">
                                <i class="fas fa-user-tie"></i>
                                <select id="customerAgentFilter">
                                    <option value="">جميع الوكلاء</option>
                                </select>
                            </div>
                        </div>

                        <div class="filter-item">
                            <label>حالة المعاملة</label>
                            <div class="input-group">
                                <i class="fas fa-tasks"></i>
                                <select id="customerStatusFilter">
                                    <option value="">جميع الحالات</option>
                                    <option value="قيد المعاملة">قيد المعاملة</option>
                                    <option value="مسلم للمكتب">مسلم للمكتب</option>
                                    <option value="قيد الترحيل للسفارة">قيد الترحيل للسفارة</option>
                                    <option value="مؤشر في السفارة">مؤشر في السفارة</option>
                                    <option value="مؤشر في المكتب">مؤشر في المكتب</option>
                                    <option value="مسلم للعميل مؤشر">مسلم للعميل مؤشر</option>
                                    <option value="مسلم للعميل غير مؤشر">مسلم للعميل غير مؤشر</option>
                                </select>
                            </div>
                        </div>

                        <div class="filter-actions">
                            <button class="filter-btn primary" onclick="filterCustomers()">
                                <i class="fas fa-filter"></i>
                                تطبيق الفلتر
                            </button>
                            <button class="filter-btn secondary" onclick="clearCustomerFilters()">
                                <i class="fas fa-eraser"></i>
                                مسح
                            </button>
                        </div>
                    </div>
                </div>

                <!-- الإحصائيات -->
                <div class="stats-section">
                    <div class="stats-row">
                        <div class="stat-box primary">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-number">1,234</span>
                                <span class="stat-label">إجمالي العملاء</span>
                            </div>
                        </div>

                        <div class="stat-box warning">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-number">45</span>
                                <span class="stat-label">قيد المعاملة</span>
                            </div>
                        </div>

                        <div class="stat-box success">
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-number">1,189</span>
                                <span class="stat-label">مكتملة</span>
                            </div>
                        </div>

                        <div class="stat-box info">
                            <div class="stat-icon">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-number">23</span>
                                <span class="stat-label">جديد اليوم</span>
                            </div>
                        </div>

                        <div class="stat-box danger">
                            <div class="stat-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-number">8</span>
                                <span class="stat-label">عاجل</span>
                            </div>
                        </div>

                        <div class="stat-box teal">
                            <div class="stat-icon">
                                <i class="fas fa-shipping-fast"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-number">156</span>
                                <span class="stat-label">تم التسليم</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الجدول -->
                <div class="table-section">
                    <div class="table-header">
                        <h3>قائمة العملاء</h3>
                        <div class="table-controls">
                            <button class="control-btn success">
                                <i class="fas fa-file-excel"></i>
                                تصدير Excel
                            </button>
                            <button class="control-btn info">
                                <i class="fas fa-print"></i>
                                طباعة
                            </button>
                        </div>
                    </div>

                    <div class="table-wrapper">
                        <table class="data-table" id="customersTable">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-hashtag"></i> م</th>
                                    <th><i class="fas fa-file-alt"></i> رقم الوثيقة</th>
                                    <th><i class="fas fa-user"></i> الاسم</th>
                                    <th><i class="fas fa-phone"></i> الجوال</th>
                                    <th><i class="fas fa-passport"></i> رقم الجواز</th>
                                    <th><i class="fas fa-user-tie"></i> الوكيل</th>
                                    <th><i class="fas fa-stamp"></i> نوع التأشيرة</th>
                                    <th><i class="fas fa-tasks"></i> الحالة</th>
                                    <th><i class="fas fa-cogs"></i> الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم ملء البيانات بواسطة JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- نافذة إدارة الموردين -->
            <div id="suppliers" class="content-section">
                <!-- هيدر النافذة المحدث -->
                <div class="modern-window-header">
                    <div class="header-content">
                        <div class="header-icon success">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="header-info">
                            <h2>إدارة الموردين</h2>
                            <p>إدارة الموردين والشركاء وأنواع الخدمات</p>
                        </div>
                        <div class="header-actions">
                            <button class="modern-btn success" onclick="showAddSupplierSidebar()">
                                <i class="fas fa-plus"></i>
                                <span>إضافة مورد</span>
                            </button>
                            <button class="modern-btn secondary" onclick="exportSuppliers()">
                                <i class="fas fa-download"></i>
                                <span>تصدير</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- شريط الأدوات المحدث -->
                <div class="modern-toolbar">
                    <div class="toolbar-grid">
                        <!-- أداة البحث -->
                        <div class="tool-card">
                            <div class="tool-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <div class="tool-content">
                                <label>البحث بالاسم</label>
                                <input type="text" class="modern-input" id="supplierNameFilter" placeholder="ابحث عن مورد...">
                            </div>
                        </div>

                        <!-- فلتر نوع الخدمة -->
                        <div class="tool-card">
                            <div class="tool-icon">
                                <i class="fas fa-tags"></i>
                            </div>
                            <div class="tool-content">
                                <label>نوع الخدمة</label>
                                <select class="modern-select" id="supplierServiceFilter">
                                    <option value="">جميع الخدمات</option>
                                    <option value="تأشيرات">تأشيرات</option>
                                    <option value="طيران">طيران</option>
                                    <option value="فنادق">فنادق</option>
                                    <option value="نقل">نقل</option>
                                    <option value="حج وعمرة">حج وعمرة</option>
                                </select>
                            </div>
                        </div>

                        <!-- فلتر الحالة -->
                        <div class="tool-card">
                            <div class="tool-icon">
                                <i class="fas fa-toggle-on"></i>
                            </div>
                            <div class="tool-content">
                                <label>حالة المورد</label>
                                <select class="modern-select" id="supplierStatusFilter">
                                    <option value="">جميع الحالات</option>
                                    <option value="نشط">نشط</option>
                                    <option value="غير نشط">غير نشط</option>
                                    <option value="معلق">معلق</option>
                                </select>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="tool-actions">
                            <button class="modern-btn primary" onclick="filterSuppliers()">
                                <i class="fas fa-filter"></i>
                                <span>تطبيق الفلتر</span>
                            </button>
                            <button class="modern-btn secondary" onclick="clearSupplierFilters()">
                                <i class="fas fa-eraser"></i>
                                <span>مسح الفلتر</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- منطقة الإحصائيات المحدثة -->
                <div class="modern-stats-section">
                    <div class="stats-grid">
                        <div class="stat-card success">
                            <div class="stat-icon">
                                <i class="fas fa-truck"></i>
                            </div>
                            <div class="stat-content">
                                <h3>45</h3>
                                <p>إجمالي الموردين</p>
                                <span class="stat-trend up">
                                    <i class="fas fa-arrow-up"></i> 8%
                                </span>
                            </div>
                        </div>

                        <div class="stat-card primary">
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-content">
                                <h3>38</h3>
                                <p>نشط</p>
                                <span class="stat-trend up">
                                    <i class="fas fa-arrow-up"></i> 5%
                                </span>
                            </div>
                        </div>

                        <div class="stat-card warning">
                            <div class="stat-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="stat-content">
                                <h3>12</h3>
                                <p>مميز</p>
                                <span class="stat-trend up">
                                    <i class="fas fa-arrow-up"></i> 20%
                                </span>
                            </div>
                        </div>

                        <div class="stat-card info">
                            <div class="stat-icon">
                                <i class="fas fa-handshake"></i>
                            </div>
                            <div class="stat-content">
                                <h3>156</h3>
                                <p>إجمالي الصفقات</p>
                                <span class="stat-trend up">
                                    <i class="fas fa-arrow-up"></i> 12%
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- منطقة الجدول المحدثة -->
                <div class="modern-table-section">
                    <div class="table-header">
                        <h3>قائمة الموردين</h3>
                        <div class="table-actions">
                            <button class="modern-btn outline-success">
                                <i class="fas fa-file-excel"></i>
                                تصدير Excel
                            </button>
                            <button class="modern-btn outline-info">
                                <i class="fas fa-print"></i>
                                طباعة
                            </button>
                        </div>
                    </div>

                    <div class="table-container">
                        <table class="modern-data-table" id="suppliersTable">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-hashtag"></i> م</th>
                                    <th><i class="fas fa-building"></i> اسم المورد</th>
                                    <th><i class="fas fa-tags"></i> نوع الخدمة</th>
                                    <th><i class="fas fa-phone"></i> الهاتف</th>
                                    <th><i class="fas fa-envelope"></i> البريد الإلكتروني</th>
                                    <th><i class="fas fa-map-marker-alt"></i> العنوان</th>
                                    <th><i class="fas fa-toggle-on"></i> الحالة</th>
                                    <th><i class="fas fa-cogs"></i> الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم ملء البيانات بواسطة JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- نافذة إدارة الوكلاء -->
            <div id="agents" class="content-section">
                <!-- هيدر النافذة -->
                <div class="window-header">
                    <div class="window-header-content">
                        <div class="window-icon-container">
                            <div class="window-icon bg-info">
                                <i class="fas fa-handshake"></i>
                            </div>
                        </div>
                        <div class="window-title-section">
                            <h2 class="window-title">إدارة الوكلاء</h2>
                            <p class="window-subtitle">إدارة الوكلاء والمندوبين والعمولات</p>
                        </div>
                        <div class="window-actions">
                            <button class="action-btn info" onclick="showAddAgentSidebar()">
                                <i class="fas fa-user-plus"></i>
                                <span>إضافة وكيل</span>
                            </button>
                            <button class="action-btn secondary" onclick="exportAgents()">
                                <i class="fas fa-download"></i>
                                <span>تصدير</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- شريط الأدوات -->
                <div class="toolbar-container">
                    <div class="toolbar-section">
                        <div class="tool-group">
                            <div class="tool-item">
                                <i class="fas fa-search"></i>
                                <input type="text" class="tool-input" id="agentNameFilter" placeholder="البحث بالاسم...">
                            </div>
                            <div class="tool-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <select class="tool-select" id="agentAreaFilter">
                                    <option value="">جميع المناطق</option>
                                    <option value="صنعاء">صنعاء</option>
                                    <option value="عدن">عدن</option>
                                    <option value="تعز">تعز</option>
                                    <option value="الحديدة">الحديدة</option>
                                </select>
                            </div>
                            <div class="tool-item">
                                <i class="fas fa-toggle-on"></i>
                                <select class="tool-select" id="agentStatusFilter">
                                    <option value="">جميع الحالات</option>
                                    <option value="نشط">نشط</option>
                                    <option value="غير نشط">غير نشط</option>
                                    <option value="معلق">معلق</option>
                                </select>
                            </div>
                        </div>
                        <div class="tool-actions">
                            <button class="tool-btn primary" onclick="filterAgents()">
                                <i class="fas fa-filter"></i>
                                تطبيق الفلتر
                            </button>
                            <button class="tool-btn secondary" onclick="clearAgentFilters()">
                                <i class="fas fa-eraser"></i>
                                مسح
                            </button>
                        </div>
                    </div>
                </div>

                <!-- منطقة المحتوى -->
                <div class="content-area">
                    <div class="data-container">
                        <div class="data-header">
                            <div class="data-stats">
                                <div class="stat-item">
                                    <i class="fas fa-handshake text-info"></i>
                                    <span class="stat-number">28</span>
                                    <span class="stat-label">إجمالي الوكلاء</span>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-check-circle text-success"></i>
                                    <span class="stat-number">24</span>
                                    <span class="stat-label">نشط</span>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-percentage text-warning"></i>
                                    <span class="stat-number">15%</span>
                                    <span class="stat-label">متوسط العمولة</span>
                                </div>
                            </div>
                        </div>

                        <div class="data-table-container">
                            <table class="modern-table" id="agentsTable">
                                <thead>
                                    <tr>
                                        <th><i class="fas fa-hashtag"></i> م</th>
                                        <th><i class="fas fa-user"></i> اسم الوكيل</th>
                                        <th><i class="fas fa-phone"></i> الهاتف</th>
                                        <th><i class="fas fa-envelope"></i> البريد الإلكتروني</th>
                                        <th><i class="fas fa-percentage"></i> العمولة %</th>
                                        <th><i class="fas fa-users"></i> عدد العملاء</th>
                                        <th><i class="fas fa-toggle-on"></i> الحالة</th>
                                        <th><i class="fas fa-cogs"></i> الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- سيتم ملء البيانات بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- النوافذ الجانبية -->

    <!-- نافذة إضافة/تعديل عميل -->
    <div class="sidebar-overlay" id="customerSidebarOverlay"></div>
    <div class="sidebar" id="customerSidebar">
        <div class="sidebar-header">
            <h5 class="sidebar-title">
                <i class="fas fa-user-plus me-2"></i>
                <span id="customerSidebarTitle">إضافة عميل جديد</span>
            </h5>
            <button class="btn-close-sidebar" onclick="hideCustomerSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="sidebar-content">
            <form id="customerForm">
                <!-- معلومات أساسية -->
                <div class="form-section">
                    <h6 class="form-section-title">
                        <i class="fas fa-user me-2"></i>المعلومات الأساسية
                    </h6>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label required">رقم الوثيقة</label>
                            <input type="text" class="form-control" id="customerDocNumber" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label required">الاسم الكامل</label>
                            <input type="text" class="form-control" id="customerName" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label required">رقم الجوال</label>
                            <input type="tel" class="form-control" id="customerMobile" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label required">رقم الجواز</label>
                            <input type="text" class="form-control" id="customerPassport" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">المهنة</label>
                            <input type="text" class="form-control" id="customerProfession">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label required">اسم الوكيل</label>
                            <select class="form-select" id="customerAgent" required>
                                <option value="">اختر الوكيل</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- معلومات التأشيرة -->
                <div class="form-section">
                    <h6 class="form-section-title">
                        <i class="fas fa-passport me-2"></i>معلومات التأشيرة
                    </h6>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">مكتب التخويل</label>
                            <input type="text" class="form-control" id="customerAuthOffice">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">رقم الطلب</label>
                            <input type="text" class="form-control" id="customerRequestNumber">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">نوع التأشيرة</label>
                            <select class="form-select" id="customerVisaType">
                                <option value="">اختر نوع التأشيرة</option>
                                <option value="شركة 3 شهور">شركة 3 شهور</option>
                                <option value="فردية سنة">فردية سنة</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">رقم الإصدار</label>
                            <input type="text" class="form-control" id="customerIssueNumber">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">رقم السجل</label>
                            <input type="text" class="form-control" id="customerRegistryNumber">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">اسم الشركة</label>
                            <input type="text" class="form-control" id="customerCompanyName">
                        </div>
                    </div>
                </div>

                <!-- التواريخ -->
                <div class="form-section">
                    <h6 class="form-section-title">
                        <i class="fas fa-calendar me-2"></i>التواريخ المهمة
                    </h6>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">تاريخ التسليم</label>
                            <input type="date" class="form-control" id="customerDeliveryDate">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">تاريخ الترحيل</label>
                            <input type="date" class="form-control" id="customerDeportationDate">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">تاريخ إصدار التأشيرة</label>
                            <input type="date" class="form-control" id="customerVisaIssueDate">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">تاريخ وصول السفارة</label>
                            <input type="date" class="form-control" id="customerEmbassyArrivalDate">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">تاريخ تسليم العميل</label>
                            <input type="date" class="form-control" id="customerClientDeliveryDate">
                        </div>
                    </div>
                </div>

                <!-- المعلومات المالية -->
                <div class="form-section">
                    <h6 class="form-section-title">
                        <i class="fas fa-money-bill me-2"></i>المعلومات المالية
                    </h6>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">رسوم المعاملة المدفوعة</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="customerTransactionFeesPaid" step="0.01">
                                <select class="form-select currency-select" id="customerTransactionFeesPaymentCurrency">
                                    <option value="YER">ريال يمني</option>
                                    <option value="SAR">ريال سعودي</option>
                                    <option value="USD">دولار أمريكي</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">رسوم المعاملة المتبقية</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="customerRemainingTransactionFees" step="0.01">
                                <select class="form-select currency-select" id="customerRemainingTransactionFeesCurrency">
                                    <option value="YER">ريال يمني</option>
                                    <option value="SAR">ريال سعودي</option>
                                    <option value="USD">دولار أمريكي</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">رسوم التأشيرة المدفوعة</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="customerVisaFeesPaid" step="0.01">
                                <select class="form-select currency-select" id="customerVisaFeesPaymentCurrency">
                                    <option value="YER">ريال يمني</option>
                                    <option value="SAR">ريال سعودي</option>
                                    <option value="USD">دولار أمريكي</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">رسوم التأشيرة المتبقية</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="customerRemainingVisaFees" step="0.01">
                                <select class="form-select currency-select" id="customerRemainingVisaFeesCurrency">
                                    <option value="YER">ريال يمني</option>
                                    <option value="SAR">ريال سعودي</option>
                                    <option value="USD">دولار أمريكي</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الحالة والملاحظات -->
                <div class="form-section">
                    <h6 class="form-section-title">
                        <i class="fas fa-info-circle me-2"></i>الحالة والملاحظات
                    </h6>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">حالة المعاملة</label>
                            <select class="form-select" id="customerTransactionStatus">
                                <option value="قيد المعاملة">قيد المعاملة</option>
                                <option value="مسلم للمكتب">مسلم للمكتب</option>
                                <option value="قيد الترحيل للسفارة">قيد الترحيل للسفارة</option>
                                <option value="مؤشر في السفارة">مؤشر في السفارة</option>
                                <option value="مؤشر في المكتب">مؤشر في المكتب</option>
                                <option value="مسلم للعميل مؤشر">مسلم للعميل مؤشر</option>
                                <option value="مسلم للعميل غير مؤشر">مسلم للعميل غير مؤشر</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">حالة الدفع لمكتب الترحيل</label>
                            <select class="form-select" id="customerPaymentStatusDeportation">
                                <option value="غير مدفوع">غير مدفوع</option>
                                <option value="مدفوع">مدفوع</option>
                                <option value="مدفوع جزئياً">مدفوع جزئياً</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">مكان التسليم</label>
                            <input type="text" class="form-control" id="customerDeliveryLocation">
                        </div>
                        <div class="col-12">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="customerNotes" rows="3"></textarea>
                        </div>
                    </div>
                </div>

                <!-- المرفقات -->
                <div class="form-section">
                    <h6 class="form-section-title">
                        <i class="fas fa-paperclip me-2"></i>المرفقات
                    </h6>

                    <div class="file-upload-area" onclick="document.getElementById('customerAttachments').click()">
                        <i class="fas fa-cloud-upload-alt fa-3x mb-3 text-muted"></i>
                        <p class="mb-2">اضغط لرفع الملفات أو اسحب الملفات هنا</p>
                        <small class="text-muted">يمكن رفع ملفات PDF, DOC, JPG, PNG (حد أقصى 10 ميجا)</small>
                        <input type="file" id="customerAttachments" multiple accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" style="display: none;">
                    </div>

                    <div id="customerAttachmentsList" class="mt-3"></div>
                </div>
            </form>
        </div>

        <div class="sidebar-footer">
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-secondary flex-fill" onclick="hideCustomerSidebar()">
                    <i class="fas fa-times me-2"></i>إلغاء
                </button>
                <button type="button" class="btn btn-primary flex-fill" onclick="saveCustomer()">
                    <i class="fas fa-save me-2"></i>حفظ
                </button>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة/تعديل مورد -->
    <div class="sidebar-overlay" id="supplierSidebarOverlay"></div>
    <div class="sidebar" id="supplierSidebar">
        <div class="sidebar-header">
            <h5 class="sidebar-title">
                <i class="fas fa-truck me-2"></i>
                <span id="supplierSidebarTitle">إضافة مورد جديد</span>
            </h5>
            <button class="btn-close-sidebar" onclick="hideSupplierSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="sidebar-content">
            <form id="supplierForm">
                <!-- معلومات أساسية -->
                <div class="form-section">
                    <h6 class="form-section-title">
                        <i class="fas fa-building me-2"></i>معلومات المورد
                    </h6>

                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label required">اسم المورد</label>
                            <input type="text" class="form-control" id="supplierName" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label required">نوع الخدمة</label>
                            <select class="form-select" id="supplierServiceType" required>
                                <option value="">اختر نوع الخدمة</option>
                                <option value="تأشيرات">تأشيرات</option>
                                <option value="طيران">طيران</option>
                                <option value="فنادق">فنادق</option>
                                <option value="نقل">نقل</option>
                                <option value="حج وعمرة">حج وعمرة</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="supplierPhone">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="supplierEmail">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">الموقع الإلكتروني</label>
                            <input type="url" class="form-control" id="supplierWebsite">
                        </div>
                        <div class="col-12">
                            <label class="form-label">العنوان</label>
                            <textarea class="form-control" id="supplierAddress" rows="2"></textarea>
                        </div>
                    </div>
                </div>

                <!-- معلومات الاتصال -->
                <div class="form-section">
                    <h6 class="form-section-title">
                        <i class="fas fa-user-tie me-2"></i>معلومات الاتصال
                    </h6>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">اسم الشخص المسؤول</label>
                            <input type="text" class="form-control" id="supplierContactPerson">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">منصب الشخص المسؤول</label>
                            <input type="text" class="form-control" id="supplierContactPosition">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">هاتف الشخص المسؤول</label>
                            <input type="tel" class="form-control" id="supplierContactPhone">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">بريد الشخص المسؤول</label>
                            <input type="email" class="form-control" id="supplierContactEmail">
                        </div>
                    </div>
                </div>

                <!-- الشروط المالية -->
                <div class="form-section">
                    <h6 class="form-section-title">
                        <i class="fas fa-money-check me-2"></i>الشروط المالية
                    </h6>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">شروط الدفع</label>
                            <select class="form-select" id="supplierPaymentTerms">
                                <option value="">اختر شروط الدفع</option>
                                <option value="فوري">فوري</option>
                                <option value="30 يوم">30 يوم</option>
                                <option value="60 يوم">60 يوم</option>
                                <option value="90 يوم">90 يوم</option>
                                <option value="حسب الاتفاق">حسب الاتفاق</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">العملة المفضلة</label>
                            <select class="form-select" id="supplierPreferredCurrency">
                                <option value="YER">ريال يمني</option>
                                <option value="SAR">ريال سعودي</option>
                                <option value="USD">دولار أمريكي</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">الحد الائتماني</label>
                            <input type="number" class="form-control" id="supplierCreditLimit" step="0.01">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">نسبة الخصم %</label>
                            <input type="number" class="form-control" id="supplierDiscountRate" step="0.01" min="0" max="100">
                        </div>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="form-section">
                    <h6 class="form-section-title">
                        <i class="fas fa-info-circle me-2"></i>معلومات إضافية
                    </h6>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">تقييم المورد</label>
                            <select class="form-select" id="supplierRating">
                                <option value="">اختر التقييم</option>
                                <option value="ممتاز">ممتاز</option>
                                <option value="جيد جداً">جيد جداً</option>
                                <option value="جيد">جيد</option>
                                <option value="مقبول">مقبول</option>
                                <option value="ضعيف">ضعيف</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">حالة المورد</label>
                            <select class="form-select" id="supplierStatus">
                                <option value="نشط">نشط</option>
                                <option value="غير نشط">غير نشط</option>
                                <option value="معلق">معلق</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="supplierNotes" rows="3"></textarea>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <div class="sidebar-footer">
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-secondary flex-fill" onclick="hideSupplierSidebar()">
                    <i class="fas fa-times me-2"></i>إلغاء
                </button>
                <button type="button" class="btn btn-success flex-fill" onclick="saveSupplier()">
                    <i class="fas fa-save me-2"></i>حفظ
                </button>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة/تعديل وكيل -->
    <div class="sidebar-overlay" id="agentSidebarOverlay"></div>
    <div class="sidebar" id="agentSidebar">
        <div class="sidebar-header">
            <h5 class="sidebar-title">
                <i class="fas fa-handshake me-2"></i>
                <span id="agentSidebarTitle">إضافة وكيل جديد</span>
            </h5>
            <button class="btn-close-sidebar" onclick="hideAgentSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="sidebar-content">
            <form id="agentForm">
                <!-- معلومات أساسية -->
                <div class="form-section">
                    <h6 class="form-section-title">
                        <i class="fas fa-user me-2"></i>معلومات الوكيل
                    </h6>

                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label required">اسم الوكيل</label>
                            <input type="text" class="form-control" id="agentName" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="agentPhone">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="agentEmail">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">رقم الهوية</label>
                            <input type="text" class="form-control" id="agentIdNumber">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">تاريخ الميلاد</label>
                            <input type="date" class="form-control" id="agentBirthDate">
                        </div>
                        <div class="col-12">
                            <label class="form-label">العنوان</label>
                            <textarea class="form-control" id="agentAddress" rows="2"></textarea>
                        </div>
                    </div>
                </div>

                <!-- معلومات العمل -->
                <div class="form-section">
                    <h6 class="form-section-title">
                        <i class="fas fa-briefcase me-2"></i>معلومات العمل
                    </h6>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">نسبة العمولة %</label>
                            <input type="number" class="form-control" id="agentCommissionRate" step="0.01" min="0" max="100">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">تاريخ بداية العمل</label>
                            <input type="date" class="form-control" id="agentStartDate">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">المنطقة المسؤول عنها</label>
                            <input type="text" class="form-control" id="agentArea">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">حالة الوكيل</label>
                            <select class="form-select" id="agentStatus">
                                <option value="نشط">نشط</option>
                                <option value="غير نشط">غير نشط</option>
                                <option value="معلق">معلق</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="agentNotes" rows="3"></textarea>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <div class="sidebar-footer">
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-secondary flex-fill" onclick="hideAgentSidebar()">
                    <i class="fas fa-times me-2"></i>إلغاء
                </button>
                <button type="button" class="btn btn-info flex-fill" onclick="saveAgent()">
                    <i class="fas fa-save me-2"></i>حفظ
                </button>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

    <!-- Core Systems -->
    <script src="src/js/core/state.js"></script>
    <script src="src/js/core/database.js"></script>
    <script src="src/js/core/auth.js"></script>
    <script src="src/js/core/router.js"></script>
    <script src="src/js/core/accounting.js"></script>

    <!-- Utilities -->
    <script src="src/js/utils/helpers.js"></script>
    <script src="src/js/utils/validation.js"></script>
    <script src="src/js/utils/performance.js"></script>
    <script src="src/js/utils/cache.js"></script>
    <script src="src/js/utils/security.js"></script>
    <script src="src/js/utils/ui.js"></script>
    <script src="src/js/utils/modals.js"></script>
    <script src="src/js/utils/navigation.js"></script>
    <script src="src/js/utils/search.js"></script>
    <script src="src/js/utils/export.js"></script>
    <script src="src/js/utils/reports.js"></script>
    <script src="src/js/utils/testing.js"></script>
    <script src="src/js/utils/notifications.js"></script>

    <!-- Components -->
    <script src="src/js/components/customers.js"></script>
    <script src="src/js/components/suppliers.js"></script>
    <script src="src/js/components/agents.js"></script>
    <script src="src/js/components/bookings.js"></script>
    <script src="src/js/components/sales.js"></script>
    <script src="src/js/components/accounting.js"></script>

    <!-- Sales System Fix -->
    <script src="fix_sales.js"></script>

    <!-- UI Enhancements -->
    <script src="src/js/sales-ui-enhancements.js"></script>

    <!-- Legacy Files (for backward compatibility) -->
    <script src="js/database.js"></script>
    <script src="js/main.js"></script>

    <!-- Main App -->
    <script src="src/js/core/app.js"></script>

    <script>
        // التحقق من تسجيل الدخول
        document.addEventListener('DOMContentLoaded', function() {
            if (localStorage.getItem('isLoggedIn') !== 'true') {
                window.location.href = 'login.html';
                return;
            }

            // عرض معلومات المستخدم
            const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
            const userInfo = document.getElementById('user-info');
            if (userInfo && currentUser.fullName) {
                userInfo.innerHTML = `
                    <i class="fas fa-user-circle me-2"></i>
                    ${currentUser.fullName}
                `;
            }

            // تهيئة الأنظمة
            if (window.Accounting) {
                window.Accounting.init();
            }
            if (window.AccountingComponent) {
                window.AccountingComponent.init();
            }
            if (window.SalesComponent) {
                try {
                    window.SalesComponent.init();
                    console.log('✅ تم تهيئة مكون المبيعات بنجاح');
                } catch (error) {
                    console.error('❌ خطأ في تهيئة مكون المبيعات:', error);
                }
            } else {
                console.error('❌ مكون المبيعات غير محمل');
            }
        });

        // وظائف النظام المحاسبي
        function showAccountingDashboard() {
            const content = document.getElementById('main-content');
            content.innerHTML = window.AccountingComponent.render({ view: 'dashboard' });
        }

        function showAccounts() {
            const content = document.getElementById('main-content');
            content.innerHTML = window.AccountingComponent.render({ view: 'accounts' });
        }

        function showJournalEntries() {
            const content = document.getElementById('main-content');
            content.innerHTML = window.AccountingComponent.render({ view: 'entries' });
        }

        function showAccountingReports() {
            const content = document.getElementById('main-content');
            content.innerHTML = window.AccountingComponent.render({ view: 'reports' });
        }

        function showPayments() {
            const content = document.getElementById('main-content');
            content.innerHTML = window.AccountingComponent.render({ view: 'payments' });
        }

        function showAgentsAndSuppliersAccounts() {
            // عرض تقرير خاص بحسابات الوكلاء والموردين
            const content = document.getElementById('main-content');
            content.innerHTML = `
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-handshake me-2"></i>حسابات الوكلاء</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>الوكيل</th>
                                                    <th>رمز الحساب</th>
                                                    <th>الرصيد</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody id="agentsAccountsTable">
                                                <!-- سيتم ملؤها ديناميكياً -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-truck me-2"></i>حسابات الموردين</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>المورد</th>
                                                    <th>رمز الحساب</th>
                                                    <th>الرصيد</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody id="suppliersAccountsTable">
                                                <!-- سيتم ملؤها ديناميكياً -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // تحميل بيانات الوكلاء والموردين
            loadAgentsAndSuppliersAccounts();
        }

        function loadAgentsAndSuppliersAccounts() {
            // تحميل حسابات الوكلاء
            const agentsTable = document.getElementById('agentsAccountsTable');
            if (agentsTable && window.Database) {
                const agents = window.Database.findAll('agents');
                agentsTable.innerHTML = agents.map(agent => {
                    const accountCode = `5230${agent.id.slice(-3)}`;
                    const balance = window.Accounting.getAccountBalance(accountCode);
                    return `
                        <tr>
                            <td>${agent.name}</td>
                            <td>${accountCode}</td>
                            <td class="${balance > 0 ? 'text-danger' : balance < 0 ? 'text-success' : ''}">
                                ${window.Accounting.formatAmount(Math.abs(balance))}
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="viewAccountDetails('${accountCode}')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                }).join('');
            }

            // تحميل حسابات الموردين
            const suppliersTable = document.getElementById('suppliersAccountsTable');
            if (suppliersTable && window.Database) {
                const suppliers = window.Database.findAll('suppliers');
                suppliersTable.innerHTML = suppliers.map(supplier => {
                    const accountCode = `2110${supplier.id.slice(-3)}`;
                    const balance = window.Accounting.getAccountBalance(accountCode);
                    return `
                        <tr>
                            <td>${supplier.name}</td>
                            <td>${accountCode}</td>
                            <td class="${balance > 0 ? 'text-danger' : balance < 0 ? 'text-success' : ''}">
                                ${window.Accounting.formatAmount(Math.abs(balance))}
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="viewAccountDetails('${accountCode}')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                }).join('');
            }
        }

        function viewAccountDetails(accountCode) {
            // عرض تفاصيل الحساب
            if (window.AccountingComponent) {
                window.AccountingComponent.viewAccountDetails(accountCode);
            }
        }

        // وظائف إضافية للنظام المحاسبي
        function auditAccountingSystem() {
            if (window.Accounting) {
                const audit = window.Accounting.auditSystem();

                let message = 'نتائج فحص النظام المحاسبي:\n\n';

                if (audit.errors.length > 0) {
                    message += '❌ الأخطاء:\n';
                    audit.errors.forEach(error => message += `• ${error}\n`);
                    message += '\n';
                }

                if (audit.warnings.length > 0) {
                    message += '⚠️ التحذيرات:\n';
                    audit.warnings.forEach(warning => message += `• ${warning}\n`);
                    message += '\n';
                }

                if (audit.info.length > 0) {
                    message += 'ℹ️ المعلومات:\n';
                    audit.info.forEach(info => message += `• ${info}\n`);
                }

                alert(message);

                if (!audit.isValid) {
                    if (confirm('هل تريد محاولة الإصلاح التلقائي؟')) {
                        const fixResults = window.Accounting.autoFix();
                        let fixMessage = 'نتائج الإصلاح التلقائي:\n\n';

                        if (fixResults.fixed.length > 0) {
                            fixMessage += '✅ تم إصلاحه:\n';
                            fixResults.fixed.forEach(fix => fixMessage += `• ${fix}\n`);
                        }

                        if (fixResults.skipped.length > 0) {
                            fixMessage += '\n❌ لم يتم إصلاحه:\n';
                            fixResults.skipped.forEach(skip => fixMessage += `• ${skip}\n`);
                        }

                        alert(fixMessage);
                    }
                }
            }
        }

        function createAccountingBackup() {
            if (window.Accounting) {
                const success = window.Accounting.createBackup();
                if (success) {
                    alert('تم إنشاء النسخة الاحتياطية بنجاح');
                } else {
                    alert('فشل في إنشاء النسخة الاحتياطية');
                }
            }
        }

        function restoreAccountingBackup() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';

            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file && window.Accounting) {
                    window.Accounting.restoreFromBackup(file)
                        .then(result => {
                            alert(result.message);
                            if (result.success) {
                                location.reload(); // إعادة تحميل الصفحة لتطبيق البيانات الجديدة
                            }
                        })
                        .catch(error => {
                            alert(error.message);
                        });
                }
            };

            input.click();
        }

        // إضافة قائمة أدوات النظام المحاسبي
        function showAccountingTools() {
            const toolsHTML = `
                <div class="modal fade" id="accountingToolsModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">أدوات النظام المحاسبي</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="list-group">
                                    <button type="button" class="list-group-item list-group-item-action" onclick="auditAccountingSystem(); bootstrap.Modal.getInstance(document.getElementById('accountingToolsModal')).hide();">
                                        <i class="fas fa-search text-primary me-2"></i>
                                        <strong>فحص النظام المحاسبي</strong>
                                        <small class="d-block text-muted">فحص شامل للنظام واكتشاف المشاكل</small>
                                    </button>

                                    <button type="button" class="list-group-item list-group-item-action" onclick="createAccountingBackup(); bootstrap.Modal.getInstance(document.getElementById('accountingToolsModal')).hide();">
                                        <i class="fas fa-download text-success me-2"></i>
                                        <strong>إنشاء نسخة احتياطية</strong>
                                        <small class="d-block text-muted">حفظ نسخة احتياطية من البيانات المحاسبية</small>
                                    </button>

                                    <button type="button" class="list-group-item list-group-item-action" onclick="restoreAccountingBackup(); bootstrap.Modal.getInstance(document.getElementById('accountingToolsModal')).hide();">
                                        <i class="fas fa-upload text-warning me-2"></i>
                                        <strong>استعادة نسخة احتياطية</strong>
                                        <small class="d-block text-muted">استعادة البيانات من نسخة احتياطية</small>
                                    </button>

                                    <button type="button" class="list-group-item list-group-item-action" onclick="window.Accounting && window.Accounting.updateBalances(); alert('تم تحديث جميع الأرصدة'); bootstrap.Modal.getInstance(document.getElementById('accountingToolsModal')).hide();">
                                        <i class="fas fa-sync text-info me-2"></i>
                                        <strong>تحديث الأرصدة</strong>
                                        <small class="d-block text-muted">إعادة حساب جميع أرصدة الحسابات</small>
                                    </button>

                                    <button type="button" class="list-group-item list-group-item-action" onclick="if(window.Accounting) { const audit = window.Accounting.auditSystem(); if(audit.isValid) alert('النظام المحاسبي سليم ✓'); else alert('يوجد مشاكل في النظام ✗'); } bootstrap.Modal.getInstance(document.getElementById('accountingToolsModal')).hide();">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        <strong>فحص سريع</strong>
                                        <small class="d-block text-muted">فحص سريع لحالة النظام</small>
                                    </button>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // إضافة النافذة للصفحة
            document.body.insertAdjacentHTML('beforeend', toolsHTML);

            // عرض النافذة
            const modal = new bootstrap.Modal(document.getElementById('accountingToolsModal'));
            modal.show();

            // إزالة النافذة عند الإغلاق
            document.getElementById('accountingToolsModal').addEventListener('hidden.bs.modal', function() {
                this.remove();
            });
        }

        // وظائف نظام المبيعات - محسنة مع معالجة أخطاء شاملة
        function showSalesDashboard() {
            console.log('🎯 طلب عرض لوحة تحكم المبيعات...');

            // التحقق من وجود الوظيفة المحسنة من fix_sales.js
            if (typeof window.showSalesDashboard === 'function' && window.showSalesDashboard !== showSalesDashboard) {
                return window.showSalesDashboard();
            }

            // الطريقة التقليدية مع تحسينات
            return executeSalesView('dashboard', 'لوحة التحكم');
        }

        function showInvoices() {
            console.log('🧾 طلب عرض الفواتير...');

            if (typeof window.showInvoices === 'function' && window.showInvoices !== showInvoices) {
                return window.showInvoices();
            }

            return executeSalesView('invoices', 'الفواتير');
        }

        function showSalesCustomers() {
            console.log('👥 طلب عرض عملاء المبيعات...');

            if (typeof window.showSalesCustomers === 'function' && window.showSalesCustomers !== showSalesCustomers) {
                return window.showSalesCustomers();
            }

            return executeSalesView('customers', 'العملاء');
        }

        function showSalesProducts() {
            console.log('📦 طلب عرض المنتجات...');

            if (typeof window.showSalesProducts === 'function' && window.showSalesProducts !== showSalesProducts) {
                return window.showSalesProducts();
            }

            return executeSalesView('products', 'المنتجات');
        }

        function showSalesReports() {
            console.log('📊 طلب عرض تقارير المبيعات...');

            if (typeof window.showSalesReports === 'function' && window.showSalesReports !== showSalesReports) {
                return window.showSalesReports();
            }

            return executeSalesView('reports', 'التقارير');
        }

        function showCreditNotes() {
            console.log('💳 طلب عرض الإشعارات الدائنة...');

            if (typeof window.showCreditNotes === 'function' && window.showCreditNotes !== showCreditNotes) {
                return window.showCreditNotes();
            }

            return executeSalesView('creditNotes', 'الإشعارات الدائنة');
        }

        function showNewCreditNote() {
            console.log('➕ طلب إنشاء إشعار دائن جديد...');

            if (typeof window.showNewCreditNote === 'function' && window.showNewCreditNote !== showNewCreditNote) {
                return window.showNewCreditNote();
            }

            // الطريقة التقليدية
            try {
                if (!window.SalesComponent) {
                    throw new Error('مكون المبيعات غير محمل');
                }

                // التأكد من تهيئة المكون
                if (typeof window.SalesComponent.init === 'function') {
                    window.SalesComponent.init();
                }

                if (typeof window.SalesComponent.showCreateCreditNoteModal === 'function') {
                    window.SalesComponent.showCreateCreditNoteModal();
                    console.log('✅ تم فتح نافذة الإشعار الدائن الجديد');
                    return true;
                } else {
                    // عرض صفحة الإشعارات الدائنة كبديل
                    return executeSalesView('creditNotes', 'الإشعارات الدائنة');
                }
            } catch (error) {
                console.error('❌ خطأ في إنشاء إشعار دائن جديد:', error);
                showSalesError('فشل في فتح نافذة الإشعار الدائن الجديد: ' + error.message);
                return false;
            }
        }

        function showNewInvoice() {
            console.log('➕ طلب إنشاء فاتورة جديدة...');

            if (typeof window.showNewInvoice === 'function' && window.showNewInvoice !== showNewInvoice) {
                return window.showNewInvoice();
            }

            // الطريقة التقليدية
            try {
                if (!window.SalesComponent) {
                    throw new Error('مكون المبيعات غير محمل');
                }

                // التأكد من تهيئة المكون
                if (typeof window.SalesComponent.init === 'function') {
                    window.SalesComponent.init();
                }

                if (typeof window.SalesComponent.showCreateInvoiceModal === 'function') {
                    window.SalesComponent.showCreateInvoiceModal();
                    console.log('✅ تم فتح نافذة الفاتورة الجديدة');
                    return true;
                } else {
                    // عرض صفحة الفواتير كبديل
                    return executeSalesView('invoices', 'الفواتير');
                }
            } catch (error) {
                console.error('❌ خطأ في إنشاء فاتورة جديدة:', error);
                showSalesError('فشل في فتح نافذة الفاتورة الجديدة: ' + error.message);
                return false;
            }
        }

        // وظيفة مساعدة لتنفيذ عرض صفحات المبيعات
        function executeSalesView(view, viewName) {
            try {
                if (!window.SalesComponent) {
                    throw new Error('مكون المبيعات غير محمل');
                }

                const container = document.getElementById('main-content');
                if (!container) {
                    throw new Error('عنصر العرض الرئيسي غير موجود');
                }

                // تهيئة المكون إذا لم يكن مهيئاً
                if (typeof window.SalesComponent.init === 'function') {
                    window.SalesComponent.init();
                }

                // عرض الصفحة المطلوبة
                window.SalesComponent.render({ view: view });
                console.log(`✅ تم عرض ${viewName} بنجاح`);
                return true;

            } catch (error) {
                console.error(`❌ خطأ في عرض ${viewName}:`, error);
                showSalesError(`فشل في عرض ${viewName}: ${error.message}`);
                return false;
            }
        }

        // وظيفة عرض أخطاء المبيعات
        function showSalesError(message) {
            console.error('❌ خطأ في نظام المبيعات:', message);

            // إظهار رسالة للمستخدم
            alert('خطأ في نظام المبيعات: ' + message);

            // إظهار رسالة في الواجهة
            const container = document.getElementById('main-content');
            if (container) {
                container.innerHTML = `
                    <div class="container-fluid">
                        <div class="alert alert-danger" role="alert">
                            <h4 class="alert-heading">
                                <i class="fas fa-exclamation-triangle me-2"></i>خطأ في نظام المبيعات
                            </h4>
                            <p>${message}</p>
                            <hr>
                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-primary" onclick="location.reload()">
                                    <i class="fas fa-refresh me-1"></i>تحديث الصفحة
                                </button>
                                <button class="btn btn-outline-info" onclick="testSalesSystem()">
                                    <i class="fas fa-vial me-1"></i>اختبار النظام
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        // وظيفة تسجيل الخروج
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('currentUser');
                window.location.href = 'login.html';
            }
        }

        // وظيفة اختبار نظام المبيعات
        function testSalesSystem() {
            console.log('🧪 اختبار نظام المبيعات...');

            if (typeof window.SalesComponent === 'undefined') {
                console.error('❌ مكون المبيعات غير محمل');
                alert('خطأ: مكون المبيعات غير محمل');
                return false;
            }

            console.log('✅ مكون المبيعات محمل');

            try {
                // اختبار التهيئة
                window.SalesComponent.init();
                console.log('✅ تم تهيئة المكون');

                // اختبار التشخيص
                const diagnosis = window.SalesComponent.diagnose();
                console.log('📊 تشخيص النظام:', diagnosis);

                // اختبار عرض لوحة التحكم
                window.SalesComponent.render({ view: 'dashboard' });
                console.log('✅ تم عرض لوحة التحكم');

                alert('✅ نظام المبيعات يعمل بشكل صحيح!');
                return true;

            } catch (error) {
                console.error('❌ خطأ في اختبار النظام:', error);
                alert('❌ خطأ في نظام المبيعات: ' + error.message);
                return false;
            }
        }

        // إضافة اختصار لوحة المفاتيح للاختبار (Ctrl+Shift+T)
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.shiftKey && e.key === 'T') {
                testSalesSystem();
            }
        });
    </script>

    <!-- تحميل تحسينات نافذة المبيعات -->
    <script src="sales_window_enhancements.js"></script>
    <!-- تحميل تحسينات ملخص الفواتير -->
    <script src="invoice_summary_enhancements.js"></script>
    <!-- تحميل إصلاح ربط عناصر الفاتورة بالملخص -->
    <script src="invoice_items_summary_fix.js"></script>
    <!-- تحميل نظام المشتريات -->
    <script src="src/js/components/purchases.js"></script>

    <!-- تحميل نظام اللوحة الجانبية -->
    <script>
        // نظام اللوحة الجانبية المدمج
        let sidebarVisible = false;

        // فتح/إغلاق اللوحة الجانبية
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            if (sidebarVisible) {
                closeSidebar();
            } else {
                openSidebar();
            }
        }

        // فتح اللوحة الجانبية
        function openSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            sidebar.style.display = 'block';
            overlay.style.display = 'block';

            // تأثير الانزلاق
            setTimeout(() => {
                sidebar.style.transform = 'translateX(0)';
                overlay.style.opacity = '1';
            }, 10);

            sidebarVisible = true;

            // تحديث أيقونة الزر
            const toggleBtn = document.getElementById('sidebarToggleBtn');
            if (toggleBtn) {
                toggleBtn.innerHTML = '<i class="fas fa-times"></i>';
                toggleBtn.title = 'إغلاق اللوحة الجانبية';
            }
        }

        // إغلاق اللوحة الجانبية
        function closeSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            sidebar.style.transform = 'translateX(100%)';
            overlay.style.opacity = '0';

            setTimeout(() => {
                sidebar.style.display = 'none';
                overlay.style.display = 'none';
            }, 300);

            sidebarVisible = false;

            // تحديث أيقونة الزر
            const toggleBtn = document.getElementById('sidebarToggleBtn');
            if (toggleBtn) {
                toggleBtn.innerHTML = '<i class="fas fa-bars"></i>';
                toggleBtn.title = 'فتح اللوحة الجانبية';
            }
        }

        // معالج النقر على العناصر التي تحتوي على قوائم فرعية
        document.addEventListener('click', function(e) {
            const navItem = e.target.closest('.nav-item.has-submenu');
            if (navItem && !e.target.closest('.submenu')) {
                e.preventDefault();
                toggleSubmenu(navItem);
            }
        });

        // فتح/إغلاق القوائم الفرعية
        function toggleSubmenu(navItem) {
            const isOpen = navItem.classList.contains('open');

            // إغلاق جميع القوائم الفرعية الأخرى
            document.querySelectorAll('.nav-item.has-submenu.open').forEach(item => {
                if (item !== navItem) {
                    item.classList.remove('open');
                }
            });

            // تبديل حالة القائمة الحالية
            navItem.classList.toggle('open', !isOpen);
        }

        // تحديد العنصر النشط
        function setActiveNavItem(functionName) {
            // إزالة الفئة النشطة من جميع العناصر
            document.querySelectorAll('.sidebar .nav-link.active').forEach(link => {
                link.classList.remove('active');
            });

            // البحث عن العنصر المطابق وتفعيله
            const activeLink = document.querySelector(`.sidebar [onclick*="${functionName}"]`);
            if (activeLink) {
                activeLink.classList.add('active');

                // فتح القائمة الفرعية إذا كان العنصر بداخلها
                const parentSubmenu = activeLink.closest('.submenu');
                if (parentSubmenu) {
                    const parentNavItem = parentSubmenu.closest('.nav-item.has-submenu');
                    if (parentNavItem) {
                        parentNavItem.classList.add('open');
                    }
                }
            }
        }

        // إغلاق اللوحة عند الضغط على Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && sidebarVisible) {
                closeSidebar();
            }
        });

        // تحديث العناصر النشطة عند استدعاء الوظائف
        const originalShowDashboard = window.showDashboard;
        window.showDashboard = function() {
            setActiveNavItem('showDashboard');
            if (originalShowDashboard) originalShowDashboard();
        };

        // إضافة تحديث العنصر النشط لجميع الوظائف الرئيسية
        const functionsToTrack = [
            'showCustomers', 'showAddCustomer', 'showAgents', 'showAddAgent',
            'showSuppliers', 'showAddSupplier', 'showBookings', 'showAddBooking',
            'showHajjUmrah', 'showVisaInventory', 'showInventory', 'showProducts',
            'showSalesDashboard', 'showInvoices', 'showSalesCustomers', 'showSalesProducts',
            'showCreditNotes', 'showSalesReports', 'showNewInvoice', 'showNewCreditNote',
            'testSalesSystem', 'showPurchasesDashboard', 'showPurchaseInvoices',
            'showPurchaseOrders', 'showPurchaseQuotes', 'showPurchasePayments',
            'showReceivedGoods', 'showPurchaseReturns', 'showPurchaseReports',
            'showNewPurchaseOrder', 'showNewPurchaseInvoice', 'openPurchasesWindow',
            'showAccountingDashboard', 'showAccounts', 'showJournalEntries',
            'showPayments', 'showAccountingReports', 'showAgentsAndSuppliersAccounts',
            'showAccountingTools', 'showCustomerReports', 'showAgentReports',
            'showTransactionReports', 'showFinancialReports', 'showUserManagement',
            'showBackupSettings', 'showSystemSettings', 'showTransportCompanies',
            'showUserProfile', 'showSettings'
        ];

        functionsToTrack.forEach(funcName => {
            const originalFunc = window[funcName];
            if (originalFunc) {
                window[funcName] = function() {
                    setActiveNavItem(funcName);
                    return originalFunc.apply(this, arguments);
                };
            }
        });

        // وظائف مؤقتة للصفحات غير المطورة
        function showCustomers() { showPlaceholderPage('العملاء', 'fas fa-users', 'إدارة قاعدة بيانات العملاء'); }
        function showAddCustomer() { showPlaceholderPage('إضافة عميل جديد', 'fas fa-user-plus', 'نموذج إضافة عميل جديد'); }
        function showAgents() { showPlaceholderPage('الوكلاء', 'fas fa-handshake', 'إدارة شبكة الوكلاء المعتمدين'); }
        function showAddAgent() { showPlaceholderPage('إضافة وكيل جديد', 'fas fa-user-plus', 'نموذج إضافة وكيل جديد'); }
        function showAddSupplier() { showPlaceholderPage('إضافة مورد جديد', 'fas fa-plus', 'نموذج إضافة مورد جديد'); }
        function showBookings() { showPlaceholderPage('الحجوزات', 'fas fa-calendar-check', 'إدارة جميع الحجوزات والرحلات'); }
        function showAddBooking() { showPlaceholderPage('حجز جديد', 'fas fa-plus', 'نموذج إنشاء حجز جديد'); }
        function showHajjUmrah() { showPlaceholderPage('حج وعمرة', 'fas fa-kaaba', 'إدارة رحلات الحج والعمرة'); }
        function showVisaInventory() { showPlaceholderPage('مخزون التأشيرات', 'fas fa-passport', 'إدارة مخزون التأشيرات'); }
        function showInventory() { showPlaceholderPage('المخزون العام', 'fas fa-warehouse', 'إدارة المخزون العام'); }
        function showProducts() { showPlaceholderPage('المنتجات والخدمات', 'fas fa-tags', 'إدارة المنتجات والخدمات'); }
        function showSalesCustomers() { showPlaceholderPage('عملاء المبيعات', 'fas fa-users', 'إدارة عملاء المبيعات'); }
        function showSalesProducts() { showPlaceholderPage('منتجات المبيعات', 'fas fa-box', 'إدارة منتجات المبيعات'); }
        function showCreditNotes() { showPlaceholderPage('الإشعارات الدائنة', 'fas fa-file-invoice-dollar', 'إدارة الإشعارات الدائنة'); }
        function showSalesReports() { showPlaceholderPage('تقارير المبيعات', 'fas fa-chart-bar', 'تقارير المبيعات التفصيلية'); }
        function showPurchaseQuotes() { showPlaceholderPage('عروض أسعار المشتريات', 'fas fa-file-contract', 'إدارة عروض أسعار المشتريات'); }
        function showPurchasePayments() { showPlaceholderPage('مدفوعات المشتريات', 'fas fa-credit-card', 'إدارة مدفوعات المشتريات'); }
        function showReceivedGoods() { showPlaceholderPage('البضائع المستلمة', 'fas fa-boxes', 'إدارة البضائع المستلمة'); }
        function showPurchaseReturns() { showPlaceholderPage('مرتجعات المشتريات', 'fas fa-undo', 'إدارة مرتجعات المشتريات'); }
        function showAccounts() { showPlaceholderPage('دليل الحسابات', 'fas fa-book', 'إدارة دليل الحسابات'); }
        function showJournalEntries() { showPlaceholderPage('القيود المحاسبية', 'fas fa-edit', 'إدارة القيود المحاسبية'); }
        function showPayments() { showPlaceholderPage('المدفوعات والمقبوضات', 'fas fa-money-bill-wave', 'إدارة المدفوعات والمقبوضات'); }
        function showAccountingReports() { showPlaceholderPage('التقارير المالية', 'fas fa-chart-bar', 'التقارير المالية الشاملة'); }
        function showCustomerReports() { showPlaceholderPage('تقارير العملاء', 'fas fa-users', 'تقارير العملاء التفصيلية'); }
        function showAgentReports() { showPlaceholderPage('تقارير الوكلاء', 'fas fa-handshake', 'تقارير الوكلاء التفصيلية'); }
        function showTransactionReports() { showPlaceholderPage('تقارير المعاملات', 'fas fa-exchange-alt', 'تقارير المعاملات التفصيلية'); }
        function showFinancialReports() { showPlaceholderPage('التقارير المالية', 'fas fa-chart-line', 'التقارير المالية الشاملة'); }
        function showUserManagement() { showPlaceholderPage('إدارة المستخدمين', 'fas fa-users-cog', 'إدارة المستخدمين والصلاحيات'); }
        function showBackupSettings() { showPlaceholderPage('النسخ الاحتياطية', 'fas fa-database', 'إعدادات النسخ الاحتياطية'); }
        function showSystemSettings() { showPlaceholderPage('الإعدادات العامة', 'fas fa-sliders-h', 'الإعدادات العامة للنظام'); }
        function showTransportCompanies() { showPlaceholderPage('شركات النقل', 'fas fa-bus', 'إدارة شركات النقل'); }
        function showNewInvoice() { showPlaceholderPage('فاتورة جديدة', 'fas fa-plus', 'نموذج إنشاء فاتورة جديدة'); }
        function showNewCreditNote() { showPlaceholderPage('إشعار دائن جديد', 'fas fa-plus', 'نموذج إنشاء إشعار دائن جديد'); }
        function showNewPurchaseOrder() { showPlaceholderPage('أمر شراء جديد', 'fas fa-plus', 'نموذج إنشاء أمر شراء جديد'); }
        function showNewPurchaseInvoice() { showPlaceholderPage('فاتورة شراء جديدة', 'fas fa-file-invoice', 'نموذج إنشاء فاتورة شراء جديدة'); }
        function showPurchaseReports() { showPlaceholderPage('تقارير المشتريات', 'fas fa-chart-bar', 'تقارير المشتريات التفصيلية'); }
        function showAgentsAndSuppliersAccounts() { showPlaceholderPage('حسابات الوكلاء والموردين', 'fas fa-users-cog', 'إدارة حسابات الوكلاء والموردين'); }
        function showAccountingTools() { showPlaceholderPage('أدوات النظام المحاسبي', 'fas fa-tools', 'أدوات وإعدادات النظام المحاسبي'); }
        function showUserProfile() { showPlaceholderPage('الملف الشخصي', 'fas fa-user', 'إدارة الملف الشخصي للمستخدم'); }
        function showSettings() { showPlaceholderPage('الإعدادات', 'fas fa-cog', 'إعدادات النظام العامة'); }
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                alert('تم تسجيل الخروج بنجاح');
                location.reload();
            }
        }

        // وظيفة عرض صفحة مؤقتة
        function showPlaceholderPage(title, icon, description) {
            document.getElementById('main-content').innerHTML = `
                <div class="container-fluid">
                    <div class="text-center py-5">
                        <div class="mb-4">
                            <i class="${icon} fa-5x text-muted"></i>
                        </div>
                        <h3 class="text-muted">${title}</h3>
                        <p class="text-muted fs-5">${description}</p>
                        <p class="text-muted">هذه الصفحة قيد التطوير...</p>
                        <button class="btn btn-primary" onclick="showDashboard()">
                            <i class="fas fa-arrow-left me-1"></i>العودة للرئيسية
                        </button>
                    </div>
                </div>
            `;
        }

        console.log('✅ تم تحميل نظام اللوحة الجانبية بنجاح');
    </script>

    <script>
        // وظائف نظام المشتريات
        function showPurchasesDashboard() {
            console.log('🛒 طلب عرض لوحة تحكم المشتريات...');

            try {
                if (!window.PurchasesComponent) {
                    throw new Error('مكون المشتريات غير محمل');
                }

                // التأكد من تهيئة المكون
                if (typeof window.PurchasesComponent.init === 'function') {
                    window.PurchasesComponent.init();
                }

                // عرض لوحة التحكم
                if (typeof window.PurchasesComponent.showDashboard === 'function') {
                    const container = document.getElementById('main-content');
                    if (container) {
                        container.innerHTML = '<div id="purchases-container"></div>';
                        window.PurchasesComponent.render();
                        console.log('✅ تم عرض لوحة تحكم المشتريات');
                        return true;
                    }
                }

                throw new Error('وظيفة عرض لوحة التحكم غير متاحة');

            } catch (error) {
                console.error('❌ خطأ في عرض لوحة تحكم المشتريات:', error);
                showPurchasesError('فشل في عرض لوحة تحكم المشتريات: ' + error.message);
                return false;
            }
        }

        function showSuppliers() {
            console.log('🚚 طلب عرض الموردين...');
            return executePurchasesView('suppliers', 'الموردين');
        }

        function showPurchaseInvoices() {
            console.log('📄 طلب عرض فواتير المشتريات...');
            return executePurchasesView('purchase-invoices', 'فواتير المشتريات');
        }

        function showPurchaseOrders() {
            console.log('📋 طلب عرض أوامر الشراء...');
            return executePurchasesView('purchase-orders', 'أوامر الشراء');
        }

        function showPurchaseQuotes() {
            console.log('📑 طلب عرض عروض أسعار المشتريات...');
            return executePurchasesView('purchase-quotes', 'عروض أسعار المشتريات');
        }

        function showPurchasePayments() {
            console.log('💳 طلب عرض مدفوعات المشتريات...');
            return executePurchasesView('purchase-payments', 'مدفوعات المشتريات');
        }

        function showReceivedGoods() {
            console.log('📦 طلب عرض البضائع المستلمة...');
            return executePurchasesView('received-goods', 'البضائع المستلمة');
        }

        function showPurchaseReturns() {
            console.log('↩️ طلب عرض مرتجعات المشتريات...');
            return executePurchasesView('purchase-returns', 'مرتجعات المشتريات');
        }

        function showPurchaseReports() {
            console.log('📊 طلب عرض تقارير المشتريات...');
            return executePurchasesView('purchase-reports', 'تقارير المشتريات');
        }

        function showNewPurchaseOrder() {
            console.log('➕ طلب إنشاء أمر شراء جديد...');

            try {
                if (!window.PurchasesComponent) {
                    throw new Error('مكون المشتريات غير محمل');
                }

                if (typeof window.PurchasesComponent.showCreatePurchaseOrderModal === 'function') {
                    window.PurchasesComponent.showCreatePurchaseOrderModal();
                    return true;
                } else {
                    throw new Error('وظيفة إنشاء أمر الشراء غير متاحة');
                }

            } catch (error) {
                console.error('❌ خطأ في إنشاء أمر شراء جديد:', error);
                showPurchasesError('فشل في إنشاء أمر شراء جديد: ' + error.message);
                return false;
            }
        }

        function showNewPurchaseInvoice() {
            console.log('📄 طلب إنشاء فاتورة شراء جديدة...');

            try {
                if (!window.PurchasesComponent) {
                    throw new Error('مكون المشتريات غير محمل');
                }

                if (typeof window.PurchasesComponent.showCreatePurchaseInvoiceModal === 'function') {
                    window.PurchasesComponent.showCreatePurchaseInvoiceModal();
                    return true;
                } else {
                    throw new Error('وظيفة إنشاء فاتورة الشراء غير متاحة');
                }

            } catch (error) {
                console.error('❌ خطأ في إنشاء فاتورة شراء جديدة:', error);
                showPurchasesError('فشل في إنشاء فاتورة شراء جديدة: ' + error.message);
                return false;
            }
        }

        function openPurchasesWindow() {
            console.log('🪟 فتح نافذة المشتريات المستقلة...');

            try {
                const purchasesWindow = window.open('purchases.html', 'PurchasesWindow',
                    'width=1400,height=900,scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no');

                if (purchasesWindow) {
                    purchasesWindow.focus();
                    console.log('✅ تم فتح نافذة المشتريات');
                    return true;
                } else {
                    throw new Error('فشل في فتح النافذة - قد يكون محجوبة بواسطة متصفح');
                }

            } catch (error) {
                console.error('❌ خطأ في فتح نافذة المشتريات:', error);
                alert('فشل في فتح نافذة المشتريات: ' + error.message);
                return false;
            }
        }

        // وظيفة مساعدة لتنفيذ عرض صفحات المشتريات
        function executePurchasesView(view, viewName) {
            try {
                if (!window.PurchasesComponent) {
                    throw new Error('مكون المشتريات غير محمل');
                }

                const container = document.getElementById('main-content');
                if (!container) {
                    throw new Error('عنصر العرض الرئيسي غير موجود');
                }

                // إنشاء حاوي المشتريات
                container.innerHTML = '<div id="purchases-container"></div>';

                // تهيئة المكون
                window.PurchasesComponent.init();

                // عرض الصفحة المطلوبة
                const viewFunction = 'show' + view.split('-').map(word =>
                    word.charAt(0).toUpperCase() + word.slice(1)
                ).join('');

                if (typeof window.PurchasesComponent[viewFunction] === 'function') {
                    window.PurchasesComponent[viewFunction]();
                } else {
                    // عرض الصفحة الافتراضية
                    window.PurchasesComponent.render();
                }

                console.log(`✅ تم عرض ${viewName}`);
                return true;

            } catch (error) {
                console.error(`❌ خطأ في عرض ${viewName}:`, error);
                showPurchasesError(`فشل في عرض ${viewName}: ${error.message}`);
                return false;
            }
        }

        // وظيفة عرض أخطاء المشتريات
        function showPurchasesError(message) {
            console.error('❌ خطأ في نظام المشتريات:', message);

            // إظهار رسالة للمستخدم
            alert('خطأ في نظام المشتريات: ' + message);

            // إظهار رسالة في الواجهة
            const container = document.getElementById('main-content');
            if (container) {
                container.innerHTML = `
                    <div class="container-fluid">
                        <div class="alert alert-danger" role="alert">
                            <h4 class="alert-heading">
                                <i class="fas fa-exclamation-triangle me-2"></i>خطأ في نظام المشتريات
                            </h4>
                            <p>${message}</p>
                            <hr>
                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-primary" onclick="location.reload()">
                                    <i class="fas fa-sync me-1"></i>إعادة تحميل الصفحة
                                </button>
                                <button class="btn btn-outline-info" onclick="showDashboard()">
                                    <i class="fas fa-home me-1"></i>العودة للرئيسية
                                </button>
                                <button class="btn btn-outline-success" onclick="openPurchasesWindow()">
                                    <i class="fas fa-external-link-alt me-1"></i>فتح نافذة المشتريات
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        // تهيئة نظام المشتريات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            if (window.PurchasesComponent) {
                try {
                    console.log('🛒 تهيئة مكون المشتريات...');
                    // لا نقوم بالتهيئة الكاملة هنا، فقط التحقق من التوفر
                    console.log('✅ مكون المشتريات متاح');
                } catch (error) {
                    console.error('❌ خطأ في تهيئة مكون المشتريات:', error);
                }
            } else {
                console.warn('⚠️ مكون المشتريات غير محمل');
            }
        });
    </script>
</body>
</html>
