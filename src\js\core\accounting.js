/**
 * ===================================
 * النظام المحاسبي المتكامل - Integrated Accounting System
 * ===================================
 */

window.Accounting = {
    // إعدادات النظام المحاسبي
    config: {
        currency: 'SAR',
        currencySymbol: 'ر.س',
        decimalPlaces: 2,
        fiscalYearStart: '01-01', // يناير
        enableMultiCurrency: true,
        autoGenerateEntries: true,
        requireApproval: false
    },

    // حالة النظام المحاسبي
    state: {
        currentFiscalYear: new Date().getFullYear(),
        lastEntryNumber: 0,
        totalAssets: 0,
        totalLiabilities: 0,
        totalEquity: 0,
        totalRevenue: 0,
        totalExpenses: 0,
        isBalanced: true
    },

    // دليل الحسابات الرئيسي
    chartOfAccounts: {
        // 1000 - الأصول (Assets)
        1000: {
            code: '1000',
            name: 'الأصول',
            nameEn: 'Assets',
            type: 'asset',
            category: 'main',
            parent: null,
            level: 1,
            isActive: true,
            children: []
        },

        // 1100 - الأصول المتداولة
        1100: {
            code: '1100',
            name: 'الأصول المتداولة',
            nameEn: 'Current Assets',
            type: 'asset',
            category: 'current_asset',
            parent: '1000',
            level: 2,
            isActive: true,
            children: []
        },

        // 1110 - النقدية والبنوك
        1110: {
            code: '1110',
            name: 'النقدية والبنوك',
            nameEn: 'Cash and Banks',
            type: 'asset',
            category: 'cash',
            parent: '1100',
            level: 3,
            isActive: true,
            children: []
        },

        // 1111 - الصندوق
        1111: {
            code: '1111',
            name: 'الصندوق',
            nameEn: 'Cash Box',
            type: 'asset',
            category: 'cash',
            parent: '1110',
            level: 4,
            isActive: true,
            balance: 0,
            children: []
        },

        // 1112 - البنك الأهلي
        1112: {
            code: '1112',
            name: 'البنك الأهلي',
            nameEn: 'National Bank',
            type: 'asset',
            category: 'bank',
            parent: '1110',
            level: 4,
            isActive: true,
            balance: 0,
            children: []
        },

        // 1120 - العملاء والذمم المدينة
        1120: {
            code: '1120',
            name: 'العملاء والذمم المدينة',
            nameEn: 'Accounts Receivable',
            type: 'asset',
            category: 'receivable',
            parent: '1100',
            level: 3,
            isActive: true,
            children: []
        },

        // 1130 - المخزون
        1130: {
            code: '1130',
            name: 'المخزون',
            nameEn: 'Inventory',
            type: 'asset',
            category: 'inventory',
            parent: '1100',
            level: 3,
            isActive: true,
            balance: 0,
            children: []
        },

        // 1200 - الأصول الثابتة
        1200: {
            code: '1200',
            name: 'الأصول الثابتة',
            nameEn: 'Fixed Assets',
            type: 'asset',
            category: 'fixed_asset',
            parent: '1000',
            level: 2,
            isActive: true,
            children: []
        },

        // 2000 - الخصوم (Liabilities)
        2000: {
            code: '2000',
            name: 'الخصوم',
            nameEn: 'Liabilities',
            type: 'liability',
            category: 'main',
            parent: null,
            level: 1,
            isActive: true,
            children: []
        },

        // 2100 - الخصوم المتداولة
        2100: {
            code: '2100',
            name: 'الخصوم المتداولة',
            nameEn: 'Current Liabilities',
            type: 'liability',
            category: 'current_liability',
            parent: '2000',
            level: 2,
            isActive: true,
            children: []
        },

        // 2110 - الموردون والذمم الدائنة
        2110: {
            code: '2110',
            name: 'الموردون والذمم الدائنة',
            nameEn: 'Accounts Payable',
            type: 'liability',
            category: 'payable',
            parent: '2100',
            level: 3,
            isActive: true,
            children: []
        },

        // 3000 - حقوق الملكية (Equity)
        3000: {
            code: '3000',
            name: 'حقوق الملكية',
            nameEn: 'Equity',
            type: 'equity',
            category: 'main',
            parent: null,
            level: 1,
            isActive: true,
            children: []
        },

        // 3100 - رأس المال
        3100: {
            code: '3100',
            name: 'رأس المال',
            nameEn: 'Capital',
            type: 'equity',
            category: 'capital',
            parent: '3000',
            level: 2,
            isActive: true,
            balance: 0,
            children: []
        },

        // 3200 - الأرباح المحتجزة
        3200: {
            code: '3200',
            name: 'الأرباح المحتجزة',
            nameEn: 'Retained Earnings',
            type: 'equity',
            category: 'retained_earnings',
            parent: '3000',
            level: 2,
            isActive: true,
            balance: 0,
            children: []
        },

        // 4000 - الإيرادات (Revenue)
        4000: {
            code: '4000',
            name: 'الإيرادات',
            nameEn: 'Revenue',
            type: 'revenue',
            category: 'main',
            parent: null,
            level: 1,
            isActive: true,
            children: []
        },

        // 4100 - إيرادات المبيعات
        4100: {
            code: '4100',
            name: 'إيرادات المبيعات',
            nameEn: 'Sales Revenue',
            type: 'revenue',
            category: 'sales',
            parent: '4000',
            level: 2,
            isActive: true,
            balance: 0,
            children: []
        },

        // 4110 - مبيعات تذاكر الطيران
        4110: {
            code: '4110',
            name: 'مبيعات تذاكر الطيران',
            nameEn: 'Flight Tickets Sales',
            type: 'revenue',
            category: 'flight_sales',
            parent: '4100',
            level: 3,
            isActive: true,
            balance: 0,
            children: []
        },

        // 4120 - مبيعات الفنادق
        4120: {
            code: '4120',
            name: 'مبيعات الفنادق',
            nameEn: 'Hotel Sales',
            type: 'revenue',
            category: 'hotel_sales',
            parent: '4100',
            level: 3,
            isActive: true,
            balance: 0,
            children: []
        },

        // 4130 - مبيعات الحج والعمرة
        4130: {
            code: '4130',
            name: 'مبيعات الحج والعمرة',
            nameEn: 'Hajj & Umrah Sales',
            type: 'revenue',
            category: 'hajj_sales',
            parent: '4100',
            level: 3,
            isActive: true,
            balance: 0,
            children: []
        },

        // 4140 - مبيعات التأشيرات
        4140: {
            code: '4140',
            name: 'مبيعات التأشيرات',
            nameEn: 'Visa Sales',
            type: 'revenue',
            category: 'visa_sales',
            parent: '4100',
            level: 3,
            isActive: true,
            balance: 0,
            children: []
        },

        // 5000 - المصروفات (Expenses)
        5000: {
            code: '5000',
            name: 'المصروفات',
            nameEn: 'Expenses',
            type: 'expense',
            category: 'main',
            parent: null,
            level: 1,
            isActive: true,
            children: []
        },

        // 5100 - تكلفة المبيعات
        5100: {
            code: '5100',
            name: 'تكلفة المبيعات',
            nameEn: 'Cost of Sales',
            type: 'expense',
            category: 'cost_of_sales',
            parent: '5000',
            level: 2,
            isActive: true,
            balance: 0,
            children: []
        },

        // 5200 - المصروفات التشغيلية
        5200: {
            code: '5200',
            name: 'المصروفات التشغيلية',
            nameEn: 'Operating Expenses',
            type: 'expense',
            category: 'operating',
            parent: '5000',
            level: 2,
            isActive: true,
            children: []
        },

        // 5210 - رواتب الموظفين
        5210: {
            code: '5210',
            name: 'رواتب الموظفين',
            nameEn: 'Employee Salaries',
            type: 'expense',
            category: 'salaries',
            parent: '5200',
            level: 3,
            isActive: true,
            balance: 0,
            children: []
        },

        // 5220 - إيجار المكتب
        5220: {
            code: '5220',
            name: 'إيجار المكتب',
            nameEn: 'Office Rent',
            type: 'expense',
            category: 'rent',
            parent: '5200',
            level: 3,
            isActive: true,
            balance: 0,
            children: []
        },

        // 5230 - عمولات الوكلاء
        5230: {
            code: '5230',
            name: 'عمولات الوكلاء',
            nameEn: 'Agent Commissions',
            type: 'expense',
            category: 'commissions',
            parent: '5200',
            level: 3,
            isActive: true,
            balance: 0,
            children: []
        }
    },

    /**
     * تهيئة النظام المحاسبي
     */
    init: function() {
        console.log('💰 تهيئة النظام المحاسبي المتكامل');

        try {
            // تحميل البيانات المحاسبية
            this.loadAccountingData();

            // إنشاء الحسابات الفرعية للوكلاء والموردين
            this.createSubAccounts();

            // تحديث الأرصدة
            this.updateBalances();

            // إعداد الأحداث
            this.setupEventListeners();

            console.log('✅ تم تهيئة النظام المحاسبي بنجاح');

        } catch (error) {
            console.error('❌ خطأ في تهيئة النظام المحاسبي:', error);
        }
    },

    /**
     * تحميل البيانات المحاسبية
     */
    loadAccountingData: function() {
        // تحميل القيود المحاسبية
        const savedEntries = localStorage.getItem('accounting_entries');
        if (savedEntries) {
            this.entries = JSON.parse(savedEntries);
        } else {
            this.entries = [];
        }

        // تحميل أرصدة الحسابات
        const savedBalances = localStorage.getItem('account_balances');
        if (savedBalances) {
            const balances = JSON.parse(savedBalances);
            Object.keys(balances).forEach(accountCode => {
                if (this.chartOfAccounts[accountCode]) {
                    this.chartOfAccounts[accountCode].balance = balances[accountCode];
                }
            });
        }

        // تحديث رقم القيد الأخير
        this.state.lastEntryNumber = this.entries.length > 0 ?
            Math.max(...this.entries.map(e => e.entryNumber)) : 0;
    },

    /**
     * إنشاء الحسابات الفرعية للوكلاء والموردين
     */
    createSubAccounts: function() {
        // إنشاء حسابات للوكلاء
        if (window.Database) {
            const agents = window.Database.findAll('agents');
            agents.forEach(agent => {
                this.createAgentAccount(agent);
            });

            // إنشاء حسابات للموردين
            const suppliers = window.Database.findAll('suppliers');
            suppliers.forEach(supplier => {
                this.createSupplierAccount(supplier);
            });

            // إنشاء حسابات للعملاء
            const customers = window.Database.findAll('customers');
            customers.forEach(customer => {
                this.createCustomerAccount(customer);
            });
        }
    },

    /**
     * إنشاء حساب للوكيل
     */
    createAgentAccount: function(agent) {
        const accountCode = `5230${agent.id.slice(-3)}`; // 5230 + آخر 3 أرقام من معرف الوكيل

        if (!this.chartOfAccounts[accountCode]) {
            this.chartOfAccounts[accountCode] = {
                code: accountCode,
                name: `عمولة الوكيل - ${agent.name}`,
                nameEn: `Agent Commission - ${agent.name}`,
                type: 'expense',
                category: 'agent_commission',
                parent: '5230',
                level: 4,
                isActive: true,
                balance: 0,
                agentId: agent.id,
                children: []
            };
        }
    },

    /**
     * إنشاء حساب للمورد
     */
    createSupplierAccount: function(supplier) {
        const accountCode = `2110${supplier.id.slice(-3)}`; // 2110 + آخر 3 أرقام من معرف المورد

        if (!this.chartOfAccounts[accountCode]) {
            this.chartOfAccounts[accountCode] = {
                code: accountCode,
                name: `المورد - ${supplier.name}`,
                nameEn: `Supplier - ${supplier.name}`,
                type: 'liability',
                category: 'supplier_payable',
                parent: '2110',
                level: 4,
                isActive: true,
                balance: 0,
                supplierId: supplier.id,
                children: []
            };
        }
    },

    /**
     * إنشاء حساب للعميل
     */
    createCustomerAccount: function(customer) {
        const accountCode = `1120${customer.id.slice(-3)}`; // 1120 + آخر 3 أرقام من معرف العميل

        if (!this.chartOfAccounts[accountCode]) {
            this.chartOfAccounts[accountCode] = {
                code: accountCode,
                name: `العميل - ${customer.name}`,
                nameEn: `Customer - ${customer.name}`,
                type: 'asset',
                category: 'customer_receivable',
                parent: '1120',
                level: 4,
                isActive: true,
                balance: 0,
                customerId: customer.id,
                children: []
            };
        }
    },

    /**
     * إنشاء قيد محاسبي
     */
    createJournalEntry: function(entryData) {
        try {
            // التحقق من صحة البيانات
            if (!this.validateJournalEntry(entryData)) {
                throw new Error('بيانات القيد غير صحيحة');
            }

            // إنشاء رقم القيد
            this.state.lastEntryNumber++;

            const entry = {
                entryNumber: this.state.lastEntryNumber,
                date: entryData.date || new Date().toISOString().split('T')[0],
                description: entryData.description,
                reference: entryData.reference || '',
                type: entryData.type || 'manual', // manual, auto, booking, payment
                status: 'posted', // draft, posted, approved
                createdBy: entryData.createdBy || 'system',
                createdAt: new Date().toISOString(),
                transactions: entryData.transactions,
                totalDebit: 0,
                totalCredit: 0
            };

            // حساب إجمالي المدين والدائن
            entry.transactions.forEach(transaction => {
                entry.totalDebit += transaction.debit || 0;
                entry.totalCredit += transaction.credit || 0;
            });

            // التحقق من توازن القيد
            if (Math.abs(entry.totalDebit - entry.totalCredit) > 0.01) {
                throw new Error('القيد غير متوازن - المدين لا يساوي الدائن');
            }

            // حفظ القيد
            this.entries.push(entry);

            // تحديث أرصدة الحسابات
            this.updateAccountBalances(entry);

            // حفظ في التخزين المحلي
            this.saveAccountingData();

            // إرسال حدث
            this.emit('entry:created', entry);

            console.log(`✅ تم إنشاء القيد رقم ${entry.entryNumber} بنجاح`);
            return entry;

        } catch (error) {
            console.error('❌ خطأ في إنشاء القيد:', error);
            throw error;
        }
    },

    /**
     * التحقق من صحة القيد المحاسبي
     */
    validateJournalEntry: function(entryData) {
        // التحقق من وجود المعاملات
        if (!entryData.transactions || entryData.transactions.length === 0) {
            return false;
        }

        // التحقق من وجود وصف
        if (!entryData.description || entryData.description.trim() === '') {
            return false;
        }

        // التحقق من صحة المعاملات
        let totalDebit = 0;
        let totalCredit = 0;

        for (let transaction of entryData.transactions) {
            // التحقق من وجود رمز الحساب
            if (!transaction.accountCode || !this.chartOfAccounts[transaction.accountCode]) {
                return false;
            }

            // التحقق من وجود مبلغ
            const debit = transaction.debit || 0;
            const credit = transaction.credit || 0;

            if (debit === 0 && credit === 0) {
                return false;
            }

            if (debit > 0 && credit > 0) {
                return false; // لا يمكن أن يكون الحساب مدين ودائن في نفس الوقت
            }

            totalDebit += debit;
            totalCredit += credit;
        }

        // التحقق من التوازن
        return Math.abs(totalDebit - totalCredit) < 0.01;
    },

    /**
     * تحديث أرصدة الحسابات
     */
    updateAccountBalances: function(entry) {
        entry.transactions.forEach(transaction => {
            const account = this.chartOfAccounts[transaction.accountCode];
            if (account) {
                if (!account.balance) account.balance = 0;

                // تحديث الرصيد حسب نوع الحساب
                if (account.type === 'asset' || account.type === 'expense') {
                    // الأصول والمصروفات تزيد بالمدين وتقل بالدائن
                    account.balance += (transaction.debit || 0) - (transaction.credit || 0);
                } else {
                    // الخصوم وحقوق الملكية والإيرادات تزيد بالدائن وتقل بالمدين
                    account.balance += (transaction.credit || 0) - (transaction.debit || 0);
                }
            }
        });
    },

    /**
     * إنشاء قيد مبيعات تلقائي
     */
    createSalesEntry: function(booking) {
        try {
            const transactions = [];
            const totalAmount = parseFloat(booking.total_amount) || 0;
            const agentCommission = this.calculateAgentCommission(booking);

            // تحديد حساب الإيراد حسب نوع الحجز
            let revenueAccount = '4100'; // إيرادات المبيعات العامة
            switch (booking.booking_type) {
                case 'طيران':
                    revenueAccount = '4110';
                    break;
                case 'فندق':
                    revenueAccount = '4120';
                    break;
                case 'حج/عمرة':
                    revenueAccount = '4130';
                    break;
                case 'تأشيرة':
                    revenueAccount = '4140';
                    break;
            }

            // قيد الإيراد (دائن)
            transactions.push({
                accountCode: revenueAccount,
                description: `مبيعات ${booking.booking_type} - ${booking.customer_name}`,
                debit: 0,
                credit: totalAmount
            });

            // قيد النقدية أو العميل (مدين)
            if (booking.payment_status === 'مدفوع') {
                transactions.push({
                    accountCode: '1111', // الصندوق
                    description: `استلام نقدية من ${booking.customer_name}`,
                    debit: totalAmount,
                    credit: 0
                });
            } else {
                // إنشاء حساب العميل إذا لم يكن موجوداً
                const customer = window.Database.find('customers', booking.customer_id);
                if (customer) {
                    this.createCustomerAccount(customer);
                    const customerAccountCode = `1120${customer.id.slice(-3)}`;

                    transactions.push({
                        accountCode: customerAccountCode,
                        description: `ذمة العميل ${booking.customer_name}`,
                        debit: totalAmount,
                        credit: 0
                    });
                }
            }

            // قيد عمولة الوكيل إذا وجدت
            if (agentCommission > 0 && booking.agent_id) {
                const agent = window.Database.find('agents', booking.agent_id);
                if (agent) {
                    this.createAgentAccount(agent);
                    const agentAccountCode = `5230${agent.id.slice(-3)}`;

                    transactions.push({
                        accountCode: agentAccountCode,
                        description: `عمولة الوكيل ${agent.name}`,
                        debit: agentCommission,
                        credit: 0
                    });

                    // تقليل الإيراد بقيمة العمولة
                    transactions[0].credit -= agentCommission;
                }
            }

            // إنشاء القيد
            const entryData = {
                description: `قيد مبيعات - حجز رقم ${booking.booking_number}`,
                reference: booking.booking_number,
                type: 'booking',
                transactions: transactions
            };

            return this.createJournalEntry(entryData);

        } catch (error) {
            console.error('❌ خطأ في إنشاء قيد المبيعات:', error);
            throw error;
        }
    },

    /**
     * حساب عمولة الوكيل
     */
    calculateAgentCommission: function(booking) {
        if (!booking.agent_id) return 0;

        const agent = window.Database.find('agents', booking.agent_id);
        if (!agent || !agent.commission_rate) return 0;

        const totalAmount = parseFloat(booking.total_amount) || 0;
        const commissionRate = parseFloat(agent.commission_rate) || 0;

        return (totalAmount * commissionRate) / 100;
    },

    /**
     * إنشاء قيد دفع للمورد
     */
    createSupplierPaymentEntry: function(payment) {
        try {
            const transactions = [];
            const amount = parseFloat(payment.amount) || 0;

            // البحث عن المورد
            const supplier = window.Database.find('suppliers', payment.supplier_id);
            if (!supplier) {
                throw new Error('المورد غير موجود');
            }

            // إنشاء حساب المورد إذا لم يكن موجوداً
            this.createSupplierAccount(supplier);
            const supplierAccountCode = `2110${supplier.id.slice(-3)}`;

            // قيد تقليل ذمة المورد (مدين)
            transactions.push({
                accountCode: supplierAccountCode,
                description: `دفع للمورد ${supplier.name}`,
                debit: amount,
                credit: 0
            });

            // قيد تقليل النقدية (دائن)
            const paymentMethod = payment.payment_method || 'cash';
            const cashAccount = paymentMethod === 'bank' ? '1112' : '1111';

            transactions.push({
                accountCode: cashAccount,
                description: `دفع ${paymentMethod === 'bank' ? 'بنكي' : 'نقدي'} للمورد ${supplier.name}`,
                debit: 0,
                credit: amount
            });

            // إنشاء القيد
            const entryData = {
                description: `دفع للمورد ${supplier.name}`,
                reference: payment.reference || '',
                type: 'payment',
                transactions: transactions
            };

            return this.createJournalEntry(entryData);

        } catch (error) {
            console.error('❌ خطأ في إنشاء قيد الدفع:', error);
            throw error;
        }
    },

    /**
     * إنشاء قيد استلام من العميل
     */
    createCustomerReceiptEntry: function(receipt) {
        try {
            const transactions = [];
            const amount = parseFloat(receipt.amount) || 0;

            // البحث عن العميل
            const customer = window.Database.find('customers', receipt.customer_id);
            if (!customer) {
                throw new Error('العميل غير موجود');
            }

            // إنشاء حساب العميل إذا لم يكن موجوداً
            this.createCustomerAccount(customer);
            const customerAccountCode = `1120${customer.id.slice(-3)}`;

            // قيد زيادة النقدية (مدين)
            const paymentMethod = receipt.payment_method || 'cash';
            const cashAccount = paymentMethod === 'bank' ? '1112' : '1111';

            transactions.push({
                accountCode: cashAccount,
                description: `استلام ${paymentMethod === 'bank' ? 'بنكي' : 'نقدي'} من العميل ${customer.name}`,
                debit: amount,
                credit: 0
            });

            // قيد تقليل ذمة العميل (دائن)
            transactions.push({
                accountCode: customerAccountCode,
                description: `استلام من العميل ${customer.name}`,
                debit: 0,
                credit: amount
            });

            // إنشاء القيد
            const entryData = {
                description: `استلام من العميل ${customer.name}`,
                reference: receipt.reference || '',
                type: 'receipt',
                transactions: transactions
            };

            return this.createJournalEntry(entryData);

        } catch (error) {
            console.error('❌ خطأ في إنشاء قيد الاستلام:', error);
            throw error;
        }
    },

    /**
     * إنشاء قيد مصروف
     */
    createExpenseEntry: function(expense) {
        try {
            const transactions = [];
            const amount = parseFloat(expense.amount) || 0;

            // قيد المصروف (مدين)
            transactions.push({
                accountCode: expense.expense_account || '5200',
                description: expense.description,
                debit: amount,
                credit: 0
            });

            // قيد تقليل النقدية (دائن)
            const paymentMethod = expense.payment_method || 'cash';
            const cashAccount = paymentMethod === 'bank' ? '1112' : '1111';

            transactions.push({
                accountCode: cashAccount,
                description: `دفع ${paymentMethod === 'bank' ? 'بنكي' : 'نقدي'} - ${expense.description}`,
                debit: 0,
                credit: amount
            });

            // إنشاء القيد
            const entryData = {
                description: `مصروف - ${expense.description}`,
                reference: expense.reference || '',
                type: 'expense',
                transactions: transactions
            };

            return this.createJournalEntry(entryData);

        } catch (error) {
            console.error('❌ خطأ في إنشاء قيد المصروف:', error);
            throw error;
        }
    },

    /**
     * تحديث جميع الأرصدة
     */
    updateBalances: function() {
        // إعادة تعيين جميع الأرصدة
        Object.keys(this.chartOfAccounts).forEach(code => {
            if (this.chartOfAccounts[code].balance !== undefined) {
                this.chartOfAccounts[code].balance = 0;
            }
        });

        // إعادة حساب الأرصدة من القيود
        this.entries.forEach(entry => {
            if (entry.status === 'posted') {
                this.updateAccountBalances(entry);
            }
        });

        // تحديث إجماليات الميزانية
        this.updateFinancialTotals();
    },

    /**
     * تحديث الإجماليات المالية
     */
    updateFinancialTotals: function() {
        this.state.totalAssets = 0;
        this.state.totalLiabilities = 0;
        this.state.totalEquity = 0;
        this.state.totalRevenue = 0;
        this.state.totalExpenses = 0;

        Object.values(this.chartOfAccounts).forEach(account => {
            if (account.balance && account.level >= 3) { // حسابات فرعية فقط
                switch (account.type) {
                    case 'asset':
                        this.state.totalAssets += account.balance;
                        break;
                    case 'liability':
                        this.state.totalLiabilities += account.balance;
                        break;
                    case 'equity':
                        this.state.totalEquity += account.balance;
                        break;
                    case 'revenue':
                        this.state.totalRevenue += account.balance;
                        break;
                    case 'expense':
                        this.state.totalExpenses += account.balance;
                        break;
                }
            }
        });

        // التحقق من توازن الميزانية
        const netIncome = this.state.totalRevenue - this.state.totalExpenses;
        const totalEquityWithIncome = this.state.totalEquity + netIncome;
        this.state.isBalanced = Math.abs(this.state.totalAssets - (this.state.totalLiabilities + totalEquityWithIncome)) < 0.01;
    },

    /**
     * إنشاء تقرير الميزانية العمومية
     */
    generateBalanceSheet: function(asOfDate = null) {
        const date = asOfDate || new Date().toISOString().split('T')[0];

        const balanceSheet = {
            title: 'الميزانية العمومية',
            titleEn: 'Balance Sheet',
            asOfDate: date,
            currency: this.config.currency,
            assets: {
                current: [],
                fixed: [],
                total: 0
            },
            liabilities: {
                current: [],
                longTerm: [],
                total: 0
            },
            equity: {
                items: [],
                total: 0
            },
            isBalanced: false
        };

        // تجميع الأصول
        Object.values(this.chartOfAccounts).forEach(account => {
            if (account.type === 'asset' && account.balance && account.level >= 3) {
                const item = {
                    code: account.code,
                    name: account.name,
                    balance: account.balance
                };

                if (account.category === 'current_asset' || account.parent === '1100') {
                    balanceSheet.assets.current.push(item);
                } else {
                    balanceSheet.assets.fixed.push(item);
                }
                balanceSheet.assets.total += account.balance;
            }
        });

        // تجميع الخصوم
        Object.values(this.chartOfAccounts).forEach(account => {
            if (account.type === 'liability' && account.balance && account.level >= 3) {
                const item = {
                    code: account.code,
                    name: account.name,
                    balance: account.balance
                };

                if (account.category === 'current_liability' || account.parent === '2100') {
                    balanceSheet.liabilities.current.push(item);
                } else {
                    balanceSheet.liabilities.longTerm.push(item);
                }
                balanceSheet.liabilities.total += account.balance;
            }
        });

        // تجميع حقوق الملكية
        Object.values(this.chartOfAccounts).forEach(account => {
            if (account.type === 'equity' && account.balance && account.level >= 3) {
                balanceSheet.equity.items.push({
                    code: account.code,
                    name: account.name,
                    balance: account.balance
                });
                balanceSheet.equity.total += account.balance;
            }
        });

        // إضافة صافي الدخل
        const netIncome = this.state.totalRevenue - this.state.totalExpenses;
        if (netIncome !== 0) {
            balanceSheet.equity.items.push({
                code: '3300',
                name: 'صافي الدخل للفترة',
                balance: netIncome
            });
            balanceSheet.equity.total += netIncome;
        }

        // التحقق من التوازن
        balanceSheet.isBalanced = Math.abs(balanceSheet.assets.total - (balanceSheet.liabilities.total + balanceSheet.equity.total)) < 0.01;

        return balanceSheet;
    },

    /**
     * إنشاء قائمة الدخل
     */
    generateIncomeStatement: function(fromDate = null, toDate = null) {
        const endDate = toDate || new Date().toISOString().split('T')[0];
        const startDate = fromDate || new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0];

        const incomeStatement = {
            title: 'قائمة الدخل',
            titleEn: 'Income Statement',
            fromDate: startDate,
            toDate: endDate,
            currency: this.config.currency,
            revenue: {
                items: [],
                total: 0
            },
            expenses: {
                costOfSales: [],
                operating: [],
                total: 0
            },
            grossProfit: 0,
            netIncome: 0
        };

        // تجميع الإيرادات
        Object.values(this.chartOfAccounts).forEach(account => {
            if (account.type === 'revenue' && account.balance && account.level >= 3) {
                incomeStatement.revenue.items.push({
                    code: account.code,
                    name: account.name,
                    balance: account.balance
                });
                incomeStatement.revenue.total += account.balance;
            }
        });

        // تجميع المصروفات
        Object.values(this.chartOfAccounts).forEach(account => {
            if (account.type === 'expense' && account.balance && account.level >= 3) {
                const item = {
                    code: account.code,
                    name: account.name,
                    balance: account.balance
                };

                if (account.category === 'cost_of_sales' || account.parent === '5100') {
                    incomeStatement.expenses.costOfSales.push(item);
                } else {
                    incomeStatement.expenses.operating.push(item);
                }
                incomeStatement.expenses.total += account.balance;
            }
        });

        // حساب الأرباح
        const costOfSalesTotal = incomeStatement.expenses.costOfSales.reduce((sum, item) => sum + item.balance, 0);
        incomeStatement.grossProfit = incomeStatement.revenue.total - costOfSalesTotal;
        incomeStatement.netIncome = incomeStatement.revenue.total - incomeStatement.expenses.total;

        return incomeStatement;
    },

    /**
     * إنشاء تقرير أرصدة الحسابات
     */
    generateTrialBalance: function(asOfDate = null) {
        const date = asOfDate || new Date().toISOString().split('T')[0];

        const trialBalance = {
            title: 'ميزان المراجعة',
            titleEn: 'Trial Balance',
            asOfDate: date,
            currency: this.config.currency,
            accounts: [],
            totalDebits: 0,
            totalCredits: 0,
            isBalanced: false
        };

        // تجميع الحسابات
        Object.values(this.chartOfAccounts).forEach(account => {
            if (account.balance !== undefined && account.level >= 3) {
                const debitBalance = account.balance > 0 && (account.type === 'asset' || account.type === 'expense') ? account.balance : 0;
                const creditBalance = account.balance > 0 && (account.type === 'liability' || account.type === 'equity' || account.type === 'revenue') ? account.balance : 0;

                if (debitBalance > 0 || creditBalance > 0) {
                    trialBalance.accounts.push({
                        code: account.code,
                        name: account.name,
                        type: account.type,
                        debitBalance: debitBalance,
                        creditBalance: creditBalance
                    });

                    trialBalance.totalDebits += debitBalance;
                    trialBalance.totalCredits += creditBalance;
                }
            }
        });

        // ترتيب الحسابات حسب الرمز
        trialBalance.accounts.sort((a, b) => a.code.localeCompare(b.code));

        // التحقق من التوازن
        trialBalance.isBalanced = Math.abs(trialBalance.totalDebits - trialBalance.totalCredits) < 0.01;

        return trialBalance;
    },

    /**
     * حفظ البيانات المحاسبية
     */
    saveAccountingData: function() {
        try {
            // حفظ القيود
            localStorage.setItem('accounting_entries', JSON.stringify(this.entries));

            // حفظ أرصدة الحسابات
            const balances = {};
            Object.keys(this.chartOfAccounts).forEach(code => {
                if (this.chartOfAccounts[code].balance !== undefined) {
                    balances[code] = this.chartOfAccounts[code].balance;
                }
            });
            localStorage.setItem('account_balances', JSON.stringify(balances));

            // حفظ حالة النظام
            localStorage.setItem('accounting_state', JSON.stringify(this.state));

        } catch (error) {
            console.error('❌ خطأ في حفظ البيانات المحاسبية:', error);
        }
    },

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners: function() {
        // الاستماع لأحداث الحجوزات الجديدة
        document.addEventListener('booking:created', (event) => {
            if (this.config.autoGenerateEntries) {
                try {
                    this.createSalesEntry(event.detail);
                } catch (error) {
                    console.error('خطأ في إنشاء قيد المبيعات التلقائي:', error);
                }
            }
        });

        // الاستماع لأحداث المدفوعات
        document.addEventListener('payment:made', (event) => {
            if (this.config.autoGenerateEntries) {
                try {
                    if (event.detail.type === 'supplier') {
                        this.createSupplierPaymentEntry(event.detail);
                    } else if (event.detail.type === 'customer') {
                        this.createCustomerReceiptEntry(event.detail);
                    }
                } catch (error) {
                    console.error('خطأ في إنشاء قيد الدفع التلقائي:', error);
                }
            }
        });
    },

    /**
     * إرسال حدث
     */
    emit: function(eventName, data = null) {
        const event = new CustomEvent(eventName, {
            detail: data
        });
        document.dispatchEvent(event);
    },

    /**
     * تنسيق المبلغ
     */
    formatAmount: function(amount) {
        if (typeof amount !== 'number') return '0.00';
        return amount.toLocaleString('ar-SA', {
            minimumFractionDigits: this.config.decimalPlaces,
            maximumFractionDigits: this.config.decimalPlaces
        }) + ' ' + this.config.currencySymbol;
    },

    /**
     * الحصول على رصيد الحساب
     */
    getAccountBalance: function(accountCode) {
        const account = this.chartOfAccounts[accountCode];
        return account ? (account.balance || 0) : 0;
    },

    /**
     * البحث في الحسابات
     */
    searchAccounts: function(query) {
        const results = [];
        const searchTerm = query.toLowerCase();

        Object.values(this.chartOfAccounts).forEach(account => {
            if (account.isActive &&
                (account.name.toLowerCase().includes(searchTerm) ||
                 account.code.includes(searchTerm))) {
                results.push(account);
            }
        });

        return results.sort((a, b) => a.code.localeCompare(b.code));
    }
};