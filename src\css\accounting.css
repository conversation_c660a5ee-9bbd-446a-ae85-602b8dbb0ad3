/**
 * ===================================
 * تصميم النظام المحاسبي - Accounting System Styles
 * ===================================
 */

/* حاوي النظام المحاسبي */
.accounting-container {
    background: var(--bg-primary);
    min-height: 100vh;
    padding: 0;
}

/* رأس النظام المحاسبي */
.accounting-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 2rem;
    margin-bottom: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 0 0 15px 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.accounting-header .header-title h2 {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
}

.accounting-header .header-title p {
    margin: 0.5rem 0 0 0;
    opacity: 0.9;
    font-size: 1.1rem;
}

.accounting-header .header-actions {
    display: flex;
    gap: 1rem;
}

.accounting-header .btn {
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.accounting-header .btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* شريط التنقل المحاسبي */
.accounting-nav {
    background: var(--bg-secondary);
    padding: 0 2rem;
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 100;
}

.accounting-nav .nav-tabs {
    border-bottom: none;
    margin-bottom: 0;
}

.accounting-nav .nav-link {
    border: none;
    border-radius: 0;
    color: var(--text-secondary);
    padding: 1rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.accounting-nav .nav-link:hover {
    color: var(--primary-color);
    background: rgba(var(--primary-rgb), 0.1);
}

.accounting-nav .nav-link.active {
    color: var(--primary-color);
    background: var(--bg-primary);
    border-bottom: 3px solid var(--primary-color);
}

.accounting-nav .nav-link i {
    margin-left: 0.5rem;
}

/* محتوى النظام المحاسبي */
.accounting-content {
    padding: 2rem;
    background: var(--bg-primary);
}

/* لوحة التحكم المحاسبية */
.accounting-dashboard .card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    overflow: hidden;
}

.accounting-dashboard .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.accounting-dashboard .card-body {
    padding: 1.5rem;
}

.accounting-dashboard .card h4 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
}

.accounting-dashboard .card h6 {
    font-size: 0.9rem;
    font-weight: 600;
    margin: 0;
    opacity: 0.9;
}

.accounting-dashboard .card .fa-2x {
    font-size: 2.5rem;
}

/* بطاقات الملخص المالي */
.accounting-dashboard .bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.accounting-dashboard .bg-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%) !important;
}

.accounting-dashboard .bg-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
}

.accounting-dashboard .bg-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
}

.accounting-dashboard .bg-danger {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%) !important;
}

/* جداول النظام المحاسبي */
.accounting-entries .table,
.accounting-accounts .table {
    background: var(--bg-primary);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.accounting-entries .table th,
.accounting-accounts .table th {
    background: var(--bg-secondary);
    border: none;
    font-weight: 600;
    color: var(--text-primary);
    padding: 1rem;
}

.accounting-entries .table td,
.accounting-accounts .table td {
    border: none;
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--border-color);
}

.accounting-entries .table tbody tr:hover,
.accounting-accounts .table tbody tr:hover {
    background: rgba(var(--primary-rgb), 0.05);
}

/* أزرار الإجراءات */
.accounting-entries .btn-sm,
.accounting-accounts .btn-sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.85rem;
    border-radius: 6px;
    margin: 0 0.2rem;
}

/* الشارات */
.badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.8rem;
    border-radius: 20px;
    font-weight: 500;
}

/* نافذة القيد الجديد */
.modal-content {
    border: none;
    border-radius: 15px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: 15px 15px 0 0;
    border: none;
}

.modal-header .btn-close {
    filter: invert(1);
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border: none;
    padding: 1rem 2rem 2rem;
}

/* صفوف المعاملات */
.transaction-row {
    background: var(--bg-secondary);
    border: 2px solid var(--border-color) !important;
    border-radius: 10px !important;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.transaction-row:hover {
    border-color: var(--primary-color) !important;
    box-shadow: 0 5px 15px rgba(var(--primary-rgb), 0.1);
}

.transaction-row .form-control {
    border: 1px solid var(--border-color);
    border-radius: 6px;
    transition: all 0.3s ease;
}

.transaction-row .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

/* تقارير النظام المحاسبي */
.accounting-reports .card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    text-align: center;
    overflow: hidden;
}

.accounting-reports .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.accounting-reports .card-body {
    padding: 2rem;
}

.accounting-reports .fa-3x {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.accounting-reports h5 {
    font-weight: 600;
    margin-bottom: 1rem;
}

.accounting-reports .text-muted {
    margin-bottom: 1.5rem;
}

/* المدفوعات والمقبوضات */
.accounting-payments .card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.accounting-payments .card-header {
    background: var(--bg-secondary);
    border: none;
    border-radius: 15px 15px 0 0;
    padding: 1.5rem;
}

.accounting-payments .card-header h5 {
    margin: 0;
    font-weight: 600;
}

.accounting-payments .card-body {
    padding: 1.5rem;
}

/* الألوان المالية */
.text-debit {
    color: #28a745 !important;
    font-weight: 600;
}

.text-credit {
    color: #dc3545 !important;
    font-weight: 600;
}

.bg-balanced {
    background-color: #d4edda !important;
    color: #155724 !important;
}

.bg-unbalanced {
    background-color: #f8d7da !important;
    color: #721c24 !important;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .accounting-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .accounting-header .header-actions {
        justify-content: center;
    }
    
    .accounting-nav {
        padding: 0 1rem;
    }
    
    .accounting-nav .nav-link {
        padding: 0.8rem 1rem;
        font-size: 0.9rem;
    }
    
    .accounting-content {
        padding: 1rem;
    }
    
    .transaction-row .row {
        margin-bottom: 0.5rem;
    }
    
    .transaction-row .col-md-4,
    .transaction-row .col-md-3,
    .transaction-row .col-md-2 {
        margin-bottom: 0.5rem;
    }
}

/* تحسينات الطباعة */
@media print {
    .accounting-header .header-actions,
    .accounting-nav,
    .btn,
    .modal {
        display: none !important;
    }
    
    .accounting-content {
        padding: 0;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
    
    .table {
        font-size: 12px;
    }
}

/* رسوم متحركة */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.accounting-dashboard .card,
.accounting-entries .card,
.accounting-accounts .card,
.accounting-reports .card {
    animation: slideInUp 0.6s ease-out;
}

/* تحسينات إضافية */
.accounting-container .alert {
    border: none;
    border-radius: 10px;
    padding: 1rem 1.5rem;
}

.accounting-container .form-control {
    border-radius: 8px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.accounting-container .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

.accounting-container .btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.accounting-container .btn:hover {
    transform: translateY(-1px);
}
