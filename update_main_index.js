/**
 * سكريبت تحديث الملف الرئيسي لاستخدام اللوحة الجانبية
 * Script to update main index.html to use sidebar navigation
 */

console.log('🔄 بدء تحديث الملف الرئيسي لاستخدام اللوحة الجانبية...');

// قراءة محتوى الملف الحالي
function updateMainIndex() {
    const fs = require('fs');
    const path = require('path');
    
    try {
        // قراءة الملف الحالي
        const currentIndexPath = path.join(__dirname, 'index.html');
        const newIndexPath = path.join(__dirname, 'index-with-sidebar.html');
        const backupPath = path.join(__dirname, 'index-backup.html');
        
        // إنشاء نسخة احتياطية
        if (fs.existsSync(currentIndexPath)) {
            fs.copyFileSync(currentIndexPath, backupPath);
            console.log('✅ تم إنشاء نسخة احتياطية: index-backup.html');
        }
        
        // نسخ الملف الجديد
        if (fs.existsSync(newIndexPath)) {
            fs.copyFileSync(newIndexPath, currentIndexPath);
            console.log('✅ تم تحديث index.html بنجاح');
        } else {
            console.error('❌ الملف الجديد غير موجود: index-with-sidebar.html');
            return false;
        }
        
        console.log('🎉 تم تحديث النظام بنجاح!');
        console.log('📋 الملفات المحدثة:');
        console.log('   - index.html (محدث)');
        console.log('   - index-backup.html (نسخة احتياطية)');
        console.log('   - index-with-sidebar.html (النسخة الجديدة)');
        
        return true;
        
    } catch (error) {
        console.error('❌ خطأ في تحديث الملف:', error.message);
        return false;
    }
}

// تشغيل التحديث إذا كان هذا ملف Node.js
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { updateMainIndex };
    
    // تشغيل التحديث مباشرة إذا تم استدعاء الملف
    if (require.main === module) {
        updateMainIndex();
    }
} else {
    // تشغيل في المتصفح
    console.log('📝 تعليمات التحديث اليدوي:');
    console.log('1. انسخ محتوى index-with-sidebar.html');
    console.log('2. احفظ نسخة احتياطية من index.html');
    console.log('3. استبدل محتوى index.html بالمحتوى الجديد');
    console.log('4. احفظ الملف');
    console.log('5. أعد تحميل الصفحة');
}

/**
 * دليل التحديث اليدوي
 * Manual Update Guide
 */
const updateGuide = {
    title: 'دليل تحديث النظام للوحة الجانبية',
    steps: [
        {
            step: 1,
            title: 'إنشاء نسخة احتياطية',
            description: 'انسخ ملف index.html واحفظه باسم index-backup.html',
            code: 'cp index.html index-backup.html'
        },
        {
            step: 2,
            title: 'نسخ الملف الجديد',
            description: 'انسخ محتوى index-with-sidebar.html إلى index.html',
            code: 'cp index-with-sidebar.html index.html'
        },
        {
            step: 3,
            title: 'التحقق من الملفات المطلوبة',
            description: 'تأكد من وجود الملفات التالية:',
            files: [
                'src/css/sidebar-navigation.css',
                'src/js/sidebar-navigation.js',
                'src/css/sales-enhanced.css',
                'src/css/purchases.css'
            ]
        },
        {
            step: 4,
            title: 'اختبار النظام',
            description: 'افتح index.html في المتصفح واختبر اللوحة الجانبية',
            tests: [
                'فتح وإغلاق اللوحة الجانبية',
                'التنقل بين الأقسام',
                'اختبار القوائم الفرعية',
                'اختبار التجاوب على الشاشات الصغيرة'
            ]
        }
    ],
    troubleshooting: {
        'اللوحة الجانبية لا تظهر': [
            'تحقق من تحميل ملف sidebar-navigation.css',
            'تحقق من تحميل ملف sidebar-navigation.js',
            'افتح وحدة تحكم المطور وابحث عن أخطاء JavaScript'
        ],
        'الألوان لا تظهر بشكل صحيح': [
            'تحقق من ترتيب تحميل ملفات CSS',
            'تأكد من عدم وجود تعارض في الأنماط',
            'امسح ذاكرة التخزين المؤقت للمتصفح'
        ],
        'القوائم الفرعية لا تعمل': [
            'تحقق من تحميل ملف sidebar-navigation.js',
            'تأكد من تهيئة الكائن sidebarNav',
            'افحص وحدة تحكم المطور للأخطاء'
        ]
    }
};

console.log('📚 دليل التحديث متاح في المتغير updateGuide');

/**
 * وظائف مساعدة للتحديث
 * Helper functions for update
 */
const updateHelpers = {
    // فحص الملفات المطلوبة
    checkRequiredFiles: function() {
        const requiredFiles = [
            'src/css/sidebar-navigation.css',
            'src/js/sidebar-navigation.js',
            'index-with-sidebar.html'
        ];
        
        console.log('🔍 فحص الملفات المطلوبة...');
        
        requiredFiles.forEach(file => {
            // في بيئة المتصفح، لا يمكن فحص الملفات مباشرة
            console.log(`📄 ${file} - يجب التحقق يدوياً`);
        });
        
        return true;
    },
    
    // إنشاء قائمة بالتغييرات
    listChanges: function() {
        const changes = [
            '🎨 إضافة لوحة تنقل جانبية متطورة',
            '📱 تصميم متجاوب محسن',
            '🗂️ تنظيم هرمي للأقسام',
            '🔄 قابلية طي وتوسيع',
            '⚡ أداء محسن للتنقل',
            '🎯 تجربة مستخدم أفضل',
            '📊 شارات إشعارات',
            '💡 تلميحات أدوات',
            '🎨 ألوان وتأثيرات جديدة',
            '⌨️ دعم اختصارات لوحة المفاتيح'
        ];
        
        console.log('📋 قائمة التحسينات الجديدة:');
        changes.forEach((change, index) => {
            console.log(`   ${index + 1}. ${change}`);
        });
        
        return changes;
    },
    
    // إنشاء تقرير التحديث
    generateUpdateReport: function() {
        const report = {
            timestamp: new Date().toISOString(),
            version: '2.0.0',
            changes: this.listChanges(),
            files: {
                added: [
                    'src/css/sidebar-navigation.css',
                    'src/js/sidebar-navigation.js',
                    'index-with-sidebar.html',
                    'SIDEBAR_NAVIGATION_README.md'
                ],
                modified: [
                    'index.html'
                ],
                backup: [
                    'index-backup.html'
                ]
            },
            features: [
                'لوحة تنقل جانبية متطورة',
                'تصميم متجاوب كامل',
                'قوائم فرعية قابلة للطي',
                'شارات إشعارات',
                'تلميحات أدوات',
                'حفظ التفضيلات',
                'تأثيرات بصرية سلسة'
            ]
        };
        
        console.log('📊 تقرير التحديث:', report);
        return report;
    }
};

// تصدير الوظائف المساعدة
if (typeof window !== 'undefined') {
    window.updateHelpers = updateHelpers;
    window.updateGuide = updateGuide;
}

console.log('✅ تم تحميل أدوات التحديث بنجاح');
console.log('💡 استخدم updateHelpers.checkRequiredFiles() للفحص');
console.log('💡 استخدم updateHelpers.listChanges() لعرض التحسينات');
console.log('💡 استخدم updateHelpers.generateUpdateReport() لتقرير شامل');
