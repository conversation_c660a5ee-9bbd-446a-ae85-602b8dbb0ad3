/**
 * نظام التنقل الجانبي المتطور
 * Advanced Sidebar Navigation System
 */

class SidebarNavigation {
    constructor() {
        this.isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
        this.isMobile = window.innerWidth <= 768;
        this.currentPage = 'dashboard';
        
        this.init();
    }

    init() {
        this.createSidebar();
        this.setupEventListeners();
        this.updateActiveNavItem();
        
        // تطبيق حالة الطي المحفوظة
        if (this.isCollapsed) {
            this.toggleSidebar();
        }
        
        console.log('✅ تم تهيئة نظام التنقل الجانبي');
    }

    createSidebar() {
        const sidebarHTML = `
            <!-- اللوحة الجانبية -->
            <div class="sidebar" id="sidebar">
                <!-- رأس اللوحة -->
                <div class="sidebar-header">
                    <a href="#" class="sidebar-logo" onclick="showDashboard()">
                        <i class="fas fa-plane"></i>
                        <span class="sidebar-logo-text">قيمة الوعد للسفر</span>
                    </a>
                    <button class="sidebar-toggle" onclick="sidebarNav.toggleSidebar()">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                </div>

                <!-- قائمة التنقل -->
                <nav class="sidebar-nav">
                    <!-- القسم الرئيسي -->
                    <div class="nav-section">
                        <div class="nav-section-title">القسم الرئيسي</div>
                        <div class="nav-item">
                            <a href="#" class="nav-link active" data-tooltip="الرئيسية" onclick="showDashboard()">
                                <i class="fas fa-home"></i>
                                <span class="nav-link-text">الرئيسية</span>
                            </a>
                        </div>
                    </div>

                    <!-- إدارة العملاء والوكلاء -->
                    <div class="nav-section">
                        <div class="nav-section-title">إدارة العملاء والوكلاء</div>
                        
                        <div class="nav-item has-submenu">
                            <a href="#" class="nav-link" data-tooltip="العملاء">
                                <i class="fas fa-users"></i>
                                <span class="nav-link-text">العملاء</span>
                            </a>
                            <div class="submenu">
                                <a href="#" class="nav-link" onclick="showCustomers()">
                                    <i class="fas fa-list"></i>
                                    <span class="nav-link-text">قائمة العملاء</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showAddCustomer()">
                                    <i class="fas fa-user-plus"></i>
                                    <span class="nav-link-text">إضافة عميل جديد</span>
                                </a>
                            </div>
                        </div>

                        <div class="nav-item has-submenu">
                            <a href="#" class="nav-link" data-tooltip="الوكلاء">
                                <i class="fas fa-handshake"></i>
                                <span class="nav-link-text">الوكلاء</span>
                            </a>
                            <div class="submenu">
                                <a href="#" class="nav-link" onclick="showAgents()">
                                    <i class="fas fa-list"></i>
                                    <span class="nav-link-text">قائمة الوكلاء</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showAddAgent()">
                                    <i class="fas fa-user-plus"></i>
                                    <span class="nav-link-text">إضافة وكيل جديد</span>
                                </a>
                            </div>
                        </div>

                        <div class="nav-item has-submenu">
                            <a href="#" class="nav-link" data-tooltip="الموردين">
                                <i class="fas fa-truck"></i>
                                <span class="nav-link-text">الموردين</span>
                            </a>
                            <div class="submenu">
                                <a href="#" class="nav-link" onclick="showSuppliers()">
                                    <i class="fas fa-list"></i>
                                    <span class="nav-link-text">قائمة الموردين</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showAddSupplier()">
                                    <i class="fas fa-plus"></i>
                                    <span class="nav-link-text">إضافة مورد جديد</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- الحجوزات والخدمات -->
                    <div class="nav-section">
                        <div class="nav-section-title">الحجوزات والخدمات</div>
                        
                        <div class="nav-item has-submenu">
                            <a href="#" class="nav-link" data-tooltip="الحجوزات">
                                <i class="fas fa-calendar-check"></i>
                                <span class="nav-link-text">الحجوزات</span>
                                <span class="nav-badge">5</span>
                            </a>
                            <div class="submenu">
                                <a href="#" class="nav-link" onclick="showBookings()">
                                    <i class="fas fa-list"></i>
                                    <span class="nav-link-text">قائمة الحجوزات</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showAddBooking()">
                                    <i class="fas fa-plus"></i>
                                    <span class="nav-link-text">حجز جديد</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showHajjUmrah()">
                                    <i class="fas fa-kaaba"></i>
                                    <span class="nav-link-text">حج وعمرة</span>
                                </a>
                            </div>
                        </div>

                        <div class="nav-item has-submenu">
                            <a href="#" class="nav-link" data-tooltip="المخزون">
                                <i class="fas fa-boxes"></i>
                                <span class="nav-link-text">المخزون</span>
                            </a>
                            <div class="submenu">
                                <a href="#" class="nav-link" onclick="showVisaInventory()">
                                    <i class="fas fa-passport"></i>
                                    <span class="nav-link-text">مخزون التأشيرات</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showInventory()">
                                    <i class="fas fa-warehouse"></i>
                                    <span class="nav-link-text">المخزون العام</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showProducts()">
                                    <i class="fas fa-tags"></i>
                                    <span class="nav-link-text">المنتجات والخدمات</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- المبيعات والمشتريات -->
                    <div class="nav-section">
                        <div class="nav-section-title">المبيعات والمشتريات</div>
                        
                        <div class="nav-item has-submenu">
                            <a href="#" class="nav-link" data-tooltip="المبيعات">
                                <i class="fas fa-shopping-cart"></i>
                                <span class="nav-link-text">المبيعات</span>
                                <span class="nav-badge">12</span>
                            </a>
                            <div class="submenu">
                                <a href="#" class="nav-link" onclick="showSalesDashboard()">
                                    <i class="fas fa-tachometer-alt"></i>
                                    <span class="nav-link-text">لوحة تحكم المبيعات</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showInvoices()">
                                    <i class="fas fa-file-invoice"></i>
                                    <span class="nav-link-text">الفواتير</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showSalesCustomers()">
                                    <i class="fas fa-users"></i>
                                    <span class="nav-link-text">عملاء المبيعات</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showSalesProducts()">
                                    <i class="fas fa-box"></i>
                                    <span class="nav-link-text">المنتجات والخدمات</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showCreditNotes()">
                                    <i class="fas fa-file-invoice-dollar"></i>
                                    <span class="nav-link-text">الإشعارات الدائنة</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showSalesReports()">
                                    <i class="fas fa-chart-bar"></i>
                                    <span class="nav-link-text">تقارير المبيعات</span>
                                </a>
                            </div>
                        </div>

                        <div class="nav-item has-submenu">
                            <a href="#" class="nav-link" data-tooltip="المشتريات">
                                <i class="fas fa-shopping-basket"></i>
                                <span class="nav-link-text">المشتريات</span>
                                <span class="nav-badge">3</span>
                            </a>
                            <div class="submenu">
                                <a href="#" class="nav-link" onclick="showPurchasesDashboard()">
                                    <i class="fas fa-tachometer-alt"></i>
                                    <span class="nav-link-text">لوحة تحكم المشتريات</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showPurchaseInvoices()">
                                    <i class="fas fa-file-invoice"></i>
                                    <span class="nav-link-text">فواتير المشتريات</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showPurchaseOrders()">
                                    <i class="fas fa-clipboard-list"></i>
                                    <span class="nav-link-text">أوامر الشراء</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showPurchaseQuotes()">
                                    <i class="fas fa-file-contract"></i>
                                    <span class="nav-link-text">عروض أسعار المشتريات</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showPurchasePayments()">
                                    <i class="fas fa-credit-card"></i>
                                    <span class="nav-link-text">مدفوعات المشتريات</span>
                                </a>
                                <a href="#" class="nav-link" onclick="openPurchasesWindow()">
                                    <i class="fas fa-external-link-alt"></i>
                                    <span class="nav-link-text">فتح نافذة المشتريات</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- المحاسبة والتقارير -->
                    <div class="nav-section">
                        <div class="nav-section-title">المحاسبة والتقارير</div>
                        
                        <div class="nav-item has-submenu">
                            <a href="#" class="nav-link" data-tooltip="الحسابات">
                                <i class="fas fa-calculator"></i>
                                <span class="nav-link-text">الحسابات</span>
                            </a>
                            <div class="submenu">
                                <a href="#" class="nav-link" onclick="showAccountingDashboard()">
                                    <i class="fas fa-tachometer-alt"></i>
                                    <span class="nav-link-text">لوحة التحكم المحاسبية</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showAccounts()">
                                    <i class="fas fa-book"></i>
                                    <span class="nav-link-text">دليل الحسابات</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showJournalEntries()">
                                    <i class="fas fa-edit"></i>
                                    <span class="nav-link-text">القيود المحاسبية</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showPayments()">
                                    <i class="fas fa-money-bill-wave"></i>
                                    <span class="nav-link-text">المدفوعات والمقبوضات</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showAccountingReports()">
                                    <i class="fas fa-chart-bar"></i>
                                    <span class="nav-link-text">التقارير المالية</span>
                                </a>
                            </div>
                        </div>

                        <div class="nav-item has-submenu">
                            <a href="#" class="nav-link" data-tooltip="التقارير">
                                <i class="fas fa-chart-line"></i>
                                <span class="nav-link-text">التقارير</span>
                            </a>
                            <div class="submenu">
                                <a href="#" class="nav-link" onclick="showCustomerReports()">
                                    <i class="fas fa-users"></i>
                                    <span class="nav-link-text">تقارير العملاء</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showAgentReports()">
                                    <i class="fas fa-handshake"></i>
                                    <span class="nav-link-text">تقارير الوكلاء</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showTransactionReports()">
                                    <i class="fas fa-exchange-alt"></i>
                                    <span class="nav-link-text">تقارير المعاملات</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showFinancialReports()">
                                    <i class="fas fa-chart-line"></i>
                                    <span class="nav-link-text">التقارير المالية</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- الإعدادات والإدارة -->
                    <div class="nav-section">
                        <div class="nav-section-title">الإعدادات والإدارة</div>
                        
                        <div class="nav-item has-submenu">
                            <a href="#" class="nav-link" data-tooltip="الإعدادات">
                                <i class="fas fa-cog"></i>
                                <span class="nav-link-text">الإعدادات</span>
                            </a>
                            <div class="submenu">
                                <a href="#" class="nav-link" onclick="showUserManagement()">
                                    <i class="fas fa-users-cog"></i>
                                    <span class="nav-link-text">إدارة المستخدمين</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showBackupSettings()">
                                    <i class="fas fa-database"></i>
                                    <span class="nav-link-text">النسخ الاحتياطية</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showSystemSettings()">
                                    <i class="fas fa-sliders-h"></i>
                                    <span class="nav-link-text">الإعدادات العامة</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showTransportCompanies()">
                                    <i class="fas fa-bus"></i>
                                    <span class="nav-link-text">شركات النقل</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </nav>

                <!-- تذييل اللوحة -->
                <div class="sidebar-footer">
                    <div class="version-info">
                        الإصدار 2.0.0
                    </div>
                </div>
            </div>

            <!-- طبقة التغطية للشاشات الصغيرة -->
            <div class="sidebar-overlay" id="sidebarOverlay" onclick="sidebarNav.hideSidebar()"></div>
        `;

        // إدراج اللوحة الجانبية في بداية الصفحة
        document.body.insertAdjacentHTML('afterbegin', sidebarHTML);
    }

    setupEventListeners() {
        // معالج النقر على العناصر التي تحتوي على قوائم فرعية
        document.addEventListener('click', (e) => {
            const navItem = e.target.closest('.nav-item.has-submenu');
            if (navItem && !e.target.closest('.submenu')) {
                e.preventDefault();
                this.toggleSubmenu(navItem);
            }
        });

        // معالج تغيير حجم النافذة
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // معالج النقر خارج اللوحة الجانبية في الشاشات الصغيرة
        document.addEventListener('click', (e) => {
            if (this.isMobile && !e.target.closest('.sidebar') && !e.target.closest('.sidebar-toggle')) {
                this.hideSidebar();
            }
        });
    }

    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        const toggleIcon = sidebar.querySelector('.sidebar-toggle i');
        
        this.isCollapsed = !this.isCollapsed;
        
        if (this.isCollapsed) {
            sidebar.classList.add('collapsed');
            toggleIcon.className = 'fas fa-chevron-right';
        } else {
            sidebar.classList.remove('collapsed');
            toggleIcon.className = 'fas fa-chevron-left';
        }
        
        // حفظ الحالة
        localStorage.setItem('sidebarCollapsed', this.isCollapsed);
        
        // إغلاق جميع القوائم الفرعية عند الطي
        if (this.isCollapsed) {
            this.closeAllSubmenus();
        }
    }

    toggleSubmenu(navItem) {
        if (this.isCollapsed) return;
        
        const isOpen = navItem.classList.contains('open');
        
        // إغلاق جميع القوائم الفرعية الأخرى
        document.querySelectorAll('.nav-item.has-submenu.open').forEach(item => {
            if (item !== navItem) {
                item.classList.remove('open');
            }
        });
        
        // تبديل حالة القائمة الحالية
        navItem.classList.toggle('open', !isOpen);
    }

    closeAllSubmenus() {
        document.querySelectorAll('.nav-item.has-submenu.open').forEach(item => {
            item.classList.remove('open');
        });
    }

    showSidebar() {
        if (this.isMobile) {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');
            
            sidebar.classList.add('show');
            overlay.classList.add('show');
        }
    }

    hideSidebar() {
        if (this.isMobile) {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');
            
            sidebar.classList.remove('show');
            overlay.classList.remove('show');
        }
    }

    handleResize() {
        const wasMobile = this.isMobile;
        this.isMobile = window.innerWidth <= 768;
        
        if (wasMobile !== this.isMobile) {
            if (!this.isMobile) {
                this.hideSidebar();
            }
        }
    }

    setActivePage(page) {
        this.currentPage = page;
        this.updateActiveNavItem();
    }

    updateActiveNavItem() {
        // إزالة الفئة النشطة من جميع العناصر
        document.querySelectorAll('.nav-link.active').forEach(link => {
            link.classList.remove('active');
        });
        
        // إضافة الفئة النشطة للعنصر الحالي
        const activeLink = document.querySelector(`[onclick*="${this.currentPage}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
            
            // فتح القائمة الفرعية إذا كان العنصر بداخلها
            const parentSubmenu = activeLink.closest('.submenu');
            if (parentSubmenu) {
                const parentNavItem = parentSubmenu.closest('.nav-item.has-submenu');
                if (parentNavItem) {
                    parentNavItem.classList.add('open');
                }
            }
        }
    }

    updatePageTitle(title) {
        const pageTitle = document.querySelector('.page-title');
        if (pageTitle) {
            pageTitle.textContent = title;
        }
    }
}

// تهيئة نظام التنقل الجانبي
let sidebarNav;

document.addEventListener('DOMContentLoaded', function() {
    sidebarNav = new SidebarNavigation();
});
