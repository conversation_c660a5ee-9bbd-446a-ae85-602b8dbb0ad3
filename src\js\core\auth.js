/**
 * ===================================
 * نظام المصادقة - Authentication System
 * ===================================
 */

window.Auth = {
    // إعدادات المصادقة
    config: {
        sessionKey: 'qimat_alwaed_session',
        tokenKey: 'qimat_alwaed_token',
        userKey: 'qimat_alwaed_user',
        sessionTimeout: 8 * 60 * 60 * 1000, // 8 ساعات
        rememberMeDuration: 30 * 24 * 60 * 60 * 1000, // 30 يوم
        maxLoginAttempts: 5,
        lockoutDuration: 15 * 60 * 1000 // 15 دقيقة
    },

    // حالة المصادقة
    state: {
        isAuthenticated: false,
        currentUser: null,
        sessionStartTime: null,
        lastActivity: null,
        loginAttempts: 0,
        isLocked: false,
        lockoutEndTime: null
    },

    // المستخدمون الافتراضيون
    defaultUsers: [
        {
            id: 1,
            username: 'admin',
            email: '<EMAIL>',
            password: 'admin123', // في الواقع يجب تشفيرها
            full_name: 'مدير النظام',
            role: 'admin',
            permissions: ['*'],
            is_active: true,
            created_at: new Date().toISOString()
        },
        {
            id: 2,
            username: 'user',
            email: '<EMAIL>',
            password: 'user123',
            full_name: 'مستخدم عادي',
            role: 'user',
            permissions: ['read', 'write'],
            is_active: true,
            created_at: new Date().toISOString()
        }
    ],

    /**
     * تهيئة نظام المصادقة
     */
    init: function() {
        console.log('🔄 تهيئة نظام المصادقة');
        
        try {
            // إنشاء المستخدمين الافتراضيين
            this.createDefaultUsers();
            
            // استعادة الجلسة
            this.restoreSession();
            
            // بدء مراقبة النشاط
            this.startActivityMonitoring();
            
            console.log('✅ تم تهيئة نظام المصادقة بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام المصادقة:', error);
        }
    },

    /**
     * إنشاء المستخدمين الافتراضيين
     */
    createDefaultUsers: function() {
        if (window.Database) {
            const existingUsers = window.Database.findAll('users');
            
            if (existingUsers.length === 0) {
                this.defaultUsers.forEach(user => {
                    // تشفير كلمة المرور (محاكاة)
                    user.password_hash = this.hashPassword(user.password);
                    delete user.password;
                    
                    window.Database.insert('users', user);
                });
                
                console.log('👥 تم إنشاء المستخدمين الافتراضيين');
            }
        }
    },

    /**
     * تسجيل الدخول
     */
    login: function(credentials) {
        return new Promise((resolve, reject) => {
            try {
                // التحقق من القفل
                if (this.isAccountLocked()) {
                    reject(new Error('الحساب مقفل مؤقتاً. يرجى المحاولة لاحقاً.'));
                    return;
                }

                const { username, password, rememberMe = false } = credentials;

                // التحقق من صحة البيانات
                if (!username || !password) {
                    reject(new Error('يرجى إدخال اسم المستخدم وكلمة المرور'));
                    return;
                }

                // البحث عن المستخدم
                const user = this.findUser(username);
                if (!user) {
                    this.handleFailedLogin();
                    reject(new Error('اسم المستخدم أو كلمة المرور غير صحيحة'));
                    return;
                }

                // التحقق من كلمة المرور
                if (!this.verifyPassword(password, user.password_hash)) {
                    this.handleFailedLogin();
                    reject(new Error('اسم المستخدم أو كلمة المرور غير صحيحة'));
                    return;
                }

                // التحقق من حالة المستخدم
                if (!user.is_active) {
                    reject(new Error('الحساب غير نشط. يرجى التواصل مع المدير'));
                    return;
                }

                // إنشاء الجلسة
                this.createSession(user, rememberMe);

                // إعادة تعيين محاولات تسجيل الدخول
                this.resetLoginAttempts();

                console.log(`✅ تم تسجيل دخول المستخدم: ${user.username}`);
                resolve(user);

            } catch (error) {
                console.error('❌ خطأ في تسجيل الدخول:', error);
                reject(error);
            }
        });
    },

    /**
     * تسجيل الخروج
     */
    logout: function() {
        try {
            // مسح الجلسة
            this.clearSession();
            
            // تحديث الحالة
            this.state.isAuthenticated = false;
            this.state.currentUser = null;
            this.state.sessionStartTime = null;
            this.state.lastActivity = null;

            // إرسال حدث تسجيل الخروج
            this.emitAuthEvent('logout');

            console.log('✅ تم تسجيل الخروج بنجاح');

            // إعادة توجيه لصفحة تسجيل الدخول
            if (window.Router) {
                window.Router.navigate('login');
            }

        } catch (error) {
            console.error('❌ خطأ في تسجيل الخروج:', error);
        }
    },

    /**
     * البحث عن مستخدم
     */
    findUser: function(username) {
        if (window.Database) {
            const users = window.Database.findAll('users');
            return users.find(user => 
                user.username === username || user.email === username
            );
        }
        return null;
    },

    /**
     * تشفير كلمة المرور (محاكاة)
     */
    hashPassword: function(password) {
        // في الواقع يجب استخدام خوارزمية تشفير قوية
        return btoa(password + 'salt_key_qimat_alwaed');
    },

    /**
     * التحقق من كلمة المرور
     */
    verifyPassword: function(password, hash) {
        return this.hashPassword(password) === hash;
    },

    /**
     * إنشاء جلسة
     */
    createSession: function(user, rememberMe = false) {
        const now = new Date();
        const sessionData = {
            userId: user.id,
            username: user.username,
            role: user.role,
            permissions: user.permissions,
            startTime: now.toISOString(),
            lastActivity: now.toISOString(),
            rememberMe: rememberMe,
            expiresAt: new Date(now.getTime() + 
                (rememberMe ? this.config.rememberMeDuration : this.config.sessionTimeout)
            ).toISOString()
        };

        // حفظ الجلسة
        localStorage.setItem(this.config.sessionKey, JSON.stringify(sessionData));
        
        // حفظ بيانات المستخدم
        const userData = { ...user };
        delete userData.password_hash; // عدم حفظ كلمة المرور
        localStorage.setItem(this.config.userKey, JSON.stringify(userData));

        // تحديث الحالة
        this.state.isAuthenticated = true;
        this.state.currentUser = userData;
        this.state.sessionStartTime = now;
        this.state.lastActivity = now;

        // إرسال حدث تسجيل الدخول
        this.emitAuthEvent('login', userData);
    },

    /**
     * استعادة الجلسة
     */
    restoreSession: function() {
        try {
            const sessionData = localStorage.getItem(this.config.sessionKey);
            const userData = localStorage.getItem(this.config.userKey);

            if (!sessionData || !userData) {
                return false;
            }

            const session = JSON.parse(sessionData);
            const user = JSON.parse(userData);

            // التحقق من انتهاء الجلسة
            const now = new Date();
            const expiresAt = new Date(session.expiresAt);

            if (now > expiresAt) {
                this.clearSession();
                return false;
            }

            // استعادة الحالة
            this.state.isAuthenticated = true;
            this.state.currentUser = user;
            this.state.sessionStartTime = new Date(session.startTime);
            this.state.lastActivity = new Date(session.lastActivity);

            // تحديث النشاط
            this.updateActivity();

            console.log(`✅ تم استعادة جلسة المستخدم: ${user.username}`);
            return true;

        } catch (error) {
            console.error('❌ خطأ في استعادة الجلسة:', error);
            this.clearSession();
            return false;
        }
    },

    /**
     * مسح الجلسة
     */
    clearSession: function() {
        localStorage.removeItem(this.config.sessionKey);
        localStorage.removeItem(this.config.userKey);
        localStorage.removeItem(this.config.tokenKey);
    },

    /**
     * تحديث النشاط
     */
    updateActivity: function() {
        if (this.state.isAuthenticated) {
            const now = new Date();
            this.state.lastActivity = now;

            // تحديث الجلسة المحفوظة
            const sessionData = localStorage.getItem(this.config.sessionKey);
            if (sessionData) {
                const session = JSON.parse(sessionData);
                session.lastActivity = now.toISOString();
                localStorage.setItem(this.config.sessionKey, JSON.stringify(session));
            }
        }
    },

    /**
     * بدء مراقبة النشاط
     */
    startActivityMonitoring: function() {
        // تحديث النشاط عند التفاعل
        const events = ['click', 'keypress', 'scroll', 'mousemove'];
        events.forEach(event => {
            document.addEventListener(event, () => {
                this.updateActivity();
            }, { passive: true });
        });

        // فحص انتهاء الجلسة كل دقيقة
        setInterval(() => {
            this.checkSessionExpiry();
        }, 60000);
    },

    /**
     * فحص انتهاء الجلسة
     */
    checkSessionExpiry: function() {
        if (!this.state.isAuthenticated) return;

        const sessionData = localStorage.getItem(this.config.sessionKey);
        if (!sessionData) {
            this.logout();
            return;
        }

        const session = JSON.parse(sessionData);
        const now = new Date();
        const expiresAt = new Date(session.expiresAt);

        if (now > expiresAt) {
            console.warn('⚠️ انتهت صلاحية الجلسة');
            this.logout();
            
            if (window.Notifications) {
                window.Notifications.warning('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.');
            }
        }
    },

    /**
     * معالجة فشل تسجيل الدخول
     */
    handleFailedLogin: function() {
        this.state.loginAttempts++;
        
        if (this.state.loginAttempts >= this.config.maxLoginAttempts) {
            this.lockAccount();
        }
    },

    /**
     * قفل الحساب
     */
    lockAccount: function() {
        this.state.isLocked = true;
        this.state.lockoutEndTime = new Date(Date.now() + this.config.lockoutDuration);
        
        console.warn('⚠️ تم قفل الحساب مؤقتاً');
        
        if (window.Notifications) {
            window.Notifications.error('تم قفل الحساب مؤقتاً بسبب محاولات تسجيل دخول فاشلة متعددة.');
        }
    },

    /**
     * التحقق من قفل الحساب
     */
    isAccountLocked: function() {
        if (!this.state.isLocked) return false;
        
        const now = new Date();
        if (now > this.state.lockoutEndTime) {
            this.resetLoginAttempts();
            return false;
        }
        
        return true;
    },

    /**
     * إعادة تعيين محاولات تسجيل الدخول
     */
    resetLoginAttempts: function() {
        this.state.loginAttempts = 0;
        this.state.isLocked = false;
        this.state.lockoutEndTime = null;
    },

    /**
     * إرسال حدث المصادقة
     */
    emitAuthEvent: function(type, data = null) {
        const event = new CustomEvent(`auth:${type}`, {
            detail: data
        });
        document.dispatchEvent(event);
    },

    /**
     * التحقق من المصادقة
     */
    isAuthenticated: function() {
        return this.state.isAuthenticated;
    },

    /**
     * الحصول على المستخدم الحالي
     */
    getCurrentUser: function() {
        return this.state.currentUser;
    },

    /**
     * التحقق من الصلاحية
     */
    hasPermission: function(permission) {
        if (!this.state.currentUser) return false;
        
        const permissions = this.state.currentUser.permissions || [];
        return permissions.includes('*') || permissions.includes(permission);
    },

    /**
     * التحقق من الدور
     */
    hasRole: function(role) {
        if (!this.state.currentUser) return false;
        return this.state.currentUser.role === role;
    },

    /**
     * تغيير كلمة المرور
     */
    changePassword: function(oldPassword, newPassword) {
        return new Promise((resolve, reject) => {
            try {
                if (!this.state.currentUser) {
                    reject(new Error('يجب تسجيل الدخول أولاً'));
                    return;
                }

                // البحث عن المستخدم
                const user = this.findUser(this.state.currentUser.username);
                if (!user) {
                    reject(new Error('المستخدم غير موجود'));
                    return;
                }

                // التحقق من كلمة المرور القديمة
                if (!this.verifyPassword(oldPassword, user.password_hash)) {
                    reject(new Error('كلمة المرور القديمة غير صحيحة'));
                    return;
                }

                // تحديث كلمة المرور
                const newPasswordHash = this.hashPassword(newPassword);
                
                if (window.Database) {
                    window.Database.update('users', user.id, {
                        password_hash: newPasswordHash,
                        password_changed_at: new Date().toISOString()
                    });
                }

                console.log('✅ تم تغيير كلمة المرور بنجاح');
                resolve();

            } catch (error) {
                console.error('❌ خطأ في تغيير كلمة المرور:', error);
                reject(error);
            }
        });
    }
};

// تصدير نظام المصادقة للاستخدام العام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Auth;
}
