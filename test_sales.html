<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام المبيعات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .test-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="card">
            <div class="card-header">
                <h2><i class="fas fa-vial me-2"></i>اختبار نظام المبيعات</h2>
            </div>
            <div class="card-body">
                <div id="test-results"></div>
                
                <div class="mt-4">
                    <button class="btn btn-primary me-2" onclick="runTests()">
                        <i class="fas fa-play me-1"></i>تشغيل الاختبارات
                    </button>
                    <button class="btn btn-success me-2" onclick="testCreateInvoice()">
                        <i class="fas fa-file-invoice me-1"></i>اختبار إنشاء فاتورة
                    </button>
                    <button class="btn btn-info me-2" onclick="showDashboard()">
                        <i class="fas fa-tachometer-alt me-1"></i>عرض لوحة التحكم
                    </button>
                    <button class="btn btn-warning me-2" onclick="resetSystem()">
                        <i class="fas fa-redo me-1"></i>إعادة تعيين النظام
                    </button>
                </div>
            </div>
        </div>

        <!-- منطقة عرض النظام -->
        <div class="card mt-4">
            <div class="card-header">
                <h3><i class="fas fa-desktop me-2"></i>عرض النظام</h3>
            </div>
            <div class="card-body">
                <div id="main-content">
                    <!-- سيتم عرض محتوى النظام هنا -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="src/js/components/sales.js"></script>
    
    <script>
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result test-${type}`;
            resultDiv.innerHTML = `<i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'} me-2"></i>${message}`;
            resultsDiv.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        function runTests() {
            clearResults();
            addTestResult('بدء الاختبارات...', 'info');

            try {
                // اختبار وجود المكون
                if (typeof window.SalesComponent !== 'undefined') {
                    addTestResult('✅ مكون المبيعات محمل بنجاح', 'success');
                } else {
                    addTestResult('❌ مكون المبيعات غير محمل', 'error');
                    return;
                }

                // اختبار تهيئة المكون
                window.SalesComponent.init();
                addTestResult('✅ تم تهيئة المكون بنجاح', 'success');

                // اختبار البيانات
                const diagnosis = window.SalesComponent.diagnose();
                addTestResult(`📊 عدد العملاء: ${diagnosis.customersCount}`, 'info');
                addTestResult(`📦 عدد المنتجات: ${diagnosis.productsCount}`, 'info');
                addTestResult(`📄 عدد الفواتير: ${diagnosis.invoicesCount}`, 'info');

                // اختبار عرض لوحة التحكم
                const dashboard = window.SalesComponent.renderDashboard();
                if (dashboard && dashboard.length > 0) {
                    addTestResult('✅ عرض لوحة التحكم يعمل', 'success');
                } else {
                    addTestResult('❌ فشل في عرض لوحة التحكم', 'error');
                }

                // اختبار الوظائف المساعدة
                const amount = window.SalesComponent.formatAmount(1000);
                if (amount) {
                    addTestResult(`✅ تنسيق المبالغ يعمل: ${amount}`, 'success');
                } else {
                    addTestResult('❌ فشل في تنسيق المبالغ', 'error');
                }

                addTestResult('🎉 جميع الاختبارات اكتملت بنجاح!', 'success');

            } catch (error) {
                addTestResult(`❌ خطأ في الاختبار: ${error.message}`, 'error');
                console.error('خطأ في الاختبار:', error);
            }
        }

        function testCreateInvoice() {
            try {
                if (window.SalesComponent && typeof window.SalesComponent.showCreateInvoiceModal === 'function') {
                    window.SalesComponent.showCreateInvoiceModal();
                    addTestResult('✅ تم فتح نافذة إنشاء الفاتورة', 'success');
                } else {
                    addTestResult('❌ وظيفة إنشاء الفاتورة غير متاحة', 'error');
                }
            } catch (error) {
                addTestResult(`❌ خطأ في إنشاء الفاتورة: ${error.message}`, 'error');
            }
        }

        function showDashboard() {
            try {
                if (window.SalesComponent) {
                    window.SalesComponent.init();
                    window.SalesComponent.render();
                    addTestResult('✅ تم عرض لوحة التحكم', 'success');
                } else {
                    addTestResult('❌ مكون المبيعات غير متاح', 'error');
                }
            } catch (error) {
                addTestResult(`❌ خطأ في عرض لوحة التحكم: ${error.message}`, 'error');
            }
        }

        function resetSystem() {
            try {
                if (window.SalesComponent && typeof window.SalesComponent.reset === 'function') {
                    window.SalesComponent.reset();
                    addTestResult('✅ تم إعادة تعيين النظام', 'success');
                } else {
                    addTestResult('❌ وظيفة إعادة التعيين غير متاحة', 'error');
                }
            } catch (error) {
                addTestResult(`❌ خطأ في إعادة تعيين النظام: ${error.message}`, 'error');
            }
        }

        // تشغيل الاختبارات تلقائياً عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(runTests, 1000);
        });
    </script>
</body>
</html>
