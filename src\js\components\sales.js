/**
 * ===================================
 * مكون المبيعات - Sales Component
 * ===================================
 */

const SalesComponent = {
    // بيانات المكون
    data: {
        currentView: 'invoices',
        invoices: {},
        customers: {},
        products: {},
        deletedInvoices: {},
        selectedInvoice: null,
        selectedCustomer: null,
        selectedProduct: null,
        filters: {
            dateFrom: '',
            dateTo: '',
            status: '',
            customer: ''
        },
        settings: {
            taxRate: 0.15,
            currency: 'SAR',
            invoicePrefix: 'INV-',
            nextInvoiceNumber: 1,
            companyName: 'قمة الوعد للسفريات',
            companyAddress: 'المملكة العربية السعودية',
            companyTaxNumber: '*********',
            companyEmail: '<EMAIL>',
            companyPhone: '+966501234567',
            companyWebsite: 'https://qimat-alwaed.com',
            language: 'ar',
            timezone: 'Asia/Riyadh',
            dateFormat: 'ar-SA',
            autoSave: true,
            showNotifications: true,
            enableBackup: true,
            autoCalculateTax: true,
            defaultDueDays: 30,
            // إعدادات تصميم الفواتير
            selectedTemplate: 'classic',
            primaryColor: '#007bff',
            secondaryColor: '#6c757d',
            fontSize: 'medium',
            fontFamily: 'segoe',
            borderStyle: 'solid',
            borderWidth: '2',
            logoPosition: 'top-right',
            logoSize: 'medium',
            showLogo: true,
            showWatermark: false,
            showFooter: true,
            watermarkOpacity: 0.1
        }
    },

    /**
     * تهيئة المكون
     */
    init: function() {
        this.loadSalesData();
        this.createSampleData();
    },

    /**
     * عرض المكون الرئيسي
     */
    render: function(params = {}) {
        this.data.currentView = params.view || 'invoices';
        
        return `
            <div class="sales-container">
                ${this.renderHeader()}
                ${this.renderNavigation()}
                <div class="sales-content">
                    ${this.renderCurrentView()}
                </div>
            </div>
        `;
    },

    /**
     * عرض رأس الصفحة
     */
    renderHeader: function() {
        return `
            <div class="sales-header">
                <div class="header-title">
                    <h2><i class="fas fa-receipt me-2"></i>نظام المبيعات</h2>
                    <p class="text-muted">إدارة شاملة للفواتير والعملاء والمنتجات</p>
                </div>
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="window.SalesComponent.showCreateInvoiceModal()">
                        <i class="fas fa-plus me-1"></i>فاتورة جديدة
                    </button>
                    <button class="btn btn-outline-success" onclick="window.SalesComponent.showCreateCustomerModal()">
                        <i class="fas fa-user-plus me-1"></i>عميل جديد
                    </button>
                    <button class="btn btn-outline-info" onclick="window.SalesComponent.showCreateProductModal()">
                        <i class="fas fa-box me-1"></i>منتج جديد
                    </button>
                </div>
            </div>
        `;
    },

    /**
     * عرض شريط التنقل
     */
    renderNavigation: function() {
        const navItems = [
            { id: 'invoices', label: 'الفواتير', icon: 'fas fa-file-invoice' },
            { id: 'customers', label: 'العملاء', icon: 'fas fa-users' },
            { id: 'products', label: 'المنتجات', icon: 'fas fa-boxes' },
            { id: 'reports', label: 'التقارير', icon: 'fas fa-chart-bar' },
            { id: 'settings', label: 'الإعدادات', icon: 'fas fa-cog' }
        ];

        return `
            <div class="sales-navigation">
                <ul class="nav nav-tabs">
                    ${navItems.map(item => `
                        <li class="nav-item">
                            <a class="nav-link ${this.data.currentView === item.id ? 'active' : ''}" 
                               href="#" onclick="window.SalesComponent.switchView('${item.id}')">
                                <i class="${item.icon} me-1"></i>${item.label}
                            </a>
                        </li>
                    `).join('')}
                </ul>
            </div>
        `;
    },

    /**
     * عرض المحتوى الحالي
     */
    renderCurrentView: function() {
        switch (this.data.currentView) {
            case 'invoices':
                return this.renderInvoicesView();
            case 'customers':
                return this.renderCustomersView();
            case 'products':
                return this.renderProductsView();
            case 'reports':
                return this.renderReportsView();
            case 'settings':
                return this.renderSettingsView();
            default:
                return this.renderInvoicesView();
        }
    },

    /**
     * تبديل العرض
     */
    switchView: function(view) {
        this.data.currentView = view;
        const content = document.getElementById('main-content');
        if (content) {
            content.innerHTML = this.renderCurrentView();
        }
        
        // تحديث التقارير إذا كان في قسم التقارير
        if (view === 'reports') {
            setTimeout(() => {
                this.updateReports();
            }, 100);
        }
    },

    /**
     * تحميل بيانات المبيعات
     */
    loadSalesData: function() {
        try {
            const savedData = localStorage.getItem('salesData');
            if (savedData) {
                const parsedData = JSON.parse(savedData);
                Object.assign(this.data, parsedData);
            }
        } catch (error) {
            console.error('خطأ في تحميل بيانات المبيعات:', error);
        }
    },

    /**
     * حفظ بيانات المبيعات
     */
    saveSalesData: function() {
        try {
            localStorage.setItem('salesData', JSON.stringify(this.data));
        } catch (error) {
            console.error('خطأ في حفظ بيانات المبيعات:', error);
        }
    },

    /**
     * إنشاء بيانات تجريبية
     */
    createSampleData: function() {
        // التحقق من وجود بيانات
        if (Object.keys(this.data.customers).length > 0) {
            return;
        }

        // إنشاء عملاء تجريبيين
        this.data.customers = {
            'customer1': {
                id: 'customer1',
                name: 'أحمد محمد السعيد',
                email: '<EMAIL>',
                phone: '+966501234567',
                address: 'الرياض، المملكة العربية السعودية',
                taxNumber: '*********',
                createdAt: new Date().toISOString()
            },
            'customer2': {
                id: 'customer2',
                name: 'شركة النور للتجارة',
                email: '<EMAIL>',
                phone: '+966509876543',
                address: 'جدة، المملكة العربية السعودية',
                taxNumber: '*********',
                createdAt: new Date().toISOString()
            }
        };

        // إنشاء منتجات تجريبية
        this.data.products = {
            'product1': {
                id: 'product1',
                name: 'تذكرة طيران - الرياض إلى دبي',
                description: 'تذكرة طيران ذهاب وإياب',
                price: 1200,
                category: 'طيران',
                sku: 'FLT-RUH-DXB',
                createdAt: new Date().toISOString()
            },
            'product2': {
                id: 'product2',
                name: 'حجز فندق - 3 ليالي',
                description: 'إقامة في فندق 4 نجوم',
                price: 800,
                category: 'إقامة',
                sku: 'HTL-3N-4S',
                createdAt: new Date().toISOString()
            }
        };

        this.saveSalesData();
    },

    /**
     * تحديث البيانات
     */
    refreshData: function() {
        this.loadSalesData();
        const content = document.getElementById('main-content');
        if (content) {
            content.innerHTML = this.renderCurrentView();
        }
        
        // تحديث التقارير إذا كان في قسم التقارير
        if (this.data.currentView === 'reports') {
            setTimeout(() => {
                this.updateReports();
            }, 100);
        }
    }
};

// تصدير المكون للاستخدام العام
window.SalesComponent = SalesComponent;
