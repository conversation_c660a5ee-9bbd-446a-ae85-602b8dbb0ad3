/**
 * مكون نظام المبيعات
 * Sales Management Component
 */

const SalesComponent = {
    // بيانات النظام
    data: {
        currentView: 'dashboard',
        customers: {},
        products: {},
        invoices: {},
        salesTransactions: {},
        settings: {
            taxRate: 0.15, // ضريبة القيمة المضافة 15%
            currency: 'SAR',
            invoicePrefix: 'INV-',
            nextInvoiceNumber: 1
        }
    },

    // إعدادات العرض
    views: [
        { key: 'dashboard', label: 'لوحة التحكم', icon: 'fas fa-tachometer-alt' },
        { key: 'invoices', label: 'الفواتير', icon: 'fas fa-file-invoice' },
        { key: 'customers', label: 'العملاء', icon: 'fas fa-users' },
        { key: 'products', label: 'المنتجات والخدمات', icon: 'fas fa-box' },
        { key: 'reports', label: 'التقارير', icon: 'fas fa-chart-bar' },
        { key: 'settings', label: 'الإعدادات', icon: 'fas fa-cog' }
    ],

    /**
     * تهيئة مكون المبيعات
     */
    init: function() {
        this.loadSalesData();
        this.setupEventListeners();
        console.log('✅ تم تهيئة نظام المبيعات');
    },

    /**
     * تحميل بيانات المبيعات
     */
    loadSalesData: function() {
        try {
            // تحميل البيانات من localStorage
            const savedData = localStorage.getItem('salesData');
            if (savedData) {
                const data = JSON.parse(savedData);
                this.data = { ...this.data, ...data };
            } else {
                // إنشاء بيانات تجريبية
                this.createSampleData();
            }
        } catch (error) {
            console.error('خطأ في تحميل بيانات المبيعات:', error);
            this.createSampleData();
        }
    },

    /**
     * حفظ بيانات المبيعات
     */
    saveSalesData: function() {
        try {
            localStorage.setItem('salesData', JSON.stringify(this.data));
        } catch (error) {
            console.error('خطأ في حفظ بيانات المبيعات:', error);
        }
    },

    /**
     * إنشاء بيانات تجريبية
     */
    createSampleData: function() {
        // عملاء تجريبيون
        this.data.customers = {
            'CUST001': {
                id: 'CUST001',
                name: 'شركة الرياض للسفر',
                email: '<EMAIL>',
                phone: '+966501234567',
                address: 'الرياض، المملكة العربية السعودية',
                taxNumber: '*********',
                type: 'corporate',
                creditLimit: 50000,
                balance: 0,
                isActive: true,
                createdAt: new Date().toISOString()
            },
            'CUST002': {
                id: 'CUST002',
                name: 'أحمد محمد العلي',
                email: '<EMAIL>',
                phone: '+966509876543',
                address: 'جدة، المملكة العربية السعودية',
                taxNumber: '',
                type: 'individual',
                creditLimit: 10000,
                balance: 0,
                isActive: true,
                createdAt: new Date().toISOString()
            }
        };

        // منتجات وخدمات تجريبية
        this.data.products = {
            'PROD001': {
                id: 'PROD001',
                name: 'تذكرة طيران داخلي',
                nameEn: 'Domestic Flight Ticket',
                category: 'flights',
                type: 'service',
                price: 500,
                cost: 400,
                taxable: true,
                isActive: true,
                description: 'تذكرة طيران داخلي - درجة اقتصادية',
                createdAt: new Date().toISOString()
            },
            'PROD002': {
                id: 'PROD002',
                name: 'حجز فندقي - ليلة واحدة',
                nameEn: 'Hotel Booking - One Night',
                category: 'hotels',
                type: 'service',
                price: 300,
                cost: 250,
                taxable: true,
                isActive: true,
                description: 'حجز فندقي لليلة واحدة - غرفة مفردة',
                createdAt: new Date().toISOString()
            },
            'PROD003': {
                id: 'PROD003',
                name: 'تأشيرة سياحية',
                nameEn: 'Tourist Visa',
                category: 'visas',
                type: 'service',
                price: 200,
                cost: 150,
                taxable: false,
                isActive: true,
                description: 'خدمة استخراج تأشيرة سياحية',
                createdAt: new Date().toISOString()
            }
        };

        this.saveSalesData();
    },

    /**
     * تبديل العرض
     */
    switchView: function(view) {
        this.data.currentView = view;
        const content = document.getElementById('main-content');
        if (content) {
            content.innerHTML = this.renderCurrentView();
        }
    },

    /**
     * عرض المحتوى الحالي
     */
    renderCurrentView: function() {
        switch (this.data.currentView) {
            case 'dashboard':
                return this.renderDashboard();
            case 'invoices':
                return this.renderInvoices();
            case 'customers':
                return this.renderCustomers();
            case 'products':
                return this.renderProducts();
            case 'reports':
                return this.renderReports();
            case 'settings':
                return this.renderSettings();
            default:
                return this.renderDashboard();
        }
    },

    /**
     * عرض نافذة المبيعات الرئيسية
     */
    render: function() {
        return `
            <div class="sales-management">
                <!-- شريط التنقل -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h4 class="mb-0">
                                        <i class="fas fa-shopping-cart text-primary me-2"></i>
                                        نظام إدارة المبيعات
                                    </h4>
                                    <div class="btn-group">
                                        <button class="btn btn-primary" onclick="window.SalesComponent.showNewInvoiceModal()">
                                            <i class="fas fa-plus me-1"></i>فاتورة جديدة
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="window.SalesComponent.refreshData()">
                                            <i class="fas fa-sync me-1"></i>تحديث
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- تبويبات التنقل -->
                                <ul class="nav nav-pills nav-fill">
                                    ${this.views.map(item => `
                                        <li class="nav-item">
                                            <a class="nav-link ${this.data.currentView === item.key ? 'active' : ''}" 
                                               href="#" onclick="window.SalesComponent.switchView('${item.key}')">
                                                <i class="${item.icon} me-2"></i>${item.label}
                                            </a>
                                        </li>
                                    `).join('')}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- المحتوى الرئيسي -->
                <div class="row">
                    <div class="col-12">
                        ${this.renderCurrentView()}
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض لوحة التحكم
     */
    renderDashboard: function() {
        const stats = this.calculateDashboardStats();
        
        return `
            <div class="sales-dashboard">
                <!-- بطاقات الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">إجمالي المبيعات اليوم</h6>
                                        <h4>${this.formatAmount(stats.todaySales)}</h4>
                                    </div>
                                    <i class="fas fa-chart-line fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">عدد الفواتير</h6>
                                        <h4>${stats.totalInvoices}</h4>
                                    </div>
                                    <i class="fas fa-file-invoice fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">عدد العملاء</h6>
                                        <h4>${stats.totalCustomers}</h4>
                                    </div>
                                    <i class="fas fa-users fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">متوسط قيمة الفاتورة</h6>
                                        <h4>${this.formatAmount(stats.averageInvoice)}</h4>
                                    </div>
                                    <i class="fas fa-calculator fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسوم البيانية والتقارير السريعة -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-area me-2"></i>مبيعات آخر 7 أيام</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="salesChart" height="100"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-list me-2"></i>آخر الفواتير</h5>
                            </div>
                            <div class="card-body">
                                ${this.renderRecentInvoices()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * حساب إحصائيات لوحة التحكم
     */
    calculateDashboardStats: function() {
        const invoices = Object.values(this.data.invoices || {});
        const customers = Object.values(this.data.customers || {});
        
        const today = new Date().toISOString().split('T')[0];
        const todayInvoices = invoices.filter(inv => inv.date === today);
        
        return {
            todaySales: todayInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0),
            totalInvoices: invoices.length,
            totalCustomers: customers.length,
            averageInvoice: invoices.length > 0 ? 
                invoices.reduce((sum, inv) => sum + (inv.total || 0), 0) / invoices.length : 0
        };
    },

    /**
     * تنسيق المبالغ
     */
    formatAmount: function(amount) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: this.data.settings.currency
        }).format(amount || 0);
    },

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners: function() {
        // سيتم إضافة مستمعي الأحداث هنا
    },

    /**
     * عرض آخر الفواتير
     */
    renderRecentInvoices: function() {
        const invoices = Object.values(this.data.invoices || {})
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, 5);

        if (invoices.length === 0) {
            return `
                <div class="text-center text-muted">
                    <i class="fas fa-file-invoice fa-3x mb-3"></i>
                    <p>لا توجد فواتير حتى الآن</p>
                    <button class="btn btn-primary btn-sm" onclick="window.SalesComponent.showNewInvoiceModal()">
                        <i class="fas fa-plus me-1"></i>إنشاء فاتورة جديدة
                    </button>
                </div>
            `;
        }

        return `
            <div class="list-group list-group-flush">
                ${invoices.map(invoice => `
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">${invoice.number}</h6>
                            <small class="text-muted">${this.data.customers[invoice.customerId]?.name || 'عميل غير محدد'}</small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-${this.getInvoiceStatusColor(invoice.status)} mb-1">${this.getInvoiceStatusLabel(invoice.status)}</span>
                            <div class="fw-bold">${this.formatAmount(invoice.total)}</div>
                        </div>
                    </div>
                `).join('')}
            </div>
            <div class="text-center mt-3">
                <button class="btn btn-outline-primary btn-sm" onclick="window.SalesComponent.switchView('invoices')">
                    عرض جميع الفواتير
                </button>
            </div>
        `;
    },

    /**
     * الحصول على لون حالة الفاتورة
     */
    getInvoiceStatusColor: function(status) {
        const colors = {
            'draft': 'secondary',
            'sent': 'primary',
            'paid': 'success',
            'overdue': 'danger',
            'cancelled': 'dark'
        };
        return colors[status] || 'secondary';
    },

    /**
     * الحصول على تسمية حالة الفاتورة
     */
    getInvoiceStatusLabel: function(status) {
        const labels = {
            'draft': 'مسودة',
            'sent': 'مرسلة',
            'paid': 'مدفوعة',
            'overdue': 'متأخرة',
            'cancelled': 'ملغية'
        };
        return labels[status] || 'غير محدد';
    },

    /**
     * عرض نافذة فاتورة جديدة
     */
    showNewInvoiceModal: function() {
        const modalHTML = `
            <div class="modal fade" id="newInvoiceModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-file-invoice me-2"></i>فاتورة جديدة
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${this.renderInvoiceForm()}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-outline-primary" onclick="window.SalesComponent.saveInvoiceAsDraft()">
                                <i class="fas fa-save me-1"></i>حفظ كمسودة
                            </button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveAndSendInvoice()">
                                <i class="fas fa-paper-plane me-1"></i>حفظ وإرسال
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('newInvoiceModal'));
        modal.show();

        document.getElementById('newInvoiceModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });

        // تهيئة النموذج
        this.initializeInvoiceForm();
    },

    /**
     * عرض نموذج الفاتورة
     */
    renderInvoiceForm: function() {
        const customers = Object.values(this.data.customers || {});
        const products = Object.values(this.data.products || {});

        return `
            <form id="invoiceForm">
                <div class="row">
                    <!-- معلومات الفاتورة -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-info-circle me-2"></i>معلومات الفاتورة</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">رقم الفاتورة</label>
                                    <input type="text" class="form-control" name="invoiceNumber"
                                           value="${this.data.settings.invoicePrefix}${this.data.settings.nextInvoiceNumber.toString().padStart(4, '0')}" readonly>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">تاريخ الفاتورة *</label>
                                    <input type="date" class="form-control" name="invoiceDate"
                                           value="${new Date().toISOString().split('T')[0]}" required>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">تاريخ الاستحقاق</label>
                                    <input type="date" class="form-control" name="dueDate">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">مرجع الطلب</label>
                                    <input type="text" class="form-control" name="reference" placeholder="رقم الطلب أو المرجع">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات العميل -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-user me-2"></i>معلومات العميل</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">العميل *</label>
                                    <select class="form-control" name="customerId" required onchange="window.SalesComponent.updateCustomerInfo()">
                                        <option value="">اختر العميل</option>
                                        ${customers.map(customer => `
                                            <option value="${customer.id}">${customer.name}</option>
                                        `).join('')}
                                    </select>
                                </div>

                                <div id="customerInfo" class="d-none">
                                    <div class="mb-2">
                                        <small class="text-muted">البريد الإلكتروني:</small>
                                        <div id="customerEmail"></div>
                                    </div>
                                    <div class="mb-2">
                                        <small class="text-muted">الهاتف:</small>
                                        <div id="customerPhone"></div>
                                    </div>
                                    <div class="mb-2">
                                        <small class="text-muted">العنوان:</small>
                                        <div id="customerAddress"></div>
                                    </div>
                                </div>

                                <div class="mt-3">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="window.SalesComponent.showNewCustomerModal()">
                                        <i class="fas fa-plus me-1"></i>عميل جديد
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- عناصر الفاتورة -->
                <div class="card mt-3">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6><i class="fas fa-list me-2"></i>عناصر الفاتورة</h6>
                            <button type="button" class="btn btn-primary btn-sm" onclick="window.SalesComponent.addInvoiceItem()">
                                <i class="fas fa-plus me-1"></i>إضافة عنصر
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th width="30%">المنتج/الخدمة</th>
                                        <th width="15%">الكمية</th>
                                        <th width="15%">السعر</th>
                                        <th width="15%">الخصم</th>
                                        <th width="15%">المجموع</th>
                                        <th width="10%">إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="invoiceItems">
                                    <!-- سيتم إضافة العناصر هنا -->
                                </tbody>
                            </table>
                        </div>

                        <!-- ملخص الفاتورة -->
                        <div class="row mt-3">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="3" placeholder="ملاحظات إضافية للفاتورة"></textarea>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>المجموع الفرعي:</span>
                                            <span id="subtotal">0.00 ر.س</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>الخصم:</span>
                                            <span id="totalDiscount">0.00 ر.س</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>الضريبة (${(this.data.settings.taxRate * 100)}%):</span>
                                            <span id="taxAmount">0.00 ر.س</span>
                                        </div>
                                        <hr>
                                        <div class="d-flex justify-content-between fw-bold">
                                            <span>الإجمالي:</span>
                                            <span id="grandTotal">0.00 ر.س</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        `;
    },

    /**
     * تهيئة نموذج الفاتورة
     */
    initializeInvoiceForm: function() {
        // إضافة عنصر افتراضي
        this.addInvoiceItem();
    },

    /**
     * إضافة عنصر للفاتورة
     */
    addInvoiceItem: function() {
        const tbody = document.getElementById('invoiceItems');
        const products = Object.values(this.data.products || {});
        const itemIndex = tbody.children.length;

        const itemHTML = `
            <tr data-item-index="${itemIndex}">
                <td>
                    <select class="form-control" name="items[${itemIndex}][productId]" onchange="window.SalesComponent.updateItemPrice(${itemIndex})" required>
                        <option value="">اختر المنتج/الخدمة</option>
                        ${products.map(product => `
                            <option value="${product.id}" data-price="${product.price}">${product.name}</option>
                        `).join('')}
                    </select>
                </td>
                <td>
                    <input type="number" class="form-control" name="items[${itemIndex}][quantity]"
                           value="1" min="1" step="0.01" onchange="window.SalesComponent.calculateItemTotal(${itemIndex})" required>
                </td>
                <td>
                    <input type="number" class="form-control" name="items[${itemIndex}][price]"
                           step="0.01" min="0" onchange="window.SalesComponent.calculateItemTotal(${itemIndex})" required>
                </td>
                <td>
                    <input type="number" class="form-control" name="items[${itemIndex}][discount]"
                           value="0" step="0.01" min="0" onchange="window.SalesComponent.calculateItemTotal(${itemIndex})">
                </td>
                <td>
                    <input type="number" class="form-control" name="items[${itemIndex}][total]" readonly>
                </td>
                <td>
                    <button type="button" class="btn btn-danger btn-sm" onclick="window.SalesComponent.removeInvoiceItem(${itemIndex})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;

        tbody.insertAdjacentHTML('beforeend', itemHTML);
    },

    /**
     * تحديث سعر العنصر عند اختيار المنتج
     */
    updateItemPrice: function(itemIndex) {
        const select = document.querySelector(`select[name="items[${itemIndex}][productId]"]`);
        const priceInput = document.querySelector(`input[name="items[${itemIndex}][price]"]`);

        if (select.selectedOptions.length > 0) {
            const price = select.selectedOptions[0].getAttribute('data-price');
            priceInput.value = price || 0;
            this.calculateItemTotal(itemIndex);
        }
    },

    /**
     * حساب إجمالي العنصر
     */
    calculateItemTotal: function(itemIndex) {
        const quantity = parseFloat(document.querySelector(`input[name="items[${itemIndex}][quantity]"]`).value) || 0;
        const price = parseFloat(document.querySelector(`input[name="items[${itemIndex}][price]"]`).value) || 0;
        const discount = parseFloat(document.querySelector(`input[name="items[${itemIndex}][discount]"]`).value) || 0;

        const total = (quantity * price) - discount;
        document.querySelector(`input[name="items[${itemIndex}][total]"]`).value = total.toFixed(2);

        this.calculateInvoiceTotal();
    },

    /**
     * حساب إجمالي الفاتورة
     */
    calculateInvoiceTotal: function() {
        const itemTotals = document.querySelectorAll('input[name*="[total]"]');
        let subtotal = 0;
        let totalDiscount = 0;

        itemTotals.forEach(input => {
            subtotal += parseFloat(input.value) || 0;
        });

        // حساب إجمالي الخصومات
        const discountInputs = document.querySelectorAll('input[name*="[discount]"]');
        discountInputs.forEach(input => {
            totalDiscount += parseFloat(input.value) || 0;
        });

        const taxAmount = subtotal * this.data.settings.taxRate;
        const grandTotal = subtotal + taxAmount;

        // تحديث العرض
        document.getElementById('subtotal').textContent = this.formatAmount(subtotal);
        document.getElementById('totalDiscount').textContent = this.formatAmount(totalDiscount);
        document.getElementById('taxAmount').textContent = this.formatAmount(taxAmount);
        document.getElementById('grandTotal').textContent = this.formatAmount(grandTotal);
    },

    /**
     * حذف عنصر من الفاتورة
     */
    removeInvoiceItem: function(itemIndex) {
        const row = document.querySelector(`tr[data-item-index="${itemIndex}"]`);
        if (row) {
            row.remove();
            this.calculateInvoiceTotal();
        }
    },

    /**
     * تحديث معلومات العميل
     */
    updateCustomerInfo: function() {
        const select = document.querySelector('select[name="customerId"]');
        const customerInfo = document.getElementById('customerInfo');

        if (select.value) {
            const customer = this.data.customers[select.value];
            if (customer) {
                document.getElementById('customerEmail').textContent = customer.email || '-';
                document.getElementById('customerPhone').textContent = customer.phone || '-';
                document.getElementById('customerAddress').textContent = customer.address || '-';
                customerInfo.classList.remove('d-none');
            }
        } else {
            customerInfo.classList.add('d-none');
        }
    },

    /**
     * حفظ الفاتورة كمسودة
     */
    saveInvoiceAsDraft: function() {
        this.saveInvoice('draft');
    },

    /**
     * حفظ وإرسال الفاتورة
     */
    saveAndSendInvoice: function() {
        this.saveInvoice('sent');
    },

    /**
     * حفظ الفاتورة
     */
    saveInvoice: function(status = 'draft') {
        const form = document.getElementById('invoiceForm');
        const formData = new FormData(form);

        try {
            // جمع بيانات الفاتورة
            const invoiceData = {
                id: this.generateId(),
                number: formData.get('invoiceNumber'),
                date: formData.get('invoiceDate'),
                dueDate: formData.get('dueDate'),
                customerId: formData.get('customerId'),
                reference: formData.get('reference'),
                notes: formData.get('notes'),
                status: status,
                items: [],
                subtotal: 0,
                totalDiscount: 0,
                taxAmount: 0,
                total: 0,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // التحقق من صحة البيانات
            if (!invoiceData.customerId) {
                throw new Error('يرجى اختيار العميل');
            }

            // جمع عناصر الفاتورة
            const itemRows = document.querySelectorAll('#invoiceItems tr');
            itemRows.forEach((row, index) => {
                const productId = row.querySelector(`select[name="items[${index}][productId]"]`)?.value;
                const quantity = parseFloat(row.querySelector(`input[name="items[${index}][quantity]"]`)?.value) || 0;
                const price = parseFloat(row.querySelector(`input[name="items[${index}][price]"]`)?.value) || 0;
                const discount = parseFloat(row.querySelector(`input[name="items[${index}][discount]"]`)?.value) || 0;
                const total = parseFloat(row.querySelector(`input[name="items[${index}][total]"]`)?.value) || 0;

                if (productId && quantity > 0 && price > 0) {
                    invoiceData.items.push({
                        productId,
                        quantity,
                        price,
                        discount,
                        total
                    });
                }
            });

            if (invoiceData.items.length === 0) {
                throw new Error('يرجى إضافة عنصر واحد على الأقل للفاتورة');
            }

            // حساب الإجماليات
            invoiceData.subtotal = invoiceData.items.reduce((sum, item) => sum + (item.quantity * item.price), 0);
            invoiceData.totalDiscount = invoiceData.items.reduce((sum, item) => sum + item.discount, 0);
            invoiceData.taxAmount = (invoiceData.subtotal - invoiceData.totalDiscount) * this.data.settings.taxRate;
            invoiceData.total = invoiceData.subtotal - invoiceData.totalDiscount + invoiceData.taxAmount;

            // حفظ الفاتورة
            this.data.invoices[invoiceData.id] = invoiceData;

            // تحديث رقم الفاتورة التالي
            this.data.settings.nextInvoiceNumber++;

            // حفظ البيانات
            this.saveSalesData();

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('newInvoiceModal'));
            modal.hide();

            // عرض رسالة نجاح
            this.showMessage(`تم ${status === 'draft' ? 'حفظ' : 'حفظ وإرسال'} الفاتورة بنجاح`, 'success');

            // تحديث العرض
            this.refreshData();

        } catch (error) {
            this.showMessage('خطأ في حفظ الفاتورة: ' + error.message, 'error');
        }
    },

    /**
     * توليد معرف فريد
     */
    generateId: function() {
        return 'ID_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    },

    /**
     * عرض رسالة للمستخدم
     */
    showMessage: function(message, type = 'info') {
        // يمكن تحسين هذا لاحقاً باستخدام نظام إشعارات أفضل
        const alertClass = type === 'success' ? 'alert-success' :
                          type === 'error' ? 'alert-danger' : 'alert-info';

        const alertHTML = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // إضافة الرسالة في أعلى الصفحة
        const container = document.querySelector('.sales-management') || document.body;
        container.insertAdjacentHTML('afterbegin', alertHTML);

        // إزالة الرسالة تلقائياً بعد 5 ثوان
        setTimeout(() => {
            const alert = container.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    },

    /**
     * عرض قائمة الفواتير
     */
    renderInvoices: function() {
        const invoices = Object.values(this.data.invoices || {})
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        return `
            <div class="invoices-management">
                <!-- شريط الأدوات -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-0">
                                    <i class="fas fa-file-invoice me-2"></i>إدارة الفواتير
                                </h5>
                            </div>
                            <div class="col-md-6 text-end">
                                <button class="btn btn-primary" onclick="window.SalesComponent.showNewInvoiceModal()">
                                    <i class="fas fa-plus me-1"></i>فاتورة جديدة
                                </button>
                                <button class="btn btn-outline-secondary" onclick="window.SalesComponent.exportInvoices()">
                                    <i class="fas fa-download me-1"></i>تصدير
                                </button>
                            </div>
                        </div>

                        <!-- فلاتر البحث -->
                        <div class="row mt-3">
                            <div class="col-md-3">
                                <input type="text" class="form-control" placeholder="البحث برقم الفاتورة..."
                                       onkeyup="window.SalesComponent.filterInvoices()">
                            </div>
                            <div class="col-md-3">
                                <select class="form-control" onchange="window.SalesComponent.filterInvoices()">
                                    <option value="">جميع الحالات</option>
                                    <option value="draft">مسودة</option>
                                    <option value="sent">مرسلة</option>
                                    <option value="paid">مدفوعة</option>
                                    <option value="overdue">متأخرة</option>
                                    <option value="cancelled">ملغية</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <input type="date" class="form-control" onchange="window.SalesComponent.filterInvoices()">
                            </div>
                            <div class="col-md-3">
                                <select class="form-control" onchange="window.SalesComponent.filterInvoices()">
                                    <option value="">جميع العملاء</option>
                                    ${Object.values(this.data.customers || {}).map(customer => `
                                        <option value="${customer.id}">${customer.name}</option>
                                    `).join('')}
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول الفواتير -->
                <div class="card">
                    <div class="card-body">
                        ${invoices.length === 0 ? this.renderEmptyInvoices() : this.renderInvoicesTable(invoices)}
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض رسالة عدم وجود فواتير
     */
    renderEmptyInvoices: function() {
        return `
            <div class="text-center py-5">
                <i class="fas fa-file-invoice fa-5x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد فواتير حتى الآن</h4>
                <p class="text-muted">ابدأ بإنشاء أول فاتورة لك</p>
                <button class="btn btn-primary btn-lg" onclick="window.SalesComponent.showNewInvoiceModal()">
                    <i class="fas fa-plus me-2"></i>إنشاء فاتورة جديدة
                </button>
            </div>
        `;
    },

    /**
     * عرض جدول الفواتير
     */
    renderInvoicesTable: function(invoices) {
        return `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>التاريخ</th>
                            <th>الإجمالي</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${invoices.map(invoice => `
                            <tr>
                                <td>
                                    <strong>${invoice.number}</strong>
                                    ${invoice.reference ? `<br><small class="text-muted">${invoice.reference}</small>` : ''}
                                </td>
                                <td>
                                    ${this.data.customers[invoice.customerId]?.name || 'عميل غير محدد'}
                                    <br><small class="text-muted">${this.data.customers[invoice.customerId]?.email || ''}</small>
                                </td>
                                <td>
                                    ${new Date(invoice.date).toLocaleDateString('ar-SA')}
                                    ${invoice.dueDate ? `<br><small class="text-muted">الاستحقاق: ${new Date(invoice.dueDate).toLocaleDateString('ar-SA')}</small>` : ''}
                                </td>
                                <td>
                                    <strong>${this.formatAmount(invoice.total)}</strong>
                                    <br><small class="text-muted">${invoice.items?.length || 0} عنصر</small>
                                </td>
                                <td>
                                    <span class="badge bg-${this.getInvoiceStatusColor(invoice.status)}">
                                        ${this.getInvoiceStatusLabel(invoice.status)}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="window.SalesComponent.viewInvoice('${invoice.id}')" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="window.SalesComponent.editInvoice('${invoice.id}')" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-info" onclick="window.SalesComponent.printInvoice('${invoice.id}')" title="طباعة">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="window.SalesComponent.deleteInvoice('${invoice.id}')" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    },

    /**
     * عرض تفاصيل الفاتورة
     */
    viewInvoice: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            this.showMessage('الفاتورة غير موجودة', 'error');
            return;
        }

        const customer = this.data.customers[invoice.customerId];
        const modalHTML = `
            <div class="modal fade" id="viewInvoiceModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-file-invoice me-2"></i>تفاصيل الفاتورة ${invoice.number}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${this.renderInvoiceDetails(invoice, customer)}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-outline-primary" onclick="window.SalesComponent.editInvoice('${invoice.id}')">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </button>
                            <button type="button" class="btn btn-info" onclick="window.SalesComponent.printInvoice('${invoice.id}')">
                                <i class="fas fa-print me-1"></i>طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('viewInvoiceModal'));
        modal.show();

        document.getElementById('viewInvoiceModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * عرض تفاصيل الفاتورة
     */
    renderInvoiceDetails: function(invoice, customer) {
        return `
            <div class="invoice-details">
                <!-- رأس الفاتورة -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h4>فاتورة رقم: ${invoice.number}</h4>
                        <p class="text-muted mb-1">تاريخ الإصدار: ${new Date(invoice.date).toLocaleDateString('ar-SA')}</p>
                        ${invoice.dueDate ? `<p class="text-muted mb-1">تاريخ الاستحقاق: ${new Date(invoice.dueDate).toLocaleDateString('ar-SA')}</p>` : ''}
                        ${invoice.reference ? `<p class="text-muted">المرجع: ${invoice.reference}</p>` : ''}
                    </div>
                    <div class="col-md-6 text-end">
                        <span class="badge bg-${this.getInvoiceStatusColor(invoice.status)} fs-6 mb-2">
                            ${this.getInvoiceStatusLabel(invoice.status)}
                        </span>
                        <h3 class="text-primary">${this.formatAmount(invoice.total)}</h3>
                    </div>
                </div>

                <!-- معلومات العميل -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-user me-2"></i>معلومات العميل</h6>
                            </div>
                            <div class="card-body">
                                <h6>${customer?.name || 'عميل غير محدد'}</h6>
                                ${customer?.email ? `<p class="mb-1"><i class="fas fa-envelope me-2"></i>${customer.email}</p>` : ''}
                                ${customer?.phone ? `<p class="mb-1"><i class="fas fa-phone me-2"></i>${customer.phone}</p>` : ''}
                                ${customer?.address ? `<p class="mb-0"><i class="fas fa-map-marker-alt me-2"></i>${customer.address}</p>` : ''}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-building me-2"></i>معلومات الشركة</h6>
                            </div>
                            <div class="card-body">
                                <h6>قمة الوعد للسفريات</h6>
                                <p class="mb-1">المملكة العربية السعودية</p>
                                <p class="mb-1">الرقم الضريبي: *********</p>
                                <p class="mb-0"><EMAIL></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- عناصر الفاتورة -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6><i class="fas fa-list me-2"></i>عناصر الفاتورة</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th>المنتج/الخدمة</th>
                                        <th>الكمية</th>
                                        <th>السعر</th>
                                        <th>الخصم</th>
                                        <th>المجموع</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${invoice.items?.map(item => {
                                        const product = this.data.products[item.productId];
                                        return `
                                            <tr>
                                                <td>${product?.name || 'منتج غير محدد'}</td>
                                                <td>${item.quantity}</td>
                                                <td>${this.formatAmount(item.price)}</td>
                                                <td>${this.formatAmount(item.discount)}</td>
                                                <td>${this.formatAmount(item.total)}</td>
                                            </tr>
                                        `;
                                    }).join('') || '<tr><td colspan="5" class="text-center">لا توجد عناصر</td></tr>'}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- ملخص الفاتورة -->
                <div class="row">
                    <div class="col-md-8">
                        ${invoice.notes ? `
                            <div class="card">
                                <div class="card-header">
                                    <h6><i class="fas fa-sticky-note me-2"></i>ملاحظات</h6>
                                </div>
                                <div class="card-body">
                                    <p>${invoice.notes}</p>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>المجموع الفرعي:</span>
                                    <span>${this.formatAmount(invoice.subtotal)}</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>الخصم:</span>
                                    <span>${this.formatAmount(invoice.totalDiscount)}</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>الضريبة:</span>
                                    <span>${this.formatAmount(invoice.taxAmount)}</span>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between fw-bold fs-5">
                                    <span>الإجمالي:</span>
                                    <span class="text-primary">${this.formatAmount(invoice.total)}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض قائمة الفواتير
     */
    renderInvoices: function() {
        const invoices = Object.values(this.data.invoices || {})
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        return `
            <div class="invoices-management">
                <!-- شريط الأدوات -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-0">
                                    <i class="fas fa-file-invoice me-2"></i>إدارة الفواتير
                                </h5>
                            </div>
                            <div class="col-md-6 text-end">
                                <button class="btn btn-primary" onclick="window.SalesComponent.showNewInvoiceModal()">
                                    <i class="fas fa-plus me-1"></i>فاتورة جديدة
                                </button>
                                <button class="btn btn-outline-secondary" onclick="window.SalesComponent.exportInvoices()">
                                    <i class="fas fa-download me-1"></i>تصدير
                                </button>
                            </div>
                        </div>

                        <!-- فلاتر البحث -->
                        <div class="row mt-3">
                            <div class="col-md-3">
                                <input type="text" class="form-control" placeholder="البحث برقم الفاتورة..."
                                       id="invoiceSearch" onkeyup="window.SalesComponent.filterInvoices()">
                            </div>
                            <div class="col-md-3">
                                <select class="form-control" id="statusFilter" onchange="window.SalesComponent.filterInvoices()">
                                    <option value="">جميع الحالات</option>
                                    <option value="draft">مسودة</option>
                                    <option value="sent">مرسلة</option>
                                    <option value="paid">مدفوعة</option>
                                    <option value="overdue">متأخرة</option>
                                    <option value="cancelled">ملغية</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <input type="date" class="form-control" id="dateFilter" onchange="window.SalesComponent.filterInvoices()">
                            </div>
                            <div class="col-md-3">
                                <select class="form-control" id="customerFilter" onchange="window.SalesComponent.filterInvoices()">
                                    <option value="">جميع العملاء</option>
                                    ${Object.values(this.data.customers || {}).map(customer => `
                                        <option value="${customer.id}">${customer.name}</option>
                                    `).join('')}
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول الفواتير -->
                <div class="card">
                    <div class="card-body">
                        <div id="invoicesTableContainer">
                            ${invoices.length === 0 ? this.renderEmptyInvoices() : this.renderInvoicesTable(invoices)}
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض رسالة عدم وجود فواتير
     */
    renderEmptyInvoices: function() {
        return `
            <div class="text-center py-5">
                <i class="fas fa-file-invoice fa-5x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد فواتير حتى الآن</h4>
                <p class="text-muted">ابدأ بإنشاء أول فاتورة لك</p>
                <button class="btn btn-primary btn-lg" onclick="window.SalesComponent.showNewInvoiceModal()">
                    <i class="fas fa-plus me-2"></i>إنشاء فاتورة جديدة
                </button>
            </div>
        `;
    },

    /**
     * عرض جدول الفواتير
     */
    renderInvoicesTable: function(invoices) {
        return `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>التاريخ</th>
                            <th>الإجمالي</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${invoices.map(invoice => `
                            <tr>
                                <td>
                                    <strong>${invoice.number}</strong>
                                    ${invoice.reference ? `<br><small class="text-muted">${invoice.reference}</small>` : ''}
                                </td>
                                <td>
                                    ${this.data.customers[invoice.customerId]?.name || 'عميل غير محدد'}
                                    <br><small class="text-muted">${this.data.customers[invoice.customerId]?.email || ''}</small>
                                </td>
                                <td>
                                    ${new Date(invoice.date).toLocaleDateString('ar-SA')}
                                    ${invoice.dueDate ? `<br><small class="text-muted">الاستحقاق: ${new Date(invoice.dueDate).toLocaleDateString('ar-SA')}</small>` : ''}
                                </td>
                                <td>
                                    <strong>${this.formatAmount(invoice.total)}</strong>
                                    <br><small class="text-muted">${invoice.items?.length || 0} عنصر</small>
                                </td>
                                <td>
                                    <span class="badge bg-${this.getInvoiceStatusColor(invoice.status)}">
                                        ${this.getInvoiceStatusLabel(invoice.status)}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="window.SalesComponent.viewInvoice('${invoice.id}')" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="window.SalesComponent.editInvoice('${invoice.id}')" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-info" onclick="window.SalesComponent.printInvoice('${invoice.id}')" title="طباعة">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="window.SalesComponent.deleteInvoice('${invoice.id}')" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    },

    /**
     * فلترة الفواتير
     */
    filterInvoices: function() {
        const searchTerm = document.getElementById('invoiceSearch')?.value.toLowerCase() || '';
        const statusFilter = document.getElementById('statusFilter')?.value || '';
        const dateFilter = document.getElementById('dateFilter')?.value || '';
        const customerFilter = document.getElementById('customerFilter')?.value || '';

        let filteredInvoices = Object.values(this.data.invoices || {});

        // فلترة بالبحث
        if (searchTerm) {
            filteredInvoices = filteredInvoices.filter(invoice =>
                invoice.number.toLowerCase().includes(searchTerm) ||
                invoice.reference?.toLowerCase().includes(searchTerm)
            );
        }

        // فلترة بالحالة
        if (statusFilter) {
            filteredInvoices = filteredInvoices.filter(invoice => invoice.status === statusFilter);
        }

        // فلترة بالتاريخ
        if (dateFilter) {
            filteredInvoices = filteredInvoices.filter(invoice => invoice.date === dateFilter);
        }

        // فلترة بالعميل
        if (customerFilter) {
            filteredInvoices = filteredInvoices.filter(invoice => invoice.customerId === customerFilter);
        }

        // ترتيب النتائج
        filteredInvoices.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        // تحديث الجدول
        const container = document.getElementById('invoicesTableContainer');
        if (container) {
            container.innerHTML = filteredInvoices.length === 0 ?
                '<div class="text-center py-4"><p class="text-muted">لا توجد فواتير تطابق معايير البحث</p></div>' :
                this.renderInvoicesTable(filteredInvoices);
        }
    },

    /**
     * عرض تفاصيل الفاتورة
     */
    viewInvoice: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            this.showMessage('الفاتورة غير موجودة', 'error');
            return;
        }

        const customer = this.data.customers[invoice.customerId];
        const modalHTML = `
            <div class="modal fade" id="viewInvoiceModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-file-invoice me-2"></i>تفاصيل الفاتورة ${invoice.number}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${this.renderInvoiceDetails(invoice, customer)}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-outline-primary" onclick="window.SalesComponent.editInvoice('${invoice.id}'); bootstrap.Modal.getInstance(document.getElementById('viewInvoiceModal')).hide();">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </button>
                            <button type="button" class="btn btn-info" onclick="window.SalesComponent.printInvoice('${invoice.id}')">
                                <i class="fas fa-print me-1"></i>طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('viewInvoiceModal'));
        modal.show();

        document.getElementById('viewInvoiceModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * عرض تفاصيل الفاتورة
     */
    renderInvoiceDetails: function(invoice, customer) {
        return `
            <div class="invoice-details">
                <!-- رأس الفاتورة -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h4>فاتورة رقم: ${invoice.number}</h4>
                        <p class="text-muted mb-1">تاريخ الإصدار: ${new Date(invoice.date).toLocaleDateString('ar-SA')}</p>
                        ${invoice.dueDate ? `<p class="text-muted mb-1">تاريخ الاستحقاق: ${new Date(invoice.dueDate).toLocaleDateString('ar-SA')}</p>` : ''}
                        ${invoice.reference ? `<p class="text-muted">المرجع: ${invoice.reference}</p>` : ''}
                    </div>
                    <div class="col-md-6 text-end">
                        <span class="badge bg-${this.getInvoiceStatusColor(invoice.status)} fs-6 mb-2">
                            ${this.getInvoiceStatusLabel(invoice.status)}
                        </span>
                        <h3 class="text-primary">${this.formatAmount(invoice.total)}</h3>
                    </div>
                </div>

                <!-- معلومات العميل -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-user me-2"></i>معلومات العميل</h6>
                            </div>
                            <div class="card-body">
                                <h6>${customer?.name || 'عميل غير محدد'}</h6>
                                ${customer?.email ? `<p class="mb-1"><i class="fas fa-envelope me-2"></i>${customer.email}</p>` : ''}
                                ${customer?.phone ? `<p class="mb-1"><i class="fas fa-phone me-2"></i>${customer.phone}</p>` : ''}
                                ${customer?.address ? `<p class="mb-0"><i class="fas fa-map-marker-alt me-2"></i>${customer.address}</p>` : ''}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-building me-2"></i>معلومات الشركة</h6>
                            </div>
                            <div class="card-body">
                                <h6>قمة الوعد للسفريات</h6>
                                <p class="mb-1">المملكة العربية السعودية</p>
                                <p class="mb-1">الرقم الضريبي: *********</p>
                                <p class="mb-0"><EMAIL></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- عناصر الفاتورة -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6><i class="fas fa-list me-2"></i>عناصر الفاتورة</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th>المنتج/الخدمة</th>
                                        <th>الكمية</th>
                                        <th>السعر</th>
                                        <th>الخصم</th>
                                        <th>المجموع</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${invoice.items?.map(item => {
                                        const product = this.data.products[item.productId];
                                        return `
                                            <tr>
                                                <td>${product?.name || 'منتج غير محدد'}</td>
                                                <td>${item.quantity}</td>
                                                <td>${this.formatAmount(item.price)}</td>
                                                <td>${this.formatAmount(item.discount)}</td>
                                                <td>${this.formatAmount(item.total)}</td>
                                            </tr>
                                        `;
                                    }).join('') || '<tr><td colspan="5" class="text-center">لا توجد عناصر</td></tr>'}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- ملخص الفاتورة -->
                <div class="row">
                    <div class="col-md-8">
                        ${invoice.notes ? `
                            <div class="card">
                                <div class="card-header">
                                    <h6><i class="fas fa-sticky-note me-2"></i>ملاحظات</h6>
                                </div>
                                <div class="card-body">
                                    <p>${invoice.notes}</p>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>المجموع الفرعي:</span>
                                    <span>${this.formatAmount(invoice.subtotal)}</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>الخصم:</span>
                                    <span>${this.formatAmount(invoice.totalDiscount)}</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>الضريبة:</span>
                                    <span>${this.formatAmount(invoice.taxAmount)}</span>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between fw-bold fs-5">
                                    <span>الإجمالي:</span>
                                    <span class="text-primary">${this.formatAmount(invoice.total)}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * تعديل الفاتورة
     */
    editInvoice: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            this.showMessage('الفاتورة غير موجودة', 'error');
            return;
        }

        // إغلاق أي نافذة مفتوحة
        const existingModal = document.getElementById('viewInvoiceModal');
        if (existingModal) {
            bootstrap.Modal.getInstance(existingModal)?.hide();
        }

        // عرض نافذة التعديل
        this.showEditInvoiceModal(invoice);
    },

    /**
     * حذف الفاتورة
     */
    deleteInvoice: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            this.showMessage('الفاتورة غير موجودة', 'error');
            return;
        }

        if (confirm(`هل أنت متأكد من حذف الفاتورة ${invoice.number}؟`)) {
            delete this.data.invoices[invoiceId];
            this.saveSalesData();
            this.showMessage('تم حذف الفاتورة بنجاح', 'success');
            this.refreshData();
        }
    },

    /**
     * طباعة الفاتورة
     */
    printInvoice: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            this.showMessage('الفاتورة غير موجودة', 'error');
            return;
        }

        const customer = this.data.customers[invoice.customerId];
        const printWindow = window.open('', '_blank');

        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>فاتورة ${invoice.number}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .header { text-align: center; margin-bottom: 30px; }
                    .invoice-info { margin-bottom: 20px; }
                    .customer-info { margin-bottom: 20px; }
                    table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                    th { background-color: #f5f5f5; }
                    .total-section { text-align: left; margin-top: 20px; }
                    .total-row { font-weight: bold; font-size: 1.2em; }
                    @media print { .no-print { display: none; } }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>قمة الوعد للسفريات</h1>
                    <p>المملكة العربية السعودية</p>
                    <p>الرقم الضريبي: *********</p>
                </div>

                <div class="invoice-info">
                    <h2>فاتورة رقم: ${invoice.number}</h2>
                    <p>تاريخ الإصدار: ${new Date(invoice.date).toLocaleDateString('ar-SA')}</p>
                    ${invoice.dueDate ? `<p>تاريخ الاستحقاق: ${new Date(invoice.dueDate).toLocaleDateString('ar-SA')}</p>` : ''}
                    ${invoice.reference ? `<p>المرجع: ${invoice.reference}</p>` : ''}
                </div>

                <div class="customer-info">
                    <h3>معلومات العميل:</h3>
                    <p><strong>${customer?.name || 'عميل غير محدد'}</strong></p>
                    ${customer?.email ? `<p>البريد الإلكتروني: ${customer.email}</p>` : ''}
                    ${customer?.phone ? `<p>الهاتف: ${customer.phone}</p>` : ''}
                    ${customer?.address ? `<p>العنوان: ${customer.address}</p>` : ''}
                </div>

                <table>
                    <thead>
                        <tr>
                            <th>المنتج/الخدمة</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>الخصم</th>
                            <th>المجموع</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${invoice.items?.map(item => {
                            const product = this.data.products[item.productId];
                            return `
                                <tr>
                                    <td>${product?.name || 'منتج غير محدد'}</td>
                                    <td>${item.quantity}</td>
                                    <td>${this.formatAmount(item.price)}</td>
                                    <td>${this.formatAmount(item.discount)}</td>
                                    <td>${this.formatAmount(item.total)}</td>
                                </tr>
                            `;
                        }).join('') || '<tr><td colspan="5">لا توجد عناصر</td></tr>'}
                    </tbody>
                </table>

                <div class="total-section">
                    <p>المجموع الفرعي: ${this.formatAmount(invoice.subtotal)}</p>
                    <p>الخصم: ${this.formatAmount(invoice.totalDiscount)}</p>
                    <p>الضريبة: ${this.formatAmount(invoice.taxAmount)}</p>
                    <p class="total-row">الإجمالي: ${this.formatAmount(invoice.total)}</p>
                </div>

                ${invoice.notes ? `<div><h3>ملاحظات:</h3><p>${invoice.notes}</p></div>` : ''}

                <button class="no-print" onclick="window.print()">طباعة</button>
            </body>
            </html>
        `);

        printWindow.document.close();
        printWindow.focus();
    },

    /**
     * تصدير الفواتير
     */
    exportInvoices: function() {
        const invoices = Object.values(this.data.invoices || {});
        if (invoices.length === 0) {
            this.showMessage('لا توجد فواتير للتصدير', 'error');
            return;
        }

        // تحضير البيانات للتصدير
        const csvData = [
            ['رقم الفاتورة', 'العميل', 'التاريخ', 'الإجمالي', 'الحالة', 'المرجع']
        ];

        invoices.forEach(invoice => {
            const customer = this.data.customers[invoice.customerId];
            csvData.push([
                invoice.number,
                customer?.name || 'عميل غير محدد',
                invoice.date,
                invoice.total,
                this.getInvoiceStatusLabel(invoice.status),
                invoice.reference || ''
            ]);
        });

        // تحويل إلى CSV
        const csvContent = csvData.map(row => row.join(',')).join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

        // تحميل الملف
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `invoices_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();

        this.showMessage('تم تصدير الفواتير بنجاح', 'success');
    },

    /**
     * عرض إدارة العملاء
     */
    renderCustomers: function() {
        const customers = Object.values(this.data.customers || {})
            .sort((a, b) => a.name.localeCompare(b.name));

        return `
            <div class="customers-management">
                <!-- شريط الأدوات -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-0">
                                    <i class="fas fa-users me-2"></i>إدارة العملاء
                                </h5>
                            </div>
                            <div class="col-md-6 text-end">
                                <button class="btn btn-primary" onclick="window.SalesComponent.showNewCustomerModal()">
                                    <i class="fas fa-plus me-1"></i>عميل جديد
                                </button>
                                <button class="btn btn-outline-secondary" onclick="window.SalesComponent.exportCustomers()">
                                    <i class="fas fa-download me-1"></i>تصدير
                                </button>
                            </div>
                        </div>

                        <!-- فلاتر البحث -->
                        <div class="row mt-3">
                            <div class="col-md-4">
                                <input type="text" class="form-control" placeholder="البحث بالاسم أو البريد الإلكتروني..."
                                       id="customerSearch" onkeyup="window.SalesComponent.filterCustomers()">
                            </div>
                            <div class="col-md-3">
                                <select class="form-control" id="customerTypeFilter" onchange="window.SalesComponent.filterCustomers()">
                                    <option value="">جميع الأنواع</option>
                                    <option value="individual">فرد</option>
                                    <option value="corporate">شركة</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-control" id="customerStatusFilter" onchange="window.SalesComponent.filterCustomers()">
                                    <option value="">جميع الحالات</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-outline-primary w-100" onclick="window.SalesComponent.clearCustomerFilters()">
                                    <i class="fas fa-times me-1"></i>مسح
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قائمة العملاء -->
                <div class="row" id="customersContainer">
                    ${customers.length === 0 ? this.renderEmptyCustomers() : this.renderCustomersGrid(customers)}
                </div>
            </div>
        `;
    },

    /**
     * عرض رسالة عدم وجود عملاء
     */
    renderEmptyCustomers: function() {
        return `
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-users fa-5x text-muted mb-3"></i>
                    <h4 class="text-muted">لا يوجد عملاء حتى الآن</h4>
                    <p class="text-muted">ابدأ بإضافة أول عميل لك</p>
                    <button class="btn btn-primary btn-lg" onclick="window.SalesComponent.showNewCustomerModal()">
                        <i class="fas fa-plus me-2"></i>إضافة عميل جديد
                    </button>
                </div>
            </div>
        `;
    },

    /**
     * عرض شبكة العملاء
     */
    renderCustomersGrid: function(customers) {
        return customers.map(customer => `
            <div class="col-md-6 col-lg-4 mb-4 customer-card" data-customer-id="${customer.id}">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div class="customer-avatar">
                                <i class="fas fa-${customer.type === 'corporate' ? 'building' : 'user'} fa-2x text-primary"></i>
                            </div>
                            <div class="customer-status">
                                <span class="badge bg-${customer.isActive ? 'success' : 'secondary'}">
                                    ${customer.isActive ? 'نشط' : 'غير نشط'}
                                </span>
                            </div>
                        </div>

                        <h6 class="card-title">${customer.name}</h6>
                        <p class="text-muted small mb-2">
                            <i class="fas fa-${customer.type === 'corporate' ? 'building' : 'user'} me-1"></i>
                            ${customer.type === 'corporate' ? 'شركة' : 'فرد'}
                        </p>

                        ${customer.email ? `
                            <p class="text-muted small mb-2">
                                <i class="fas fa-envelope me-1"></i>${customer.email}
                            </p>
                        ` : ''}

                        ${customer.phone ? `
                            <p class="text-muted small mb-2">
                                <i class="fas fa-phone me-1"></i>${customer.phone}
                            </p>
                        ` : ''}

                        <div class="customer-stats mt-3">
                            <div class="row text-center">
                                <div class="col-6">
                                    <small class="text-muted">الرصيد</small>
                                    <div class="fw-bold">${this.formatAmount(customer.balance || 0)}</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">الحد الائتماني</small>
                                    <div class="fw-bold">${this.formatAmount(customer.creditLimit || 0)}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer bg-transparent">
                        <div class="btn-group w-100">
                            <button class="btn btn-outline-primary btn-sm" onclick="window.SalesComponent.viewCustomer('${customer.id}')" title="عرض">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="window.SalesComponent.editCustomer('${customer.id}')" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-info btn-sm" onclick="window.SalesComponent.customerInvoices('${customer.id}')" title="الفواتير">
                                <i class="fas fa-file-invoice"></i>
                            </button>
                            <button class="btn btn-outline-danger btn-sm" onclick="window.SalesComponent.deleteCustomer('${customer.id}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    },

    /**
     * عرض نافذة عميل جديد
     */
    showNewCustomerModal: function() {
        const modalHTML = `
            <div class="modal fade" id="newCustomerModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-user-plus me-2"></i>إضافة عميل جديد
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${this.renderCustomerForm()}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveNewCustomer()">
                                <i class="fas fa-save me-1"></i>حفظ العميل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('newCustomerModal'));
        modal.show();

        document.getElementById('newCustomerModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * عرض نموذج العميل
     */
    renderCustomerForm: function(customer = null) {
        const isEdit = customer !== null;

        return `
            <form id="customerForm">
                <div class="row">
                    <!-- المعلومات الأساسية -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-info-circle me-2"></i>المعلومات الأساسية</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">نوع العميل *</label>
                                    <select class="form-control" name="type" required onchange="window.SalesComponent.toggleCustomerFields()">
                                        <option value="individual" ${customer?.type === 'individual' ? 'selected' : ''}>فرد</option>
                                        <option value="corporate" ${customer?.type === 'corporate' ? 'selected' : ''}>شركة</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الاسم *</label>
                                    <input type="text" class="form-control" name="name" value="${customer?.name || ''}" required>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" name="email" value="${customer?.email || ''}">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف *</label>
                                    <input type="tel" class="form-control" name="phone" value="${customer?.phone || ''}" required>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea class="form-control" name="address" rows="3">${customer?.address || ''}</textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- المعلومات المالية -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-money-bill me-2"></i>المعلومات المالية</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3" id="taxNumberField" style="display: ${customer?.type === 'corporate' ? 'block' : 'none'}">
                                    <label class="form-label">الرقم الضريبي</label>
                                    <input type="text" class="form-control" name="taxNumber" value="${customer?.taxNumber || ''}">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الحد الائتماني</label>
                                    <input type="number" class="form-control" name="creditLimit" value="${customer?.creditLimit || 0}" min="0" step="0.01">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الرصيد الحالي</label>
                                    <input type="number" class="form-control" name="balance" value="${customer?.balance || 0}" step="0.01" ${isEdit ? '' : 'readonly'}>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="isActive" ${customer?.isActive !== false ? 'checked' : ''}>
                                        <label class="form-check-label">عميل نشط</label>
                                    </div>
                                </div>

                                ${isEdit ? `<input type="hidden" name="customerId" value="${customer.id}">` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        `;
    },

    /**
     * تبديل حقول العميل حسب النوع
     */
    toggleCustomerFields: function() {
        const typeSelect = document.querySelector('select[name="type"]');
        const taxNumberField = document.getElementById('taxNumberField');

        if (typeSelect && taxNumberField) {
            taxNumberField.style.display = typeSelect.value === 'corporate' ? 'block' : 'none';
        }
    },

    /**
     * حفظ عميل جديد
     */
    saveNewCustomer: function() {
        const form = document.getElementById('customerForm');
        const formData = new FormData(form);

        try {
            const customerData = {
                id: this.generateId(),
                name: formData.get('name'),
                email: formData.get('email'),
                phone: formData.get('phone'),
                address: formData.get('address'),
                type: formData.get('type'),
                taxNumber: formData.get('taxNumber'),
                creditLimit: parseFloat(formData.get('creditLimit')) || 0,
                balance: parseFloat(formData.get('balance')) || 0,
                isActive: formData.has('isActive'),
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // التحقق من صحة البيانات
            if (!customerData.name || !customerData.phone) {
                throw new Error('يرجى ملء جميع الحقول المطلوبة');
            }

            // التحقق من عدم تكرار البريد الإلكتروني
            if (customerData.email) {
                const existingCustomer = Object.values(this.data.customers || {})
                    .find(c => c.email === customerData.email);
                if (existingCustomer) {
                    throw new Error('البريد الإلكتروني مستخدم بالفعل');
                }
            }

            // حفظ العميل
            this.data.customers[customerData.id] = customerData;
            this.saveSalesData();

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('newCustomerModal'));
            modal.hide();

            // عرض رسالة نجاح
            this.showMessage('تم إضافة العميل بنجاح', 'success');

            // تحديث العرض
            this.refreshData();

        } catch (error) {
            this.showMessage('خطأ في حفظ العميل: ' + error.message, 'error');
        }
    },

    /**
     * عرض تفاصيل العميل
     */
    viewCustomer: function(customerId) {
        const customer = this.data.customers[customerId];
        if (!customer) {
            this.showMessage('العميل غير موجود', 'error');
            return;
        }

        // حساب إحصائيات العميل
        const customerInvoices = Object.values(this.data.invoices || {})
            .filter(invoice => invoice.customerId === customerId);

        const totalSales = customerInvoices.reduce((sum, invoice) => sum + (invoice.total || 0), 0);
        const paidInvoices = customerInvoices.filter(invoice => invoice.status === 'paid').length;

        const modalHTML = `
            <div class="modal fade" id="viewCustomerModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-user me-2"></i>تفاصيل العميل: ${customer.name}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <!-- معلومات العميل -->
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-info-circle me-2"></i>المعلومات الأساسية</h6>
                                        </div>
                                        <div class="card-body">
                                            <table class="table table-borderless">
                                                <tr>
                                                    <td><strong>الاسم:</strong></td>
                                                    <td>${customer.name}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>النوع:</strong></td>
                                                    <td>${customer.type === 'corporate' ? 'شركة' : 'فرد'}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>البريد الإلكتروني:</strong></td>
                                                    <td>${customer.email || '-'}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>الهاتف:</strong></td>
                                                    <td>${customer.phone || '-'}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>العنوان:</strong></td>
                                                    <td>${customer.address || '-'}</td>
                                                </tr>
                                                ${customer.taxNumber ? `
                                                <tr>
                                                    <td><strong>الرقم الضريبي:</strong></td>
                                                    <td>${customer.taxNumber}</td>
                                                </tr>
                                                ` : ''}
                                                <tr>
                                                    <td><strong>الحالة:</strong></td>
                                                    <td>
                                                        <span class="badge bg-${customer.isActive ? 'success' : 'secondary'}">
                                                            ${customer.isActive ? 'نشط' : 'غير نشط'}
                                                        </span>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <!-- الإحصائيات المالية -->
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-chart-bar me-2"></i>الإحصائيات المالية</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row text-center">
                                                <div class="col-6 mb-3">
                                                    <div class="border rounded p-3">
                                                        <h4 class="text-primary">${this.formatAmount(customer.balance || 0)}</h4>
                                                        <small class="text-muted">الرصيد الحالي</small>
                                                    </div>
                                                </div>
                                                <div class="col-6 mb-3">
                                                    <div class="border rounded p-3">
                                                        <h4 class="text-info">${this.formatAmount(customer.creditLimit || 0)}</h4>
                                                        <small class="text-muted">الحد الائتماني</small>
                                                    </div>
                                                </div>
                                                <div class="col-6 mb-3">
                                                    <div class="border rounded p-3">
                                                        <h4 class="text-success">${this.formatAmount(totalSales)}</h4>
                                                        <small class="text-muted">إجمالي المبيعات</small>
                                                    </div>
                                                </div>
                                                <div class="col-6 mb-3">
                                                    <div class="border rounded p-3">
                                                        <h4 class="text-warning">${customerInvoices.length}</h4>
                                                        <small class="text-muted">عدد الفواتير</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- آخر الفواتير -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h6><i class="fas fa-file-invoice me-2"></i>آخر الفواتير</h6>
                                </div>
                                <div class="card-body">
                                    ${customerInvoices.length === 0 ?
                                        '<p class="text-muted text-center">لا توجد فواتير لهذا العميل</p>' :
                                        this.renderCustomerInvoicesTable(customerInvoices.slice(0, 5))
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-outline-primary" onclick="window.SalesComponent.editCustomer('${customer.id}'); bootstrap.Modal.getInstance(document.getElementById('viewCustomerModal')).hide();">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.createInvoiceForCustomer('${customer.id}'); bootstrap.Modal.getInstance(document.getElementById('viewCustomerModal')).hide();">
                                <i class="fas fa-plus me-1"></i>فاتورة جديدة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('viewCustomerModal'));
        modal.show();

        document.getElementById('viewCustomerModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * عرض جدول فواتير العميل
     */
    renderCustomerInvoicesTable: function(invoices) {
        return `
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>التاريخ</th>
                            <th>المبلغ</th>
                            <th>الحالة</th>
                            <th>إجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${invoices.map(invoice => `
                            <tr>
                                <td>${invoice.number}</td>
                                <td>${new Date(invoice.date).toLocaleDateString('ar-SA')}</td>
                                <td>${this.formatAmount(invoice.total)}</td>
                                <td>
                                    <span class="badge bg-${this.getInvoiceStatusColor(invoice.status)}">
                                        ${this.getInvoiceStatusLabel(invoice.status)}
                                    </span>
                                </td>
                                <td>
                                    <button class="btn btn-outline-primary btn-sm" onclick="window.SalesComponent.viewInvoice('${invoice.id}')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    },

    /**
     * فلترة العملاء
     */
    filterCustomers: function() {
        const searchTerm = document.getElementById('customerSearch')?.value.toLowerCase() || '';
        const typeFilter = document.getElementById('customerTypeFilter')?.value || '';
        const statusFilter = document.getElementById('customerStatusFilter')?.value || '';

        let filteredCustomers = Object.values(this.data.customers || {});

        // فلترة بالبحث
        if (searchTerm) {
            filteredCustomers = filteredCustomers.filter(customer =>
                customer.name.toLowerCase().includes(searchTerm) ||
                customer.email?.toLowerCase().includes(searchTerm)
            );
        }

        // فلترة بالنوع
        if (typeFilter) {
            filteredCustomers = filteredCustomers.filter(customer => customer.type === typeFilter);
        }

        // فلترة بالحالة
        if (statusFilter) {
            const isActive = statusFilter === 'active';
            filteredCustomers = filteredCustomers.filter(customer => customer.isActive === isActive);
        }

        // ترتيب النتائج
        filteredCustomers.sort((a, b) => a.name.localeCompare(b.name));

        // تحديث العرض
        const container = document.getElementById('customersContainer');
        if (container) {
            container.innerHTML = filteredCustomers.length === 0 ?
                '<div class="col-12"><div class="text-center py-4"><p class="text-muted">لا يوجد عملاء يطابقون معايير البحث</p></div></div>' :
                this.renderCustomersGrid(filteredCustomers);
        }
    },

    /**
     * مسح فلاتر العملاء
     */
    clearCustomerFilters: function() {
        document.getElementById('customerSearch').value = '';
        document.getElementById('customerTypeFilter').value = '';
        document.getElementById('customerStatusFilter').value = '';
        this.filterCustomers();
    },

    /**
     * عرض إدارة المنتجات والخدمات
     */
    renderProducts: function() {
        const products = Object.values(this.data.products || {})
            .sort((a, b) => a.name.localeCompare(b.name));

        return `
            <div class="products-management">
                <!-- شريط الأدوات -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-0">
                                    <i class="fas fa-box me-2"></i>إدارة المنتجات والخدمات
                                </h5>
                            </div>
                            <div class="col-md-6 text-end">
                                <button class="btn btn-primary" onclick="window.SalesComponent.showNewProductModal()">
                                    <i class="fas fa-plus me-1"></i>منتج/خدمة جديدة
                                </button>
                                <button class="btn btn-outline-secondary" onclick="window.SalesComponent.exportProducts()">
                                    <i class="fas fa-download me-1"></i>تصدير
                                </button>
                            </div>
                        </div>

                        <!-- فلاتر البحث -->
                        <div class="row mt-3">
                            <div class="col-md-4">
                                <input type="text" class="form-control" placeholder="البحث بالاسم..."
                                       id="productSearch" onkeyup="window.SalesComponent.filterProducts()">
                            </div>
                            <div class="col-md-3">
                                <select class="form-control" id="categoryFilter" onchange="window.SalesComponent.filterProducts()">
                                    <option value="">جميع التصنيفات</option>
                                    <option value="flights">تذاكر الطيران</option>
                                    <option value="hotels">الفنادق</option>
                                    <option value="visas">التأشيرات</option>
                                    <option value="transport">النقل</option>
                                    <option value="tours">الجولات السياحية</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-control" id="typeFilter" onchange="window.SalesComponent.filterProducts()">
                                    <option value="">جميع الأنواع</option>
                                    <option value="service">خدمة</option>
                                    <option value="product">منتج</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-outline-primary w-100" onclick="window.SalesComponent.clearProductFilters()">
                                    <i class="fas fa-times me-1"></i>مسح
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قائمة المنتجات -->
                <div class="row" id="productsContainer">
                    ${products.length === 0 ? this.renderEmptyProducts() : this.renderProductsGrid(products)}
                </div>
            </div>
        `;
    },

    /**
     * عرض رسالة عدم وجود منتجات
     */
    renderEmptyProducts: function() {
        return `
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-box fa-5x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد منتجات أو خدمات حتى الآن</h4>
                    <p class="text-muted">ابدأ بإضافة أول منتج أو خدمة</p>
                    <button class="btn btn-primary btn-lg" onclick="window.SalesComponent.showNewProductModal()">
                        <i class="fas fa-plus me-2"></i>إضافة منتج/خدمة جديدة
                    </button>
                </div>
            </div>
        `;
    },

    /**
     * عرض شبكة المنتجات
     */
    renderProductsGrid: function(products) {
        return products.map(product => `
            <div class="col-md-6 col-lg-4 mb-4 product-card" data-product-id="${product.id}">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div class="product-icon">
                                <i class="fas fa-${this.getProductIcon(product.category)} fa-2x text-primary"></i>
                            </div>
                            <div class="product-status">
                                <span class="badge bg-${product.isActive ? 'success' : 'secondary'}">
                                    ${product.isActive ? 'نشط' : 'غير نشط'}
                                </span>
                            </div>
                        </div>

                        <h6 class="card-title">${product.name}</h6>
                        <p class="text-muted small mb-2">
                            <i class="fas fa-tag me-1"></i>
                            ${this.getCategoryLabel(product.category)}
                        </p>

                        <p class="text-muted small mb-2">
                            <i class="fas fa-${product.type === 'service' ? 'cogs' : 'box'} me-1"></i>
                            ${product.type === 'service' ? 'خدمة' : 'منتج'}
                        </p>

                        ${product.description ? `
                            <p class="text-muted small mb-3">${product.description.substring(0, 80)}${product.description.length > 80 ? '...' : ''}</p>
                        ` : ''}

                        <div class="product-pricing mt-3">
                            <div class="row text-center">
                                <div class="col-6">
                                    <small class="text-muted">سعر البيع</small>
                                    <div class="fw-bold text-success">${this.formatAmount(product.price || 0)}</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">التكلفة</small>
                                    <div class="fw-bold text-info">${this.formatAmount(product.cost || 0)}</div>
                                </div>
                            </div>
                            <div class="text-center mt-2">
                                <small class="text-muted">الربح: </small>
                                <span class="fw-bold text-primary">${this.formatAmount((product.price || 0) - (product.cost || 0))}</span>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer bg-transparent">
                        <div class="btn-group w-100">
                            <button class="btn btn-outline-primary btn-sm" onclick="window.SalesComponent.viewProduct('${product.id}')" title="عرض">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="window.SalesComponent.editProduct('${product.id}')" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-info btn-sm" onclick="window.SalesComponent.duplicateProduct('${product.id}')" title="نسخ">
                                <i class="fas fa-copy"></i>
                            </button>
                            <button class="btn btn-outline-danger btn-sm" onclick="window.SalesComponent.deleteProduct('${product.id}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    },

    /**
     * الحصول على أيقونة المنتج حسب التصنيف
     */
    getProductIcon: function(category) {
        const icons = {
            'flights': 'plane',
            'hotels': 'bed',
            'visas': 'passport',
            'transport': 'bus',
            'tours': 'map-marked-alt',
            'other': 'box'
        };
        return icons[category] || 'box';
    },

    /**
     * الحصول على تسمية التصنيف
     */
    getCategoryLabel: function(category) {
        const labels = {
            'flights': 'تذاكر الطيران',
            'hotels': 'الفنادق',
            'visas': 'التأشيرات',
            'transport': 'النقل',
            'tours': 'الجولات السياحية',
            'other': 'أخرى'
        };
        return labels[category] || 'غير محدد';
    },

    /**
     * عرض نافذة منتج/خدمة جديدة
     */
    showNewProductModal: function() {
        const modalHTML = `
            <div class="modal fade" id="newProductModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-plus me-2"></i>إضافة منتج/خدمة جديدة
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${this.renderProductForm()}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveNewProduct()">
                                <i class="fas fa-save me-1"></i>حفظ المنتج/الخدمة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('newProductModal'));
        modal.show();

        document.getElementById('newProductModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * تحديث البيانات
     */
    refreshData: function() {
        this.loadSalesData();
        const content = document.getElementById('main-content');
        if (content) {
            content.innerHTML = this.renderCurrentView();
        }
    }
};

// تصدير المكون للاستخدام العام
window.SalesComponent = SalesComponent;
