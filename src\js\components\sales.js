/**
 * مكون نظام المبيعات
 * Sales Management Component
 */

const SalesComponent = {
    // بيانات النظام
    data: {
        currentView: 'dashboard',
        customers: {},
        products: {},
        invoices: {},
        salesTransactions: {},
        settings: {
            taxRate: 0.15, // ضريبة القيمة المضافة 15%
            currency: 'SAR',
            invoicePrefix: 'INV-',
            nextInvoiceNumber: 1
        }
    },

    // إعدادات العرض
    views: [
        { key: 'dashboard', label: 'لوحة التحكم', icon: 'fas fa-tachometer-alt' },
        { key: 'invoices', label: 'الفواتير', icon: 'fas fa-file-invoice' },
        { key: 'customers', label: 'العملاء', icon: 'fas fa-users' },
        { key: 'products', label: 'المنتجات والخدمات', icon: 'fas fa-box' },
        { key: 'reports', label: 'التقارير', icon: 'fas fa-chart-bar' },
        { key: 'settings', label: 'الإعدادات', icon: 'fas fa-cog' }
    ],

    /**
     * تهيئة مكون المبيعات
     */
    init: function() {
        this.loadSalesData();
        this.setupEventListeners();
        console.log('✅ تم تهيئة نظام المبيعات');
    },

    /**
     * تحميل بيانات المبيعات
     */
    loadSalesData: function() {
        try {
            // تحميل البيانات من localStorage
            const savedData = localStorage.getItem('salesData');
            if (savedData) {
                const data = JSON.parse(savedData);
                this.data = { ...this.data, ...data };
            } else {
                // إنشاء بيانات تجريبية
                this.createSampleData();
            }
        } catch (error) {
            console.error('خطأ في تحميل بيانات المبيعات:', error);
            this.createSampleData();
        }
    },

    /**
     * حفظ بيانات المبيعات
     */
    saveSalesData: function() {
        try {
            localStorage.setItem('salesData', JSON.stringify(this.data));
        } catch (error) {
            console.error('خطأ في حفظ بيانات المبيعات:', error);
        }
    },

    /**
     * إنشاء بيانات تجريبية
     */
    createSampleData: function() {
        // عملاء تجريبيون
        this.data.customers = {
            'CUST001': {
                id: 'CUST001',
                name: 'شركة الرياض للسفر',
                email: '<EMAIL>',
                phone: '+966501234567',
                address: 'الرياض، المملكة العربية السعودية',
                taxNumber: '*********',
                type: 'corporate',
                creditLimit: 50000,
                balance: 0,
                isActive: true,
                createdAt: new Date().toISOString()
            },
            'CUST002': {
                id: 'CUST002',
                name: 'أحمد محمد العلي',
                email: '<EMAIL>',
                phone: '+966509876543',
                address: 'جدة، المملكة العربية السعودية',
                taxNumber: '',
                type: 'individual',
                creditLimit: 10000,
                balance: 0,
                isActive: true,
                createdAt: new Date().toISOString()
            }
        };

        // منتجات وخدمات تجريبية
        this.data.products = {
            'PROD001': {
                id: 'PROD001',
                name: 'تذكرة طيران داخلي',
                nameEn: 'Domestic Flight Ticket',
                category: 'flights',
                type: 'service',
                price: 500,
                cost: 400,
                taxable: true,
                isActive: true,
                description: 'تذكرة طيران داخلي - درجة اقتصادية',
                createdAt: new Date().toISOString()
            },
            'PROD002': {
                id: 'PROD002',
                name: 'حجز فندقي - ليلة واحدة',
                nameEn: 'Hotel Booking - One Night',
                category: 'hotels',
                type: 'service',
                price: 300,
                cost: 250,
                taxable: true,
                isActive: true,
                description: 'حجز فندقي لليلة واحدة - غرفة مفردة',
                createdAt: new Date().toISOString()
            },
            'PROD003': {
                id: 'PROD003',
                name: 'تأشيرة سياحية',
                nameEn: 'Tourist Visa',
                category: 'visas',
                type: 'service',
                price: 200,
                cost: 150,
                taxable: false,
                isActive: true,
                description: 'خدمة استخراج تأشيرة سياحية',
                createdAt: new Date().toISOString()
            }
        };

        this.saveSalesData();
    },

    /**
     * تبديل العرض
     */
    switchView: function(view) {
        this.data.currentView = view;
        const content = document.getElementById('main-content');
        if (content) {
            content.innerHTML = this.renderCurrentView();
        }
    },

    /**
     * عرض المحتوى الحالي
     */
    renderCurrentView: function() {
        switch (this.data.currentView) {
            case 'dashboard':
                return this.renderDashboard();
            case 'invoices':
                return this.renderInvoices();
            case 'customers':
                return this.renderCustomers();
            case 'products':
                return this.renderProducts();
            case 'reports':
                return this.renderReports();
            case 'settings':
                return this.renderSettings();
            default:
                return this.renderDashboard();
        }
    },

    /**
     * عرض نافذة المبيعات الرئيسية
     */
    render: function() {
        return `
            <div class="sales-management">
                <!-- شريط التنقل -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h4 class="mb-0">
                                        <i class="fas fa-shopping-cart text-primary me-2"></i>
                                        نظام إدارة المبيعات
                                    </h4>
                                    <div class="btn-group">
                                        <button class="btn btn-primary" onclick="window.SalesComponent.showNewInvoiceModal()">
                                            <i class="fas fa-plus me-1"></i>فاتورة جديدة
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="window.SalesComponent.refreshData()">
                                            <i class="fas fa-sync me-1"></i>تحديث
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- تبويبات التنقل -->
                                <ul class="nav nav-pills nav-fill">
                                    ${this.views.map(item => `
                                        <li class="nav-item">
                                            <a class="nav-link ${this.data.currentView === item.key ? 'active' : ''}" 
                                               href="#" onclick="window.SalesComponent.switchView('${item.key}')">
                                                <i class="${item.icon} me-2"></i>${item.label}
                                            </a>
                                        </li>
                                    `).join('')}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- المحتوى الرئيسي -->
                <div class="row">
                    <div class="col-12">
                        ${this.renderCurrentView()}
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض لوحة التحكم
     */
    renderDashboard: function() {
        const stats = this.calculateDashboardStats();
        
        return `
            <div class="sales-dashboard">
                <!-- بطاقات الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">إجمالي المبيعات اليوم</h6>
                                        <h4>${this.formatAmount(stats.todaySales)}</h4>
                                    </div>
                                    <i class="fas fa-chart-line fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">عدد الفواتير</h6>
                                        <h4>${stats.totalInvoices}</h4>
                                    </div>
                                    <i class="fas fa-file-invoice fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">عدد العملاء</h6>
                                        <h4>${stats.totalCustomers}</h4>
                                    </div>
                                    <i class="fas fa-users fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">متوسط قيمة الفاتورة</h6>
                                        <h4>${this.formatAmount(stats.averageInvoice)}</h4>
                                    </div>
                                    <i class="fas fa-calculator fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسوم البيانية والتقارير السريعة -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-area me-2"></i>مبيعات آخر 7 أيام</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="salesChart" height="100"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-list me-2"></i>آخر الفواتير</h5>
                            </div>
                            <div class="card-body">
                                ${this.renderRecentInvoices()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * حساب إحصائيات لوحة التحكم
     */
    calculateDashboardStats: function() {
        const invoices = Object.values(this.data.invoices || {});
        const customers = Object.values(this.data.customers || {});
        
        const today = new Date().toISOString().split('T')[0];
        const todayInvoices = invoices.filter(inv => inv.date === today);
        
        return {
            todaySales: todayInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0),
            totalInvoices: invoices.length,
            totalCustomers: customers.length,
            averageInvoice: invoices.length > 0 ? 
                invoices.reduce((sum, inv) => sum + (inv.total || 0), 0) / invoices.length : 0
        };
    },

    /**
     * تنسيق المبالغ
     */
    formatAmount: function(amount) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: this.data.settings.currency
        }).format(amount || 0);
    },

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners: function() {
        // سيتم إضافة مستمعي الأحداث هنا
    },

    /**
     * عرض آخر الفواتير
     */
    renderRecentInvoices: function() {
        const invoices = Object.values(this.data.invoices || {})
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, 5);

        if (invoices.length === 0) {
            return `
                <div class="text-center text-muted">
                    <i class="fas fa-file-invoice fa-3x mb-3"></i>
                    <p>لا توجد فواتير حتى الآن</p>
                    <button class="btn btn-primary btn-sm" onclick="window.SalesComponent.showNewInvoiceModal()">
                        <i class="fas fa-plus me-1"></i>إنشاء فاتورة جديدة
                    </button>
                </div>
            `;
        }

        return `
            <div class="list-group list-group-flush">
                ${invoices.map(invoice => `
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">${invoice.number}</h6>
                            <small class="text-muted">${this.data.customers[invoice.customerId]?.name || 'عميل غير محدد'}</small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-${this.getInvoiceStatusColor(invoice.status)} mb-1">${this.getInvoiceStatusLabel(invoice.status)}</span>
                            <div class="fw-bold">${this.formatAmount(invoice.total)}</div>
                        </div>
                    </div>
                `).join('')}
            </div>
            <div class="text-center mt-3">
                <button class="btn btn-outline-primary btn-sm" onclick="window.SalesComponent.switchView('invoices')">
                    عرض جميع الفواتير
                </button>
            </div>
        `;
    },

    /**
     * الحصول على لون حالة الفاتورة
     */
    getInvoiceStatusColor: function(status) {
        const colors = {
            'draft': 'secondary',
            'sent': 'primary',
            'paid': 'success',
            'overdue': 'danger',
            'cancelled': 'dark'
        };
        return colors[status] || 'secondary';
    },

    /**
     * الحصول على تسمية حالة الفاتورة
     */
    getInvoiceStatusLabel: function(status) {
        const labels = {
            'draft': 'مسودة',
            'sent': 'مرسلة',
            'paid': 'مدفوعة',
            'overdue': 'متأخرة',
            'cancelled': 'ملغية'
        };
        return labels[status] || 'غير محدد';
    },

    /**
     * عرض نافذة فاتورة جديدة
     */
    showNewInvoiceModal: function() {
        const modalHTML = `
            <div class="modal fade" id="newInvoiceModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-file-invoice me-2"></i>فاتورة جديدة
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${this.renderInvoiceForm()}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-outline-primary" onclick="window.SalesComponent.saveInvoiceAsDraft()">
                                <i class="fas fa-save me-1"></i>حفظ كمسودة
                            </button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveAndSendInvoice()">
                                <i class="fas fa-paper-plane me-1"></i>حفظ وإرسال
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('newInvoiceModal'));
        modal.show();

        document.getElementById('newInvoiceModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });

        // تهيئة النموذج
        this.initializeInvoiceForm();
    },

    /**
     * عرض نموذج الفاتورة
     */
    renderInvoiceForm: function() {
        const customers = Object.values(this.data.customers || {});
        const products = Object.values(this.data.products || {});

        return `
            <form id="invoiceForm">
                <div class="row">
                    <!-- معلومات الفاتورة -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-info-circle me-2"></i>معلومات الفاتورة</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">رقم الفاتورة</label>
                                    <input type="text" class="form-control" name="invoiceNumber"
                                           value="${this.data.settings.invoicePrefix}${this.data.settings.nextInvoiceNumber.toString().padStart(4, '0')}" readonly>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">تاريخ الفاتورة *</label>
                                    <input type="date" class="form-control" name="invoiceDate"
                                           value="${new Date().toISOString().split('T')[0]}" required>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">تاريخ الاستحقاق</label>
                                    <input type="date" class="form-control" name="dueDate">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">مرجع الطلب</label>
                                    <input type="text" class="form-control" name="reference" placeholder="رقم الطلب أو المرجع">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات العميل -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-user me-2"></i>معلومات العميل</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">العميل *</label>
                                    <select class="form-control" name="customerId" required onchange="window.SalesComponent.updateCustomerInfo()">
                                        <option value="">اختر العميل</option>
                                        ${customers.map(customer => `
                                            <option value="${customer.id}">${customer.name}</option>
                                        `).join('')}
                                    </select>
                                </div>

                                <div id="customerInfo" class="d-none">
                                    <div class="mb-2">
                                        <small class="text-muted">البريد الإلكتروني:</small>
                                        <div id="customerEmail"></div>
                                    </div>
                                    <div class="mb-2">
                                        <small class="text-muted">الهاتف:</small>
                                        <div id="customerPhone"></div>
                                    </div>
                                    <div class="mb-2">
                                        <small class="text-muted">العنوان:</small>
                                        <div id="customerAddress"></div>
                                    </div>
                                </div>

                                <div class="mt-3">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="window.SalesComponent.showNewCustomerModal()">
                                        <i class="fas fa-plus me-1"></i>عميل جديد
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- عناصر الفاتورة -->
                <div class="card mt-3">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6><i class="fas fa-list me-2"></i>عناصر الفاتورة</h6>
                            <button type="button" class="btn btn-primary btn-sm" onclick="window.SalesComponent.addInvoiceItem()">
                                <i class="fas fa-plus me-1"></i>إضافة عنصر
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th width="30%">المنتج/الخدمة</th>
                                        <th width="15%">الكمية</th>
                                        <th width="15%">السعر</th>
                                        <th width="15%">الخصم</th>
                                        <th width="15%">المجموع</th>
                                        <th width="10%">إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="invoiceItems">
                                    <!-- سيتم إضافة العناصر هنا -->
                                </tbody>
                            </table>
                        </div>

                        <!-- ملخص الفاتورة -->
                        <div class="row mt-3">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="3" placeholder="ملاحظات إضافية للفاتورة"></textarea>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>المجموع الفرعي:</span>
                                            <span id="subtotal">0.00 ر.س</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>الخصم:</span>
                                            <span id="totalDiscount">0.00 ر.س</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>الضريبة (${(this.data.settings.taxRate * 100)}%):</span>
                                            <span id="taxAmount">0.00 ر.س</span>
                                        </div>
                                        <hr>
                                        <div class="d-flex justify-content-between fw-bold">
                                            <span>الإجمالي:</span>
                                            <span id="grandTotal">0.00 ر.س</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        `;
    },

    /**
     * تهيئة نموذج الفاتورة
     */
    initializeInvoiceForm: function() {
        // إضافة عنصر افتراضي
        this.addInvoiceItem();
    },

    /**
     * إضافة عنصر للفاتورة
     */
    addInvoiceItem: function() {
        const tbody = document.getElementById('invoiceItems');
        const products = Object.values(this.data.products || {});
        const itemIndex = tbody.children.length;

        const itemHTML = `
            <tr data-item-index="${itemIndex}">
                <td>
                    <select class="form-control" name="items[${itemIndex}][productId]" onchange="window.SalesComponent.updateItemPrice(${itemIndex})" required>
                        <option value="">اختر المنتج/الخدمة</option>
                        ${products.map(product => `
                            <option value="${product.id}" data-price="${product.price}">${product.name}</option>
                        `).join('')}
                    </select>
                </td>
                <td>
                    <input type="number" class="form-control" name="items[${itemIndex}][quantity]"
                           value="1" min="1" step="0.01" onchange="window.SalesComponent.calculateItemTotal(${itemIndex})" required>
                </td>
                <td>
                    <input type="number" class="form-control" name="items[${itemIndex}][price]"
                           step="0.01" min="0" onchange="window.SalesComponent.calculateItemTotal(${itemIndex})" required>
                </td>
                <td>
                    <input type="number" class="form-control" name="items[${itemIndex}][discount]"
                           value="0" step="0.01" min="0" onchange="window.SalesComponent.calculateItemTotal(${itemIndex})">
                </td>
                <td>
                    <input type="number" class="form-control" name="items[${itemIndex}][total]" readonly>
                </td>
                <td>
                    <button type="button" class="btn btn-danger btn-sm" onclick="window.SalesComponent.removeInvoiceItem(${itemIndex})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;

        tbody.insertAdjacentHTML('beforeend', itemHTML);
    },

    /**
     * تحديث سعر العنصر عند اختيار المنتج
     */
    updateItemPrice: function(itemIndex) {
        const select = document.querySelector(`select[name="items[${itemIndex}][productId]"]`);
        const priceInput = document.querySelector(`input[name="items[${itemIndex}][price]"]`);

        if (select.selectedOptions.length > 0) {
            const price = select.selectedOptions[0].getAttribute('data-price');
            priceInput.value = price || 0;
            this.calculateItemTotal(itemIndex);
        }
    },

    /**
     * حساب إجمالي العنصر
     */
    calculateItemTotal: function(itemIndex) {
        const quantity = parseFloat(document.querySelector(`input[name="items[${itemIndex}][quantity]"]`).value) || 0;
        const price = parseFloat(document.querySelector(`input[name="items[${itemIndex}][price]"]`).value) || 0;
        const discount = parseFloat(document.querySelector(`input[name="items[${itemIndex}][discount]"]`).value) || 0;

        const total = (quantity * price) - discount;
        document.querySelector(`input[name="items[${itemIndex}][total]"]`).value = total.toFixed(2);

        this.calculateInvoiceTotal();
    },

    /**
     * حساب إجمالي الفاتورة
     */
    calculateInvoiceTotal: function() {
        const itemTotals = document.querySelectorAll('input[name*="[total]"]');
        let subtotal = 0;
        let totalDiscount = 0;

        itemTotals.forEach(input => {
            subtotal += parseFloat(input.value) || 0;
        });

        // حساب إجمالي الخصومات
        const discountInputs = document.querySelectorAll('input[name*="[discount]"]');
        discountInputs.forEach(input => {
            totalDiscount += parseFloat(input.value) || 0;
        });

        const taxAmount = subtotal * this.data.settings.taxRate;
        const grandTotal = subtotal + taxAmount;

        // تحديث العرض
        document.getElementById('subtotal').textContent = this.formatAmount(subtotal);
        document.getElementById('totalDiscount').textContent = this.formatAmount(totalDiscount);
        document.getElementById('taxAmount').textContent = this.formatAmount(taxAmount);
        document.getElementById('grandTotal').textContent = this.formatAmount(grandTotal);
    },

    /**
     * حذف عنصر من الفاتورة
     */
    removeInvoiceItem: function(itemIndex) {
        const row = document.querySelector(`tr[data-item-index="${itemIndex}"]`);
        if (row) {
            row.remove();
            this.calculateInvoiceTotal();
        }
    },

    /**
     * تحديث معلومات العميل
     */
    updateCustomerInfo: function() {
        const select = document.querySelector('select[name="customerId"]');
        const customerInfo = document.getElementById('customerInfo');

        if (select.value) {
            const customer = this.data.customers[select.value];
            if (customer) {
                document.getElementById('customerEmail').textContent = customer.email || '-';
                document.getElementById('customerPhone').textContent = customer.phone || '-';
                document.getElementById('customerAddress').textContent = customer.address || '-';
                customerInfo.classList.remove('d-none');
            }
        } else {
            customerInfo.classList.add('d-none');
        }
    },

    /**
     * حفظ الفاتورة كمسودة
     */
    saveInvoiceAsDraft: function() {
        this.saveInvoice('draft');
    },

    /**
     * حفظ وإرسال الفاتورة
     */
    saveAndSendInvoice: function() {
        this.saveInvoice('sent');
    },

    /**
     * حفظ الفاتورة
     */
    saveInvoice: function(status = 'draft') {
        const form = document.getElementById('invoiceForm');
        const formData = new FormData(form);

        try {
            // جمع بيانات الفاتورة
            const invoiceData = {
                id: this.generateId(),
                number: formData.get('invoiceNumber'),
                date: formData.get('invoiceDate'),
                dueDate: formData.get('dueDate'),
                customerId: formData.get('customerId'),
                reference: formData.get('reference'),
                notes: formData.get('notes'),
                status: status,
                items: [],
                subtotal: 0,
                totalDiscount: 0,
                taxAmount: 0,
                total: 0,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // التحقق من صحة البيانات
            if (!invoiceData.customerId) {
                throw new Error('يرجى اختيار العميل');
            }

            // جمع عناصر الفاتورة
            const itemRows = document.querySelectorAll('#invoiceItems tr');
            itemRows.forEach((row, index) => {
                const productId = row.querySelector(`select[name="items[${index}][productId]"]`)?.value;
                const quantity = parseFloat(row.querySelector(`input[name="items[${index}][quantity]"]`)?.value) || 0;
                const price = parseFloat(row.querySelector(`input[name="items[${index}][price]"]`)?.value) || 0;
                const discount = parseFloat(row.querySelector(`input[name="items[${index}][discount]"]`)?.value) || 0;
                const total = parseFloat(row.querySelector(`input[name="items[${index}][total]"]`)?.value) || 0;

                if (productId && quantity > 0 && price > 0) {
                    invoiceData.items.push({
                        productId,
                        quantity,
                        price,
                        discount,
                        total
                    });
                }
            });

            if (invoiceData.items.length === 0) {
                throw new Error('يرجى إضافة عنصر واحد على الأقل للفاتورة');
            }

            // حساب الإجماليات
            invoiceData.subtotal = invoiceData.items.reduce((sum, item) => sum + (item.quantity * item.price), 0);
            invoiceData.totalDiscount = invoiceData.items.reduce((sum, item) => sum + item.discount, 0);
            invoiceData.taxAmount = (invoiceData.subtotal - invoiceData.totalDiscount) * this.data.settings.taxRate;
            invoiceData.total = invoiceData.subtotal - invoiceData.totalDiscount + invoiceData.taxAmount;

            // حفظ الفاتورة
            this.data.invoices[invoiceData.id] = invoiceData;

            // تحديث رقم الفاتورة التالي
            this.data.settings.nextInvoiceNumber++;

            // حفظ البيانات
            this.saveSalesData();

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('newInvoiceModal'));
            modal.hide();

            // عرض رسالة نجاح
            this.showMessage(`تم ${status === 'draft' ? 'حفظ' : 'حفظ وإرسال'} الفاتورة بنجاح`, 'success');

            // تحديث العرض
            this.refreshData();

        } catch (error) {
            this.showMessage('خطأ في حفظ الفاتورة: ' + error.message, 'error');
        }
    },

    /**
     * توليد معرف فريد
     */
    generateId: function() {
        return 'ID_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    },

    /**
     * عرض رسالة للمستخدم
     */
    showMessage: function(message, type = 'info') {
        // يمكن تحسين هذا لاحقاً باستخدام نظام إشعارات أفضل
        const alertClass = type === 'success' ? 'alert-success' :
                          type === 'error' ? 'alert-danger' : 'alert-info';

        const alertHTML = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // إضافة الرسالة في أعلى الصفحة
        const container = document.querySelector('.sales-management') || document.body;
        container.insertAdjacentHTML('afterbegin', alertHTML);

        // إزالة الرسالة تلقائياً بعد 5 ثوان
        setTimeout(() => {
            const alert = container.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    },

    /**
     * تحديث البيانات
     */
    refreshData: function() {
        this.loadSalesData();
        const content = document.getElementById('main-content');
        if (content) {
            content.innerHTML = this.renderCurrentView();
        }
    }
};

// تصدير المكون للاستخدام العام
window.SalesComponent = SalesComponent;
