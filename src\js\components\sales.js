/**
 * مكون نظام المبيعات المتقدم
 * يوفر إدارة شاملة للفواتير، العملاء، المنتجات، عروض الأسعار، المدفوعات، والمخزون
 */

const SalesComponent = {
    // بيانات النظام
    data: {
        currentView: 'dashboard',
        customers: {},
        products: {},
        invoices: {},
        quotes: {},
        creditNotes: {},
        payments: {},
        inventory: {},
        stockAdjustments: {},
        filters: {
            dateFrom: '',
            dateTo: '',
            status: '',
            customer: '',
            quickSearch: ''
        },
        quoteFilters: {
            dateFrom: '',
            dateTo: '',
            status: '',
            customer: ''
        },
        settings: {
            taxRate: 0.15,
            currency: 'SAR',
            currencies: {
                'YER': { name: 'ريال يمني', symbol: 'ر.ي', rate: 1 },
                'SAR': { name: 'ريال سعودي', symbol: 'ر.س', rate: 0.133 },
                'USD': { name: 'دولار أمريكي', symbol: '$', rate: 0.00027 }
            },
            invoicePrefix: 'INV-',
            creditNotePrefix: 'CN-',
            nextInvoiceNumber: 1,
            nextQuoteNumber: 1,
            nextCreditNoteNumber: 1,
            companyName: 'قمة الوعد للسفريات',
            companyAddress: 'المملكة العربية السعودية',
            companyTaxNumber: '*********',
            companyEmail: '<EMAIL>',
            companyPhone: '+966501234567',
            companyWebsite: 'https://qimat-alwaed.com',
            language: 'ar',
            timezone: 'Asia/Riyadh',
            dateFormat: 'ar-SA',
            autoSave: true,
            showNotifications: true,
            enableBackup: true,
            autoCalculateTax: true,
            defaultDueDays: 30
        }
    },

    /**
     * تهيئة المكون مع معالجة أخطاء شاملة
     */
    init: function() {
        try {
            console.log('🔧 بدء تهيئة مكون المبيعات...');

            // تهيئة معالج الأخطاء
            this.initErrorHandler();

            // تحميل البيانات
            this.loadSalesData();

            // إنشاء البيانات التجريبية إذا لم تكن موجودة
            this.createSampleData();

            // تسجيل نجاح التهيئة
            console.log('✅ تم تهيئة مكون المبيعات بنجاح');
            this.showNotification('تم تهيئة نظام المبيعات بنجاح', 'success');

        } catch (error) {
            console.error('❌ خطأ في تهيئة مكون المبيعات:', error);
            this.handleError('فشل في تهيئة نظام المبيعات', error);
        }
    },

    /**
     * تهيئة معالج الأخطاء
     */
    initErrorHandler: function() {
        // إضافة معالج أخطاء عام للنافذة
        if (!window.salesErrorHandlerInitialized) {
            window.addEventListener('error', (event) => {
                if (event.filename && event.filename.includes('sales.js')) {
                    this.handleError('خطأ في نظام المبيعات', event.error);
                }
            });

            window.addEventListener('unhandledrejection', (event) => {
                this.handleError('خطأ غير معالج في نظام المبيعات', event.reason);
            });

            window.salesErrorHandlerInitialized = true;
        }
    },

    /**
     * معالج الأخطاء الرئيسي
     */
    handleError: function(message, error = null) {
        const errorInfo = {
            message: message,
            error: error ? error.message : 'خطأ غير محدد',
            stack: error ? error.stack : null,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };

        // تسجيل الخطأ
        console.error('🚨 خطأ في نظام المبيعات:', errorInfo);

        // حفظ الخطأ في التخزين المحلي للمراجعة
        this.logError(errorInfo);

        // إظهار رسالة للمستخدم
        this.showErrorNotification(message, error);

        // محاولة الاسترداد التلقائي
        this.attemptAutoRecovery(errorInfo);
    },

    /**
     * تسجيل الأخطاء في التخزين المحلي
     */
    logError: function(errorInfo) {
        try {
            const errorLog = JSON.parse(localStorage.getItem('salesErrorLog') || '[]');
            errorLog.push(errorInfo);

            // الاحتفاظ بآخر 50 خطأ فقط
            if (errorLog.length > 50) {
                errorLog.splice(0, errorLog.length - 50);
            }

            localStorage.setItem('salesErrorLog', JSON.stringify(errorLog));
        } catch (e) {
            console.error('فشل في حفظ سجل الأخطاء:', e);
        }
    },

    /**
     * إظهار إشعار خطأ للمستخدم
     */
    showErrorNotification: function(message, error = null) {
        const container = document.getElementById('main-content');
        if (!container) return;

        const errorDetails = error ? `<br><small class="text-muted">التفاصيل: ${error.message}</small>` : '';

        const errorHtml = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <h4 class="alert-heading">
                    <i class="fas fa-exclamation-triangle me-2"></i>خطأ في نظام المبيعات
                </h4>
                <p class="mb-0">${message}${errorDetails}</p>
                <hr>
                <div class="d-flex gap-2 mt-3">
                    <button class="btn btn-outline-primary btn-sm" onclick="location.reload()">
                        <i class="fas fa-refresh me-1"></i>تحديث الصفحة
                    </button>
                    <button class="btn btn-outline-info btn-sm" onclick="window.SalesComponent.attemptAutoRecovery()">
                        <i class="fas fa-wrench me-1"></i>محاولة الإصلاح
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="window.SalesComponent.showErrorLog()">
                        <i class="fas fa-list me-1"></i>سجل الأخطاء
                    </button>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // إدراج الخطأ في أعلى المحتوى
        container.insertAdjacentHTML('afterbegin', errorHtml);
    },

    /**
     * إظهار إشعار نجاح
     */
    showNotification: function(message, type = 'info') {
        if (!this.data.settings.showNotifications) return;

        const colors = {
            success: 'alert-success',
            info: 'alert-info',
            warning: 'alert-warning',
            error: 'alert-danger'
        };

        const icons = {
            success: 'fas fa-check-circle',
            info: 'fas fa-info-circle',
            warning: 'fas fa-exclamation-triangle',
            error: 'fas fa-times-circle'
        };

        const notification = document.createElement('div');
        notification.className = `alert ${colors[type]} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        notification.innerHTML = `
            <i class="${icons[type]} me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // إزالة الإشعار تلقائياً بعد 5 ثوان
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    },

    /**
     * محاولة الاسترداد التلقائي
     */
    attemptAutoRecovery: function(errorInfo = null) {
        console.log('🔄 محاولة الاسترداد التلقائي...');

        try {
            // إعادة تهيئة البيانات
            this.data = this.getDefaultData();

            // إعادة تحميل البيانات
            this.loadSalesData();

            // إنشاء البيانات التجريبية
            this.createSampleData();

            // إعادة عرض لوحة التحكم
            this.render({ view: 'dashboard' });

            console.log('✅ تم الاسترداد التلقائي بنجاح');
            this.showNotification('تم إصلاح النظام تلقائياً', 'success');

            return true;
        } catch (error) {
            console.error('❌ فشل في الاسترداد التلقائي:', error);
            this.showNotification('فشل في الإصلاح التلقائي، يرجى تحديث الصفحة', 'error');
            return false;
        }
    },

    /**
     * عرض سجل الأخطاء
     */
    showErrorLog: function() {
        try {
            const errorLog = JSON.parse(localStorage.getItem('salesErrorLog') || '[]');

            if (errorLog.length === 0) {
                this.showNotification('لا توجد أخطاء مسجلة', 'info');
                return;
            }

            const logHtml = errorLog.map((error, index) => `
                <div class="card mb-2">
                    <div class="card-body">
                        <h6 class="card-title text-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>خطأ #${index + 1}
                        </h6>
                        <p class="card-text">${error.message}</p>
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>${new Date(error.timestamp).toLocaleString('ar-SA')}
                        </small>
                        ${error.error ? `<br><small class="text-muted">التفاصيل: ${error.error}</small>` : ''}
                    </div>
                </div>
            `).join('');

            const container = document.getElementById('main-content');
            if (container) {
                container.innerHTML = `
                    <div class="container-fluid">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2><i class="fas fa-list me-2"></i>سجل الأخطاء</h2>
                            <div class="btn-group">
                                <button class="btn btn-outline-danger" onclick="window.SalesComponent.clearErrorLog()">
                                    <i class="fas fa-trash me-1"></i>مسح السجل
                                </button>
                                <button class="btn btn-outline-primary" onclick="window.SalesComponent.render({view: 'dashboard'})">
                                    <i class="fas fa-arrow-left me-1"></i>العودة
                                </button>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            يحتوي هذا السجل على آخر ${errorLog.length} خطأ في نظام المبيعات
                        </div>

                        ${logHtml}
                    </div>
                `;
            }
        } catch (error) {
            console.error('خطأ في عرض سجل الأخطاء:', error);
            this.showNotification('فشل في عرض سجل الأخطاء', 'error');
        }
    },

    /**
     * مسح سجل الأخطاء
     */
    clearErrorLog: function() {
        try {
            localStorage.removeItem('salesErrorLog');
            this.showNotification('تم مسح سجل الأخطاء', 'success');
            this.render({ view: 'dashboard' });
        } catch (error) {
            console.error('خطأ في مسح سجل الأخطاء:', error);
            this.showNotification('فشل في مسح سجل الأخطاء', 'error');
        }
    },

    /**
     * عرض المكون مع معالجة أخطاء محسنة
     */
    render: function(params = {}) {
        try {
            this.data.currentView = params.view || 'dashboard';

            const container = document.getElementById('main-content');
            if (!container) {
                throw new Error('عنصر العرض الرئيسي غير موجود');
            }

            // تحديث شريط التنقل
            this.updateNavigation();

            // عرض المحتوى حسب العرض المختار
            let content = '';

            switch (this.data.currentView) {
                case 'dashboard':
                    content = this.renderDashboard();
                    break;
                case 'invoices':
                    content = this.renderInvoicesView();
                    break;
                case 'creditNotes':
                    content = this.renderCreditNotesView();
                    break;
                case 'quotes':
                    content = this.renderQuotesView();
                    break;
                case 'customers':
                    content = this.renderCustomersView();
                    break;
                case 'products':
                    content = this.renderProductsView();
                    break;
                case 'inventory':
                    content = this.renderInventoryView();
                    break;
                case 'payments':
                    content = this.renderPaymentsView();
                    break;
                case 'reports':
                    content = this.renderReportsView();
                    break;
                case 'settings':
                    content = this.renderSettingsView();
                    break;
                default:
                    content = this.renderDashboard();
            }

            // التحقق من وجود المحتوى
            if (!content || content.trim().length === 0) {
                throw new Error(`فشل في إنشاء محتوى للعرض: ${this.data.currentView}`);
            }

            // عرض المحتوى
            container.innerHTML = content;

            // تسجيل نجاح العرض
            console.log(`✅ تم عرض ${this.data.currentView} بنجاح`);

        } catch (error) {
            console.error(`❌ خطأ في عرض ${this.data.currentView}:`, error);
            this.handleError(`فشل في عرض ${this.getViewDisplayName(this.data.currentView)}`, error);

            // محاولة عرض صفحة خطأ
            this.renderErrorPage(error);
        }
    },

    /**
     * عرض صفحة خطأ
     */
    renderErrorPage: function(error) {
        const container = document.getElementById('main-content');
        if (!container) return;

        container.innerHTML = `
            <div class="container-fluid">
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h4 class="mb-0">
                                    <i class="fas fa-exclamation-triangle me-2"></i>خطأ في النظام
                                </h4>
                            </div>
                            <div class="card-body">
                                <div class="text-center mb-4">
                                    <i class="fas fa-bug fa-4x text-danger mb-3"></i>
                                    <h5>عذراً، حدث خطأ في عرض هذه الصفحة</h5>
                                    <p class="text-muted">نعتذر عن الإزعاج، يرجى المحاولة مرة أخرى</p>
                                </div>

                                <div class="alert alert-light">
                                    <strong>تفاصيل الخطأ:</strong><br>
                                    <code>${error.message}</code>
                                </div>

                                <div class="d-flex justify-content-center gap-3">
                                    <button class="btn btn-primary" onclick="window.SalesComponent.render({view: 'dashboard'})">
                                        <i class="fas fa-home me-2"></i>العودة للرئيسية
                                    </button>
                                    <button class="btn btn-outline-info" onclick="window.SalesComponent.attemptAutoRecovery()">
                                        <i class="fas fa-wrench me-2"></i>محاولة الإصلاح
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="location.reload()">
                                        <i class="fas fa-refresh me-2"></i>تحديث الصفحة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * الحصول على اسم العرض للعرض
     */
    getViewDisplayName: function(view) {
        const names = {
            dashboard: 'لوحة التحكم',
            invoices: 'الفواتير',
            customers: 'العملاء',
            products: 'المنتجات',
            reports: 'التقارير',
            quotes: 'عروض الأسعار',
            inventory: 'المخزون',
            payments: 'المدفوعات',
            settings: 'الإعدادات'
        };
        return names[view] || view;

        // تحديث الرسوم البيانية إذا كانت في لوحة التحكم
        if (this.data.currentView === 'dashboard') {
            setTimeout(() => {
                this.updateCharts();
            }, 100);
        }

        // تحديث التقارير إذا كانت في صفحة التقارير
        if (this.data.currentView === 'reports') {
            setTimeout(() => {
                this.updateReports();
            }, 100);
        }
    },

    /**
     * تحميل بيانات المبيعات من التخزين المحلي
     */
    loadSalesData: function() {
        try {
            const savedData = localStorage.getItem('salesData');
            if (savedData) {
                const parsedData = JSON.parse(savedData);
                Object.assign(this.data, parsedData);
            }

            // التأكد من وجود البيانات الأساسية
            if (!this.data.customers) this.data.customers = {};
            if (!this.data.products) this.data.products = {};
            if (!this.data.invoices) this.data.invoices = {};
            if (!this.data.quotes) this.data.quotes = {};
            if (!this.data.payments) this.data.payments = {};
            if (!this.data.inventory) this.data.inventory = {};
            if (!this.data.stockAdjustments) this.data.stockAdjustments = {};
            if (!this.data.filters) this.data.filters = {
                dateFrom: '',
                dateTo: '',
                status: '',
                customer: '',
                quickSearch: ''
            };
            if (!this.data.quoteFilters) this.data.quoteFilters = {
                dateFrom: '',
                dateTo: '',
                status: '',
                customer: ''
            };
            if (!this.data.settings) this.data.settings = {
                taxRate: 0.15,
                currency: 'SAR',
                invoicePrefix: 'INV-',
                nextInvoiceNumber: 1,
                nextQuoteNumber: 1,
                companyName: 'قمة الوعد للسفريات',
                companyAddress: 'المملكة العربية السعودية',
                companyTaxNumber: '*********',
                companyEmail: '<EMAIL>',
                companyPhone: '+966501234567',
                companyWebsite: 'https://qimat-alwaed.com',
                language: 'ar',
                timezone: 'Asia/Riyadh',
                dateFormat: 'ar-SA',
                autoSave: true,
                showNotifications: true,
                enableBackup: true,
                autoCalculateTax: true,
                defaultDueDays: 30
            };
        } catch (error) {
            console.error('خطأ في تحميل بيانات المبيعات:', error);
            // إعادة تهيئة البيانات في حالة الخطأ
            this.data = {
                currentView: 'dashboard',
                customers: {},
                products: {},
                invoices: {},
                quotes: {},
                payments: {},
                inventory: {},
                stockAdjustments: {},
                filters: {
                    dateFrom: '',
                    dateTo: '',
                    status: '',
                    customer: '',
                    quickSearch: ''
                },
                quoteFilters: {
                    dateFrom: '',
                    dateTo: '',
                    status: '',
                    customer: ''
                },
                settings: {
                    taxRate: 0.15,
                    currency: 'SAR',
                    invoicePrefix: 'INV-',
                    nextInvoiceNumber: 1,
                    nextQuoteNumber: 1,
                    companyName: 'قمة الوعد للسفريات',
                    companyAddress: 'المملكة العربية السعودية',
                    companyTaxNumber: '*********',
                    companyEmail: '<EMAIL>',
                    companyPhone: '+966501234567',
                    companyWebsite: 'https://qimat-alwaed.com',
                    language: 'ar',
                    timezone: 'Asia/Riyadh',
                    dateFormat: 'ar-SA',
                    autoSave: true,
                    showNotifications: true,
                    enableBackup: true,
                    autoCalculateTax: true,
                    defaultDueDays: 30
                }
            };
        }
    },

    /**
     * حفظ بيانات المبيعات في التخزين المحلي
     */
    saveSalesData: function() {
        try {
            localStorage.setItem('salesData', JSON.stringify(this.data));
        } catch (error) {
            console.error('خطأ في حفظ بيانات المبيعات:', error);
        }
    },

    /**
     * إنشاء بيانات تجريبية
     */
    createSampleData: function() {
        // التحقق من وجود بيانات - إنشاء البيانات المفقودة فقط
        const hasData = Object.keys(this.data.customers || {}).length > 0 &&
                       Object.keys(this.data.products || {}).length > 0 &&
                       Object.keys(this.data.invoices || {}).length > 0;

        if (hasData) {
            return;
        }

        // تهيئة البيانات إذا لم تكن موجودة
        if (!this.data.customers) this.data.customers = {};
        if (!this.data.products) this.data.products = {};
        if (!this.data.invoices) this.data.invoices = {};
        if (!this.data.quotes) this.data.quotes = {};
        if (!this.data.creditNotes) this.data.creditNotes = {};
        if (!this.data.payments) this.data.payments = {};
        if (!this.data.inventory) this.data.inventory = {};
        if (!this.data.stockAdjustments) this.data.stockAdjustments = {};
        if (!this.data.quoteFilters) {
            this.data.quoteFilters = {
                dateFrom: '',
                dateTo: '',
                status: '',
                customer: ''
            };
        }
        if (!this.data.filters) {
            this.data.filters = {
                dateFrom: '',
                dateTo: '',
                status: '',
                customer: '',
                quickSearch: ''
            };
        }

        // إنشاء عملاء تجريبيين
        this.data.customers = {
            'customer1': {
                id: 'customer1',
                name: 'أحمد محمد السعيد',
                email: '<EMAIL>',
                phone: '+966501234567',
                address: 'الرياض، المملكة العربية السعودية',
                taxNumber: '*********',
                status: 'active',
                createdAt: new Date().toISOString()
            },
            'customer2': {
                id: 'customer2',
                name: 'فاطمة علي الزهراني',
                email: '<EMAIL>',
                phone: '+966507654321',
                address: 'جدة، المملكة العربية السعودية',
                taxNumber: '*********',
                status: 'active',
                createdAt: new Date().toISOString()
            }
        };

        // إنشاء منتجات تجريبية
        this.data.products = {
            'product1': {
                id: 'product1',
                name: 'تذكرة طيران - الرياض إلى دبي',
                description: 'تذكرة طيران ذهاب وإياب من الرياض إلى دبي',
                price: 1200,
                cost: 1000,
                currency: 'SAR',
                profitMargin: 200,
                profitPercentage: 20,
                category: 'تذاكر طيران',
                sku: 'FLIGHT-RUH-DXB',
                quantity: 10,
                unit: 'تذكرة',
                status: 'active',
                createdAt: new Date().toISOString()
            },
            'product2': {
                id: 'product2',
                name: 'حجز فندق - دبي 5 نجوم',
                description: 'حجز فندق 5 نجوم في دبي لمدة 3 ليالي',
                price: 800,
                cost: 600,
                currency: 'SAR',
                profitMargin: 200,
                profitPercentage: 33.33,
                category: 'حجوزات فنادق',
                sku: 'HOTEL-DXB-5STAR',
                quantity: 5,
                unit: 'ليلة',
                status: 'active',
                createdAt: new Date().toISOString()
            },
            'product3': {
                id: 'product3',
                name: 'تأشيرة سياحية - الإمارات',
                description: 'تأشيرة سياحية للإمارات العربية المتحدة',
                price: 150000,
                cost: 120000,
                currency: 'YER',
                profitMargin: 30000,
                profitPercentage: 25,
                category: 'تأشيرات',
                sku: 'VISA-UAE-TOURIST',
                quantity: 20,
                unit: 'تأشيرة',
                status: 'active',
                createdAt: new Date().toISOString()
            },
            'product4': {
                id: 'product4',
                name: 'جولة سياحية - إسطنبول',
                description: 'جولة سياحية شاملة في إسطنبول لمدة 7 أيام',
                price: 1500,
                cost: 1200,
                currency: 'USD',
                profitMargin: 300,
                profitPercentage: 25,
                category: 'جولات سياحية',
                sku: 'TOUR-IST-7DAYS',
                quantity: 8,
                unit: 'جولة',
                status: 'active',
                createdAt: new Date().toISOString()
            }
        };

        // إنشاء فاتورة تجريبية
        this.data.invoices = {
            'invoice1': {
                id: 'invoice1',
                number: 'INV-001',
                date: new Date().toISOString().split('T')[0],
                dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                customerId: 'customer1',
                reference: 'REF-001',
                items: [
                    {
                        productId: 'product1',
                        name: 'تذكرة طيران - الرياض إلى دبي',
                        quantity: 2,
                        price: 1200,
                        total: 2400
                    }
                ],
                subtotal: 2400,
                tax: 360,
                total: 2760,
                status: 'draft',
                notes: 'فاتورة تجريبية',
                createdAt: new Date().toISOString()
            }
        };

        // إنشاء عروض أسعار تجريبية
        this.data.quotes = {
            'quote1': {
                id: 'quote1',
                number: 'QUO-001',
                date: new Date().toISOString().split('T')[0],
                validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                customerId: 'customer1',
                title: 'عرض سعر رحلة عائلية',
                items: [
                    {
                        productId: 'product1',
                        name: 'تذكرة طيران - الرياض إلى دبي',
                        quantity: 4,
                        price: 1200,
                        total: 4800
                    }
                ],
                subtotal: 4800,
                tax: 720,
                total: 5520,
                status: 'pending',
                notes: 'عرض خاص للعائلات - صالح لمدة 30 يوم',
                createdAt: new Date().toISOString()
            }
        };

        // إنشاء مدفوعات تجريبية
        this.data.payments = {};

        // إنشاء مخزون تجريبي
        this.data.inventory = {
            'product1': 25,
            'product2': 15
        };

        // إنشاء سجل تعديلات المخزون
        this.data.stockAdjustments = {};

        // إنشاء إشعارات دائنة تجريبية
        this.data.creditNotes = {
            'creditnote1': {
                id: 'creditnote1',
                number: 'CN-001',
                date: new Date().toISOString().split('T')[0],
                type: 'partial_refund',
                customerId: 'customer1',
                invoiceId: 'invoice1',
                reference: 'REF-CN-001',
                amount: 500,
                tax: 75,
                total: 575,
                reason: 'إلغاء جزئي للحجز بناءً على طلب العميل',
                notes: 'تم الموافقة على الاسترداد الجزئي',
                status: 'pending',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                createdBy: 'نظام المبيعات'
            },
            'creditnote2': {
                id: 'creditnote2',
                number: 'CN-002',
                date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                type: 'correction',
                customerId: 'customer2',
                invoiceId: null,
                reference: 'REF-CN-002',
                amount: 200,
                tax: 30,
                total: 230,
                reason: 'تصحيح خطأ في الفوترة السابقة',
                notes: 'خطأ في حساب الضريبة',
                status: 'applied',
                appliedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
                createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
                updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
                createdBy: 'نظام المبيعات'
            }
        };

        // تحديث الإعدادات لتشمل عروض الأسعار والإشعارات الدائنة
        if (!this.data.settings.nextQuoteNumber) {
            this.data.settings.nextQuoteNumber = 2;
        }
        if (!this.data.settings.nextCreditNoteNumber) {
            this.data.settings.nextCreditNoteNumber = 3;
        }

        this.saveSalesData();
    },

    /**
     * تحديث شريط التنقل
     */
    updateNavigation: function() {
        const navItems = document.querySelectorAll('.nav-link[data-view]');
        navItems.forEach(item => {
            const view = item.getAttribute('data-view');
            if (view === this.data.currentView) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
    },

    /**
     * التبديل بين العروض
     */
    switchView: function(view) {
        this.data.currentView = view;
        this.render();
    },

    /**
     * تحديث البيانات
     */
    refreshData: function() {
        this.render();
    },

    /**
     * وظيفة بديلة لإنشاء فاتورة جديدة (للتوافق مع الاستدعاءات القديمة)
     */
    showNewInvoiceModal: function() {
        this.showCreateInvoiceModal();
    },

    /**
     * تشخيص حالة المكون
     */
    diagnose: function() {
        console.log('🔍 تشخيص مكون المبيعات:');
        console.log('- البيانات محملة:', !!this.data);
        console.log('- عدد العملاء:', Object.keys(this.data.customers || {}).length);
        console.log('- عدد المنتجات:', Object.keys(this.data.products || {}).length);
        console.log('- عدد الفواتير:', Object.keys(this.data.invoices || {}).length);
        console.log('- الإعدادات:', !!this.data.settings);
        console.log('- العرض الحالي:', this.data.currentView);

        // اختبار وظيفة إنشاء الفاتورة
        try {
            console.log('- اختبار وظيفة إنشاء الفاتورة...');
            if (typeof this.showCreateInvoiceModal === 'function') {
                console.log('✅ وظيفة showCreateInvoiceModal متاحة');
            } else {
                console.log('❌ وظيفة showCreateInvoiceModal غير متاحة');
            }
        } catch (error) {
            console.log('❌ خطأ في اختبار وظيفة إنشاء الفاتورة:', error);
        }

        return {
            dataLoaded: !!this.data,
            customersCount: Object.keys(this.data.customers || {}).length,
            productsCount: Object.keys(this.data.products || {}).length,
            invoicesCount: Object.keys(this.data.invoices || {}).length,
            hasSettings: !!this.data.settings,
            currentView: this.data.currentView
        };
    },

    /**
     * تحديث البيانات
     */
    refreshData: function() {
        this.render();
    },

    /**
     * وظائف مساعدة أساسية
     */
    formatAmount: function(amount, currency = null) {
        const currentCurrency = currency || this.data.settings?.currency || 'SAR';
        const currencies = this.data.settings?.currencies || {
            'YER': { name: 'ريال يمني', symbol: 'ر.ي', rate: 1 },
            'SAR': { name: 'ريال سعودي', symbol: 'ر.س', rate: 0.133 },
            'USD': { name: 'دولار أمريكي', symbol: '$', rate: 0.00027 }
        };

        const currencyInfo = currencies[currentCurrency];
        if (currencyInfo) {
            const formattedNumber = new Intl.NumberFormat('ar-SA', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(amount || 0);
            return `${formattedNumber} ${currencyInfo.symbol}`;
        }

        // Fallback للعملات غير المعرفة
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: currentCurrency
        }).format(amount || 0);
    },

    /**
     * تحويل العملة
     */
    convertCurrency: function(amount, fromCurrency, toCurrency) {
        const currencies = this.data.settings?.currencies || {};
        const fromRate = currencies[fromCurrency]?.rate || 1;
        const toRate = currencies[toCurrency]?.rate || 1;

        // تحويل إلى العملة الأساسية (ريال يمني) ثم إلى العملة المطلوبة
        const baseAmount = amount / fromRate;
        return baseAmount * toRate;
    },

    /**
     * الحصول على قائمة العملات المتاحة
     */
    getAvailableCurrencies: function() {
        return this.data.settings?.currencies || {
            'YER': { name: 'ريال يمني', symbol: 'ر.ي', rate: 1 },
            'SAR': { name: 'ريال سعودي', symbol: 'ر.س', rate: 0.133 },
            'USD': { name: 'دولار أمريكي', symbol: '$', rate: 0.00027 }
        };
    },

    /**
     * الحصول على اسم العملة
     */
    getCurrencyName: function(currencyCode) {
        const currencies = this.getAvailableCurrencies();
        return currencies[currencyCode]?.name || currencyCode;
    },

    /**
     * الحصول على رمز العملة
     */
    getCurrencySymbol: function(currencyCode) {
        const currencies = this.getAvailableCurrencies();
        return currencies[currencyCode]?.symbol || currencyCode;
    },

    /**
     * تحويل مبلغ العملة في لوحة التحكم
     */
    convertCurrencyAmount: function() {
        const amountInput = document.getElementById('convertAmount');
        const fromCurrencySelect = document.getElementById('fromCurrency');
        const toCurrencySelect = document.getElementById('toCurrency');
        const resultElement = document.getElementById('conversionResult');

        if (!amountInput || !fromCurrencySelect || !toCurrencySelect || !resultElement) return;

        const amount = parseFloat(amountInput.value) || 0;
        const fromCurrency = fromCurrencySelect.value;
        const toCurrency = toCurrencySelect.value;

        if (amount === 0) {
            resultElement.textContent = `0.00 ${this.getCurrencySymbol(toCurrency)}`;
            return;
        }

        const convertedAmount = this.convertCurrency(amount, fromCurrency, toCurrency);
        resultElement.textContent = this.formatAmount(convertedAmount, toCurrency);

        // تأثير بصري
        resultElement.style.transition = 'all 0.3s ease';
        resultElement.style.transform = 'scale(1.1)';
        resultElement.style.color = '#28a745';

        setTimeout(() => {
            resultElement.style.transform = 'scale(1)';
            resultElement.style.color = '';
        }, 300);
    },

    getStatusLabel: function(status) {
        const labels = {
            draft: 'مسودة',
            sent: 'مرسلة',
            paid: 'مدفوعة',
            overdue: 'متأخرة',
            cancelled: 'ملغية',
            pending: 'معلق',
            accepted: 'مقبول',
            rejected: 'مرفوض',
            expired: 'منتهي الصلاحية',
            active: 'نشط',
            inactive: 'غير نشط'
        };
        return labels[status] || status;
    },

    getStatusColor: function(status) {
        const colors = {
            draft: 'secondary',
            sent: 'primary',
            paid: 'success',
            overdue: 'danger',
            cancelled: 'dark',
            pending: 'warning',
            accepted: 'success',
            rejected: 'danger',
            expired: 'secondary',
            active: 'success',
            inactive: 'secondary'
        };
        return colors[status] || 'secondary';
    },

    /**
     * عرض لوحة التحكم
     */
    renderDashboard: function() {
        const totalInvoices = Object.keys(this.data.invoices || {}).length;
        const totalCustomers = Object.keys(this.data.customers || {}).length;
        const totalProducts = Object.keys(this.data.products || {}).length;
        const totalCreditNotes = Object.keys(this.data.creditNotes || {}).length;

        const totalSales = Object.values(this.data.invoices || {})
            .filter(inv => inv.status === 'paid')
            .reduce((sum, inv) => sum + inv.total, 0);

        const totalCreditAmount = Object.values(this.data.creditNotes || {})
            .reduce((sum, creditNote) => sum + (creditNote.total || 0), 0);

        return `
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم</h2>
                    <button class="btn btn-primary" onclick="window.SalesComponent.showCreateInvoiceModal()">
                        <i class="fas fa-plus me-1"></i>فاتورة جديدة
                    </button>
                </div>

                <!-- الإحصائيات الرئيسية -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${totalInvoices}</h4>
                                        <p class="mb-0">إجمالي الفواتير</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-file-invoice fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${this.formatAmount(totalSales)}</h4>
                                        <p class="mb-0">إجمالي المبيعات</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-dollar-sign fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${totalCustomers}</h4>
                                        <p class="mb-0">العملاء</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${totalProducts}</h4>
                                        <p class="mb-0">المنتجات</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-box fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات إضافية -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${totalCreditNotes}</h4>
                                        <p class="mb-0">الإشعارات الدائنة</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-file-invoice-dollar fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-secondary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${this.formatAmount(totalCreditAmount)}</h4>
                                        <p class="mb-0">قيمة الإشعارات</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-money-bill-wave fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-dark text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${this.formatAmount(totalSales - totalCreditAmount)}</h4>
                                        <p class="mb-0">صافي المبيعات</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-chart-line fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light text-dark border">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${Object.values(this.data.invoices || {}).filter(inv => inv.status === 'paid').length}</h4>
                                        <p class="mb-0">فواتير مدفوعة</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-check-circle fa-2x text-success"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الإجراءات السريعة -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>الإجراءات السريعة</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-2">
                                        <button class="btn btn-outline-primary w-100 mb-2" onclick="window.SalesComponent.showCreateInvoiceModal()">
                                            <i class="fas fa-file-invoice d-block mb-1"></i>فاتورة جديدة
                                        </button>
                                    </div>
                                    <div class="col-md-2">
                                        <button class="btn btn-outline-success w-100 mb-2" onclick="window.SalesComponent.showCreateCreditNoteModal()">
                                            <i class="fas fa-file-invoice-dollar d-block mb-1"></i>إشعار دائن
                                        </button>
                                    </div>
                                    <div class="col-md-2">
                                        <button class="btn btn-outline-info w-100 mb-2" onclick="window.SalesComponent.showCreateCustomerModal()">
                                            <i class="fas fa-user-plus d-block mb-1"></i>عميل جديد
                                        </button>
                                    </div>
                                    <div class="col-md-2">
                                        <button class="btn btn-outline-warning w-100 mb-2" onclick="window.SalesComponent.showCreateProductModal()">
                                            <i class="fas fa-plus-circle d-block mb-1"></i>منتج جديد
                                        </button>
                                    </div>
                                    <div class="col-md-2">
                                        <button class="btn btn-outline-secondary w-100 mb-2" onclick="window.SalesComponent.switchView('invoices')">
                                            <i class="fas fa-list d-block mb-1"></i>عرض الفواتير
                                        </button>
                                    </div>
                                    <div class="col-md-2">
                                        <button class="btn btn-outline-dark w-100 mb-2" onclick="window.SalesComponent.switchView('creditNotes')">
                                            <i class="fas fa-file-invoice-dollar d-block mb-1"></i>الإشعارات الدائنة
                                        </button>
                                    </div>
                                </div>

                                <!-- محول العملات -->
                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0"><i class="fas fa-exchange-alt me-2"></i>محول العملات</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <label class="form-label small">المبلغ</label>
                                                        <input type="number" class="form-control" id="convertAmount"
                                                               placeholder="0.00" step="0.01" min="0"
                                                               onchange="window.SalesComponent.convertCurrencyAmount()">
                                                    </div>
                                                    <div class="col-md-4">
                                                        <label class="form-label small">من</label>
                                                        <select class="form-control" id="fromCurrency" onchange="window.SalesComponent.convertCurrencyAmount()">
                                                            <option value="YER">ريال يمني</option>
                                                            <option value="SAR">ريال سعودي</option>
                                                            <option value="USD">دولار أمريكي</option>
                                                        </select>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <label class="form-label small">إلى</label>
                                                        <select class="form-control" id="toCurrency" onchange="window.SalesComponent.convertCurrencyAmount()">
                                                            <option value="SAR">ريال سعودي</option>
                                                            <option value="YER">ريال يمني</option>
                                                            <option value="USD">دولار أمريكي</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="mt-3">
                                                    <div class="alert alert-info mb-0">
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <span>النتيجة:</span>
                                                            <strong id="conversionResult">0.00 ر.س</strong>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0"><i class="fas fa-chart-line me-2"></i>أسعار الصرف الحالية</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row text-center">
                                                    <div class="col-4">
                                                        <div class="border rounded p-2">
                                                            <div class="text-muted small">ريال يمني</div>
                                                            <div class="fw-bold">1.00</div>
                                                            <div class="text-success small">أساسي</div>
                                                        </div>
                                                    </div>
                                                    <div class="col-4">
                                                        <div class="border rounded p-2">
                                                            <div class="text-muted small">ريال سعودي</div>
                                                            <div class="fw-bold">0.133</div>
                                                            <div class="text-info small">ر.ي</div>
                                                        </div>
                                                    </div>
                                                    <div class="col-4">
                                                        <div class="border rounded p-2">
                                                            <div class="text-muted small">دولار أمريكي</div>
                                                            <div class="fw-bold">0.00027</div>
                                                            <div class="text-warning small">ر.ي</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="mt-2 text-center">
                                                    <small class="text-muted">آخر تحديث: ${new Date().toLocaleDateString('ar-SA')}</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الفواتير الأخيرة -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-file-invoice me-2"></i>الفواتير الأخيرة</h5>
                            </div>
                            <div class="card-body">
                                ${this.renderRecentInvoices()}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>توزيع المبيعات</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="salesChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض الفواتير الأخيرة
     */
    renderRecentInvoices: function() {
        const recentInvoices = Object.values(this.data.invoices || {})
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, 5);

        if (recentInvoices.length === 0) {
            return '<p class="text-muted text-center">لا توجد فواتير</p>';
        }

        return `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>المبلغ</th>
                            <th>الحالة</th>
                            <th>التاريخ</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${recentInvoices.map(invoice => {
                            const customer = this.data.customers[invoice.customerId];
                            return `
                                <tr>
                                    <td><strong>${invoice.number}</strong></td>
                                    <td>${customer?.name || 'غير محدد'}</td>
                                    <td>${this.formatAmount(invoice.total)}</td>
                                    <td><span class="badge bg-${this.getStatusColor(invoice.status)}">${this.getStatusLabel(invoice.status)}</span></td>
                                    <td>${new Date(invoice.date).toLocaleDateString('ar-SA')}</td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
        `;
    },

    /**
     * تحديث الرسوم البيانية
     */
    updateCharts: function() {
        // رسم بياني بسيط للمبيعات
        const canvas = document.getElementById('salesChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');

        // بيانات المبيعات حسب الحالة
        const invoicesByStatus = Object.values(this.data.invoices || {}).reduce((acc, invoice) => {
            acc[invoice.status] = (acc[invoice.status] || 0) + 1;
            return acc;
        }, {});

        // ألوان للرسم البياني
        const colors = ['#007bff', '#28a745', '#ffc107', '#dc3545', '#6c757d'];
        const labels = Object.keys(invoicesByStatus);
        const data = Object.values(invoicesByStatus);

        // رسم دائري بسيط
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const radius = Math.min(centerX, centerY) - 20;

        ctx.clearRect(0, 0, canvas.width, canvas.height);

        let currentAngle = 0;
        const total = data.reduce((sum, value) => sum + value, 0);

        data.forEach((value, index) => {
            const sliceAngle = (value / total) * 2 * Math.PI;

            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
            ctx.closePath();
            ctx.fillStyle = colors[index % colors.length];
            ctx.fill();

            currentAngle += sliceAngle;
        });

        // إضافة وسيلة إيضاح بسيطة
        let legendY = 10;
        labels.forEach((label, index) => {
            ctx.fillStyle = colors[index % colors.length];
            ctx.fillRect(10, legendY, 15, 15);
            ctx.fillStyle = '#000';
            ctx.font = '12px Arial';
            ctx.fillText(`${this.getStatusLabel(label)}: ${data[index]}`, 30, legendY + 12);
            legendY += 20;
        });
    },

    /**
     * عرض نافذة إنشاء فاتورة جديدة محسنة
     */
    showCreateInvoiceModal: function() {
        try {
            // التأكد من تحميل البيانات
            if (!this.data || Object.keys(this.data).length === 0) {
                this.loadSalesData();
                this.createSampleData();
            }

            const customers = Object.values(this.data.customers || {});
            const products = Object.values(this.data.products || {});

            // التحقق من وجود العملاء والمنتجات
            if (customers.length === 0) {
                this.showNotification('لا توجد عملاء. سيتم إنشاء بيانات تجريبية...', 'warning');
                this.createSampleData();

                // إعادة تحميل البيانات
                const customersAfter = Object.values(this.data.customers || {});
                if (customersAfter.length === 0) {
                    this.showNotification('فشل في تحميل بيانات العملاء. يرجى إعادة تحميل الصفحة.', 'error');
                    return;
                }
            }

            if (products.length === 0) {
                this.showNotification('لا توجد منتجات. سيتم إنشاء بيانات تجريبية...', 'warning');
                this.createSampleData();

                // إعادة تحميل البيانات
                const productsAfter = Object.values(this.data.products || {});
                if (productsAfter.length === 0) {
                    this.showNotification('فشل في تحميل بيانات المنتجات. يرجى إضافة منتجات أولاً.', 'error');
                    return;
                }
            }

            // إنشاء النافذة المحسنة
            this.createEnhancedInvoiceModal();

        } catch (error) {
            console.error('خطأ في فتح نافذة الفاتورة:', error);
            this.handleError('فشل في فتح نافذة إنشاء الفاتورة', error);
        }
    },

    /**
     * إنشاء نافذة الفاتورة المحسنة
     */
    createEnhancedInvoiceModal: function() {
        const customers = Object.values(this.data.customers || {});
        const products = Object.values(this.data.products || {});

        // إنشاء رقم فاتورة جديد
        const invoiceNumber = this.generateInvoiceNumber();

        // تواريخ افتراضية
        const today = new Date().toISOString().split('T')[0];
        const dueDate = new Date(Date.now() + (this.data.settings?.defaultDueDays || 30) * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

        const modalHTML = `
            <div class="modal fade" id="createInvoiceModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-file-invoice me-2"></i>إنشاء فاتورة جديدة
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <!-- شريط التقدم -->
                            <div class="progress mb-4" style="height: 8px;">
                                <div class="progress-bar bg-success" role="progressbar" style="width: 33%" id="invoice-progress">
                                    <span class="sr-only">33% مكتمل</span>
                                </div>
                            </div>

                            <form id="createInvoiceForm">
                                <!-- معلومات الفاتورة الأساسية -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات الفاتورة</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <label class="form-label">رقم الفاتورة</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="fas fa-hashtag"></i></span>
                                                    <input type="text" class="form-control" name="number" value="${invoiceNumber}" readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">تاريخ الفاتورة *</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                                    <input type="date" class="form-control" name="date" value="${today}" required>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">تاريخ الاستحقاق *</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="fas fa-calendar-check"></i></span>
                                                    <input type="date" class="form-control" name="dueDate" value="${dueDate}" required>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- معلومات العميل -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="fas fa-user me-2"></i>معلومات العميل</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <label class="form-label">العميل *</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                                    <select class="form-control" name="customerId" required onchange="window.SalesComponent.updateCustomerInfo(this.value)">
                                                        <option value="">اختر العميل</option>
                                                        ${customers.map(customer => `
                                                            <option value="${customer.id}" data-phone="${customer.phone || ''}" data-email="${customer.email || ''}" data-address="${customer.address || ''}">
                                                                ${customer.name} ${customer.phone ? '- ' + customer.phone : ''}
                                                            </option>
                                                        `).join('')}
                                                    </select>
                                                </div>
                                                <button type="button" class="btn btn-sm btn-outline-success mt-2" onclick="window.SalesComponent.showCreateCustomerModal()">
                                                    <i class="fas fa-plus me-1"></i>إضافة عميل جديد
                                                </button>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">رقم المرجع</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="fas fa-tag"></i></span>
                                                    <input type="text" class="form-control" name="reference" placeholder="رقم المرجع (اختياري)">
                                                </div>

                                                <!-- معلومات العميل المختار -->
                                                <div id="customer-info" class="mt-2 p-2 bg-light rounded" style="display: none;">
                                                    <small class="text-muted">
                                                        <div id="customer-details"></div>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- عناصر الفاتورة -->
                                <div class="card mb-4">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0"><i class="fas fa-shopping-cart me-2"></i>عناصر الفاتورة</h6>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-primary" onclick="window.SalesComponent.addInvoiceItem()">
                                                <i class="fas fa-plus me-1"></i>إضافة عنصر
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="window.SalesComponent.addMultipleItems()">
                                                <i class="fas fa-layer-group me-1"></i>إضافة متعددة
                                            </button>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <!-- رؤوس الأعمدة -->
                                        <div class="row mb-3 bg-light p-2 rounded">
                                            <div class="col-md-4"><strong><i class="fas fa-box me-1"></i>المنتج</strong></div>
                                            <div class="col-md-2"><strong><i class="fas fa-sort-numeric-up me-1"></i>الكمية</strong></div>
                                            <div class="col-md-2"><strong><i class="fas fa-dollar-sign me-1"></i>السعر</strong></div>
                                            <div class="col-md-2"><strong><i class="fas fa-calculator me-1"></i>الإجمالي</strong></div>
                                            <div class="col-md-2"><strong><i class="fas fa-cogs me-1"></i>إجراءات</strong></div>
                                        </div>

                                        <!-- منطقة العناصر -->
                                        <div id="invoiceItems" class="invoice-items-container">
                                            <!-- سيتم إضافة العناصر هنا -->
                                        </div>

                                        <!-- رسالة عدم وجود عناصر -->
                                        <div id="no-items-message" class="text-center py-4 text-muted">
                                            <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                                            <p>لا توجد عناصر في الفاتورة</p>
                                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.addInvoiceItem()">
                                                <i class="fas fa-plus me-1"></i>إضافة أول عنصر
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- الملاحظات والإجماليات -->
                                <div class="row">
                                    <div class="col-md-7">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0"><i class="fas fa-sticky-note me-2"></i>ملاحظات وشروط</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-3">
                                                    <label class="form-label">ملاحظات الفاتورة</label>
                                                    <textarea class="form-control" name="notes" rows="3" placeholder="ملاحظات إضافية للفاتورة..."></textarea>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">شروط الدفع</label>
                                                    <select class="form-control" name="paymentTerms">
                                                        <option value="30">الدفع خلال 30 يوم</option>
                                                        <option value="15">الدفع خلال 15 يوم</option>
                                                        <option value="7">الدفع خلال 7 أيام</option>
                                                        <option value="0">الدفع فوري</option>
                                                    </select>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="sendEmail" id="sendEmail">
                                                    <label class="form-check-label" for="sendEmail">
                                                        إرسال الفاتورة بالبريد الإلكتروني
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-5">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0"><i class="fas fa-calculator me-2"></i>ملخص الفاتورة</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="invoice-summary">
                                                    <!-- التحكم في عملة الفاتورة -->
                                                    <div class="d-flex justify-content-between mb-3">
                                                        <span><i class="fas fa-coins me-1"></i>عملة الفاتورة:</span>
                                                        <select class="form-select form-select-sm" id="invoiceCurrencySelect" style="width: 120px;" onchange="window.SalesComponent.changeInvoiceCurrency(this.value)">
                                                            <option value="YER">ريال يمني</option>
                                                            <option value="SAR">ريال سعودي</option>
                                                            <option value="USD">دولار أمريكي</option>
                                                        </select>
                                                    </div>

                                                    <!-- التحكم في الضريبة -->
                                                    <div class="tax-controls mb-3">
                                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                                            <span><i class="fas fa-percentage me-1"></i>إعدادات الضريبة:</span>
                                                            <div class="btn-group btn-group-sm" role="group">
                                                                <input type="checkbox" class="btn-check" id="taxEnabledCheck" checked onchange="window.SalesComponent.toggleTax(this.checked)">
                                                                <label class="btn btn-outline-primary" for="taxEnabledCheck" title="تفعيل الضريبة">
                                                                    <i class="fas fa-calculator"></i>
                                                                </label>

                                                                <input type="checkbox" class="btn-check" id="taxDisplayCheck" checked onchange="window.SalesComponent.toggleTaxDisplay(this.checked)">
                                                                <label class="btn btn-outline-info" for="taxDisplayCheck" title="إظهار في الملخص">
                                                                    <i class="fas fa-eye"></i>
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <small class="text-muted">
                                                            <i class="fas fa-info-circle me-1"></i>
                                                            الأول: تفعيل الضريبة | الثاني: إظهار في الملخص
                                                        </small>
                                                    </div>
                                                    <hr>

                                                    <div class="d-flex justify-content-between mb-2">
                                                        <span>عدد العناصر:</span>
                                                        <span id="invoiceItemCount" class="badge bg-primary">0</span>
                                                    </div>
                                                    <hr>
                                                    <div class="d-flex justify-content-between mb-2">
                                                        <span>المجموع الفرعي:</span>
                                                        <span id="invoiceSubtotal" class="fw-bold">0.00 ر.ي</span>
                                                    </div>
                                                    <div class="d-flex justify-content-between mb-2">
                                                        <span>الخصم:</span>
                                                        <div class="input-group input-group-sm" style="width: 120px;">
                                                            <input type="number" class="form-control" id="discountAmount" value="0" min="0" step="0.01" onchange="window.SalesComponent.calculateInvoiceTotal()">
                                                            <span class="input-group-text">ر.ي</span>
                                                        </div>
                                                    </div>
                                                    <div class="d-flex justify-content-between mb-2" id="taxRow">
                                                        <span>الضريبة (${((this.data.settings?.taxRate || 0.15) * 100).toFixed(0)}%):</span>
                                                        <span id="invoiceTax" class="fw-bold">0.00 ر.ي</span>
                                                    </div>
                                                    <hr>
                                                    <div class="d-flex justify-content-between mb-3">
                                                        <span class="h6">الإجمالي النهائي:</span>
                                                        <span id="invoiceTotal" class="h5 text-primary fw-bold">0.00 ر.ي</span>
                                                    </div>

                                                    <!-- معاينة سريعة -->
                                                    <button type="button" class="btn btn-outline-info btn-sm w-100 mb-2" onclick="window.SalesComponent.previewInvoice()">
                                                        <i class="fas fa-eye me-1"></i>معاينة الفاتورة
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer bg-light">
                            <div class="d-flex justify-content-between w-100">
                                <div>
                                    <button type="button" class="btn btn-outline-warning" onclick="window.SalesComponent.saveDraftInvoice()">
                                        <i class="fas fa-file-alt me-1"></i>حفظ كمسودة
                                    </button>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal">
                                        <i class="fas fa-times me-1"></i>إلغاء
                                    </button>
                                    <button type="button" class="btn btn-success" onclick="window.SalesComponent.saveInvoice()">
                                        <i class="fas fa-save me-1"></i>حفظ الفاتورة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إضافة النافذة للصفحة
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // إظهار النافذة
        const modal = new bootstrap.Modal(document.getElementById('createInvoiceModal'));
        modal.show();

        // إضافة عنصر افتراضي وتحديث شريط التقدم
        setTimeout(() => {
            this.addInvoiceItem();
            this.updateInvoiceProgress(66); // تحديث التقدم إلى 66%
            this.updateNoItemsMessage();
        }, 200);

        // تنظيف النافذة عند الإغلاق
        document.getElementById('createInvoiceModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * إنشاء رقم فاتورة جديد
     */
    generateInvoiceNumber: function() {
        const prefix = this.data.settings?.invoicePrefix || 'INV-';
        const nextNumber = this.data.settings?.nextInvoiceNumber || 1;
        return prefix + String(nextNumber).padStart(4, '0');
    },

    /**
     * تحديث شريط التقدم في نافذة الفاتورة
     */
    updateInvoiceProgress: function(percentage) {
        const progressBar = document.getElementById('invoice-progress');
        if (progressBar) {
            progressBar.style.width = percentage + '%';
            progressBar.setAttribute('aria-valuenow', percentage);
        }
    },

    /**
     * تحديث معلومات العميل المختار
     */
    updateCustomerInfo: function(customerId) {
        const customerInfo = document.getElementById('customer-info');
        const customerDetails = document.getElementById('customer-details');

        if (!customerId) {
            customerInfo.style.display = 'none';
            return;
        }

        const customer = this.data.customers[customerId];
        if (customer) {
            let details = `<strong>${customer.name}</strong><br>`;
            if (customer.phone) details += `📞 ${customer.phone}<br>`;
            if (customer.email) details += `📧 ${customer.email}<br>`;
            if (customer.address) details += `📍 ${customer.address}`;

            customerDetails.innerHTML = details;
            customerInfo.style.display = 'block';

            // تحديث شريط التقدم
            this.updateInvoiceProgress(50);
        }
    },

    /**
     * تحديث رسالة عدم وجود عناصر
     */
    updateNoItemsMessage: function() {
        const itemsContainer = document.getElementById('invoiceItems');
        const noItemsMessage = document.getElementById('no-items-message');

        if (itemsContainer && noItemsMessage) {
            const hasItems = itemsContainer.children.length > 0;
            noItemsMessage.style.display = hasItems ? 'none' : 'block';
        }
    },

    /**
     * إضافة عنصر جديد للفاتورة محسن
     */
    addInvoiceItem: function() {
        const itemsContainer = document.getElementById('invoiceItems');
        if (!itemsContainer) {
            console.error('عنصر invoiceItems غير موجود');
            this.showNotification('خطأ في إضافة العنصر', 'error');
            return;
        }

        const itemCount = itemsContainer.children.length;
        const products = Object.values(this.data.products || {});
        const itemId = 'item_' + Date.now() + '_' + itemCount;

        if (products.length === 0) {
            this.showNotification('لا توجد منتجات متاحة. يرجى إضافة منتجات أولاً.', 'warning');
            return;
        }

        const itemHTML = `
            <div class="invoice-item row mb-3 p-3 border rounded" data-item-id="${itemId}" style="animation: fadeIn 0.3s ease-in;">
                <div class="col-md-4">
                    <label class="form-label small">المنتج *</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-box"></i></span>
                        <select class="form-control item-product" name="items[${itemCount}][productId]"
                                onchange="window.SalesComponent.updateItemFromProduct(this)" required>
                            <option value="">اختر المنتج</option>
                            ${products.map(product => `
                                <option value="${product.id}" data-price="${product.price}" data-name="${product.name}"
                                        data-unit="${product.unit || 'قطعة'}" data-currency="${product.currency || 'YER'}">
                                    ${product.name} - ${this.formatAmount(product.price, product.currency)} (${product.quantity || 0} متاح)
                                </option>
                            `).join('')}
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label small">الكمية *</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-sort-numeric-up"></i></span>
                        <input type="number" class="form-control item-quantity" name="items[${itemCount}][quantity]"
                               placeholder="1" value="1" min="1" step="1" required
                               onchange="window.SalesComponent.calculateItemTotal(this)">
                    </div>
                    <small class="text-muted item-unit">قطعة</small>
                </div>
                <div class="col-md-2">
                    <label class="form-label small">السعر *</label>
                    <div class="input-group">
                        <span class="input-group-text item-currency-symbol">ر.ي</span>
                        <input type="number" class="form-control item-price" name="items[${itemCount}][price]"
                               placeholder="0.00" step="0.01" min="0" required
                               onchange="window.SalesComponent.calculateItemTotal(this)">
                        <input type="hidden" class="item-currency" name="items[${itemCount}][currency]" value="YER">
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label small">الإجمالي</label>
                    <div class="input-group">
                        <span class="input-group-text item-total-currency">ر.ي</span>
                        <input type="number" class="form-control item-total" name="items[${itemCount}][total]"
                               placeholder="0.00" readonly>
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label small">إجراءات</label>
                    <div class="btn-group w-100">
                        <button type="button" class="btn btn-outline-danger btn-sm"
                                onclick="window.SalesComponent.removeInvoiceItem(this.closest('.invoice-item'))"
                                title="حذف العنصر">
                            <i class="fas fa-trash"></i>
                        </button>
                        <button type="button" class="btn btn-outline-info btn-sm"
                                onclick="window.SalesComponent.duplicateInvoiceItem(this.closest('.invoice-item'))"
                                title="نسخ العنصر">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;

        itemsContainer.insertAdjacentHTML('beforeend', itemHTML);

        // تحديث رسالة عدم وجود عناصر
        this.updateNoItemsMessage();

        // التركيز على حقل المنتج الجديد
        const newItem = itemsContainer.lastElementChild;
        const productSelect = newItem.querySelector('.item-product');
        if (productSelect) {
            setTimeout(() => {
                productSelect.focus();
            }, 100);
        }

        // تحديث شريط التقدم
        this.updateInvoiceProgress(75);

        this.showNotification('تم إضافة عنصر جديد للفاتورة', 'success');
    },

    /**
     * إضافة عناصر متعددة
     */
    addMultipleItems: function() {
        const count = prompt('كم عنصر تريد إضافة؟', '3');
        if (count && !isNaN(count) && count > 0) {
            for (let i = 0; i < Math.min(count, 10); i++) {
                this.addInvoiceItem();
            }
            this.showNotification(`تم إضافة ${count} عناصر`, 'success');
        }
    },

    /**
     * نسخ عنصر فاتورة
     */
    duplicateInvoiceItem: function(itemElement) {
        if (!itemElement) return;

        const productSelect = itemElement.querySelector('.item-product');
        const quantityInput = itemElement.querySelector('.item-quantity');
        const priceInput = itemElement.querySelector('.item-price');

        // إضافة عنصر جديد
        this.addInvoiceItem();

        // نسخ القيم للعنصر الجديد
        setTimeout(() => {
            const itemsContainer = document.getElementById('invoiceItems');
            const newItem = itemsContainer.lastElementChild;

            if (newItem) {
                const newProductSelect = newItem.querySelector('.item-product');
                const newQuantityInput = newItem.querySelector('.item-quantity');
                const newPriceInput = newItem.querySelector('.item-price');

                if (newProductSelect && productSelect.value) {
                    newProductSelect.value = productSelect.value;
                    this.updateItemFromProduct(newProductSelect);
                }
                if (newQuantityInput && quantityInput.value) {
                    newQuantityInput.value = quantityInput.value;
                }
                if (newPriceInput && priceInput.value) {
                    newPriceInput.value = priceInput.value;
                    this.calculateItemTotal(newPriceInput);
                }
            }
        }, 100);

        this.showNotification('تم نسخ العنصر', 'success');
    },

    /**
     * إزالة عنصر من الفاتورة
     */
    removeInvoiceItem: function(itemIndex) {
        const itemsContainer = document.getElementById('invoiceItems');
        if (!itemsContainer) return;

        const items = Array.from(itemsContainer.children);

        if (items.length > 1) {
            // البحث عن العنصر بالفهرس أو بالعنصر نفسه
            let itemToRemove = null;

            if (typeof itemIndex === 'number') {
                itemToRemove = items[itemIndex];
            } else {
                // إذا تم تمرير العنصر نفسه
                itemToRemove = itemIndex;
            }

            if (itemToRemove && itemToRemove.parentNode === itemsContainer) {
                itemToRemove.remove();
                this.calculateInvoiceTotal();
                this.showNotification('تم حذف العنصر', 'success');
            }
        } else {
            this.showNotification('يجب أن تحتوي الفاتورة على عنصر واحد على الأقل', 'warning');
        }
    },

    /**
     * تحديث عنصر من المنتج المختار محسن
     */
    updateItemFromProduct: function(selectElement) {
        const productId = selectElement.value;
        const item = selectElement.closest('.invoice-item');

        if (!item) return;

        const priceInput = item.querySelector('.item-price');
        const quantityInput = item.querySelector('.item-quantity');
        const unitLabel = item.querySelector('.item-unit');
        const currencySymbol = item.querySelector('.item-currency-symbol');
        const totalCurrencySymbol = item.querySelector('.item-total-currency');
        const currencyInput = item.querySelector('.item-currency');

        if (!productId) {
            // مسح القيم عند عدم اختيار منتج
            if (priceInput) priceInput.value = '';
            if (unitLabel) unitLabel.textContent = 'قطعة';
            if (currencySymbol) currencySymbol.textContent = 'ر.ي';
            if (totalCurrencySymbol) totalCurrencySymbol.textContent = 'ر.ي';
            if (currencyInput) currencyInput.value = 'YER';
            this.calculateItemTotal(priceInput);
            return;
        }

        const product = this.data.products?.[productId];
        if (!product) {
            this.showNotification('المنتج غير موجود', 'error');
            return;
        }

        // تحديث السعر
        if (priceInput) {
            priceInput.value = product.price;
        }

        // تحديث العملة
        const productCurrency = product.currency || 'YER';
        const currencySymbolText = this.getCurrencySymbol(productCurrency);

        if (currencySymbol) {
            currencySymbol.textContent = currencySymbolText;
        }
        if (totalCurrencySymbol) {
            totalCurrencySymbol.textContent = currencySymbolText;
        }
        if (currencyInput) {
            currencyInput.value = productCurrency;
        }

        // تحديث وحدة القياس
        if (unitLabel) {
            unitLabel.textContent = this.getUnitLabel(product.unit);
        }

        // التحقق من توفر الكمية
        const requestedQuantity = parseInt(quantityInput?.value || 1);
        const availableQuantity = product.quantity || 0;

        if (product.type === 'product' && availableQuantity < requestedQuantity) {
            this.showNotification(`تحذير: الكمية المطلوبة (${requestedQuantity}) أكبر من المتاح (${availableQuantity})`, 'warning');

            // تعديل الكمية للمتاح
            if (quantityInput && availableQuantity > 0) {
                quantityInput.value = availableQuantity;
            }
        }

        // حساب الإجمالي
        this.calculateItemTotal(priceInput);

        // تأثير بصري للتحديث
        item.style.backgroundColor = '#e8f5e8';
        setTimeout(() => {
            item.style.backgroundColor = '';
        }, 1000);

        // تحديث شريط التقدم
        this.updateInvoiceProgress(85);
    },

    /**
     * الحصول على تسمية وحدة القياس
     */
    getUnitLabel: function(unit) {
        const units = {
            piece: 'قطعة',
            service: 'خدمة',
            night: 'ليلة',
            day: 'يوم',
            person: 'شخص',
            kg: 'كيلوجرام',
            liter: 'لتر',
            hour: 'ساعة',
            month: 'شهر'
        };
        return units[unit] || unit || 'قطعة';
    },

    /**
     * حساب إجمالي العنصر محسن
     */
    calculateItemTotal: function(inputElement) {
        if (!inputElement) return;

        const item = inputElement.closest('.invoice-item');
        if (!item) return;

        const quantityInput = item.querySelector('.item-quantity');
        const priceInput = item.querySelector('.item-price');
        const totalInput = item.querySelector('.item-total');

        if (!quantityInput || !priceInput || !totalInput) return;

        const quantity = parseFloat(quantityInput.value) || 0;
        const price = parseFloat(priceInput.value) || 0;

        // التحقق من صحة القيم
        if (quantity < 0) {
            quantityInput.value = 1;
            this.showNotification('الكمية يجب أن تكون أكبر من صفر', 'warning');
            return;
        }

        if (price < 0) {
            priceInput.value = 0;
            this.showNotification('السعر يجب أن يكون أكبر من أو يساوي صفر', 'warning');
            return;
        }

        const total = quantity * price;
        totalInput.value = total.toFixed(2);

        // تأثير بصري للتحديث
        totalInput.style.transition = 'all 0.3s ease';
        totalInput.style.backgroundColor = '#28a745';
        totalInput.style.color = 'white';
        totalInput.style.transform = 'scale(1.05)';

        setTimeout(() => {
            totalInput.style.backgroundColor = '';
            totalInput.style.color = '';
            totalInput.style.transform = 'scale(1)';
        }, 600);

        // حساب إجمالي الفاتورة
        this.calculateInvoiceTotal();

        // تحديث شريط التقدم
        this.updateInvoiceProgress(90);
    },

    /**
     * إعدادات الفاتورة المحسنة
     */
    invoiceSettings: {
        showTax: true,
        defaultCurrency: 'YER',
        currentInvoiceCurrency: 'YER',
        taxEnabled: true,
        taxRate: 0.15
    },

    /**
     * حساب إجمالي الفاتورة محسن مع دعم العملات المتعددة
     */
    calculateInvoiceTotal: function() {
        // استخدام الوظيفة المحسنة إذا كانت متاحة
        if (typeof this.calculateInvoiceTotalEnhanced === 'function') {
            return this.calculateInvoiceTotalEnhanced();
        }

        const itemsContainer = document.getElementById('invoiceItems');
        if (!itemsContainer) return { subtotal: 0, tax: 0, total: 0, itemCount: 0 };

        let subtotalByCurrency = {};
        let itemCount = 0;
        let mainCurrency = this.invoiceSettings?.currentInvoiceCurrency || 'YER';

        // حساب المجموع الفرعي مع مراعاة العملات
        Array.from(itemsContainer.children).forEach(item => {
            const productSelect = item.querySelector('.product-select');
            const quantityInput = item.querySelector('.item-quantity');
            const priceInput = item.querySelector('.item-price');
            const totalInput = item.querySelector('.item-total');

            if (productSelect && quantityInput && priceInput && totalInput) {
                const productId = productSelect.value;
                const quantity = parseFloat(quantityInput.value) || 0;
                const price = parseFloat(priceInput.value) || 0;
                const total = quantity * price;

                if (productId && quantity > 0 && price > 0) {
                    // الحصول على عملة المنتج
                    const product = this.data.products[productId];
                    const productCurrency = product?.currency || 'YER';

                    // تحويل إلى العملة الرئيسية للفاتورة
                    const convertedTotal = this.convertCurrency(total, productCurrency, mainCurrency);

                    if (!subtotalByCurrency[mainCurrency]) {
                        subtotalByCurrency[mainCurrency] = 0;
                    }
                    subtotalByCurrency[mainCurrency] += convertedTotal;

                    // تحديث إجمالي العنصر
                    totalInput.value = convertedTotal.toFixed(2);

                    itemCount++;
                }
            } else {
                // الطريقة القديمة للتوافق
                const totalInput = item.querySelector('.item-total');
                if (totalInput && totalInput.value) {
                    const total = parseFloat(totalInput.value) || 0;
                    if (!subtotalByCurrency[mainCurrency]) {
                        subtotalByCurrency[mainCurrency] = 0;
                    }
                    subtotalByCurrency[mainCurrency] += total;
                    itemCount++;
                }
            }
        });

        const subtotal = subtotalByCurrency[mainCurrency] || 0;

        // حساب الخصم
        const discountElement = document.getElementById('discountAmount');
        const discount = discountElement ? parseFloat(discountElement.value) || 0 : 0;
        const validDiscount = Math.min(discount, subtotal);

        if (discountElement && discount > subtotal) {
            discountElement.value = subtotal.toFixed(2);
            this.showNotification('تم تعديل الخصم ليكون أقل من أو يساوي المجموع الفرعي', 'warning');
        }

        // حساب المجموع بعد الخصم
        const subtotalAfterDiscount = subtotal - validDiscount;

        // حساب الضريبة (مع مراعاة الإعدادات)
        let tax = 0;
        const taxEnabled = this.invoiceSettings?.taxEnabled !== false;
        const showTax = this.invoiceSettings?.showTax !== false;

        if (taxEnabled && showTax) {
            const taxRate = this.invoiceSettings?.taxRate || this.data.settings?.taxRate || 0.15;
            tax = subtotalAfterDiscount * taxRate;
        }

        // حساب الإجمالي النهائي
        const total = subtotalAfterDiscount + tax;

        // تحديث عناصر العرض
        this.updateInvoiceSummaryDisplay(subtotal, tax, total, itemCount, mainCurrency);

        // تحديث شريط التقدم إذا كان هناك عناصر
        if (itemCount > 0) {
            this.updateInvoiceProgress(100);
        }

        return { subtotal, tax, total, itemCount, discount: validDiscount, currency: mainCurrency };
    },

    /**
     * تحديث عرض ملخص الفاتورة المحسن
     */
    updateInvoiceSummaryDisplay: function(subtotal, tax, total, itemCount, currency) {
        const currencySymbol = this.getCurrencySymbol(currency || 'YER');

        // تحديث عدد العناصر
        const itemCountElement = document.getElementById('invoiceItemCount');
        if (itemCountElement) {
            itemCountElement.textContent = itemCount;
            itemCountElement.className = itemCount > 0 ? 'badge bg-success' : 'badge bg-secondary';
        }

        // تحديث المجموع الفرعي
        const subtotalElement = document.getElementById('invoiceSubtotal');
        if (subtotalElement) {
            subtotalElement.textContent = `${subtotal.toFixed(2)} ${currencySymbol}`;
            this.animateValue(subtotalElement);
        }

        // تحديث الضريبة
        const taxElement = document.getElementById('invoiceTax');
        if (taxElement) {
            const showTax = this.invoiceSettings?.showTax !== false;
            const taxEnabled = this.invoiceSettings?.taxEnabled !== false;

            if (showTax && taxEnabled) {
                taxElement.textContent = `${tax.toFixed(2)} ${currencySymbol}`;
                taxElement.parentElement.style.display = 'flex';
            } else {
                taxElement.parentElement.style.display = 'none';
            }
            this.animateValue(taxElement);
        }

        // تحديث الإجمالي
        const totalElement = document.getElementById('invoiceTotal');
        if (totalElement) {
            totalElement.textContent = `${total.toFixed(2)} ${currencySymbol}`;
            this.animateValue(totalElement);

            // تغيير لون الإجمالي حسب القيمة
            if (total > 1000) {
                totalElement.className = 'h5 text-success fw-bold';
            } else if (total > 500) {
                totalElement.className = 'h5 text-primary fw-bold';
            } else {
                totalElement.className = 'h5 text-info fw-bold';
            }
        }

        // تحديث رمز العملة في حقل الخصم
        const discountCurrencySpan = document.querySelector('#discountAmount + .input-group-text');
        if (discountCurrencySpan) {
            discountCurrencySpan.textContent = currencySymbol;
        }
    },

    /**
     * تغيير عملة الفاتورة
     */
    changeInvoiceCurrency: function(newCurrency) {
        if (!this.invoiceSettings) {
            this.invoiceSettings = { currentInvoiceCurrency: 'YER', taxEnabled: true, showTax: true };
        }

        this.invoiceSettings.currentInvoiceCurrency = newCurrency;
        this.calculateInvoiceTotal();
        this.showNotification(`تم تغيير عملة الفاتورة إلى ${this.getCurrencyName(newCurrency)}`, 'success');
    },

    /**
     * تفعيل/إلغاء الضريبة
     */
    toggleTax: function(enabled) {
        if (!this.invoiceSettings) {
            this.invoiceSettings = { currentInvoiceCurrency: 'YER', taxEnabled: true, showTax: true };
        }

        this.invoiceSettings.taxEnabled = enabled;
        this.calculateInvoiceTotal();

        const message = enabled ? 'تم تفعيل الضريبة' : 'تم إلغاء الضريبة';
        this.showNotification(message, 'info');
    },

    /**
     * إظهار/إخفاء الضريبة
     */
    toggleTaxDisplay: function(show) {
        if (!this.invoiceSettings) {
            this.invoiceSettings = { currentInvoiceCurrency: 'YER', taxEnabled: true, showTax: true };
        }

        this.invoiceSettings.showTax = show;
        this.calculateInvoiceTotal();

        const message = show ? 'تم إظهار الضريبة في الملخص' : 'تم إخفاء الضريبة من الملخص';
        this.showNotification(message, 'info');
    },

    /**
     * تحديث عملة المنتج في الفاتورة
     */
    updateProductCurrencyInInvoice: function(itemElement, productId) {
        const product = this.data.products[productId];
        if (!product) return;

        const productCurrency = product.currency || 'YER';
        const priceInput = itemElement.querySelector('.item-price');
        const currencySpan = itemElement.querySelector('.product-currency');

        if (priceInput) {
            priceInput.value = product.price || 0;
        }

        if (currencySpan) {
            currencySpan.textContent = this.getCurrencySymbol(productCurrency);
            currencySpan.setAttribute('data-currency', productCurrency);
        }

        // تحديث شارة العملة
        const currencyBadge = itemElement.querySelector('.currency-badge');
        if (currencyBadge) {
            currencyBadge.textContent = this.getCurrencyName(productCurrency);
            currencyBadge.setAttribute('data-currency', productCurrency);
        }

        // إعادة حساب الإجماليات
        this.calculateInvoiceTotal();
    },

    /**
     * تأثير بصري لتحديث القيم
     */
    animateValue: function(element) {
        if (!element) return;

        element.style.transition = 'all 0.3s ease';
        element.style.backgroundColor = '#007bff';
        element.style.color = 'white';
        element.style.transform = 'scale(1.02)';

        setTimeout(() => {
            element.style.backgroundColor = '';
            element.style.color = '';
            element.style.transform = 'scale(1)';
        }, 400);
    },

    /**
     * معاينة الفاتورة
     */
    previewInvoice: function() {
        const form = document.getElementById('createInvoiceForm');
        if (!form) return;

        const formData = new FormData(form);
        const customerId = formData.get('customerId');

        if (!customerId) {
            this.showNotification('يرجى اختيار العميل أولاً', 'warning');
            return;
        }

        const customer = this.data.customers[customerId];
        const items = this.getInvoiceItems();

        if (items.length === 0) {
            this.showNotification('يرجى إضافة عناصر للفاتورة', 'warning');
            return;
        }

        const totals = this.calculateInvoiceTotal();

        const previewHTML = `
            <div class="modal fade" id="invoicePreviewModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-eye me-2"></i>معاينة الفاتورة
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="invoice-preview">
                                <!-- رأس الفاتورة -->
                                <div class="text-center mb-4">
                                    <h3 class="text-primary">فاتورة مبيعات</h3>
                                    <p class="text-muted">رقم الفاتورة: ${formData.get('number')}</p>
                                </div>

                                <!-- معلومات العميل والتواريخ -->
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <h6>معلومات العميل:</h6>
                                        <p><strong>${customer.name}</strong><br>
                                        ${customer.phone ? customer.phone + '<br>' : ''}
                                        ${customer.email ? customer.email + '<br>' : ''}
                                        ${customer.address ? customer.address : ''}</p>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <p><strong>تاريخ الفاتورة:</strong> ${new Date(formData.get('date')).toLocaleDateString('ar-SA')}</p>
                                        <p><strong>تاريخ الاستحقاق:</strong> ${new Date(formData.get('dueDate')).toLocaleDateString('ar-SA')}</p>
                                        ${formData.get('reference') ? `<p><strong>رقم المرجع:</strong> ${formData.get('reference')}</p>` : ''}
                                    </div>
                                </div>

                                <!-- جدول العناصر -->
                                <table class="table table-bordered">
                                    <thead class="table-primary">
                                        <tr>
                                            <th>المنتج</th>
                                            <th>الكمية</th>
                                            <th>السعر</th>
                                            <th>الإجمالي</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${items.map(item => `
                                            <tr>
                                                <td>${item.productName}</td>
                                                <td>${item.quantity}</td>
                                                <td>${this.formatAmount(item.price)}</td>
                                                <td>${this.formatAmount(item.total)}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>

                                <!-- الإجماليات -->
                                <div class="row">
                                    <div class="col-md-6"></div>
                                    <div class="col-md-6">
                                        <table class="table table-sm">
                                            <tr>
                                                <td>المجموع الفرعي:</td>
                                                <td class="text-end">${this.formatAmount(totals.subtotal)}</td>
                                            </tr>
                                            ${totals.discount > 0 ? `
                                                <tr>
                                                    <td>الخصم:</td>
                                                    <td class="text-end">-${this.formatAmount(totals.discount)}</td>
                                                </tr>
                                            ` : ''}
                                            <tr>
                                                <td>الضريبة:</td>
                                                <td class="text-end">${this.formatAmount(totals.tax)}</td>
                                            </tr>
                                            <tr class="table-primary">
                                                <td><strong>الإجمالي:</strong></td>
                                                <td class="text-end"><strong>${this.formatAmount(totals.total)}</strong></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>

                                ${formData.get('notes') ? `
                                    <div class="mt-3">
                                        <h6>ملاحظات:</h6>
                                        <p>${formData.get('notes')}</p>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" onclick="window.print()">
                                <i class="fas fa-print me-1"></i>طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', previewHTML);
        const modal = new bootstrap.Modal(document.getElementById('invoicePreviewModal'));
        modal.show();

        document.getElementById('invoicePreviewModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * الحصول على عناصر الفاتورة
     */
    getInvoiceItems: function() {
        const itemsContainer = document.getElementById('invoiceItems');
        if (!itemsContainer) return [];

        const items = [];
        Array.from(itemsContainer.children).forEach(item => {
            const productSelect = item.querySelector('.item-product');
            const quantityInput = item.querySelector('.item-quantity');
            const priceInput = item.querySelector('.item-price');
            const totalInput = item.querySelector('.item-total');

            if (productSelect && productSelect.value && quantityInput && priceInput) {
                const product = this.data.products[productSelect.value];
                items.push({
                    productId: productSelect.value,
                    productName: product ? product.name : 'منتج غير محدد',
                    quantity: parseFloat(quantityInput.value) || 0,
                    price: parseFloat(priceInput.value) || 0,
                    total: parseFloat(totalInput.value) || 0
                });
            }
        });

        return items;
    },

    /**
     * حفظ الفاتورة كمسودة
     */
    saveDraftInvoice: function() {
        this.saveInvoice('draft');
    },

    /**
     * حفظ الفاتورة محسن
     */
    saveInvoice: function(status = 'pending') {
        try {
            const form = document.getElementById('createInvoiceForm');
            if (!form) {
                throw new Error('نموذج الفاتورة غير موجود');
            }

            const formData = new FormData(form);

            // التحقق من البيانات المطلوبة
            if (!formData.get('customerId')) {
                this.showNotification('يرجى اختيار العميل', 'warning');
                return;
            }

            if (!formData.get('date')) {
                this.showNotification('يرجى تحديد تاريخ الفاتورة', 'warning');
                return;
            }

            if (!formData.get('dueDate')) {
                this.showNotification('يرجى تحديد تاريخ الاستحقاق', 'warning');
                return;
            }

            // جمع عناصر الفاتورة
            const items = this.getInvoiceItems();

            if (items.length === 0) {
                this.showNotification('يرجى إضافة عنصر واحد على الأقل للفاتورة', 'warning');
                return;
            }

            // التحقق من صحة العناصر
            for (let i = 0; i < items.length; i++) {
                const item = items[i];
                if (!item.productId || item.quantity <= 0 || item.price < 0) {
                    this.showNotification(`خطأ في العنصر رقم ${i + 1}: يرجى التحقق من البيانات`, 'error');
                    return;
                }
            }

            // حساب الإجماليات
            const totals = this.calculateInvoiceTotal();

            // التحقق من تاريخ الاستحقاق
            const invoiceDate = new Date(formData.get('date'));
            const dueDate = new Date(formData.get('dueDate'));

            if (dueDate < invoiceDate) {
                this.showNotification('تاريخ الاستحقاق يجب أن يكون بعد تاريخ الفاتورة', 'warning');
                return;
            }

            // إنشاء الفاتورة
            const invoiceId = 'invoice_' + Date.now();
            const invoice = {
                id: invoiceId,
                number: formData.get('number'),
                date: formData.get('date'),
                dueDate: formData.get('dueDate'),
                customerId: formData.get('customerId'),
                reference: formData.get('reference') || '',
                items: items,
                subtotal: totals.subtotal,
                discount: totals.discount || 0,
                tax: totals.tax,
                total: totals.total,
                status: status,
                notes: formData.get('notes') || '',
                paymentTerms: parseInt(formData.get('paymentTerms')) || 30,
                sendEmail: formData.get('sendEmail') === 'on',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                createdBy: 'نظام المبيعات'
            };

            // تحديث مخزون المنتجات (إذا لم تكن مسودة)
            if (status !== 'draft') {
                this.updateProductInventory(items);
            }

            // حفظ الفاتورة
            if (!this.data.invoices) this.data.invoices = {};
            this.data.invoices[invoiceId] = invoice;

            // تحديث رقم الفاتورة التالي
            if (!this.data.settings.nextInvoiceNumber) this.data.settings.nextInvoiceNumber = 1;
            this.data.settings.nextInvoiceNumber++;

            // تحديث إحصائيات العميل
            this.updateCustomerStats(formData.get('customerId'), totals.total);

            // حفظ البيانات
            this.saveSalesData();

            // إغلاق النافذة
            const modalElement = document.getElementById('createInvoiceModal');
            if (modalElement) {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                }
            }

            // إظهار رسالة نجاح
            const statusText = status === 'draft' ? 'كمسودة' : '';
            this.showNotification(`تم حفظ الفاتورة ${statusText} بنجاح - رقم: ${invoice.number}`, 'success');

            // تحديث العرض إذا كنا في صفحة الفواتير
            if (this.data.currentView === 'invoices') {
                this.render({ view: 'invoices' });
            }

            return invoice;

        } catch (error) {
            console.error('خطأ في حفظ الفاتورة:', error);
            this.handleError('فشل في حفظ الفاتورة', error);
            return null;
        }
    },

    /**
     * تحديث مخزون المنتجات
     */
    updateProductInventory: function(items) {
        items.forEach(item => {
            const product = this.data.products[item.productId];
            if (product && product.type === 'product') {
                product.quantity = Math.max(0, (product.quantity || 0) - item.quantity);
                product.soldQuantity = (product.soldQuantity || 0) + item.quantity;
                product.updatedAt = new Date().toISOString();
            }
        });
    },

    /**
     * تحديث إحصائيات العميل
     */
    updateCustomerStats: function(customerId, amount) {
        const customer = this.data.customers[customerId];
        if (customer) {
            customer.totalPurchases = (customer.totalPurchases || 0) + amount;
            customer.lastPurchase = new Date().toISOString();
            customer.updatedAt = new Date().toISOString();
        }
    },

    /**
     * عرض نافذة إنشاء عميل جديد
     */
    showCreateCustomerModal: function() {
        const modalHTML = `
            <div class="modal fade" id="createCustomerModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-user-plus me-2"></i>إضافة عميل جديد
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="createCustomerForm">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">اسم العميل *</label>
                                        <input type="text" class="form-control" name="name" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" name="email">
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" name="phone">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">الرقم الضريبي</label>
                                        <input type="text" class="form-control" name="taxNumber">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea class="form-control" name="address" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveCustomer()">
                                <i class="fas fa-save me-1"></i>حفظ العميل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('createCustomerModal'));
        modal.show();

        document.getElementById('createCustomerModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * حفظ العميل الجديد
     */
    saveCustomer: function() {
        const form = document.getElementById('createCustomerForm');
        const formData = new FormData(form);

        if (!formData.get('name')) {
            alert('يرجى إدخال اسم العميل');
            return;
        }

        const customerId = 'customer_' + Date.now();
        const customer = {
            id: customerId,
            name: formData.get('name'),
            email: formData.get('email'),
            phone: formData.get('phone'),
            address: formData.get('address'),
            taxNumber: formData.get('taxNumber'),
            status: 'active',
            createdAt: new Date().toISOString()
        };

        if (!this.data.customers) this.data.customers = {};
        this.data.customers[customerId] = customer;
        this.saveSalesData();

        const modalElement = document.getElementById('createCustomerModal');
        if (modalElement) {
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.hide();
            }
        }

        alert('تم إضافة العميل بنجاح');
    },

    /**
     * عرض نافذة إنشاء منتج جديد
     */
    showCreateProductModal: function() {
        const modalHTML = `
            <div class="modal fade" id="createProductModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-plus-circle me-2"></i>إضافة منتج جديد
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="createProductForm">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">اسم المنتج *</label>
                                        <input type="text" class="form-control" name="name" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">السعر *</label>
                                        <input type="number" class="form-control" name="price" step="0.01" required>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">الفئة</label>
                                        <input type="text" class="form-control" name="category">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">رمز المنتج</label>
                                        <input type="text" class="form-control" name="sku">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الوصف</label>
                                    <textarea class="form-control" name="description" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveProduct()">
                                <i class="fas fa-save me-1"></i>حفظ المنتج
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('createProductModal'));
        modal.show();

        document.getElementById('createProductModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * حفظ المنتج الجديد
     */
    saveProduct: function() {
        const form = document.getElementById('createProductForm');
        const formData = new FormData(form);

        if (!formData.get('name') || !formData.get('price')) {
            alert('يرجى إدخال اسم المنتج والسعر');
            return;
        }

        const productId = 'product_' + Date.now();
        const product = {
            id: productId,
            name: formData.get('name'),
            description: formData.get('description'),
            price: parseFloat(formData.get('price')),
            category: formData.get('category'),
            sku: formData.get('sku'),
            status: 'active',
            createdAt: new Date().toISOString()
        };

        if (!this.data.products) this.data.products = {};
        this.data.products[productId] = product;
        this.saveSalesData();

        const modalElement = document.getElementById('createProductModal');
        if (modalElement) {
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.hide();
            }
        }

        alert('تم إضافة المنتج بنجاح');
    },

    /**
     * عرض نافذة التقارير
     */
    showReportsModal: function() {
        alert('ميزة التقارير متاحة في النسخة الكاملة');
    },

    /**
     * عرض صفحة الفواتير المحسنة
     */
    renderInvoicesView: function() {
        const invoices = Object.values(this.data.invoices || {});
        const totalInvoices = invoices.length;
        const totalAmount = invoices.reduce((sum, inv) => sum + (inv.total || 0), 0);
        const paidInvoices = invoices.filter(inv => inv.status === 'paid').length;
        const pendingInvoices = invoices.filter(inv => inv.status === 'pending').length;
        const overdueInvoices = invoices.filter(inv => {
            if (inv.status !== 'pending') return false;
            return new Date(inv.dueDate) < new Date();
        }).length;

        return `
            <div class="container-fluid">
                <!-- رأس الصفحة -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2><i class="fas fa-file-invoice me-2 text-primary"></i>إدارة الفواتير</h2>
                        <p class="text-muted mb-0">إدارة شاملة لجميع الفواتير مع أدوات متقدمة</p>
                    </div>
                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="window.SalesComponent.showCreateInvoiceModal()">
                            <i class="fas fa-plus me-1"></i>فاتورة جديدة
                        </button>
                        <button class="btn btn-outline-info" onclick="window.SalesComponent.refreshInvoices()">
                            <i class="fas fa-sync me-1"></i>تحديث
                        </button>
                        <button class="btn btn-outline-success" onclick="window.SalesComponent.exportInvoices()">
                            <i class="fas fa-download me-1"></i>تصدير
                        </button>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0">${totalInvoices}</h4>
                                        <p class="mb-0">إجمالي الفواتير</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-file-invoice fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0">${paidInvoices}</h4>
                                        <p class="mb-0">فواتير مدفوعة</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-check-circle fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0">${pendingInvoices}</h4>
                                        <p class="mb-0">فواتير معلقة</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-clock fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0">${overdueInvoices}</h4>
                                        <p class="mb-0">فواتير متأخرة</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أدوات البحث والفلترة -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-search me-2"></i>البحث والفلترة</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label">البحث</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" id="invoice-search"
                                           placeholder="رقم الفاتورة أو اسم العميل..."
                                           onkeyup="window.SalesComponent.filterInvoices()">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">الحالة</label>
                                <select class="form-control" id="status-filter" onchange="window.SalesComponent.filterInvoices()">
                                    <option value="">جميع الحالات</option>
                                    <option value="draft">مسودة</option>
                                    <option value="pending">معلقة</option>
                                    <option value="paid">مدفوعة</option>
                                    <option value="overdue">متأخرة</option>
                                    <option value="cancelled">ملغية</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="date-from" onchange="window.SalesComponent.filterInvoices()">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="date-to" onchange="window.SalesComponent.filterInvoices()">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">إجراءات</label>
                                <div class="btn-group w-100">
                                    <button class="btn btn-outline-secondary" onclick="window.SalesComponent.clearFilters()">
                                        <i class="fas fa-times me-1"></i>مسح
                                    </button>
                                    <button class="btn btn-outline-primary" onclick="window.SalesComponent.filterInvoices()">
                                        <i class="fas fa-filter me-1"></i>تطبيق
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول الفواتير -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="fas fa-list me-2"></i>قائمة الفواتير</h6>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-info" onclick="window.SalesComponent.selectAllInvoices()">
                                <i class="fas fa-check-square me-1"></i>تحديد الكل
                            </button>
                            <button class="btn btn-outline-warning" onclick="window.SalesComponent.bulkActions()">
                                <i class="fas fa-cogs me-1"></i>إجراءات مجمعة
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover" id="invoices-table">
                                <thead class="table-light">
                                    <tr>
                                        <th width="40">
                                            <input type="checkbox" id="select-all-invoices" onchange="window.SalesComponent.toggleSelectAll()">
                                        </th>
                                        <th>رقم الفاتورة</th>
                                        <th>التاريخ</th>
                                        <th>تاريخ الاستحقاق</th>
                                        <th>العميل</th>
                                        <th>المبلغ</th>
                                        <th>الحالة</th>
                                        <th width="200">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="invoices-tbody">
                                    ${this.renderInvoicesRows()}
                                </tbody>
                            </table>
                        </div>

                        <!-- رسالة عدم وجود فواتير -->
                        <div id="no-invoices-message" class="text-center py-5" style="display: none;">
                            <i class="fas fa-file-invoice fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد فواتير</h5>
                            <p class="text-muted">ابدأ بإنشاء أول فاتورة</p>
                            <button class="btn btn-primary" onclick="window.SalesComponent.showCreateInvoiceModal()">
                                <i class="fas fa-plus me-1"></i>إنشاء فاتورة جديدة
                            </button>
                        </div>
                    </div>
                </div>

                <!-- إجمالي المبلغ -->
                <div class="row mt-3">
                    <div class="col-md-6"></div>
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <strong>إجمالي قيمة الفواتير:</strong>
                                    <strong class="text-primary">${this.formatAmount(totalAmount)}</strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض صفوف الفواتير المحسنة
     */
    renderInvoicesRows: function() {
        const invoices = Object.values(this.data.invoices || {});

        if (invoices.length === 0) {
            return '<tr><td colspan="8" class="text-center text-muted py-4">لا توجد فواتير متاحة</td></tr>';
        }

        // ترتيب الفواتير حسب التاريخ (الأحدث أولاً)
        const sortedInvoices = invoices.sort((a, b) => new Date(b.date) - new Date(a.date));

        return sortedInvoices.map(invoice => {
            const customer = this.data.customers[invoice.customerId];
            const isOverdue = invoice.status === 'pending' && new Date(invoice.dueDate) < new Date();
            const daysDiff = Math.ceil((new Date(invoice.dueDate) - new Date()) / (1000 * 60 * 60 * 24));

            return `
                <tr class="${isOverdue ? 'table-danger' : ''}" data-invoice-id="${invoice.id}">
                    <td>
                        <input type="checkbox" class="invoice-checkbox" value="${invoice.id}">
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <strong class="text-primary">${invoice.number}</strong>
                            ${invoice.reference ? `<small class="text-muted ms-2">(${invoice.reference})</small>` : ''}
                        </div>
                    </td>
                    <td>
                        <span class="text-nowrap">${new Date(invoice.date).toLocaleDateString('ar-SA')}</span>
                    </td>
                    <td>
                        <span class="text-nowrap ${isOverdue ? 'text-danger fw-bold' : ''}">${new Date(invoice.dueDate).toLocaleDateString('ar-SA')}</span>
                        ${isOverdue ? `<br><small class="text-danger">متأخر ${Math.abs(daysDiff)} يوم</small>` :
                          daysDiff <= 7 && daysDiff > 0 ? `<br><small class="text-warning">يستحق خلال ${daysDiff} يوم</small>` : ''}
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-user me-2 text-muted"></i>
                            <div>
                                <div class="fw-bold">${customer?.name || 'غير محدد'}</div>
                                ${customer?.phone ? `<small class="text-muted">${customer.phone}</small>` : ''}
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="text-end">
                            <strong class="text-success">${this.formatAmount(invoice.total)}</strong>
                            ${invoice.discount > 0 ? `<br><small class="text-muted">خصم: ${this.formatAmount(invoice.discount)}</small>` : ''}
                        </div>
                    </td>
                    <td>
                        <span class="badge bg-${this.getStatusColor(invoice.status)} fs-6">
                            ${this.getStatusLabel(invoice.status)}
                        </span>
                        ${isOverdue ? '<br><span class="badge bg-danger mt-1">متأخر</span>' : ''}
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="window.SalesComponent.viewInvoice('${invoice.id}')"
                                    title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-secondary" onclick="window.SalesComponent.editInvoice('${invoice.id}')"
                                    title="تعديل" ${invoice.status === 'paid' ? 'disabled' : ''}>
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-success" onclick="window.SalesComponent.printInvoice('${invoice.id}')"
                                    title="طباعة">
                                <i class="fas fa-print"></i>
                            </button>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-info dropdown-toggle" data-bs-toggle="dropdown" title="المزيد">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.duplicateInvoice('${invoice.id}')">
                                        <i class="fas fa-copy me-2"></i>نسخ الفاتورة
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.sendInvoiceEmail('${invoice.id}')">
                                        <i class="fas fa-envelope me-2"></i>إرسال بالبريد
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.downloadInvoicePDF('${invoice.id}')">
                                        <i class="fas fa-file-pdf me-2"></i>تحميل PDF
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    ${invoice.status === 'pending' ? `
                                        <li><a class="dropdown-item text-success" href="#" onclick="window.SalesComponent.markAsPaid('${invoice.id}')">
                                            <i class="fas fa-check me-2"></i>تسجيل كمدفوع
                                        </a></li>
                                    ` : ''}
                                    ${invoice.status !== 'cancelled' ? `
                                        <li><a class="dropdown-item text-warning" href="#" onclick="window.SalesComponent.cancelInvoice('${invoice.id}')">
                                            <i class="fas fa-ban me-2"></i>إلغاء الفاتورة
                                        </a></li>
                                    ` : ''}
                                    ${invoice.status !== 'paid' ? `
                                        <li><a class="dropdown-item text-danger" href="#" onclick="window.SalesComponent.deleteInvoice('${invoice.id}')">
                                            <i class="fas fa-trash me-2"></i>حذف الفاتورة
                                        </a></li>
                                    ` : ''}
                                </ul>
                            </div>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
    },

    /**
     * عرض تفاصيل الفاتورة المحسنة
     */
    viewInvoice: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            this.showNotification('الفاتورة غير موجودة', 'error');
            return;
        }

        const customer = this.data.customers[invoice.customerId];
        const isOverdue = invoice.status === 'pending' && new Date(invoice.dueDate) < new Date();
        const daysDiff = Math.ceil((new Date(invoice.dueDate) - new Date()) / (1000 * 60 * 60 * 24));

        const modalHTML = `
            <div class="modal fade" id="viewInvoiceModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-file-invoice me-2"></i>تفاصيل الفاتورة ${invoice.number}
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <!-- معلومات الفاتورة الأساسية -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات الفاتورة</h6>
                                        </div>
                                        <div class="card-body">
                                            <table class="table table-borderless table-sm">
                                                <tr>
                                                    <td><strong>رقم الفاتورة:</strong></td>
                                                    <td>${invoice.number}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>تاريخ الإنشاء:</strong></td>
                                                    <td>${new Date(invoice.date).toLocaleDateString('ar-SA')}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>تاريخ الاستحقاق:</strong></td>
                                                    <td class="${isOverdue ? 'text-danger fw-bold' : ''}">
                                                        ${new Date(invoice.dueDate).toLocaleDateString('ar-SA')}
                                                        ${isOverdue ? `<br><small class="text-danger">متأخر ${Math.abs(daysDiff)} يوم</small>` : ''}
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><strong>الحالة:</strong></td>
                                                    <td>
                                                        <span class="badge bg-${this.getStatusColor(invoice.status)} fs-6">
                                                            ${this.getStatusLabel(invoice.status)}
                                                        </span>
                                                        ${isOverdue ? '<span class="badge bg-danger ms-2">متأخر</span>' : ''}
                                                    </td>
                                                </tr>
                                                ${invoice.reference ? `
                                                    <tr>
                                                        <td><strong>رقم المرجع:</strong></td>
                                                        <td>${invoice.reference}</td>
                                                    </tr>
                                                ` : ''}
                                                <tr>
                                                    <td><strong>تاريخ الإنشاء:</strong></td>
                                                    <td>${new Date(invoice.createdAt).toLocaleString('ar-SA')}</td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0"><i class="fas fa-user me-2"></i>معلومات العميل</h6>
                                        </div>
                                        <div class="card-body">
                                            ${customer ? `
                                                <div class="d-flex align-items-center mb-3">
                                                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3"
                                                         style="width: 50px; height: 50px;">
                                                        <i class="fas fa-user text-white"></i>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0">${customer.name}</h6>
                                                        <small class="text-muted">عميل</small>
                                                    </div>
                                                </div>
                                                <table class="table table-borderless table-sm">
                                                    ${customer.phone ? `
                                                        <tr>
                                                            <td><i class="fas fa-phone me-2"></i><strong>الهاتف:</strong></td>
                                                            <td>${customer.phone}</td>
                                                        </tr>
                                                    ` : ''}
                                                    ${customer.email ? `
                                                        <tr>
                                                            <td><i class="fas fa-envelope me-2"></i><strong>البريد:</strong></td>
                                                            <td>${customer.email}</td>
                                                        </tr>
                                                    ` : ''}
                                                    ${customer.address ? `
                                                        <tr>
                                                            <td><i class="fas fa-map-marker-alt me-2"></i><strong>العنوان:</strong></td>
                                                            <td>${customer.address}</td>
                                                        </tr>
                                                    ` : ''}
                                                </table>
                                            ` : `
                                                <div class="text-center text-muted py-3">
                                                    <i class="fas fa-user-slash fa-2x mb-2"></i>
                                                    <p>معلومات العميل غير متاحة</p>
                                                </div>
                                            `}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- عناصر الفاتورة -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-shopping-cart me-2"></i>عناصر الفاتورة</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>#</th>
                                                    <th>المنتج/الخدمة</th>
                                                    <th>الكمية</th>
                                                    <th>السعر</th>
                                                    <th>الإجمالي</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${invoice.items.map((item, index) => `
                                                    <tr>
                                                        <td>${index + 1}</td>
                                                        <td>
                                                            <strong>${item.name || item.productName}</strong>
                                                            ${item.description ? `<br><small class="text-muted">${item.description}</small>` : ''}
                                                        </td>
                                                        <td class="text-center">${item.quantity}</td>
                                                        <td class="text-end">${this.formatAmount(item.price)}</td>
                                                        <td class="text-end"><strong>${this.formatAmount(item.total)}</strong></td>
                                                    </tr>
                                                `).join('')}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <!-- الإجماليات -->
                            <div class="row">
                                <div class="col-md-6">
                                    ${invoice.notes ? `
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0"><i class="fas fa-sticky-note me-2"></i>ملاحظات</h6>
                                            </div>
                                            <div class="card-body">
                                                <p class="mb-0">${invoice.notes}</p>
                                            </div>
                                        </div>
                                    ` : ''}
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0"><i class="fas fa-calculator me-2"></i>ملخص المبالغ</h6>
                                        </div>
                                        <div class="card-body">
                                            <table class="table table-borderless">
                                                <tr>
                                                    <td>المجموع الفرعي:</td>
                                                    <td class="text-end">${this.formatAmount(invoice.subtotal)}</td>
                                                </tr>
                                                ${invoice.discount > 0 ? `
                                                    <tr>
                                                        <td>الخصم:</td>
                                                        <td class="text-end text-danger">-${this.formatAmount(invoice.discount)}</td>
                                                    </tr>
                                                ` : ''}
                                                <tr>
                                                    <td>الضريبة:</td>
                                                    <td class="text-end">${this.formatAmount(invoice.tax)}</td>
                                                </tr>
                                                <tr class="table-primary">
                                                    <td><strong>الإجمالي النهائي:</strong></td>
                                                    <td class="text-end"><strong class="h5 text-primary">${this.formatAmount(invoice.total)}</strong></td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i>إغلاق
                            </button>
                            <button type="button" class="btn btn-info" onclick="window.SalesComponent.editInvoice('${invoice.id}')">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </button>
                            <button type="button" class="btn btn-success" onclick="window.SalesComponent.printInvoice('${invoice.id}')">
                                <i class="fas fa-print me-1"></i>طباعة
                            </button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.downloadInvoicePDF('${invoice.id}')">
                                <i class="fas fa-download me-1"></i>تحميل PDF
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('viewInvoiceModal'));
        modal.show();

        document.getElementById('viewInvoiceModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * طباعة الفاتورة المحسنة
     */
    printInvoice: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            this.showNotification('الفاتورة غير موجودة', 'error');
            return;
        }

        const customer = this.data.customers[invoice.customerId];
        const companyInfo = this.data.settings?.companyInfo || {};

        // إنشاء نافذة الطباعة
        const printWindow = window.open('', '_blank', 'width=800,height=600');

        const printHTML = `
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>فاتورة ${invoice.number}</title>
                <style>
                    body {
                        font-family: 'Arial', sans-serif;
                        margin: 0;
                        padding: 20px;
                        background: white;
                        color: #333;
                        line-height: 1.6;
                    }
                    .invoice-container {
                        max-width: 800px;
                        margin: 0 auto;
                        background: white;
                        border: 1px solid #ddd;
                        border-radius: 8px;
                        overflow: hidden;
                    }
                    .invoice-header {
                        background: linear-gradient(135deg, #007bff, #0056b3);
                        color: white;
                        padding: 30px;
                        text-align: center;
                    }
                    .invoice-header h1 {
                        margin: 0;
                        font-size: 2.5em;
                        font-weight: bold;
                    }
                    .invoice-header p {
                        margin: 10px 0 0 0;
                        font-size: 1.2em;
                        opacity: 0.9;
                    }
                    .invoice-body {
                        padding: 30px;
                    }
                    .invoice-info {
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 30px;
                        gap: 30px;
                    }
                    .company-info, .customer-info {
                        flex: 1;
                    }
                    .company-info {
                        text-align: right;
                    }
                    .customer-info {
                        text-align: left;
                    }
                    .info-box {
                        background: #f8f9fa;
                        padding: 20px;
                        border-radius: 8px;
                        border-left: 4px solid #007bff;
                    }
                    .info-box h3 {
                        margin: 0 0 15px 0;
                        color: #007bff;
                        font-size: 1.3em;
                    }
                    .info-box p {
                        margin: 5px 0;
                        color: #666;
                    }
                    .invoice-details {
                        background: #e9ecef;
                        padding: 20px;
                        border-radius: 8px;
                        margin: 30px 0;
                        text-align: center;
                    }
                    .invoice-details table {
                        width: 100%;
                        margin: 0;
                    }
                    .invoice-details td {
                        padding: 8px 15px;
                        font-weight: bold;
                    }
                    .items-table {
                        width: 100%;
                        border-collapse: collapse;
                        margin: 30px 0;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                        border-radius: 8px;
                        overflow: hidden;
                    }
                    .items-table th {
                        background: #007bff;
                        color: white;
                        padding: 15px;
                        text-align: center;
                        font-weight: bold;
                    }
                    .items-table td {
                        padding: 12px 15px;
                        border-bottom: 1px solid #eee;
                        text-align: center;
                    }
                    .items-table tr:nth-child(even) {
                        background: #f8f9fa;
                    }
                    .items-table tr:hover {
                        background: #e3f2fd;
                    }
                    .totals-section {
                        margin-top: 30px;
                        display: flex;
                        justify-content: flex-end;
                    }
                    .totals-table {
                        width: 300px;
                        border-collapse: collapse;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                        border-radius: 8px;
                        overflow: hidden;
                    }
                    .totals-table td {
                        padding: 12px 20px;
                        border-bottom: 1px solid #eee;
                    }
                    .totals-table .total-row {
                        background: #007bff;
                        color: white;
                        font-weight: bold;
                        font-size: 1.2em;
                    }
                    .notes-section {
                        margin-top: 30px;
                        padding: 20px;
                        background: #f8f9fa;
                        border-radius: 8px;
                        border-left: 4px solid #28a745;
                    }
                    .notes-section h4 {
                        margin: 0 0 10px 0;
                        color: #28a745;
                    }
                    .footer {
                        text-align: center;
                        margin-top: 40px;
                        padding: 20px;
                        background: #f8f9fa;
                        border-radius: 8px;
                        color: #666;
                    }
                    .status-badge {
                        display: inline-block;
                        padding: 8px 16px;
                        border-radius: 20px;
                        font-weight: bold;
                        text-transform: uppercase;
                        font-size: 0.9em;
                    }
                    .status-paid { background: #d4edda; color: #155724; }
                    .status-pending { background: #fff3cd; color: #856404; }
                    .status-overdue { background: #f8d7da; color: #721c24; }
                    .status-draft { background: #d1ecf1; color: #0c5460; }
                    .status-cancelled { background: #f5c6cb; color: #721c24; }

                    @media print {
                        body { margin: 0; padding: 0; }
                        .invoice-container { border: none; box-shadow: none; }
                        .no-print { display: none !important; }
                    }
                </style>
            </head>
            <body>
                <div class="invoice-container">
                    <!-- رأس الفاتورة -->
                    <div class="invoice-header">
                        <h1>فاتورة مبيعات</h1>
                        <p>رقم الفاتورة: ${invoice.number}</p>
                    </div>

                    <div class="invoice-body">
                        <!-- معلومات الشركة والعميل -->
                        <div class="invoice-info">
                            <div class="company-info">
                                <div class="info-box">
                                    <h3>معلومات الشركة</h3>
                                    <p><strong>${companyInfo.name || 'اسم الشركة'}</strong></p>
                                    ${companyInfo.address ? `<p>${companyInfo.address}</p>` : ''}
                                    ${companyInfo.phone ? `<p>هاتف: ${companyInfo.phone}</p>` : ''}
                                    ${companyInfo.email ? `<p>بريد: ${companyInfo.email}</p>` : ''}
                                    ${companyInfo.taxNumber ? `<p>الرقم الضريبي: ${companyInfo.taxNumber}</p>` : ''}
                                </div>
                            </div>
                            <div class="customer-info">
                                <div class="info-box">
                                    <h3>معلومات العميل</h3>
                                    <p><strong>${customer?.name || 'غير محدد'}</strong></p>
                                    ${customer?.phone ? `<p>هاتف: ${customer.phone}</p>` : ''}
                                    ${customer?.email ? `<p>بريد: ${customer.email}</p>` : ''}
                                    ${customer?.address ? `<p>عنوان: ${customer.address}</p>` : ''}
                                </div>
                            </div>
                        </div>

                        <!-- تفاصيل الفاتورة -->
                        <div class="invoice-details">
                            <table>
                                <tr>
                                    <td>تاريخ الفاتورة: <strong>${new Date(invoice.date).toLocaleDateString('ar-SA')}</strong></td>
                                    <td>تاريخ الاستحقاق: <strong>${new Date(invoice.dueDate).toLocaleDateString('ar-SA')}</strong></td>
                                    <td>الحالة: <span class="status-badge status-${invoice.status}">${this.getStatusLabel(invoice.status)}</span></td>
                                </tr>
                            </table>
                        </div>

                        <!-- جدول العناصر -->
                        <table class="items-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>المنتج/الخدمة</th>
                                    <th>الكمية</th>
                                    <th>السعر</th>
                                    <th>الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${invoice.items.map((item, index) => `
                                    <tr>
                                        <td>${index + 1}</td>
                                        <td style="text-align: right;">
                                            <strong>${item.name || item.productName}</strong>
                                            ${item.description ? `<br><small style="color: #666;">${item.description}</small>` : ''}
                                        </td>
                                        <td>${item.quantity}</td>
                                        <td>${this.formatAmount(item.price)}</td>
                                        <td><strong>${this.formatAmount(item.total)}</strong></td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>

                        <!-- الإجماليات -->
                        <div class="totals-section">
                            <table class="totals-table">
                                <tr>
                                    <td>المجموع الفرعي:</td>
                                    <td style="text-align: left;"><strong>${this.formatAmount(invoice.subtotal)}</strong></td>
                                </tr>
                                ${invoice.discount > 0 ? `
                                    <tr>
                                        <td>الخصم:</td>
                                        <td style="text-align: left; color: #dc3545;"><strong>-${this.formatAmount(invoice.discount)}</strong></td>
                                    </tr>
                                ` : ''}
                                <tr>
                                    <td>الضريبة:</td>
                                    <td style="text-align: left;"><strong>${this.formatAmount(invoice.tax)}</strong></td>
                                </tr>
                                <tr class="total-row">
                                    <td>الإجمالي النهائي:</td>
                                    <td style="text-align: left;"><strong>${this.formatAmount(invoice.total)}</strong></td>
                                </tr>
                            </table>
                        </div>

                        <!-- الملاحظات -->
                        ${invoice.notes ? `
                            <div class="notes-section">
                                <h4>ملاحظات:</h4>
                                <p>${invoice.notes}</p>
                            </div>
                        ` : ''}

                        <!-- تذييل الفاتورة -->
                        <div class="footer">
                            <p>شكراً لتعاملكم معنا</p>
                            <p>تم إنشاء هذه الفاتورة في: ${new Date().toLocaleString('ar-SA')}</p>
                        </div>
                    </div>
                </div>

                <script>
                    window.onload = function() {
                        window.print();
                        window.onafterprint = function() {
                            window.close();
                        };
                    };
                </script>
            </body>
            </html>
        `;

        printWindow.document.write(printHTML);
        printWindow.document.close();
    },

    /**
     * تعديل الفاتورة
     */
    editInvoice: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            this.showNotification('الفاتورة غير موجودة', 'error');
            return;
        }

        if (invoice.status === 'paid') {
            this.showNotification('لا يمكن تعديل فاتورة مدفوعة', 'warning');
            return;
        }

        // إغلاق أي نوافذ مفتوحة
        const existingModal = document.getElementById('createInvoiceModal');
        if (existingModal) {
            existingModal.remove();
        }

        // فتح نافذة التعديل (مشابهة لنافذة الإنشاء)
        this.showEditInvoiceModal(invoice);
    },

    /**
     * عرض نافذة تعديل الفاتورة
     */
    showEditInvoiceModal: function(invoice) {
        const customers = Object.values(this.data.customers || {});
        const products = Object.values(this.data.products || {});

        const modalHTML = `
            <div class="modal fade" id="editInvoiceModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header bg-warning text-dark">
                            <h5 class="modal-title">
                                <i class="fas fa-edit me-2"></i>تعديل الفاتورة ${invoice.number}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="editInvoiceForm">
                                <input type="hidden" name="invoiceId" value="${invoice.id}">

                                <!-- معلومات الفاتورة -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات الفاتورة</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <label class="form-label">رقم الفاتورة</label>
                                                <input type="text" class="form-control" name="number" value="${invoice.number}" readonly>
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">تاريخ الفاتورة *</label>
                                                <input type="date" class="form-control" name="date" value="${invoice.date}" required>
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">تاريخ الاستحقاق *</label>
                                                <input type="date" class="form-control" name="dueDate" value="${invoice.dueDate}" required>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- معلومات العميل -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="fas fa-user me-2"></i>معلومات العميل</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <label class="form-label">العميل *</label>
                                                <select class="form-control" name="customerId" required>
                                                    <option value="">اختر العميل</option>
                                                    ${customers.map(customer => `
                                                        <option value="${customer.id}" ${customer.id === invoice.customerId ? 'selected' : ''}>
                                                            ${customer.name}
                                                        </option>
                                                    `).join('')}
                                                </select>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">رقم المرجع</label>
                                                <input type="text" class="form-control" name="reference" value="${invoice.reference || ''}" placeholder="رقم المرجع (اختياري)">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- عناصر الفاتورة -->
                                <div class="card mb-4">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0"><i class="fas fa-shopping-cart me-2"></i>عناصر الفاتورة</h6>
                                        <button type="button" class="btn btn-sm btn-primary" onclick="window.SalesComponent.addEditInvoiceItem()">
                                            <i class="fas fa-plus me-1"></i>إضافة عنصر
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row mb-2 bg-light p-2 rounded">
                                            <div class="col-md-4"><strong>المنتج</strong></div>
                                            <div class="col-md-2"><strong>الكمية</strong></div>
                                            <div class="col-md-2"><strong>السعر</strong></div>
                                            <div class="col-md-2"><strong>الإجمالي</strong></div>
                                            <div class="col-md-2"><strong>إجراءات</strong></div>
                                        </div>
                                        <div id="editInvoiceItems">
                                            ${this.renderEditInvoiceItems(invoice.items, products)}
                                        </div>
                                    </div>
                                </div>

                                <!-- الملاحظات والإجماليات -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0"><i class="fas fa-sticky-note me-2"></i>ملاحظات</h6>
                                            </div>
                                            <div class="card-body">
                                                <textarea class="form-control" name="notes" rows="4" placeholder="ملاحظات الفاتورة...">${invoice.notes || ''}</textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0"><i class="fas fa-calculator me-2"></i>ملخص الفاتورة</h6>
                                            </div>
                                            <div class="card-body">
                                                <table class="table table-sm">
                                                    <tr>
                                                        <td>المجموع الفرعي:</td>
                                                        <td class="text-end" id="editInvoiceSubtotal">${this.formatAmount(invoice.subtotal)}</td>
                                                    </tr>
                                                    <tr>
                                                        <td>الخصم:</td>
                                                        <td class="text-end">
                                                            <input type="number" class="form-control form-control-sm" id="editDiscountAmount"
                                                                   value="${invoice.discount || 0}" min="0" step="0.01"
                                                                   onchange="window.SalesComponent.calculateEditInvoiceTotal()">
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>الضريبة:</td>
                                                        <td class="text-end" id="editInvoiceTax">${this.formatAmount(invoice.tax)}</td>
                                                    </tr>
                                                    <tr class="table-primary">
                                                        <td><strong>الإجمالي:</strong></td>
                                                        <td class="text-end" id="editInvoiceTotal"><strong>${this.formatAmount(invoice.total)}</strong></td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </button>
                            <button type="button" class="btn btn-warning" onclick="window.SalesComponent.updateInvoice()">
                                <i class="fas fa-save me-1"></i>حفظ التعديلات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('editInvoiceModal'));
        modal.show();

        document.getElementById('editInvoiceModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * عرض عناصر الفاتورة للتعديل
     */
    renderEditInvoiceItems: function(items, products) {
        return items.map((item, index) => {
            const itemId = 'edit_item_' + index;
            return `
                <div class="invoice-item row mb-2 p-2 border rounded" data-item-id="${itemId}">
                    <div class="col-md-4">
                        <select class="form-control item-product" name="items[${index}][productId]"
                                onchange="window.SalesComponent.updateEditItemFromProduct(this)">
                            <option value="">اختر المنتج</option>
                            ${products.map(product => `
                                <option value="${product.id}" data-price="${product.price}" data-name="${product.name}"
                                        data-currency="${product.currency || 'YER'}" ${product.id === item.productId ? 'selected' : ''}>
                                    ${product.name} - ${this.formatAmount(product.price, product.currency)}
                                </option>
                            `).join('')}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <input type="number" class="form-control item-quantity" name="items[${index}][quantity]"
                               value="${item.quantity}" min="1"
                               onchange="window.SalesComponent.calculateEditItemTotal(this)">
                    </div>
                    <div class="col-md-2">
                        <input type="number" class="form-control item-price" name="items[${index}][price]"
                               value="${item.price}" step="0.01" min="0"
                               onchange="window.SalesComponent.calculateEditItemTotal(this)">
                    </div>
                    <div class="col-md-2">
                        <input type="number" class="form-control item-total" name="items[${index}][total]"
                               value="${item.total}" readonly>
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-outline-danger btn-sm"
                                onclick="window.SalesComponent.removeEditInvoiceItem(this.closest('.invoice-item'))"
                                title="حذف العنصر">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        }).join('');
    },

    /**
     * تحديث الفاتورة
     */
    updateInvoice: function() {
        try {
            const form = document.getElementById('editInvoiceForm');
            const formData = new FormData(form);
            const invoiceId = formData.get('invoiceId');

            const invoice = this.data.invoices[invoiceId];
            if (!invoice) {
                throw new Error('الفاتورة غير موجودة');
            }

            // التحقق من البيانات
            if (!formData.get('customerId')) {
                this.showNotification('يرجى اختيار العميل', 'warning');
                return;
            }

            // جمع العناصر
            const items = this.getEditInvoiceItems();
            if (items.length === 0) {
                this.showNotification('يرجى إضافة عنصر واحد على الأقل', 'warning');
                return;
            }

            // حساب الإجماليات
            const totals = this.calculateEditInvoiceTotal();

            // تحديث الفاتورة
            invoice.date = formData.get('date');
            invoice.dueDate = formData.get('dueDate');
            invoice.customerId = formData.get('customerId');
            invoice.reference = formData.get('reference') || '';
            invoice.items = items;
            invoice.subtotal = totals.subtotal;
            invoice.discount = totals.discount || 0;
            invoice.tax = totals.tax;
            invoice.total = totals.total;
            invoice.notes = formData.get('notes') || '';
            invoice.updatedAt = new Date().toISOString();

            // حفظ البيانات
            this.saveSalesData();

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('editInvoiceModal'));
            modal.hide();

            // تحديث العرض
            this.render({ view: 'invoices' });

            this.showNotification(`تم تحديث الفاتورة ${invoice.number} بنجاح`, 'success');

        } catch (error) {
            console.error('خطأ في تحديث الفاتورة:', error);
            this.handleError('فشل في تحديث الفاتورة', error);
        }
    },

    /**
     * الحصول على عناصر الفاتورة المعدلة
     */
    getEditInvoiceItems: function() {
        const itemsContainer = document.getElementById('editInvoiceItems');
        if (!itemsContainer) return [];

        const items = [];
        Array.from(itemsContainer.children).forEach(item => {
            const productSelect = item.querySelector('.item-product');
            const quantityInput = item.querySelector('.item-quantity');
            const priceInput = item.querySelector('.item-price');

            if (productSelect && productSelect.value && quantityInput && priceInput) {
                const product = this.data.products[productSelect.value];
                items.push({
                    productId: productSelect.value,
                    name: product ? product.name : 'منتج غير محدد',
                    quantity: parseFloat(quantityInput.value) || 0,
                    price: parseFloat(priceInput.value) || 0,
                    total: parseFloat(quantityInput.value || 0) * parseFloat(priceInput.value || 0)
                });
            }
        });

        return items;
    },

    /**
     * فلترة الفواتير
     */
    filterInvoices: function() {
        const searchTerm = document.getElementById('invoice-search')?.value.toLowerCase() || '';
        const statusFilter = document.getElementById('status-filter')?.value || '';
        const dateFrom = document.getElementById('date-from')?.value || '';
        const dateTo = document.getElementById('date-to')?.value || '';

        const invoices = Object.values(this.data.invoices || {});
        let filteredInvoices = invoices;

        // فلترة بالبحث
        if (searchTerm) {
            filteredInvoices = filteredInvoices.filter(invoice => {
                const customer = this.data.customers[invoice.customerId];
                return invoice.number.toLowerCase().includes(searchTerm) ||
                       (customer?.name.toLowerCase().includes(searchTerm)) ||
                       (invoice.reference && invoice.reference.toLowerCase().includes(searchTerm));
            });
        }

        // فلترة بالحالة
        if (statusFilter) {
            if (statusFilter === 'overdue') {
                filteredInvoices = filteredInvoices.filter(invoice => {
                    return invoice.status === 'pending' && new Date(invoice.dueDate) < new Date();
                });
            } else {
                filteredInvoices = filteredInvoices.filter(invoice => invoice.status === statusFilter);
            }
        }

        // فلترة بالتاريخ
        if (dateFrom) {
            filteredInvoices = filteredInvoices.filter(invoice =>
                new Date(invoice.date) >= new Date(dateFrom)
            );
        }

        if (dateTo) {
            filteredInvoices = filteredInvoices.filter(invoice =>
                new Date(invoice.date) <= new Date(dateTo)
            );
        }

        // تحديث الجدول
        this.updateInvoicesTable(filteredInvoices);
    },

    /**
     * تحديث جدول الفواتير
     */
    updateInvoicesTable: function(invoices) {
        const tbody = document.getElementById('invoices-tbody');
        const noInvoicesMessage = document.getElementById('no-invoices-message');

        if (!tbody) return;

        if (invoices.length === 0) {
            tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted py-4">لا توجد فواتير تطابق معايير البحث</td></tr>';
            if (noInvoicesMessage) noInvoicesMessage.style.display = 'block';
        } else {
            // ترتيب الفواتير
            const sortedInvoices = invoices.sort((a, b) => new Date(b.date) - new Date(a.date));

            tbody.innerHTML = sortedInvoices.map(invoice => {
                const customer = this.data.customers[invoice.customerId];
                const isOverdue = invoice.status === 'pending' && new Date(invoice.dueDate) < new Date();
                const daysDiff = Math.ceil((new Date(invoice.dueDate) - new Date()) / (1000 * 60 * 60 * 24));

                return `
                    <tr class="${isOverdue ? 'table-danger' : ''}" data-invoice-id="${invoice.id}">
                        <td><input type="checkbox" class="invoice-checkbox" value="${invoice.id}"></td>
                        <td><strong class="text-primary">${invoice.number}</strong></td>
                        <td>${new Date(invoice.date).toLocaleDateString('ar-SA')}</td>
                        <td class="${isOverdue ? 'text-danger fw-bold' : ''}">${new Date(invoice.dueDate).toLocaleDateString('ar-SA')}</td>
                        <td>${customer?.name || 'غير محدد'}</td>
                        <td class="text-end"><strong>${this.formatAmount(invoice.total)}</strong></td>
                        <td><span class="badge bg-${this.getStatusColor(invoice.status)}">${this.getStatusLabel(invoice.status)}</span></td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="window.SalesComponent.viewInvoice('${invoice.id}')" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-secondary" onclick="window.SalesComponent.editInvoice('${invoice.id}')" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-success" onclick="window.SalesComponent.printInvoice('${invoice.id}')" title="طباعة">
                                    <i class="fas fa-print"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');

            if (noInvoicesMessage) noInvoicesMessage.style.display = 'none';
        }
    },

    /**
     * مسح الفلاتر
     */
    clearFilters: function() {
        document.getElementById('invoice-search').value = '';
        document.getElementById('status-filter').value = '';
        document.getElementById('date-from').value = '';
        document.getElementById('date-to').value = '';

        // إعادة عرض جميع الفواتير
        const tbody = document.getElementById('invoices-tbody');
        if (tbody) {
            tbody.innerHTML = this.renderInvoicesRows();
        }
    },

    /**
     * تحديث الفواتير
     */
    refreshInvoices: function() {
        this.loadSalesData();
        this.render({ view: 'invoices' });
        this.showNotification('تم تحديث قائمة الفواتير', 'success');
    },

    /**
     * تصدير الفواتير
     */
    exportInvoices: function() {
        const invoices = Object.values(this.data.invoices || {});
        if (invoices.length === 0) {
            this.showNotification('لا توجد فواتير للتصدير', 'warning');
            return;
        }

        // إنشاء CSV
        const headers = ['رقم الفاتورة', 'التاريخ', 'تاريخ الاستحقاق', 'العميل', 'المبلغ', 'الحالة'];
        const csvContent = [
            headers.join(','),
            ...invoices.map(invoice => {
                const customer = this.data.customers[invoice.customerId];
                return [
                    invoice.number,
                    new Date(invoice.date).toLocaleDateString('ar-SA'),
                    new Date(invoice.dueDate).toLocaleDateString('ar-SA'),
                    customer?.name || 'غير محدد',
                    invoice.total,
                    this.getStatusLabel(invoice.status)
                ].join(',');
            })
        ].join('\n');

        // تحميل الملف
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `invoices_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();

        this.showNotification('تم تصدير الفواتير بنجاح', 'success');
    },

    /**
     * حذف الفاتورة
     */
    deleteInvoice: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            this.showNotification('الفاتورة غير موجودة', 'error');
            return;
        }

        if (invoice.status === 'paid') {
            this.showNotification('لا يمكن حذف فاتورة مدفوعة', 'warning');
            return;
        }

        if (confirm(`هل أنت متأكد من حذف الفاتورة ${invoice.number}؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            delete this.data.invoices[invoiceId];
            this.saveSalesData();
            this.render({ view: 'invoices' });
            this.showNotification(`تم حذف الفاتورة ${invoice.number} بنجاح`, 'success');
        }
    },

    /**
     * إلغاء الفاتورة
     */
    cancelInvoice: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            this.showNotification('الفاتورة غير موجودة', 'error');
            return;
        }

        if (invoice.status === 'paid') {
            this.showNotification('لا يمكن إلغاء فاتورة مدفوعة', 'warning');
            return;
        }

        if (confirm(`هل أنت متأكد من إلغاء الفاتورة ${invoice.number}؟`)) {
            invoice.status = 'cancelled';
            invoice.updatedAt = new Date().toISOString();
            this.saveSalesData();
            this.render({ view: 'invoices' });
            this.showNotification(`تم إلغاء الفاتورة ${invoice.number}`, 'warning');
        }
    },

    /**
     * تسجيل الفاتورة كمدفوعة
     */
    markAsPaid: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            this.showNotification('الفاتورة غير موجودة', 'error');
            return;
        }

        if (invoice.status === 'paid') {
            this.showNotification('الفاتورة مدفوعة بالفعل', 'info');
            return;
        }

        if (confirm(`هل تم دفع الفاتورة ${invoice.number} بالكامل؟`)) {
            invoice.status = 'paid';
            invoice.paidAt = new Date().toISOString();
            invoice.updatedAt = new Date().toISOString();
            this.saveSalesData();
            this.render({ view: 'invoices' });
            this.showNotification(`تم تسجيل الفاتورة ${invoice.number} كمدفوعة`, 'success');
        }
    },

    /**
     * نسخ الفاتورة
     */
    duplicateInvoice: function(invoiceId) {
        const originalInvoice = this.data.invoices[invoiceId];
        if (!originalInvoice) {
            this.showNotification('الفاتورة غير موجودة', 'error');
            return;
        }

        // إنشاء فاتورة جديدة بنفس البيانات
        const newInvoiceId = 'invoice_' + Date.now();
        const newInvoiceNumber = this.generateInvoiceNumber();

        const duplicatedInvoice = {
            ...originalInvoice,
            id: newInvoiceId,
            number: newInvoiceNumber,
            date: new Date().toISOString().split('T')[0],
            dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            status: 'draft',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            reference: (originalInvoice.reference || '') + ' (نسخة)'
        };

        this.data.invoices[newInvoiceId] = duplicatedInvoice;
        this.data.settings.nextInvoiceNumber++;
        this.saveSalesData();
        this.render({ view: 'invoices' });

        this.showNotification(`تم نسخ الفاتورة بنجاح - رقم الفاتورة الجديدة: ${newInvoiceNumber}`, 'success');
    },

    /**
     * إرسال الفاتورة بالبريد الإلكتروني
     */
    sendInvoiceEmail: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            this.showNotification('الفاتورة غير موجودة', 'error');
            return;
        }

        const customer = this.data.customers[invoice.customerId];
        if (!customer || !customer.email) {
            this.showNotification('لا يوجد بريد إلكتروني للعميل', 'warning');
            return;
        }

        // محاكاة إرسال البريد الإلكتروني
        this.showNotification(`تم إرسال الفاتورة ${invoice.number} إلى ${customer.email}`, 'success');
    },

    /**
     * تحميل الفاتورة كـ PDF
     */
    downloadInvoicePDF: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            this.showNotification('الفاتورة غير موجودة', 'error');
            return;
        }

        // محاكاة تحميل PDF
        this.showNotification(`جاري تحميل الفاتورة ${invoice.number} كملف PDF...`, 'info');

        // يمكن هنا إضافة مكتبة PDF مثل jsPDF
        setTimeout(() => {
            this.showNotification(`تم تحميل الفاتورة ${invoice.number} بنجاح`, 'success');
        }, 1500);
    },

    /**
     * تحديد جميع الفواتير
     */
    selectAllInvoices: function() {
        const checkboxes = document.querySelectorAll('.invoice-checkbox');
        const selectAllCheckbox = document.getElementById('select-all-invoices');

        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
        });
    },

    /**
     * تبديل تحديد الكل
     */
    toggleSelectAll: function() {
        const checkboxes = document.querySelectorAll('.invoice-checkbox');
        const selectAllCheckbox = document.getElementById('select-all-invoices');

        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
        });
    },

    /**
     * الإجراءات المجمعة
     */
    bulkActions: function() {
        const selectedInvoices = Array.from(document.querySelectorAll('.invoice-checkbox:checked'))
            .map(checkbox => checkbox.value);

        if (selectedInvoices.length === 0) {
            this.showNotification('يرجى تحديد فاتورة واحدة على الأقل', 'warning');
            return;
        }

        const actions = [
            { value: 'mark-paid', text: 'تسجيل كمدفوع', icon: 'fas fa-check' },
            { value: 'cancel', text: 'إلغاء الفواتير', icon: 'fas fa-ban' },
            { value: 'delete', text: 'حذف الفواتير', icon: 'fas fa-trash' },
            { value: 'export', text: 'تصدير المحدد', icon: 'fas fa-download' }
        ];

        const actionHTML = `
            <div class="modal fade" id="bulkActionsModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-cogs me-2"></i>إجراءات مجمعة
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p>تم تحديد <strong>${selectedInvoices.length}</strong> فاتورة</p>
                            <p>اختر الإجراء المطلوب:</p>
                            <div class="list-group">
                                ${actions.map(action => `
                                    <button class="list-group-item list-group-item-action"
                                            onclick="window.SalesComponent.executeBulkAction('${action.value}', ${JSON.stringify(selectedInvoices)})">
                                        <i class="${action.icon} me-2"></i>${action.text}
                                    </button>
                                `).join('')}
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', actionHTML);
        const modal = new bootstrap.Modal(document.getElementById('bulkActionsModal'));
        modal.show();

        document.getElementById('bulkActionsModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * تنفيذ الإجراء المجمع
     */
    executeBulkAction: function(action, invoiceIds) {
        const modal = bootstrap.Modal.getInstance(document.getElementById('bulkActionsModal'));
        modal.hide();

        switch (action) {
            case 'mark-paid':
                this.bulkMarkAsPaid(invoiceIds);
                break;
            case 'cancel':
                this.bulkCancelInvoices(invoiceIds);
                break;
            case 'delete':
                this.bulkDeleteInvoices(invoiceIds);
                break;
            case 'export':
                this.bulkExportInvoices(invoiceIds);
                break;
        }
    },

    /**
     * تسجيل فواتير متعددة كمدفوعة
     */
    bulkMarkAsPaid: function(invoiceIds) {
        if (confirm(`هل تريد تسجيل ${invoiceIds.length} فاتورة كمدفوعة؟`)) {
            let updatedCount = 0;
            invoiceIds.forEach(id => {
                const invoice = this.data.invoices[id];
                if (invoice && invoice.status !== 'paid') {
                    invoice.status = 'paid';
                    invoice.paidAt = new Date().toISOString();
                    invoice.updatedAt = new Date().toISOString();
                    updatedCount++;
                }
            });

            this.saveSalesData();
            this.render({ view: 'invoices' });
            this.showNotification(`تم تسجيل ${updatedCount} فاتورة كمدفوعة`, 'success');
        }
    },

    /**
     * إلغاء فواتير متعددة
     */
    bulkCancelInvoices: function(invoiceIds) {
        if (confirm(`هل تريد إلغاء ${invoiceIds.length} فاتورة؟`)) {
            let cancelledCount = 0;
            invoiceIds.forEach(id => {
                const invoice = this.data.invoices[id];
                if (invoice && invoice.status !== 'paid' && invoice.status !== 'cancelled') {
                    invoice.status = 'cancelled';
                    invoice.updatedAt = new Date().toISOString();
                    cancelledCount++;
                }
            });

            this.saveSalesData();
            this.render({ view: 'invoices' });
            this.showNotification(`تم إلغاء ${cancelledCount} فاتورة`, 'warning');
        }
    },

    /**
     * حذف فواتير متعددة
     */
    bulkDeleteInvoices: function(invoiceIds) {
        if (confirm(`هل تريد حذف ${invoiceIds.length} فاتورة؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            let deletedCount = 0;
            invoiceIds.forEach(id => {
                const invoice = this.data.invoices[id];
                if (invoice && invoice.status !== 'paid') {
                    delete this.data.invoices[id];
                    deletedCount++;
                }
            });

            this.saveSalesData();
            this.render({ view: 'invoices' });
            this.showNotification(`تم حذف ${deletedCount} فاتورة`, 'success');
        }
    },

    /**
     * تصدير فواتير محددة
     */
    bulkExportInvoices: function(invoiceIds) {
        const selectedInvoices = invoiceIds.map(id => this.data.invoices[id]).filter(Boolean);

        if (selectedInvoices.length === 0) {
            this.showNotification('لا توجد فواتير صالحة للتصدير', 'warning');
            return;
        }

        // إنشاء CSV للفواتير المحددة
        const headers = ['رقم الفاتورة', 'التاريخ', 'تاريخ الاستحقاق', 'العميل', 'المبلغ', 'الحالة'];
        const csvContent = [
            headers.join(','),
            ...selectedInvoices.map(invoice => {
                const customer = this.data.customers[invoice.customerId];
                return [
                    invoice.number,
                    new Date(invoice.date).toLocaleDateString('ar-SA'),
                    new Date(invoice.dueDate).toLocaleDateString('ar-SA'),
                    customer?.name || 'غير محدد',
                    invoice.total,
                    this.getStatusLabel(invoice.status)
                ].join(',');
            })
        ].join('\n');

        // تحميل الملف
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `selected_invoices_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();

        this.showNotification(`تم تصدير ${selectedInvoices.length} فاتورة بنجاح`, 'success');
    },

    /**
     * عرض صفحة الإشعارات الدائنة
     */
    renderCreditNotesView: function() {
        const creditNotes = Object.values(this.data.creditNotes || {});
        const totalCreditNotes = creditNotes.length;
        const totalAmount = creditNotes.reduce((sum, cn) => sum + (cn.total || 0), 0);
        const appliedCreditNotes = creditNotes.filter(cn => cn.status === 'applied').length;
        const pendingCreditNotes = creditNotes.filter(cn => cn.status === 'pending').length;

        return `
            <div class="container-fluid">
                <!-- رأس الصفحة -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2><i class="fas fa-file-invoice-dollar me-2 text-success"></i>إدارة الإشعارات الدائنة</h2>
                        <p class="text-muted mb-0">إدارة شاملة للإشعارات الدائنة وإرجاع المبالغ</p>
                    </div>
                    <div class="btn-group">
                        <button class="btn btn-success" onclick="window.SalesComponent.showCreateCreditNoteModal()">
                            <i class="fas fa-plus me-1"></i>إشعار دائن جديد
                        </button>
                        <button class="btn btn-outline-info" onclick="window.SalesComponent.refreshCreditNotes()">
                            <i class="fas fa-sync me-1"></i>تحديث
                        </button>
                        <button class="btn btn-outline-primary" onclick="window.SalesComponent.exportCreditNotes()">
                            <i class="fas fa-download me-1"></i>تصدير
                        </button>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0">${totalCreditNotes}</h4>
                                        <p class="mb-0">إجمالي الإشعارات</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-file-invoice-dollar fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0">${appliedCreditNotes}</h4>
                                        <p class="mb-0">مطبقة</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-check-circle fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0">${pendingCreditNotes}</h4>
                                        <p class="mb-0">معلقة</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-clock fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0">${this.formatAmount(totalAmount)}</h4>
                                        <p class="mb-0">إجمالي المبلغ</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-money-bill-wave fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أدوات البحث والفلترة -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-search me-2"></i>البحث والفلترة</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label">البحث</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" id="creditnote-search"
                                           placeholder="رقم الإشعار أو اسم العميل..."
                                           onkeyup="window.SalesComponent.filterCreditNotes()">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">الحالة</label>
                                <select class="form-control" id="creditnote-status-filter" onchange="window.SalesComponent.filterCreditNotes()">
                                    <option value="">جميع الحالات</option>
                                    <option value="draft">مسودة</option>
                                    <option value="pending">معلقة</option>
                                    <option value="applied">مطبقة</option>
                                    <option value="cancelled">ملغية</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="creditnote-date-from" onchange="window.SalesComponent.filterCreditNotes()">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="creditnote-date-to" onchange="window.SalesComponent.filterCreditNotes()">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">إجراءات</label>
                                <div class="btn-group w-100">
                                    <button class="btn btn-outline-secondary" onclick="window.SalesComponent.clearCreditNoteFilters()">
                                        <i class="fas fa-times me-1"></i>مسح
                                    </button>
                                    <button class="btn btn-outline-primary" onclick="window.SalesComponent.filterCreditNotes()">
                                        <i class="fas fa-filter me-1"></i>تطبيق
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول الإشعارات الدائنة -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="fas fa-list me-2"></i>قائمة الإشعارات الدائنة</h6>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-info" onclick="window.SalesComponent.selectAllCreditNotes()">
                                <i class="fas fa-check-square me-1"></i>تحديد الكل
                            </button>
                            <button class="btn btn-outline-warning" onclick="window.SalesComponent.bulkCreditNoteActions()">
                                <i class="fas fa-cogs me-1"></i>إجراءات مجمعة
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover" id="creditnotes-table">
                                <thead class="table-light">
                                    <tr>
                                        <th width="40">
                                            <input type="checkbox" id="select-all-creditnotes" onchange="window.SalesComponent.toggleSelectAllCreditNotes()">
                                        </th>
                                        <th>رقم الإشعار</th>
                                        <th>التاريخ</th>
                                        <th>العميل</th>
                                        <th>الفاتورة المرجعية</th>
                                        <th>المبلغ</th>
                                        <th>الحالة</th>
                                        <th width="200">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="creditnotes-tbody">
                                    ${this.renderCreditNotesRows()}
                                </tbody>
                            </table>
                        </div>

                        <!-- رسالة عدم وجود إشعارات -->
                        <div id="no-creditnotes-message" class="text-center py-5" style="display: none;">
                            <i class="fas fa-file-invoice-dollar fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد إشعارات دائنة</h5>
                            <p class="text-muted">ابدأ بإنشاء أول إشعار دائن</p>
                            <button class="btn btn-success" onclick="window.SalesComponent.showCreateCreditNoteModal()">
                                <i class="fas fa-plus me-1"></i>إنشاء إشعار دائن جديد
                            </button>
                        </div>
                    </div>
                </div>

                <!-- إجمالي المبلغ -->
                <div class="row mt-3">
                    <div class="col-md-6"></div>
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <strong>إجمالي قيمة الإشعارات الدائنة:</strong>
                                    <strong class="text-success">${this.formatAmount(totalAmount)}</strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض صفوف الإشعارات الدائنة
     */
    renderCreditNotesRows: function() {
        const creditNotes = Object.values(this.data.creditNotes || {});

        if (creditNotes.length === 0) {
            return '<tr><td colspan="8" class="text-center text-muted py-4">لا توجد إشعارات دائنة متاحة</td></tr>';
        }

        // ترتيب الإشعارات حسب التاريخ (الأحدث أولاً)
        const sortedCreditNotes = creditNotes.sort((a, b) => new Date(b.date) - new Date(a.date));

        return sortedCreditNotes.map(creditNote => {
            const customer = this.data.customers[creditNote.customerId];
            const invoice = this.data.invoices[creditNote.invoiceId];

            return `
                <tr data-creditnote-id="${creditNote.id}">
                    <td>
                        <input type="checkbox" class="creditnote-checkbox" value="${creditNote.id}">
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <strong class="text-success">${creditNote.number}</strong>
                            ${creditNote.reference ? `<small class="text-muted ms-2">(${creditNote.reference})</small>` : ''}
                        </div>
                    </td>
                    <td>
                        <span class="text-nowrap">${new Date(creditNote.date).toLocaleDateString('ar-SA')}</span>
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-user me-2 text-muted"></i>
                            <div>
                                <div class="fw-bold">${customer?.name || 'غير محدد'}</div>
                                ${customer?.phone ? `<small class="text-muted">${customer.phone}</small>` : ''}
                            </div>
                        </div>
                    </td>
                    <td>
                        ${invoice ? `
                            <span class="badge bg-info">${invoice.number}</span>
                        ` : `
                            <span class="text-muted">غير مرتبط</span>
                        `}
                    </td>
                    <td>
                        <div class="text-end">
                            <strong class="text-success">${this.formatAmount(creditNote.total)}</strong>
                        </div>
                    </td>
                    <td>
                        <span class="badge bg-${this.getCreditNoteStatusColor(creditNote.status)} fs-6">
                            ${this.getCreditNoteStatusLabel(creditNote.status)}
                        </span>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="window.SalesComponent.viewCreditNote('${creditNote.id}')"
                                    title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-secondary" onclick="window.SalesComponent.editCreditNote('${creditNote.id}')"
                                    title="تعديل" ${creditNote.status === 'applied' ? 'disabled' : ''}>
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-success" onclick="window.SalesComponent.printCreditNote('${creditNote.id}')"
                                    title="طباعة">
                                <i class="fas fa-print"></i>
                            </button>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-info dropdown-toggle" data-bs-toggle="dropdown" title="المزيد">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    ${creditNote.status === 'pending' ? `
                                        <li><a class="dropdown-item text-success" href="#" onclick="window.SalesComponent.applyCreditNote('${creditNote.id}')">
                                            <i class="fas fa-check me-2"></i>تطبيق الإشعار
                                        </a></li>
                                    ` : ''}
                                    <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.duplicateCreditNote('${creditNote.id}')">
                                        <i class="fas fa-copy me-2"></i>نسخ الإشعار
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.downloadCreditNotePDF('${creditNote.id}')">
                                        <i class="fas fa-file-pdf me-2"></i>تحميل PDF
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    ${creditNote.status !== 'applied' ? `
                                        <li><a class="dropdown-item text-warning" href="#" onclick="window.SalesComponent.cancelCreditNote('${creditNote.id}')">
                                            <i class="fas fa-ban me-2"></i>إلغاء الإشعار
                                        </a></li>
                                        <li><a class="dropdown-item text-danger" href="#" onclick="window.SalesComponent.deleteCreditNote('${creditNote.id}')">
                                            <i class="fas fa-trash me-2"></i>حذف الإشعار
                                        </a></li>
                                    ` : ''}
                                </ul>
                            </div>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
    },

    /**
     * الحصول على لون حالة الإشعار الدائن
     */
    getCreditNoteStatusColor: function(status) {
        const colors = {
            'draft': 'secondary',
            'pending': 'warning',
            'applied': 'success',
            'cancelled': 'danger'
        };
        return colors[status] || 'secondary';
    },

    /**
     * الحصول على تسمية حالة الإشعار الدائن
     */
    getCreditNoteStatusLabel: function(status) {
        const labels = {
            'draft': 'مسودة',
            'pending': 'معلق',
            'applied': 'مطبق',
            'cancelled': 'ملغي'
        };
        return labels[status] || 'غير محدد';
    },

    /**
     * عرض نافذة إنشاء إشعار دائن جديد
     */
    showCreateCreditNoteModal: function() {
        try {
            // التأكد من تحميل البيانات
            if (!this.data || Object.keys(this.data).length === 0) {
                this.loadSalesData();
                this.createSampleData();
            }

            const customers = Object.values(this.data.customers || {});
            const invoices = Object.values(this.data.invoices || {}).filter(inv => inv.status === 'paid');

            if (customers.length === 0) {
                this.showNotification('لا توجد عملاء. سيتم إنشاء بيانات تجريبية...', 'warning');
                this.createSampleData();
            }

            if (invoices.length === 0) {
                this.showNotification('لا توجد فواتير مدفوعة لإنشاء إشعار دائن عنها', 'warning');
            }

            // إنشاء النافذة المحسنة
            this.createEnhancedCreditNoteModal(customers, invoices);

        } catch (error) {
            console.error('خطأ في فتح نافذة الإشعار الدائن:', error);
            this.handleError('فشل في فتح نافذة إنشاء الإشعار الدائن', error);
        }
    },

    /**
     * إنشاء نافذة الإشعار الدائن المحسنة
     */
    createEnhancedCreditNoteModal: function(customers, invoices) {
        // إنشاء رقم إشعار دائن جديد
        const creditNoteNumber = this.generateCreditNoteNumber();

        // تاريخ اليوم
        const today = new Date().toISOString().split('T')[0];

        const modalHTML = `
            <div class="modal fade" id="createCreditNoteModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header bg-success text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-file-invoice-dollar me-2"></i>إنشاء إشعار دائن جديد
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <!-- شريط التقدم -->
                            <div class="progress mb-4" style="height: 8px;">
                                <div class="progress-bar bg-success" role="progressbar" style="width: 25%" id="creditnote-progress">
                                    <span class="sr-only">25% مكتمل</span>
                                </div>
                            </div>

                            <form id="createCreditNoteForm">
                                <!-- معلومات الإشعار الأساسية -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات الإشعار الدائن</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <label class="form-label">رقم الإشعار الدائن</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="fas fa-hashtag"></i></span>
                                                    <input type="text" class="form-control" name="number" value="${creditNoteNumber}" readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">تاريخ الإشعار *</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                                    <input type="date" class="form-control" name="date" value="${today}" required>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">نوع الإشعار *</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="fas fa-tag"></i></span>
                                                    <select class="form-control" name="type" required onchange="window.SalesComponent.updateCreditNoteType(this.value)">
                                                        <option value="">اختر النوع</option>
                                                        <option value="refund">استرداد كامل</option>
                                                        <option value="partial_refund">استرداد جزئي</option>
                                                        <option value="discount">خصم إضافي</option>
                                                        <option value="correction">تصحيح خطأ</option>
                                                        <option value="return">إرجاع بضاعة</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- معلومات العميل والفاتورة -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="fas fa-user me-2"></i>معلومات العميل والفاتورة</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <label class="form-label">العميل *</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                                    <select class="form-control" name="customerId" required onchange="window.SalesComponent.updateCreditNoteCustomer(this.value)">
                                                        <option value="">اختر العميل</option>
                                                        ${customers.map(customer => `
                                                            <option value="${customer.id}">
                                                                ${customer.name} ${customer.phone ? '- ' + customer.phone : ''}
                                                            </option>
                                                        `).join('')}
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">الفاتورة المرجعية</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="fas fa-file-invoice"></i></span>
                                                    <select class="form-control" name="invoiceId" onchange="window.SalesComponent.updateCreditNoteInvoice(this.value)">
                                                        <option value="">اختر الفاتورة (اختياري)</option>
                                                        ${invoices.map(invoice => {
                                                            const customer = this.data.customers[invoice.customerId];
                                                            return `
                                                                <option value="${invoice.id}" data-customer="${invoice.customerId}" data-total="${invoice.total}">
                                                                    ${invoice.number} - ${customer?.name || 'غير محدد'} - ${this.formatAmount(invoice.total)}
                                                                </option>
                                                            `;
                                                        }).join('')}
                                                    </select>
                                                </div>
                                                <small class="text-muted">اختر الفاتورة المراد إنشاء إشعار دائن عنها</small>
                                            </div>
                                        </div>

                                        <!-- معلومات العميل المختار -->
                                        <div id="creditnote-customer-info" class="mt-3 p-2 bg-light rounded" style="display: none;">
                                            <small class="text-muted">
                                                <div id="creditnote-customer-details"></div>
                                            </small>
                                        </div>
                                    </div>
                                </div>

                                <!-- تفاصيل الإشعار -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="fas fa-edit me-2"></i>تفاصيل الإشعار الدائن</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <label class="form-label">المبلغ *</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">ر.س</span>
                                                    <input type="number" class="form-control" name="amount" step="0.01" min="0" required
                                                           onchange="window.SalesComponent.calculateCreditNoteTotal()">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">رقم المرجع</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="fas fa-tag"></i></span>
                                                    <input type="text" class="form-control" name="reference" placeholder="رقم المرجع (اختياري)">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row mt-3">
                                            <div class="col-md-12">
                                                <label class="form-label">سبب الإشعار الدائن *</label>
                                                <textarea class="form-control" name="reason" rows="3" required
                                                          placeholder="اذكر سبب إصدار الإشعار الدائن..."></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- ملخص الإشعار -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="fas fa-calculator me-2"></i>ملخص الإشعار الدائن</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">ملاحظات إضافية</label>
                                                    <textarea class="form-control" name="notes" rows="3" placeholder="ملاحظات إضافية..."></textarea>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="card bg-light">
                                                    <div class="card-body">
                                                        <div class="d-flex justify-content-between mb-2">
                                                            <span>المبلغ الأساسي:</span>
                                                            <span id="creditnote-amount" class="fw-bold">0.00 ر.س</span>
                                                        </div>
                                                        <div class="d-flex justify-content-between mb-2">
                                                            <span>الضريبة:</span>
                                                            <span id="creditnote-tax" class="fw-bold">0.00 ر.س</span>
                                                        </div>
                                                        <hr>
                                                        <div class="d-flex justify-content-between mb-3">
                                                            <span class="h6">الإجمالي:</span>
                                                            <span id="creditnote-total" class="h5 text-success fw-bold">0.00 ر.س</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer bg-light">
                            <div class="d-flex justify-content-between w-100">
                                <div>
                                    <button type="button" class="btn btn-outline-warning" onclick="window.SalesComponent.saveDraftCreditNote()">
                                        <i class="fas fa-file-alt me-1"></i>حفظ كمسودة
                                    </button>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal">
                                        <i class="fas fa-times me-1"></i>إلغاء
                                    </button>
                                    <button type="button" class="btn btn-success" onclick="window.SalesComponent.saveCreditNote()">
                                        <i class="fas fa-save me-1"></i>حفظ الإشعار الدائن
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إضافة النافذة للصفحة
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // إظهار النافذة
        const modal = new bootstrap.Modal(document.getElementById('createCreditNoteModal'));
        modal.show();

        // تنظيف النافذة عند الإغلاق
        document.getElementById('createCreditNoteModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * إنشاء رقم إشعار دائن جديد
     */
    generateCreditNoteNumber: function() {
        const prefix = this.data.settings?.creditNotePrefix || 'CN-';
        const nextNumber = this.data.settings?.nextCreditNoteNumber || 1;
        return prefix + String(nextNumber).padStart(4, '0');
    },

    /**
     * تحديث نوع الإشعار الدائن
     */
    updateCreditNoteType: function(type) {
        const progressBar = document.getElementById('creditnote-progress');
        if (progressBar) {
            progressBar.style.width = '50%';
        }
    },

    /**
     * تحديث معلومات العميل في الإشعار الدائن
     */
    updateCreditNoteCustomer: function(customerId) {
        const customerInfo = document.getElementById('creditnote-customer-info');
        const customerDetails = document.getElementById('creditnote-customer-details');

        if (!customerId) {
            customerInfo.style.display = 'none';
            return;
        }

        const customer = this.data.customers[customerId];
        if (customer) {
            let details = `<strong>${customer.name}</strong><br>`;
            if (customer.phone) details += `📞 ${customer.phone}<br>`;
            if (customer.email) details += `📧 ${customer.email}<br>`;
            if (customer.address) details += `📍 ${customer.address}`;

            customerDetails.innerHTML = details;
            customerInfo.style.display = 'block';

            // تحديث قائمة الفواتير للعميل المختار
            this.updateInvoiceOptionsForCustomer(customerId);

            // تحديث شريط التقدم
            const progressBar = document.getElementById('creditnote-progress');
            if (progressBar) {
                progressBar.style.width = '75%';
            }
        }
    },

    /**
     * تحديث خيارات الفواتير للعميل المختار
     */
    updateInvoiceOptionsForCustomer: function(customerId) {
        const invoiceSelect = document.querySelector('select[name="invoiceId"]');
        if (!invoiceSelect) return;

        const customerInvoices = Object.values(this.data.invoices || {})
            .filter(inv => inv.customerId === customerId && inv.status === 'paid');

        // مسح الخيارات الحالية
        invoiceSelect.innerHTML = '<option value="">اختر الفاتورة (اختياري)</option>';

        // إضافة فواتير العميل
        customerInvoices.forEach(invoice => {
            const option = document.createElement('option');
            option.value = invoice.id;
            option.setAttribute('data-total', invoice.total);
            option.textContent = `${invoice.number} - ${this.formatAmount(invoice.total)} - ${new Date(invoice.date).toLocaleDateString('ar-SA')}`;
            invoiceSelect.appendChild(option);
        });
    },

    /**
     * تحديث معلومات الفاتورة المختارة
     */
    updateCreditNoteInvoice: function(invoiceId) {
        if (!invoiceId) return;

        const invoice = this.data.invoices[invoiceId];
        if (invoice) {
            // تحديث العميل تلقائياً
            const customerSelect = document.querySelector('select[name="customerId"]');
            if (customerSelect) {
                customerSelect.value = invoice.customerId;
                this.updateCreditNoteCustomer(invoice.customerId);
            }

            // اقتراح مبلغ الإشعار (يمكن تعديله)
            const amountInput = document.querySelector('input[name="amount"]');
            if (amountInput && !amountInput.value) {
                amountInput.value = invoice.total;
                this.calculateCreditNoteTotal();
            }
        }
    },

    /**
     * حساب إجمالي الإشعار الدائن
     */
    calculateCreditNoteTotal: function() {
        const amountInput = document.querySelector('input[name="amount"]');
        if (!amountInput) return;

        const amount = parseFloat(amountInput.value) || 0;
        const taxRate = this.data.settings?.taxRate || 0.15;
        const tax = amount * taxRate;
        const total = amount + tax;

        // تحديث عناصر العرض
        const amountElement = document.getElementById('creditnote-amount');
        const taxElement = document.getElementById('creditnote-tax');
        const totalElement = document.getElementById('creditnote-total');

        if (amountElement) amountElement.textContent = this.formatAmount(amount);
        if (taxElement) taxElement.textContent = this.formatAmount(tax);
        if (totalElement) totalElement.textContent = this.formatAmount(total);

        // تحديث شريط التقدم
        const progressBar = document.getElementById('creditnote-progress');
        if (progressBar && amount > 0) {
            progressBar.style.width = '100%';
        }

        return { amount, tax, total };
    },

    /**
     * حفظ الإشعار الدائن
     */
    saveCreditNote: function(status = 'pending') {
        try {
            const form = document.getElementById('createCreditNoteForm');
            if (!form) {
                throw new Error('نموذج الإشعار الدائن غير موجود');
            }

            const formData = new FormData(form);

            // التحقق من البيانات المطلوبة
            if (!formData.get('customerId')) {
                this.showNotification('يرجى اختيار العميل', 'warning');
                return;
            }

            if (!formData.get('type')) {
                this.showNotification('يرجى اختيار نوع الإشعار', 'warning');
                return;
            }

            if (!formData.get('amount') || parseFloat(formData.get('amount')) <= 0) {
                this.showNotification('يرجى إدخال مبلغ صحيح', 'warning');
                return;
            }

            if (!formData.get('reason')) {
                this.showNotification('يرجى إدخال سبب الإشعار الدائن', 'warning');
                return;
            }

            // حساب الإجماليات
            const totals = this.calculateCreditNoteTotal();

            // إنشاء الإشعار الدائن
            const creditNoteId = 'creditnote_' + Date.now();
            const creditNote = {
                id: creditNoteId,
                number: formData.get('number'),
                date: formData.get('date'),
                type: formData.get('type'),
                customerId: formData.get('customerId'),
                invoiceId: formData.get('invoiceId') || null,
                reference: formData.get('reference') || '',
                amount: totals.amount,
                tax: totals.tax,
                total: totals.total,
                reason: formData.get('reason'),
                notes: formData.get('notes') || '',
                status: status,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                createdBy: 'نظام المبيعات'
            };

            // حفظ الإشعار الدائن
            if (!this.data.creditNotes) this.data.creditNotes = {};
            this.data.creditNotes[creditNoteId] = creditNote;

            // تحديث رقم الإشعار التالي
            if (!this.data.settings.nextCreditNoteNumber) this.data.settings.nextCreditNoteNumber = 1;
            this.data.settings.nextCreditNoteNumber++;

            // حفظ البيانات
            this.saveSalesData();

            // إغلاق النافذة
            const modalElement = document.getElementById('createCreditNoteModal');
            if (modalElement) {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                }
            }

            // إظهار رسالة نجاح
            const statusText = status === 'draft' ? 'كمسودة' : '';
            this.showNotification(`تم حفظ الإشعار الدائن ${statusText} بنجاح - رقم: ${creditNote.number}`, 'success');

            // تحديث العرض إذا كنا في صفحة الإشعارات الدائنة
            if (this.data.currentView === 'creditNotes') {
                this.render({ view: 'creditNotes' });
            }

            return creditNote;

        } catch (error) {
            console.error('خطأ في حفظ الإشعار الدائن:', error);
            this.handleError('فشل في حفظ الإشعار الدائن', error);
            return null;
        }
    },

    /**
     * حفظ الإشعار الدائن كمسودة
     */
    saveDraftCreditNote: function() {
        this.saveCreditNote('draft');
    },

    /**
     * عرض تفاصيل الإشعار الدائن
     */
    viewCreditNote: function(creditNoteId) {
        const creditNote = this.data.creditNotes[creditNoteId];
        if (!creditNote) {
            this.showNotification('الإشعار الدائن غير موجود', 'error');
            return;
        }

        const customer = this.data.customers[creditNote.customerId];
        const invoice = creditNote.invoiceId ? this.data.invoices[creditNote.invoiceId] : null;

        const modalHTML = `
            <div class="modal fade" id="viewCreditNoteModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-success text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-file-invoice-dollar me-2"></i>تفاصيل الإشعار الدائن ${creditNote.number}
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <!-- معلومات الإشعار الأساسية -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات الإشعار</h6>
                                        </div>
                                        <div class="card-body">
                                            <table class="table table-borderless table-sm">
                                                <tr>
                                                    <td><strong>رقم الإشعار:</strong></td>
                                                    <td>${creditNote.number}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>التاريخ:</strong></td>
                                                    <td>${new Date(creditNote.date).toLocaleDateString('ar-SA')}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>النوع:</strong></td>
                                                    <td>${this.getCreditNoteTypeLabel(creditNote.type)}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>الحالة:</strong></td>
                                                    <td>
                                                        <span class="badge bg-${this.getCreditNoteStatusColor(creditNote.status)} fs-6">
                                                            ${this.getCreditNoteStatusLabel(creditNote.status)}
                                                        </span>
                                                    </td>
                                                </tr>
                                                ${creditNote.reference ? `
                                                    <tr>
                                                        <td><strong>رقم المرجع:</strong></td>
                                                        <td>${creditNote.reference}</td>
                                                    </tr>
                                                ` : ''}
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0"><i class="fas fa-user me-2"></i>معلومات العميل</h6>
                                        </div>
                                        <div class="card-body">
                                            ${customer ? `
                                                <div class="d-flex align-items-center mb-3">
                                                    <div class="bg-success rounded-circle d-flex align-items-center justify-content-center me-3"
                                                         style="width: 50px; height: 50px;">
                                                        <i class="fas fa-user text-white"></i>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0">${customer.name}</h6>
                                                        <small class="text-muted">عميل</small>
                                                    </div>
                                                </div>
                                                <table class="table table-borderless table-sm">
                                                    ${customer.phone ? `
                                                        <tr>
                                                            <td><i class="fas fa-phone me-2"></i><strong>الهاتف:</strong></td>
                                                            <td>${customer.phone}</td>
                                                        </tr>
                                                    ` : ''}
                                                    ${customer.email ? `
                                                        <tr>
                                                            <td><i class="fas fa-envelope me-2"></i><strong>البريد:</strong></td>
                                                            <td>${customer.email}</td>
                                                        </tr>
                                                    ` : ''}
                                                </table>
                                            ` : `
                                                <div class="text-center text-muted py-3">
                                                    <i class="fas fa-user-slash fa-2x mb-2"></i>
                                                    <p>معلومات العميل غير متاحة</p>
                                                </div>
                                            `}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- الفاتورة المرجعية -->
                            ${invoice ? `
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="fas fa-file-invoice me-2"></i>الفاتورة المرجعية</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <strong>رقم الفاتورة:</strong><br>
                                                <span class="badge bg-info">${invoice.number}</span>
                                            </div>
                                            <div class="col-md-3">
                                                <strong>تاريخ الفاتورة:</strong><br>
                                                ${new Date(invoice.date).toLocaleDateString('ar-SA')}
                                            </div>
                                            <div class="col-md-3">
                                                <strong>مبلغ الفاتورة:</strong><br>
                                                ${this.formatAmount(invoice.total)}
                                            </div>
                                            <div class="col-md-3">
                                                <strong>حالة الفاتورة:</strong><br>
                                                <span class="badge bg-${this.getStatusColor(invoice.status)}">${this.getStatusLabel(invoice.status)}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ` : ''}

                            <!-- تفاصيل الإشعار -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-edit me-2"></i>تفاصيل الإشعار الدائن</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-md-12">
                                            <strong>سبب الإشعار الدائن:</strong>
                                            <p class="mt-2 p-3 bg-light rounded">${creditNote.reason}</p>
                                        </div>
                                    </div>
                                    ${creditNote.notes ? `
                                        <div class="row">
                                            <div class="col-md-12">
                                                <strong>ملاحظات إضافية:</strong>
                                                <p class="mt-2 p-3 bg-light rounded">${creditNote.notes}</p>
                                            </div>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>

                            <!-- الإجماليات -->
                            <div class="row">
                                <div class="col-md-6"></div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0"><i class="fas fa-calculator me-2"></i>ملخص المبالغ</h6>
                                        </div>
                                        <div class="card-body">
                                            <table class="table table-borderless">
                                                <tr>
                                                    <td>المبلغ الأساسي:</td>
                                                    <td class="text-end">${this.formatAmount(creditNote.amount)}</td>
                                                </tr>
                                                <tr>
                                                    <td>الضريبة:</td>
                                                    <td class="text-end">${this.formatAmount(creditNote.tax)}</td>
                                                </tr>
                                                <tr class="table-success">
                                                    <td><strong>الإجمالي:</strong></td>
                                                    <td class="text-end"><strong class="h5 text-success">${this.formatAmount(creditNote.total)}</strong></td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i>إغلاق
                            </button>
                            <button type="button" class="btn btn-info" onclick="window.SalesComponent.editCreditNote('${creditNote.id}')">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </button>
                            <button type="button" class="btn btn-success" onclick="window.SalesComponent.printCreditNote('${creditNote.id}')">
                                <i class="fas fa-print me-1"></i>طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('viewCreditNoteModal'));
        modal.show();

        document.getElementById('viewCreditNoteModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * الحصول على تسمية نوع الإشعار الدائن
     */
    getCreditNoteTypeLabel: function(type) {
        const labels = {
            'refund': 'استرداد كامل',
            'partial_refund': 'استرداد جزئي',
            'discount': 'خصم إضافي',
            'correction': 'تصحيح خطأ',
            'return': 'إرجاع بضاعة'
        };
        return labels[type] || 'غير محدد';
    },

    /**
     * حذف الإشعار الدائن
     */
    deleteCreditNote: function(creditNoteId) {
        const creditNote = this.data.creditNotes[creditNoteId];
        if (!creditNote) {
            this.showNotification('الإشعار الدائن غير موجود', 'error');
            return;
        }

        if (creditNote.status === 'applied') {
            this.showNotification('لا يمكن حذف إشعار دائن مطبق', 'warning');
            return;
        }

        if (confirm(`هل أنت متأكد من حذف الإشعار الدائن ${creditNote.number}؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            delete this.data.creditNotes[creditNoteId];
            this.saveSalesData();
            this.render({ view: 'creditNotes' });
            this.showNotification(`تم حذف الإشعار الدائن ${creditNote.number} بنجاح`, 'success');
        }
    },

    /**
     * تطبيق الإشعار الدائن
     */
    applyCreditNote: function(creditNoteId) {
        const creditNote = this.data.creditNotes[creditNoteId];
        if (!creditNote) {
            this.showNotification('الإشعار الدائن غير موجود', 'error');
            return;
        }

        if (creditNote.status === 'applied') {
            this.showNotification('الإشعار الدائن مطبق بالفعل', 'info');
            return;
        }

        if (confirm(`هل تريد تطبيق الإشعار الدائن ${creditNote.number}؟`)) {
            creditNote.status = 'applied';
            creditNote.appliedAt = new Date().toISOString();
            creditNote.updatedAt = new Date().toISOString();
            this.saveSalesData();
            this.render({ view: 'creditNotes' });
            this.showNotification(`تم تطبيق الإشعار الدائن ${creditNote.number}`, 'success');
        }
    },

    /**
     * طباعة الإشعار الدائن
     */
    printCreditNote: function(creditNoteId) {
        const creditNote = this.data.creditNotes[creditNoteId];
        if (!creditNote) {
            this.showNotification('الإشعار الدائن غير موجود', 'error');
            return;
        }

        const customer = this.data.customers[creditNote.customerId];
        const invoice = creditNote.invoiceId ? this.data.invoices[creditNote.invoiceId] : null;
        const companyInfo = this.data.settings?.companyInfo || {};

        // إنشاء نافذة الطباعة
        const printWindow = window.open('', '_blank', 'width=800,height=600');

        const printHTML = `
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>إشعار دائن ${creditNote.number}</title>
                <style>
                    body {
                        font-family: 'Arial', sans-serif;
                        margin: 0;
                        padding: 20px;
                        background: white;
                        color: #333;
                        line-height: 1.6;
                    }
                    .creditnote-container {
                        max-width: 800px;
                        margin: 0 auto;
                        background: white;
                        border: 1px solid #ddd;
                        border-radius: 8px;
                        overflow: hidden;
                    }
                    .creditnote-header {
                        background: linear-gradient(135deg, #28a745, #20c997);
                        color: white;
                        padding: 30px;
                        text-align: center;
                    }
                    .creditnote-header h1 {
                        margin: 0;
                        font-size: 2.5em;
                        font-weight: bold;
                    }
                    .creditnote-header p {
                        margin: 10px 0 0 0;
                        font-size: 1.2em;
                        opacity: 0.9;
                    }
                    .creditnote-body {
                        padding: 30px;
                    }
                    .info-section {
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 30px;
                        gap: 30px;
                    }
                    .info-box {
                        flex: 1;
                        background: #f8f9fa;
                        padding: 20px;
                        border-radius: 8px;
                        border-left: 4px solid #28a745;
                    }
                    .info-box h3 {
                        margin: 0 0 15px 0;
                        color: #28a745;
                        font-size: 1.3em;
                    }
                    .info-box p {
                        margin: 5px 0;
                        color: #666;
                    }
                    .details-section {
                        background: #e9ecef;
                        padding: 20px;
                        border-radius: 8px;
                        margin: 30px 0;
                    }
                    .reason-box {
                        background: #fff3cd;
                        border: 1px solid #ffeaa7;
                        padding: 20px;
                        border-radius: 8px;
                        margin: 20px 0;
                    }
                    .totals-section {
                        margin-top: 30px;
                        display: flex;
                        justify-content: flex-end;
                    }
                    .totals-table {
                        width: 300px;
                        border-collapse: collapse;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                        border-radius: 8px;
                        overflow: hidden;
                    }
                    .totals-table td {
                        padding: 12px 20px;
                        border-bottom: 1px solid #eee;
                    }
                    .totals-table .total-row {
                        background: #28a745;
                        color: white;
                        font-weight: bold;
                        font-size: 1.2em;
                    }
                    .footer {
                        text-align: center;
                        margin-top: 40px;
                        padding: 20px;
                        background: #f8f9fa;
                        border-radius: 8px;
                        color: #666;
                    }
                    @media print {
                        body { margin: 0; padding: 0; }
                        .creditnote-container { border: none; box-shadow: none; }
                    }
                </style>
            </head>
            <body>
                <div class="creditnote-container">
                    <!-- رأس الإشعار -->
                    <div class="creditnote-header">
                        <h1>إشعار دائن</h1>
                        <p>رقم الإشعار: ${creditNote.number}</p>
                    </div>

                    <div class="creditnote-body">
                        <!-- معلومات الشركة والعميل -->
                        <div class="info-section">
                            <div class="info-box">
                                <h3>معلومات الشركة</h3>
                                <p><strong>${companyInfo.name || 'اسم الشركة'}</strong></p>
                                ${companyInfo.address ? `<p>${companyInfo.address}</p>` : ''}
                                ${companyInfo.phone ? `<p>هاتف: ${companyInfo.phone}</p>` : ''}
                                ${companyInfo.email ? `<p>بريد: ${companyInfo.email}</p>` : ''}
                            </div>
                            <div class="info-box">
                                <h3>معلومات العميل</h3>
                                <p><strong>${customer?.name || 'غير محدد'}</strong></p>
                                ${customer?.phone ? `<p>هاتف: ${customer.phone}</p>` : ''}
                                ${customer?.email ? `<p>بريد: ${customer.email}</p>` : ''}
                                ${customer?.address ? `<p>عنوان: ${customer.address}</p>` : ''}
                            </div>
                        </div>

                        <!-- تفاصيل الإشعار -->
                        <div class="details-section">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
                                <div><strong>تاريخ الإشعار:</strong> ${new Date(creditNote.date).toLocaleDateString('ar-SA')}</div>
                                <div><strong>نوع الإشعار:</strong> ${this.getCreditNoteTypeLabel(creditNote.type)}</div>
                                <div><strong>الحالة:</strong> ${this.getCreditNoteStatusLabel(creditNote.status)}</div>
                            </div>
                            ${invoice ? `
                                <div><strong>الفاتورة المرجعية:</strong> ${invoice.number} - ${this.formatAmount(invoice.total)}</div>
                            ` : ''}
                        </div>

                        <!-- سبب الإشعار -->
                        <div class="reason-box">
                            <h4 style="margin: 0 0 10px 0; color: #856404;">سبب الإشعار الدائن:</h4>
                            <p style="margin: 0;">${creditNote.reason}</p>
                        </div>

                        ${creditNote.notes ? `
                            <div style="background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 8px; margin: 20px 0;">
                                <h4 style="margin: 0 0 10px 0; color: #0c5460;">ملاحظات إضافية:</h4>
                                <p style="margin: 0;">${creditNote.notes}</p>
                            </div>
                        ` : ''}

                        <!-- الإجماليات -->
                        <div class="totals-section">
                            <table class="totals-table">
                                <tr>
                                    <td>المبلغ الأساسي:</td>
                                    <td style="text-align: left;"><strong>${this.formatAmount(creditNote.amount)}</strong></td>
                                </tr>
                                <tr>
                                    <td>الضريبة:</td>
                                    <td style="text-align: left;"><strong>${this.formatAmount(creditNote.tax)}</strong></td>
                                </tr>
                                <tr class="total-row">
                                    <td>الإجمالي:</td>
                                    <td style="text-align: left;"><strong>${this.formatAmount(creditNote.total)}</strong></td>
                                </tr>
                            </table>
                        </div>

                        <!-- تذييل الإشعار -->
                        <div class="footer">
                            <p>شكراً لتعاملكم معنا</p>
                            <p>تم إنشاء هذا الإشعار في: ${new Date().toLocaleString('ar-SA')}</p>
                        </div>
                    </div>
                </div>

                <script>
                    window.onload = function() {
                        window.print();
                        window.onafterprint = function() {
                            window.close();
                        };
                    };
                </script>
            </body>
            </html>
        `;

        printWindow.document.write(printHTML);
        printWindow.document.close();
    },

    /**
     * فلترة الإشعارات الدائنة
     */
    filterCreditNotes: function() {
        const searchTerm = document.getElementById('creditnote-search')?.value.toLowerCase() || '';
        const statusFilter = document.getElementById('creditnote-status-filter')?.value || '';
        const dateFrom = document.getElementById('creditnote-date-from')?.value || '';
        const dateTo = document.getElementById('creditnote-date-to')?.value || '';

        const creditNotes = Object.values(this.data.creditNotes || {});
        let filteredCreditNotes = creditNotes;

        // فلترة بالبحث
        if (searchTerm) {
            filteredCreditNotes = filteredCreditNotes.filter(creditNote => {
                const customer = this.data.customers[creditNote.customerId];
                return creditNote.number.toLowerCase().includes(searchTerm) ||
                       (customer?.name.toLowerCase().includes(searchTerm)) ||
                       (creditNote.reference && creditNote.reference.toLowerCase().includes(searchTerm));
            });
        }

        // فلترة بالحالة
        if (statusFilter) {
            filteredCreditNotes = filteredCreditNotes.filter(creditNote => creditNote.status === statusFilter);
        }

        // فلترة بالتاريخ
        if (dateFrom) {
            filteredCreditNotes = filteredCreditNotes.filter(creditNote =>
                new Date(creditNote.date) >= new Date(dateFrom)
            );
        }

        if (dateTo) {
            filteredCreditNotes = filteredCreditNotes.filter(creditNote =>
                new Date(creditNote.date) <= new Date(dateTo)
            );
        }

        // تحديث الجدول
        this.updateCreditNotesTable(filteredCreditNotes);
    },

    /**
     * تحديث جدول الإشعارات الدائنة
     */
    updateCreditNotesTable: function(creditNotes) {
        const tbody = document.getElementById('creditnotes-tbody');
        const noMessage = document.getElementById('no-creditnotes-message');

        if (!tbody) return;

        if (creditNotes.length === 0) {
            tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted py-4">لا توجد إشعارات دائنة تطابق معايير البحث</td></tr>';
            if (noMessage) noMessage.style.display = 'block';
        } else {
            // ترتيب الإشعارات
            const sortedCreditNotes = creditNotes.sort((a, b) => new Date(b.date) - new Date(a.date));

            tbody.innerHTML = sortedCreditNotes.map(creditNote => {
                const customer = this.data.customers[creditNote.customerId];
                const invoice = creditNote.invoiceId ? this.data.invoices[creditNote.invoiceId] : null;

                return `
                    <tr data-creditnote-id="${creditNote.id}">
                        <td><input type="checkbox" class="creditnote-checkbox" value="${creditNote.id}"></td>
                        <td><strong class="text-success">${creditNote.number}</strong></td>
                        <td>${new Date(creditNote.date).toLocaleDateString('ar-SA')}</td>
                        <td>${customer?.name || 'غير محدد'}</td>
                        <td>${invoice ? `<span class="badge bg-info">${invoice.number}</span>` : '<span class="text-muted">غير مرتبط</span>'}</td>
                        <td class="text-end"><strong>${this.formatAmount(creditNote.total)}</strong></td>
                        <td><span class="badge bg-${this.getCreditNoteStatusColor(creditNote.status)}">${this.getCreditNoteStatusLabel(creditNote.status)}</span></td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="window.SalesComponent.viewCreditNote('${creditNote.id}')" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-secondary" onclick="window.SalesComponent.editCreditNote('${creditNote.id}')" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-success" onclick="window.SalesComponent.printCreditNote('${creditNote.id}')" title="طباعة">
                                    <i class="fas fa-print"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');

            if (noMessage) noMessage.style.display = 'none';
        }
    },

    /**
     * مسح فلاتر الإشعارات الدائنة
     */
    clearCreditNoteFilters: function() {
        document.getElementById('creditnote-search').value = '';
        document.getElementById('creditnote-status-filter').value = '';
        document.getElementById('creditnote-date-from').value = '';
        document.getElementById('creditnote-date-to').value = '';

        // إعادة عرض جميع الإشعارات
        const tbody = document.getElementById('creditnotes-tbody');
        if (tbody) {
            tbody.innerHTML = this.renderCreditNotesRows();
        }
    },

    /**
     * تحديث الإشعارات الدائنة
     */
    refreshCreditNotes: function() {
        this.loadSalesData();
        this.render({ view: 'creditNotes' });
        this.showNotification('تم تحديث قائمة الإشعارات الدائنة', 'success');
    },

    /**
     * تصدير الإشعارات الدائنة
     */
    exportCreditNotes: function() {
        const creditNotes = Object.values(this.data.creditNotes || {});
        if (creditNotes.length === 0) {
            this.showNotification('لا توجد إشعارات دائنة للتصدير', 'warning');
            return;
        }

        // إنشاء CSV
        const headers = ['رقم الإشعار', 'التاريخ', 'العميل', 'النوع', 'المبلغ', 'الحالة'];
        const csvContent = [
            headers.join(','),
            ...creditNotes.map(creditNote => {
                const customer = this.data.customers[creditNote.customerId];
                return [
                    creditNote.number,
                    new Date(creditNote.date).toLocaleDateString('ar-SA'),
                    customer?.name || 'غير محدد',
                    this.getCreditNoteTypeLabel(creditNote.type),
                    creditNote.total,
                    this.getCreditNoteStatusLabel(creditNote.status)
                ].join(',');
            })
        ].join('\n');

        // تحميل الملف
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `credit_notes_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();

        this.showNotification('تم تصدير الإشعارات الدائنة بنجاح', 'success');
    },

    /**
     * تحديد جميع الإشعارات الدائنة
     */
    selectAllCreditNotes: function() {
        const checkboxes = document.querySelectorAll('.creditnote-checkbox');
        const selectAllCheckbox = document.getElementById('select-all-creditnotes');

        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
        });
    },

    /**
     * تبديل تحديد الكل للإشعارات الدائنة
     */
    toggleSelectAllCreditNotes: function() {
        const checkboxes = document.querySelectorAll('.creditnote-checkbox');
        const selectAllCheckbox = document.getElementById('select-all-creditnotes');

        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
        });
    },

    /**
     * الإجراءات المجمعة للإشعارات الدائنة
     */
    bulkCreditNoteActions: function() {
        const selectedCreditNotes = Array.from(document.querySelectorAll('.creditnote-checkbox:checked'))
            .map(checkbox => checkbox.value);

        if (selectedCreditNotes.length === 0) {
            this.showNotification('يرجى تحديد إشعار دائن واحد على الأقل', 'warning');
            return;
        }

        const actions = [
            { value: 'apply', text: 'تطبيق الإشعارات', icon: 'fas fa-check' },
            { value: 'cancel', text: 'إلغاء الإشعارات', icon: 'fas fa-ban' },
            { value: 'delete', text: 'حذف الإشعارات', icon: 'fas fa-trash' },
            { value: 'export', text: 'تصدير المحدد', icon: 'fas fa-download' }
        ];

        const actionHTML = `
            <div class="modal fade" id="bulkCreditNoteActionsModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-cogs me-2"></i>إجراءات مجمعة للإشعارات الدائنة
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p>تم تحديد <strong>${selectedCreditNotes.length}</strong> إشعار دائن</p>
                            <p>اختر الإجراء المطلوب:</p>
                            <div class="list-group">
                                ${actions.map(action => `
                                    <button class="list-group-item list-group-item-action"
                                            onclick="window.SalesComponent.executeBulkCreditNoteAction('${action.value}', ${JSON.stringify(selectedCreditNotes)})">
                                        <i class="${action.icon} me-2"></i>${action.text}
                                    </button>
                                `).join('')}
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', actionHTML);
        const modal = new bootstrap.Modal(document.getElementById('bulkCreditNoteActionsModal'));
        modal.show();

        document.getElementById('bulkCreditNoteActionsModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * تنفيذ الإجراء المجمع للإشعارات الدائنة
     */
    executeBulkCreditNoteAction: function(action, creditNoteIds) {
        const modal = bootstrap.Modal.getInstance(document.getElementById('bulkCreditNoteActionsModal'));
        modal.hide();

        switch (action) {
            case 'apply':
                this.bulkApplyCreditNotes(creditNoteIds);
                break;
            case 'cancel':
                this.bulkCancelCreditNotes(creditNoteIds);
                break;
            case 'delete':
                this.bulkDeleteCreditNotes(creditNoteIds);
                break;
            case 'export':
                this.bulkExportCreditNotes(creditNoteIds);
                break;
        }
    },

    /**
     * تطبيق إشعارات دائنة متعددة
     */
    bulkApplyCreditNotes: function(creditNoteIds) {
        if (confirm(`هل تريد تطبيق ${creditNoteIds.length} إشعار دائن؟`)) {
            let appliedCount = 0;
            creditNoteIds.forEach(id => {
                const creditNote = this.data.creditNotes[id];
                if (creditNote && creditNote.status === 'pending') {
                    creditNote.status = 'applied';
                    creditNote.appliedAt = new Date().toISOString();
                    creditNote.updatedAt = new Date().toISOString();
                    appliedCount++;
                }
            });

            this.saveSalesData();
            this.render({ view: 'creditNotes' });
            this.showNotification(`تم تطبيق ${appliedCount} إشعار دائن`, 'success');
        }
    },

    /**
     * إلغاء إشعارات دائنة متعددة
     */
    bulkCancelCreditNotes: function(creditNoteIds) {
        if (confirm(`هل تريد إلغاء ${creditNoteIds.length} إشعار دائن؟`)) {
            let cancelledCount = 0;
            creditNoteIds.forEach(id => {
                const creditNote = this.data.creditNotes[id];
                if (creditNote && creditNote.status !== 'applied') {
                    creditNote.status = 'cancelled';
                    creditNote.updatedAt = new Date().toISOString();
                    cancelledCount++;
                }
            });

            this.saveSalesData();
            this.render({ view: 'creditNotes' });
            this.showNotification(`تم إلغاء ${cancelledCount} إشعار دائن`, 'warning');
        }
    },

    /**
     * حذف إشعارات دائنة متعددة
     */
    bulkDeleteCreditNotes: function(creditNoteIds) {
        if (confirm(`هل تريد حذف ${creditNoteIds.length} إشعار دائن؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            let deletedCount = 0;
            creditNoteIds.forEach(id => {
                const creditNote = this.data.creditNotes[id];
                if (creditNote && creditNote.status !== 'applied') {
                    delete this.data.creditNotes[id];
                    deletedCount++;
                }
            });

            this.saveSalesData();
            this.render({ view: 'creditNotes' });
            this.showNotification(`تم حذف ${deletedCount} إشعار دائن`, 'success');
        }
    },

    /**
     * تصدير إشعارات دائنة محددة
     */
    bulkExportCreditNotes: function(creditNoteIds) {
        const selectedCreditNotes = creditNoteIds.map(id => this.data.creditNotes[id]).filter(Boolean);

        if (selectedCreditNotes.length === 0) {
            this.showNotification('لا توجد إشعارات دائنة صالحة للتصدير', 'warning');
            return;
        }

        // إنشاء CSV للإشعارات المحددة
        const headers = ['رقم الإشعار', 'التاريخ', 'العميل', 'النوع', 'المبلغ', 'الحالة'];
        const csvContent = [
            headers.join(','),
            ...selectedCreditNotes.map(creditNote => {
                const customer = this.data.customers[creditNote.customerId];
                return [
                    creditNote.number,
                    new Date(creditNote.date).toLocaleDateString('ar-SA'),
                    customer?.name || 'غير محدد',
                    this.getCreditNoteTypeLabel(creditNote.type),
                    creditNote.total,
                    this.getCreditNoteStatusLabel(creditNote.status)
                ].join(',');
            })
        ].join('\n');

        // تحميل الملف
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `selected_credit_notes_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();

        this.showNotification(`تم تصدير ${selectedCreditNotes.length} إشعار دائن بنجاح`, 'success');
    },

    /**
     * تعديل الإشعار الدائن
     */
    editCreditNote: function(creditNoteId) {
        const creditNote = this.data.creditNotes[creditNoteId];
        if (!creditNote) {
            this.showNotification('الإشعار الدائن غير موجود', 'error');
            return;
        }

        if (creditNote.status === 'applied') {
            this.showNotification('لا يمكن تعديل إشعار دائن مطبق', 'warning');
            return;
        }

        // إغلاق نافذة العرض إذا كانت مفتوحة
        const viewModal = document.getElementById('viewCreditNoteModal');
        if (viewModal) {
            const modal = bootstrap.Modal.getInstance(viewModal);
            if (modal) modal.hide();
        }

        // فتح نافذة التعديل
        this.showEditCreditNoteModal(creditNote);
    },

    /**
     * عرض نافذة تعديل الإشعار الدائن
     */
    showEditCreditNoteModal: function(creditNote) {
        const customers = Object.values(this.data.customers || {});
        const invoices = Object.values(this.data.invoices || {}).filter(inv => inv.status === 'paid');

        const modalHTML = `
            <div class="modal fade" id="editCreditNoteModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header bg-warning text-dark">
                            <h5 class="modal-title">
                                <i class="fas fa-edit me-2"></i>تعديل الإشعار الدائن ${creditNote.number}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="editCreditNoteForm">
                                <input type="hidden" name="creditNoteId" value="${creditNote.id}">

                                <!-- معلومات الإشعار الأساسية -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات الإشعار الدائن</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <label class="form-label">رقم الإشعار الدائن</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="fas fa-hashtag"></i></span>
                                                    <input type="text" class="form-control" name="number" value="${creditNote.number}" readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">تاريخ الإشعار *</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                                    <input type="date" class="form-control" name="date" value="${creditNote.date}" required>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">نوع الإشعار *</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="fas fa-tag"></i></span>
                                                    <select class="form-control" name="type" required>
                                                        <option value="refund" ${creditNote.type === 'refund' ? 'selected' : ''}>استرداد كامل</option>
                                                        <option value="partial_refund" ${creditNote.type === 'partial_refund' ? 'selected' : ''}>استرداد جزئي</option>
                                                        <option value="discount" ${creditNote.type === 'discount' ? 'selected' : ''}>خصم إضافي</option>
                                                        <option value="correction" ${creditNote.type === 'correction' ? 'selected' : ''}>تصحيح خطأ</option>
                                                        <option value="return" ${creditNote.type === 'return' ? 'selected' : ''}>إرجاع بضاعة</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- معلومات العميل والفاتورة -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="fas fa-user me-2"></i>معلومات العميل والفاتورة</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <label class="form-label">العميل *</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                                    <select class="form-control" name="customerId" required>
                                                        ${customers.map(customer => `
                                                            <option value="${customer.id}" ${customer.id === creditNote.customerId ? 'selected' : ''}>
                                                                ${customer.name} ${customer.phone ? '- ' + customer.phone : ''}
                                                            </option>
                                                        `).join('')}
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">الفاتورة المرجعية</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="fas fa-file-invoice"></i></span>
                                                    <select class="form-control" name="invoiceId">
                                                        <option value="">اختر الفاتورة (اختياري)</option>
                                                        ${invoices.map(invoice => {
                                                            const customer = this.data.customers[invoice.customerId];
                                                            return `
                                                                <option value="${invoice.id}" ${invoice.id === creditNote.invoiceId ? 'selected' : ''}>
                                                                    ${invoice.number} - ${customer?.name || 'غير محدد'} - ${this.formatAmount(invoice.total)}
                                                                </option>
                                                            `;
                                                        }).join('')}
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- تفاصيل الإشعار -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="fas fa-edit me-2"></i>تفاصيل الإشعار الدائن</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <label class="form-label">المبلغ *</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">ر.س</span>
                                                    <input type="number" class="form-control" name="amount" step="0.01" min="0"
                                                           value="${creditNote.amount}" required onchange="window.SalesComponent.calculateEditCreditNoteTotal()">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">رقم المرجع</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="fas fa-tag"></i></span>
                                                    <input type="text" class="form-control" name="reference" value="${creditNote.reference || ''}" placeholder="رقم المرجع (اختياري)">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row mt-3">
                                            <div class="col-md-12">
                                                <label class="form-label">سبب الإشعار الدائن *</label>
                                                <textarea class="form-control" name="reason" rows="3" required>${creditNote.reason}</textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- ملخص الإشعار -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="fas fa-calculator me-2"></i>ملخص الإشعار الدائن</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">ملاحظات إضافية</label>
                                                    <textarea class="form-control" name="notes" rows="3">${creditNote.notes || ''}</textarea>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="card bg-light">
                                                    <div class="card-body">
                                                        <div class="d-flex justify-content-between mb-2">
                                                            <span>المبلغ الأساسي:</span>
                                                            <span id="edit-creditnote-amount" class="fw-bold">${this.formatAmount(creditNote.amount)}</span>
                                                        </div>
                                                        <div class="d-flex justify-content-between mb-2">
                                                            <span>الضريبة:</span>
                                                            <span id="edit-creditnote-tax" class="fw-bold">${this.formatAmount(creditNote.tax)}</span>
                                                        </div>
                                                        <hr>
                                                        <div class="d-flex justify-content-between mb-3">
                                                            <span class="h6">الإجمالي:</span>
                                                            <span id="edit-creditnote-total" class="h5 text-success fw-bold">${this.formatAmount(creditNote.total)}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer bg-light">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </button>
                            <button type="button" class="btn btn-warning" onclick="window.SalesComponent.updateCreditNote()">
                                <i class="fas fa-save me-1"></i>حفظ التعديلات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('editCreditNoteModal'));
        modal.show();

        document.getElementById('editCreditNoteModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * حساب إجمالي الإشعار الدائن في التعديل
     */
    calculateEditCreditNoteTotal: function() {
        const amountInput = document.querySelector('#editCreditNoteForm input[name="amount"]');
        if (!amountInput) return;

        const amount = parseFloat(amountInput.value) || 0;
        const taxRate = this.data.settings?.taxRate || 0.15;
        const tax = amount * taxRate;
        const total = amount + tax;

        // تحديث عناصر العرض
        const amountElement = document.getElementById('edit-creditnote-amount');
        const taxElement = document.getElementById('edit-creditnote-tax');
        const totalElement = document.getElementById('edit-creditnote-total');

        if (amountElement) amountElement.textContent = this.formatAmount(amount);
        if (taxElement) taxElement.textContent = this.formatAmount(tax);
        if (totalElement) totalElement.textContent = this.formatAmount(total);

        return { amount, tax, total };
    },

    /**
     * تحديث الإشعار الدائن
     */
    updateCreditNote: function() {
        try {
            const form = document.getElementById('editCreditNoteForm');
            if (!form) {
                throw new Error('نموذج تعديل الإشعار الدائن غير موجود');
            }

            const formData = new FormData(form);
            const creditNoteId = formData.get('creditNoteId');

            if (!creditNoteId || !this.data.creditNotes[creditNoteId]) {
                throw new Error('الإشعار الدائن غير موجود');
            }

            // التحقق من البيانات المطلوبة
            if (!formData.get('customerId')) {
                this.showNotification('يرجى اختيار العميل', 'warning');
                return;
            }

            if (!formData.get('type')) {
                this.showNotification('يرجى اختيار نوع الإشعار', 'warning');
                return;
            }

            if (!formData.get('amount') || parseFloat(formData.get('amount')) <= 0) {
                this.showNotification('يرجى إدخال مبلغ صحيح', 'warning');
                return;
            }

            if (!formData.get('reason')) {
                this.showNotification('يرجى إدخال سبب الإشعار الدائن', 'warning');
                return;
            }

            // حساب الإجماليات
            const totals = this.calculateEditCreditNoteTotal();

            // تحديث الإشعار الدائن
            const creditNote = this.data.creditNotes[creditNoteId];
            creditNote.date = formData.get('date');
            creditNote.type = formData.get('type');
            creditNote.customerId = formData.get('customerId');
            creditNote.invoiceId = formData.get('invoiceId') || null;
            creditNote.reference = formData.get('reference') || '';
            creditNote.amount = totals.amount;
            creditNote.tax = totals.tax;
            creditNote.total = totals.total;
            creditNote.reason = formData.get('reason');
            creditNote.notes = formData.get('notes') || '';
            creditNote.updatedAt = new Date().toISOString();

            // حفظ البيانات
            this.saveSalesData();

            // إغلاق النافذة
            const modalElement = document.getElementById('editCreditNoteModal');
            if (modalElement) {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                }
            }

            // إظهار رسالة نجاح
            this.showNotification(`تم تحديث الإشعار الدائن ${creditNote.number} بنجاح`, 'success');

            // تحديث العرض إذا كنا في صفحة الإشعارات الدائنة
            if (this.data.currentView === 'creditNotes') {
                this.render({ view: 'creditNotes' });
            }

            return creditNote;

        } catch (error) {
            console.error('خطأ في تحديث الإشعار الدائن:', error);
            this.handleError('فشل في تحديث الإشعار الدائن', error);
            return null;
        }
    },

    /**
     * نسخ الإشعار الدائن
     */
    duplicateCreditNote: function(creditNoteId) {
        const creditNote = this.data.creditNotes[creditNoteId];
        if (!creditNote) {
            this.showNotification('الإشعار الدائن غير موجود', 'error');
            return;
        }

        // إنشاء نسخة جديدة
        const newCreditNoteId = 'creditnote_' + Date.now();
        const newCreditNote = {
            ...creditNote,
            id: newCreditNoteId,
            number: this.generateCreditNoteNumber(),
            date: new Date().toISOString().split('T')[0],
            status: 'draft',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            appliedAt: null
        };

        // حفظ النسخة الجديدة
        this.data.creditNotes[newCreditNoteId] = newCreditNote;

        // تحديث رقم الإشعار التالي
        this.data.settings.nextCreditNoteNumber++;

        this.saveSalesData();
        this.render({ view: 'creditNotes' });

        this.showNotification(`تم نسخ الإشعار الدائن - رقم جديد: ${newCreditNote.number}`, 'success');
    },

    /**
     * إلغاء الإشعار الدائن
     */
    cancelCreditNote: function(creditNoteId) {
        const creditNote = this.data.creditNotes[creditNoteId];
        if (!creditNote) {
            this.showNotification('الإشعار الدائن غير موجود', 'error');
            return;
        }

        if (creditNote.status === 'applied') {
            this.showNotification('لا يمكن إلغاء إشعار دائن مطبق', 'warning');
            return;
        }

        if (confirm(`هل تريد إلغاء الإشعار الدائن ${creditNote.number}؟`)) {
            creditNote.status = 'cancelled';
            creditNote.updatedAt = new Date().toISOString();
            this.saveSalesData();
            this.render({ view: 'creditNotes' });
            this.showNotification(`تم إلغاء الإشعار الدائن ${creditNote.number}`, 'warning');
        }
    },

    /**
     * تحميل الإشعار الدائن كـ PDF
     */
    downloadCreditNotePDF: function(creditNoteId) {
        // هذه الوظيفة ستحتاج مكتبة PDF خارجية
        // حالياً سنعرض رسالة
        this.showNotification('وظيفة تحميل PDF ستكون متاحة قريباً', 'info');
    },

    /**
     * عرض صفحة العملاء
     */
    renderCustomersView: function() {
        const customers = Object.values(this.data.customers || {});

        return `
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-users me-2"></i>إدارة العملاء</h2>
                    <button class="btn btn-primary" onclick="window.SalesComponent.showCreateCustomerModal()">
                        <i class="fas fa-plus me-1"></i>عميل جديد
                    </button>
                </div>

                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الهاتف</th>
                                        <th>المدينة</th>
                                        <th>إجمالي المشتريات</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${customers.map(customer => `
                                        <tr>
                                            <td><strong>${customer.name}</strong></td>
                                            <td>${customer.email || 'غير محدد'}</td>
                                            <td>${customer.phone || 'غير محدد'}</td>
                                            <td>${customer.city || 'غير محدد'}</td>
                                            <td>${this.formatAmount(customer.totalPurchases || 0)}</td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="window.SalesComponent.viewCustomer('${customer.id}')">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-success" onclick="window.SalesComponent.editCustomer('${customer.id}')">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>

                        ${customers.length === 0 ? `
                            <div class="text-center py-4">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد عملاء مسجلين</p>
                                <button class="btn btn-primary" onclick="window.SalesComponent.showCreateCustomerModal()">
                                    <i class="fas fa-plus me-1"></i>إضافة عميل جديد
                                </button>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض صفحة المنتجات
     */
    renderProductsView: function() {
        const products = Object.values(this.data.products || {});

        return `
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-box me-2"></i>إدارة المنتجات والخدمات</h2>
                    <button class="btn btn-primary" onclick="window.SalesComponent.showCreateProductModal()">
                        <i class="fas fa-plus me-1"></i>منتج جديد
                    </button>
                </div>

                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>اسم المنتج</th>
                                        <th>الفئة</th>
                                        <th>السعر</th>
                                        <th>الكمية المتاحة</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${products.map(product => `
                                        <tr>
                                            <td><strong>${product.name}</strong></td>
                                            <td>${product.category || 'غير محدد'}</td>
                                            <td>${this.formatAmount(product.price, product.currency)}</td>
                                            <td>${product.quantity || 0}</td>
                                            <td>
                                                <span class="badge bg-${product.quantity > 0 ? 'success' : 'warning'}">
                                                    ${product.quantity > 0 ? 'متاح' : 'نفد المخزون'}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="window.SalesComponent.viewProduct('${product.id}')">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-success" onclick="window.SalesComponent.editProduct('${product.id}')">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>

                        ${products.length === 0 ? `
                            <div class="text-center py-4">
                                <i class="fas fa-box fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد منتجات مسجلة</p>
                                <button class="btn btn-primary" onclick="window.SalesComponent.showCreateProductModal()">
                                    <i class="fas fa-plus me-1"></i>إضافة منتج جديد
                                </button>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض صفحة التقارير المحسنة
     */
    renderReportsView: function() {
        const invoices = Object.values(this.data.invoices || {});
        const customers = Object.values(this.data.customers || {});
        const products = Object.values(this.data.products || {});

        // حساب الإحصائيات المتقدمة
        const totalSales = invoices.filter(inv => inv.status === 'paid').reduce((sum, inv) => sum + inv.total, 0);
        const pendingSales = invoices.filter(inv => inv.status === 'pending').reduce((sum, inv) => sum + inv.total, 0);
        const draftSales = invoices.filter(inv => inv.status === 'draft').reduce((sum, inv) => sum + inv.total, 0);

        // إحصائيات شهرية
        const now = new Date();
        const thisMonthSales = invoices.filter(inv => {
            const invDate = new Date(inv.date);
            return invDate.getMonth() === now.getMonth() && invDate.getFullYear() === now.getFullYear();
        }).reduce((sum, inv) => sum + inv.total, 0);

        const lastMonthSales = invoices.filter(inv => {
            const invDate = new Date(inv.date);
            const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
            return invDate.getMonth() === lastMonth.getMonth() && invDate.getFullYear() === lastMonth.getFullYear();
        }).reduce((sum, inv) => sum + inv.total, 0);

        // حساب النمو
        const growthRate = lastMonthSales > 0 ? ((thisMonthSales - lastMonthSales) / lastMonthSales * 100) : 0;

        // أفضل العملاء (مرتبين حسب المشتريات)
        const topCustomers = customers
            .sort((a, b) => (b.totalPurchases || 0) - (a.totalPurchases || 0))
            .slice(0, 5);

        // أكثر المنتجات مبيعاً
        const topProducts = products
            .sort((a, b) => (b.soldQuantity || 0) - (a.soldQuantity || 0))
            .slice(0, 5);

        // إحصائيات المخزون
        const lowStockProducts = products.filter(p => p.quantity <= p.minStock && p.status === 'active');
        const outOfStockProducts = products.filter(p => p.quantity === 0 && p.status === 'active');

        return `
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-chart-bar me-2"></i>تقارير المبيعات المتقدمة</h2>
                    <div class="btn-group">
                        <button class="btn btn-outline-primary" onclick="window.SalesComponent.exportReport('pdf')">
                            <i class="fas fa-file-pdf me-1"></i>تصدير PDF
                        </button>
                        <button class="btn btn-outline-success" onclick="window.SalesComponent.exportReport('excel')">
                            <i class="fas fa-file-excel me-1"></i>تصدير Excel
                        </button>
                        <button class="btn btn-outline-info" onclick="window.SalesComponent.refreshReports()">
                            <i class="fas fa-sync me-1"></i>تحديث
                        </button>
                    </div>
                </div>

                <!-- الإحصائيات الرئيسية المحسنة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${this.formatAmount(totalSales)}</h4>
                                        <p class="mb-0">إجمالي المبيعات المحصلة</p>
                                    </div>
                                    <i class="fas fa-money-bill-wave fa-2x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${this.formatAmount(pendingSales)}</h4>
                                        <p class="mb-0">المبيعات المعلقة</p>
                                    </div>
                                    <i class="fas fa-clock fa-2x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${this.formatAmount(thisMonthSales)}</h4>
                                        <p class="mb-0">مبيعات هذا الشهر</p>
                                        <small class="text-light">
                                            ${growthRate >= 0 ? '↗️' : '↘️'} ${Math.abs(growthRate).toFixed(1)}%
                                        </small>
                                    </div>
                                    <i class="fas fa-calendar-month fa-2x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${invoices.length}</h4>
                                        <p class="mb-0">إجمالي الفواتير</p>
                                        <small class="text-light">${draftSales > 0 ? this.formatAmount(draftSales) + ' مسودات' : 'لا توجد مسودات'}</small>
                                    </div>
                                    <i class="fas fa-file-invoice fa-2x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تحذيرات المخزون -->
                ${(lowStockProducts.length > 0 || outOfStockProducts.length > 0) ? `
                    <div class="alert alert-warning mb-4">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>تنبيهات المخزون</h6>
                        ${outOfStockProducts.length > 0 ? `<p class="mb-1"><strong>نفد المخزون:</strong> ${outOfStockProducts.length} منتج</p>` : ''}
                        ${lowStockProducts.length > 0 ? `<p class="mb-0"><strong>مخزون منخفض:</strong> ${lowStockProducts.length} منتج</p>` : ''}
                    </div>
                ` : ''}

                <!-- تقارير تفصيلية -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-users me-2"></i>أفضل العملاء</h5>
                            </div>
                            <div class="card-body">
                                ${customers.slice(0, 5).map(customer => `
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>${customer.name}</span>
                                        <span class="badge bg-primary">${this.formatAmount(customer.totalPurchases || 0)}</span>
                                    </div>
                                `).join('')}
                                ${customers.length === 0 ? '<p class="text-muted">لا توجد بيانات عملاء</p>' : ''}
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-box me-2"></i>أكثر المنتجات مبيعاً</h5>
                            </div>
                            <div class="card-body">
                                ${products.slice(0, 5).map(product => `
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>${product.name}</span>
                                        <span class="badge bg-success">${product.soldQuantity || 0} وحدة</span>
                                    </div>
                                `).join('')}
                                ${products.length === 0 ? '<p class="text-muted">لا توجد بيانات منتجات</p>' : ''}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسوم البيانية -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-line me-2"></i>اتجاه المبيعات</h5>
                            </div>
                            <div class="card-body">
                                <div id="salesChart" style="height: 300px; display: flex; align-items: center; justify-content: center; background: #f8f9fa; border-radius: 8px;">
                                    <div class="text-center">
                                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">رسم بياني لاتجاه المبيعات</p>
                                        <small class="text-muted">يتطلب مكتبة Chart.js للعرض الكامل</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-pie me-2"></i>توزيع الفواتير</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>مدفوعة</span>
                                        <span class="badge bg-success">${invoices.filter(i => i.status === 'paid').length}</span>
                                    </div>
                                    <div class="progress mb-2">
                                        <div class="progress-bar bg-success" style="width: ${invoices.length > 0 ? (invoices.filter(i => i.status === 'paid').length / invoices.length * 100) : 0}%"></div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>معلقة</span>
                                        <span class="badge bg-warning">${invoices.filter(i => i.status === 'pending').length}</span>
                                    </div>
                                    <div class="progress mb-2">
                                        <div class="progress-bar bg-warning" style="width: ${invoices.length > 0 ? (invoices.filter(i => i.status === 'pending').length / invoices.length * 100) : 0}%"></div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>مسودات</span>
                                        <span class="badge bg-secondary">${invoices.filter(i => i.status === 'draft').length}</span>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar bg-secondary" style="width: ${invoices.length > 0 ? (invoices.filter(i => i.status === 'draft').length / invoices.length * 100) : 0}%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول الفواتير الأخيرة -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-file-invoice me-2"></i>آخر الفواتير</h5>
                    </div>
                    <div class="card-body">
                        ${invoices.length > 0 ? `
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>رقم الفاتورة</th>
                                            <th>التاريخ</th>
                                            <th>العميل</th>
                                            <th>المبلغ</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${invoices.slice(0, 10).map(invoice => {
                                            const customer = customers.find(c => c.id === invoice.customerId);
                                            return `
                                                <tr>
                                                    <td>${invoice.number}</td>
                                                    <td>${new Date(invoice.date).toLocaleDateString('ar-SA')}</td>
                                                    <td>${customer?.name || 'غير محدد'}</td>
                                                    <td>${this.formatAmount(invoice.total)}</td>
                                                    <td><span class="badge bg-${this.getStatusColor(invoice.status)}">${this.getStatusLabel(invoice.status)}</span></td>
                                                </tr>
                                            `;
                                        }).join('')}
                                    </tbody>
                                </table>
                            </div>
                        ` : `
                            <div class="text-center py-4">
                                <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد فواتير</p>
                            </div>
                        `}
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * العروض الأخرى (مبسطة)
     */
    renderQuotesView: function() { return '<div class="alert alert-info">عرض عروض الأسعار متاح في النسخة الكاملة</div>'; },
    renderInventoryView: function() { return '<div class="alert alert-info">عرض المخزون متاح في النسخة الكاملة</div>'; },
    renderPaymentsView: function() {
        const invoices = Object.values(this.data.invoices || {});
        const payments = Object.values(this.data.payments || {});

        // حساب الإحصائيات
        const totalReceived = payments.reduce((sum, payment) => sum + payment.amount, 0);
        const totalPending = invoices.filter(inv => inv.status === 'pending').reduce((sum, inv) => sum + inv.total, 0);
        const totalOverdue = invoices.filter(inv => {
            if (inv.status !== 'pending') return false;
            const dueDate = new Date(inv.dueDate);
            return dueDate < new Date();
        }).reduce((sum, inv) => sum + inv.total, 0);

        // آخر المدفوعات
        const recentPayments = payments
            .sort((a, b) => new Date(b.date) - new Date(a.date))
            .slice(0, 10);

        // الفواتير المستحقة
        const overdueInvoices = invoices.filter(inv => {
            if (inv.status !== 'pending') return false;
            const dueDate = new Date(inv.dueDate);
            return dueDate < new Date();
        }).sort((a, b) => new Date(a.dueDate) - new Date(b.dueDate));

        return `
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-credit-card me-2"></i>إدارة المدفوعات</h2>
                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="window.SalesComponent.showAddPaymentModal()">
                            <i class="fas fa-plus me-1"></i>تسجيل دفعة
                        </button>
                        <button class="btn btn-outline-info" onclick="window.SalesComponent.refreshPayments()">
                            <i class="fas fa-sync me-1"></i>تحديث
                        </button>
                    </div>
                </div>

                <!-- إحصائيات المدفوعات -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${this.formatAmount(totalReceived)}</h4>
                                        <p class="mb-0">إجمالي المحصل</p>
                                    </div>
                                    <i class="fas fa-money-bill-wave fa-2x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${this.formatAmount(totalPending)}</h4>
                                        <p class="mb-0">المبالغ المعلقة</p>
                                    </div>
                                    <i class="fas fa-clock fa-2x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${this.formatAmount(totalOverdue)}</h4>
                                        <p class="mb-0">المتأخرات</p>
                                    </div>
                                    <i class="fas fa-exclamation-triangle fa-2x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${payments.length}</h4>
                                        <p class="mb-0">عدد المدفوعات</p>
                                    </div>
                                    <i class="fas fa-list fa-2x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تحذيرات المتأخرات -->
                ${overdueInvoices.length > 0 ? `
                    <div class="alert alert-danger mb-4">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>فواتير متأخرة السداد</h6>
                        <p class="mb-0">يوجد ${overdueInvoices.length} فاتورة متأخرة بإجمالي ${this.formatAmount(totalOverdue)}</p>
                    </div>
                ` : ''}

                <div class="row">
                    <!-- آخر المدفوعات -->
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5><i class="fas fa-history me-2"></i>آخر المدفوعات</h5>
                                <span class="badge bg-primary">${recentPayments.length}</span>
                            </div>
                            <div class="card-body">
                                ${recentPayments.length > 0 ? `
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>التاريخ</th>
                                                    <th>رقم الفاتورة</th>
                                                    <th>العميل</th>
                                                    <th>المبلغ</th>
                                                    <th>طريقة الدفع</th>
                                                    <th>الحالة</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${recentPayments.map(payment => {
                                                    const invoice = this.data.invoices[payment.invoiceId];
                                                    const customer = invoice ? this.data.customers[invoice.customerId] : null;
                                                    return `
                                                        <tr>
                                                            <td>${new Date(payment.date).toLocaleDateString('ar-SA')}</td>
                                                            <td>${invoice ? invoice.number : 'غير محدد'}</td>
                                                            <td>${customer ? customer.name : 'غير محدد'}</td>
                                                            <td>${this.formatAmount(payment.amount)}</td>
                                                            <td>
                                                                <span class="badge bg-secondary">${this.getPaymentMethodLabel(payment.method)}</span>
                                                            </td>
                                                            <td>
                                                                <span class="badge bg-${payment.status === 'confirmed' ? 'success' : 'warning'}">
                                                                    ${payment.status === 'confirmed' ? 'مؤكد' : 'معلق'}
                                                                </span>
                                                            </td>
                                                        </tr>
                                                    `;
                                                }).join('')}
                                            </tbody>
                                        </table>
                                    </div>
                                ` : `
                                    <div class="text-center py-4">
                                        <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد مدفوعات مسجلة</p>
                                        <button class="btn btn-primary" onclick="window.SalesComponent.showAddPaymentModal()">
                                            <i class="fas fa-plus me-1"></i>تسجيل أول دفعة
                                        </button>
                                    </div>
                                `}
                            </div>
                        </div>
                    </div>

                    <!-- الفواتير المستحقة -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5><i class="fas fa-exclamation-circle me-2"></i>فواتير مستحقة</h5>
                                <span class="badge bg-warning">${overdueInvoices.length}</span>
                            </div>
                            <div class="card-body">
                                ${overdueInvoices.length > 0 ? overdueInvoices.slice(0, 5).map(invoice => {
                                    const customer = this.data.customers[invoice.customerId];
                                    const daysPastDue = Math.floor((new Date() - new Date(invoice.dueDate)) / (1000 * 60 * 60 * 24));
                                    return `
                                        <div class="d-flex justify-content-between align-items-center mb-3 p-2 border rounded">
                                            <div>
                                                <strong>${invoice.number}</strong>
                                                <br><small class="text-muted">${customer ? customer.name : 'غير محدد'}</small>
                                                <br><small class="text-danger">متأخر ${daysPastDue} يوم</small>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge bg-danger">${this.formatAmount(invoice.total)}</span>
                                                <br><button class="btn btn-sm btn-outline-primary mt-1" onclick="window.SalesComponent.showAddPaymentModal('${invoice.id}')">
                                                    دفع
                                                </button>
                                            </div>
                                        </div>
                                    `;
                                }).join('') : `
                                    <div class="text-center py-3">
                                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                        <p class="text-muted mb-0">لا توجد فواتير متأخرة</p>
                                    </div>
                                `}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },
    renderSettingsView: function() { return '<div class="alert alert-info">عرض الإعدادات متاح في النسخة الكاملة</div>'; },
    updateReports: function() { /* وظيفة فارغة للتوافق */ },

    /**
     * وظائف مساعدة للعملاء والمنتجات
     */
    showCreateCustomerModal: function() {
        const modalHTML = `
            <div class="modal fade" id="createCustomerModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-user-plus me-2"></i>إضافة عميل جديد
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="createCustomerForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">اسم العميل *</label>
                                            <input type="text" class="form-control" name="name" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">نوع العميل</label>
                                            <select class="form-control" name="type">
                                                <option value="individual">فرد</option>
                                                <option value="company">شركة</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">البريد الإلكتروني</label>
                                            <input type="email" class="form-control" name="email">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رقم الهاتف *</label>
                                            <input type="tel" class="form-control" name="phone" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">المدينة</label>
                                            <input type="text" class="form-control" name="city">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الرقم الضريبي</label>
                                            <input type="text" class="form-control" name="taxNumber">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea class="form-control" name="address" rows="3"></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">حد الائتمان</label>
                                            <input type="number" class="form-control" name="creditLimit" step="0.01" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">مدة السداد (أيام)</label>
                                            <input type="number" class="form-control" name="paymentTerms" value="30" min="0">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="2"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveCustomer()">
                                <i class="fas fa-save me-1"></i>حفظ العميل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('createCustomerModal'));
        modal.show();

        document.getElementById('createCustomerModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * حفظ العميل الجديد
     */
    saveCustomer: function() {
        const form = document.getElementById('createCustomerForm');
        const formData = new FormData(form);

        // التحقق من البيانات المطلوبة
        if (!formData.get('name') || !formData.get('phone')) {
            this.showNotification('يرجى ملء الحقول المطلوبة (الاسم والهاتف)', 'warning');
            return;
        }

        // إنشاء العميل
        const customerId = 'customer_' + Date.now();
        const customer = {
            id: customerId,
            name: formData.get('name'),
            type: formData.get('type') || 'individual',
            email: formData.get('email'),
            phone: formData.get('phone'),
            city: formData.get('city'),
            taxNumber: formData.get('taxNumber'),
            address: formData.get('address'),
            creditLimit: parseFloat(formData.get('creditLimit')) || 0,
            paymentTerms: parseInt(formData.get('paymentTerms')) || 30,
            notes: formData.get('notes'),
            totalPurchases: 0,
            outstandingBalance: 0,
            createdAt: new Date().toISOString(),
            lastPurchase: null,
            status: 'active'
        };

        try {
            // حفظ العميل
            if (!this.data.customers) this.data.customers = {};
            this.data.customers[customerId] = customer;
            this.saveSalesData();

            // إغلاق النافذة وتحديث العرض
            const modalElement = document.getElementById('createCustomerModal');
            if (modalElement) {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                }
            }

            this.showNotification('تم إضافة العميل بنجاح', 'success');

            // تحديث العرض إذا كنا في صفحة العملاء
            if (this.data.currentView === 'customers') {
                this.render({ view: 'customers' });
            }

        } catch (error) {
            console.error('خطأ في حفظ العميل:', error);
            this.showNotification('حدث خطأ أثناء حفظ العميل', 'error');
        }
    },

    showCreateProductModal: function() {
        const modalHTML = `
            <div class="modal fade" id="createProductModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-box me-2"></i>إضافة منتج جديد
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="createProductForm">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label class="form-label">اسم المنتج *</label>
                                            <input type="text" class="form-control" name="name" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">كود المنتج</label>
                                            <input type="text" class="form-control" name="code" placeholder="اختياري">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الفئة</label>
                                            <select class="form-control" name="category">
                                                <option value="">اختر الفئة</option>
                                                <option value="travel">خدمات سفر</option>
                                                <option value="accommodation">إقامة</option>
                                                <option value="transportation">نقل</option>
                                                <option value="tours">جولات سياحية</option>
                                                <option value="visa">تأشيرات</option>
                                                <option value="insurance">تأمين</option>
                                                <option value="other">أخرى</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">نوع المنتج</label>
                                            <select class="form-control" name="type">
                                                <option value="service">خدمة</option>
                                                <option value="product">منتج</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">وصف المنتج</label>
                                    <textarea class="form-control" name="description" rows="3"></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">السعر *</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" name="price" step="0.01" min="0" required
                                                       onchange="window.SalesComponent.updatePricePreview()">
                                                <button class="btn btn-outline-secondary dropdown-toggle" type="button"
                                                        data-bs-toggle="dropdown" id="currencyDropdown">
                                                    <span id="selectedCurrency">ر.ي</span>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.selectCurrency('YER', 'ر.ي')">
                                                        <i class="fas fa-coins me-2"></i>ريال يمني (ر.ي)
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.selectCurrency('SAR', 'ر.س')">
                                                        <i class="fas fa-coins me-2"></i>ريال سعودي (ر.س)
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.selectCurrency('USD', '$')">
                                                        <i class="fas fa-dollar-sign me-2"></i>دولار أمريكي ($)
                                                    </a></li>
                                                </ul>
                                                <input type="hidden" name="currency" value="YER" id="productCurrency">
                                            </div>
                                            <small class="text-muted" id="pricePreview">0.00 ر.ي</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">تكلفة الشراء</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" name="cost" step="0.01" min="0"
                                                       onchange="window.SalesComponent.updateCostPreview()">
                                                <span class="input-group-text" id="costCurrencySymbol">ر.ي</span>
                                            </div>
                                            <small class="text-muted" id="costPreview">0.00 ر.ي</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">الكمية المتاحة</label>
                                            <input type="number" class="form-control" name="quantity" min="0" value="0">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">هامش الربح</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="profitMargin" step="0.01" min="0" readonly>
                                                <span class="input-group-text" id="profitCurrencySymbol">ر.ي</span>
                                            </div>
                                            <small class="text-muted" id="profitPercentage">0%</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الحد الأدنى للمخزون</label>
                                            <input type="number" class="form-control" name="minStock" min="0" value="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">وحدة القياس</label>
                                            <select class="form-control" name="unit">
                                                <option value="piece">قطعة</option>
                                                <option value="service">خدمة</option>
                                                <option value="night">ليلة</option>
                                                <option value="day">يوم</option>
                                                <option value="person">شخص</option>
                                                <option value="kg">كيلوجرام</option>
                                                <option value="liter">لتر</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">المورد</label>
                                            <input type="text" class="form-control" name="supplier">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الحالة</label>
                                            <select class="form-control" name="status">
                                                <option value="active">نشط</option>
                                                <option value="inactive">غير نشط</option>
                                                <option value="discontinued">متوقف</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="2"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveProduct()">
                                <i class="fas fa-save me-1"></i>حفظ المنتج
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('createProductModal'));
        modal.show();

        document.getElementById('createProductModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * اختيار العملة في نافذة المنتج
     */
    selectCurrency: function(currencyCode, currencySymbol) {
        document.getElementById('selectedCurrency').textContent = currencySymbol;
        document.getElementById('productCurrency').value = currencyCode;
        document.getElementById('costCurrencySymbol').textContent = currencySymbol;
        document.getElementById('profitCurrencySymbol').textContent = currencySymbol;

        // تحديث المعاينات
        this.updatePricePreview();
        this.updateCostPreview();
        this.calculateProfitMargin();
    },

    /**
     * تحديث معاينة السعر
     */
    updatePricePreview: function() {
        const priceInput = document.querySelector('input[name="price"]');
        const currencyCode = document.getElementById('productCurrency')?.value || 'YER';
        const price = parseFloat(priceInput?.value) || 0;

        const previewElement = document.getElementById('pricePreview');
        if (previewElement) {
            previewElement.textContent = this.formatAmount(price, currencyCode);
        }

        this.calculateProfitMargin();
    },

    /**
     * تحديث معاينة التكلفة
     */
    updateCostPreview: function() {
        const costInput = document.querySelector('input[name="cost"]');
        const currencyCode = document.getElementById('productCurrency')?.value || 'YER';
        const cost = parseFloat(costInput?.value) || 0;

        const previewElement = document.getElementById('costPreview');
        if (previewElement) {
            previewElement.textContent = this.formatAmount(cost, currencyCode);
        }

        this.calculateProfitMargin();
    },

    /**
     * حساب هامش الربح
     */
    calculateProfitMargin: function() {
        const priceInput = document.querySelector('input[name="price"]');
        const costInput = document.querySelector('input[name="cost"]');
        const profitMarginElement = document.getElementById('profitMargin');
        const profitPercentageElement = document.getElementById('profitPercentage');

        if (!priceInput || !costInput || !profitMarginElement || !profitPercentageElement) return;

        const price = parseFloat(priceInput.value) || 0;
        const cost = parseFloat(costInput.value) || 0;
        const profit = price - cost;
        const profitPercentage = cost > 0 ? ((profit / cost) * 100) : 0;

        profitMarginElement.value = profit.toFixed(2);
        profitPercentageElement.textContent = `${profitPercentage.toFixed(1)}%`;

        // تلوين هامش الربح
        if (profit > 0) {
            profitPercentageElement.className = 'text-success';
        } else if (profit < 0) {
            profitPercentageElement.className = 'text-danger';
        } else {
            profitPercentageElement.className = 'text-muted';
        }
    },

    /**
     * حفظ المنتج الجديد
     */
    saveProduct: function() {
        const form = document.getElementById('createProductForm');
        const formData = new FormData(form);

        // التحقق من البيانات المطلوبة
        if (!formData.get('name') || !formData.get('price')) {
            this.showNotification('يرجى ملء الحقول المطلوبة (الاسم والسعر)', 'warning');
            return;
        }

        // إنشاء المنتج
        const productId = 'product_' + Date.now();
        const currency = formData.get('currency') || 'YER';
        const price = parseFloat(formData.get('price'));
        const cost = parseFloat(formData.get('cost')) || 0;

        const product = {
            id: productId,
            name: formData.get('name'),
            code: formData.get('code') || productId,
            category: formData.get('category'),
            type: formData.get('type') || 'service',
            description: formData.get('description'),
            price: price,
            cost: cost,
            currency: currency,
            profitMargin: price - cost,
            profitPercentage: cost > 0 ? ((price - cost) / cost * 100) : 0,
            quantity: parseInt(formData.get('quantity')) || 0,
            minStock: parseInt(formData.get('minStock')) || 0,
            unit: formData.get('unit') || 'piece',
            supplier: formData.get('supplier'),
            status: formData.get('status') || 'active',
            notes: formData.get('notes'),
            soldQuantity: 0,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        try {
            // حفظ المنتج
            if (!this.data.products) this.data.products = {};
            this.data.products[productId] = product;
            this.saveSalesData();

            // إغلاق النافذة وتحديث العرض
            const modalElement = document.getElementById('createProductModal');
            if (modalElement) {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                }
            }

            this.showNotification('تم إضافة المنتج بنجاح', 'success');

            // تحديث العرض إذا كنا في صفحة المنتجات
            if (this.data.currentView === 'products') {
                this.render({ view: 'products' });
            }

        } catch (error) {
            console.error('خطأ في حفظ المنتج:', error);
            this.showNotification('حدث خطأ أثناء حفظ المنتج', 'error');
        }
    },

    viewCustomer: function(customerId) {
        const customer = this.data.customers[customerId];
        if (!customer) {
            this.showNotification('العميل غير موجود', 'error');
            return;
        }

        const modalHTML = `
            <div class="modal fade" id="viewCustomerModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-user me-2"></i>تفاصيل العميل: ${customer.name}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-info-circle me-2"></i>المعلومات الأساسية</h6>
                                        </div>
                                        <div class="card-body">
                                            <p><strong>الاسم:</strong> ${customer.name}</p>
                                            <p><strong>النوع:</strong> ${customer.type === 'individual' ? 'فرد' : 'شركة'}</p>
                                            <p><strong>البريد الإلكتروني:</strong> ${customer.email || 'غير محدد'}</p>
                                            <p><strong>الهاتف:</strong> ${customer.phone}</p>
                                            <p><strong>المدينة:</strong> ${customer.city || 'غير محدد'}</p>
                                            <p><strong>الرقم الضريبي:</strong> ${customer.taxNumber || 'غير محدد'}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-chart-line me-2"></i>الإحصائيات</h6>
                                        </div>
                                        <div class="card-body">
                                            <p><strong>إجمالي المشتريات:</strong> ${this.formatAmount(customer.totalPurchases || 0)}</p>
                                            <p><strong>الرصيد المستحق:</strong> ${this.formatAmount(customer.outstandingBalance || 0)}</p>
                                            <p><strong>حد الائتمان:</strong> ${this.formatAmount(customer.creditLimit || 0)}</p>
                                            <p><strong>مدة السداد:</strong> ${customer.paymentTerms || 30} يوم</p>
                                            <p><strong>تاريخ الإنشاء:</strong> ${new Date(customer.createdAt).toLocaleDateString('ar-SA')}</p>
                                            <p><strong>الحالة:</strong>
                                                <span class="badge bg-${customer.status === 'active' ? 'success' : 'warning'}">
                                                    ${customer.status === 'active' ? 'نشط' : 'غير نشط'}
                                                </span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            ${customer.address ? `
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h6><i class="fas fa-map-marker-alt me-2"></i>العنوان</h6>
                                    </div>
                                    <div class="card-body">
                                        <p>${customer.address}</p>
                                    </div>
                                </div>
                            ` : ''}

                            ${customer.notes ? `
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h6><i class="fas fa-sticky-note me-2"></i>ملاحظات</h6>
                                    </div>
                                    <div class="card-body">
                                        <p>${customer.notes}</p>
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.editCustomer('${customerId}')">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('viewCustomerModal'));
        modal.show();

        document.getElementById('viewCustomerModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    editCustomer: function(customerId) {
        const customer = this.data.customers[customerId];
        if (!customer) {
            this.showNotification('العميل غير موجود', 'error');
            return;
        }

        // إغلاق نافذة العرض إذا كانت مفتوحة
        const viewModal = document.getElementById('viewCustomerModal');
        if (viewModal) {
            const modal = bootstrap.Modal.getInstance(viewModal);
            if (modal) modal.hide();
        }

        const modalHTML = `
            <div class="modal fade" id="editCustomerModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-user-edit me-2"></i>تعديل العميل: ${customer.name}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="editCustomerForm">
                                <input type="hidden" name="customerId" value="${customerId}">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">اسم العميل *</label>
                                            <input type="text" class="form-control" name="name" value="${customer.name}" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">نوع العميل</label>
                                            <select class="form-control" name="type">
                                                <option value="individual" ${customer.type === 'individual' ? 'selected' : ''}>فرد</option>
                                                <option value="company" ${customer.type === 'company' ? 'selected' : ''}>شركة</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">البريد الإلكتروني</label>
                                            <input type="email" class="form-control" name="email" value="${customer.email || ''}">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رقم الهاتف *</label>
                                            <input type="tel" class="form-control" name="phone" value="${customer.phone}" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">المدينة</label>
                                            <input type="text" class="form-control" name="city" value="${customer.city || ''}">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الرقم الضريبي</label>
                                            <input type="text" class="form-control" name="taxNumber" value="${customer.taxNumber || ''}">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea class="form-control" name="address" rows="3">${customer.address || ''}</textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">حد الائتمان</label>
                                            <input type="number" class="form-control" name="creditLimit" value="${customer.creditLimit || 0}" step="0.01" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">مدة السداد (أيام)</label>
                                            <input type="number" class="form-control" name="paymentTerms" value="${customer.paymentTerms || 30}" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">الحالة</label>
                                            <select class="form-control" name="status">
                                                <option value="active" ${customer.status === 'active' ? 'selected' : ''}>نشط</option>
                                                <option value="inactive" ${customer.status === 'inactive' ? 'selected' : ''}>غير نشط</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="2">${customer.notes || ''}</textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.updateCustomer()">
                                <i class="fas fa-save me-1"></i>حفظ التغييرات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('editCustomerModal'));
        modal.show();

        document.getElementById('editCustomerModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * تحديث بيانات العميل
     */
    updateCustomer: function() {
        const form = document.getElementById('editCustomerForm');
        const formData = new FormData(form);
        const customerId = formData.get('customerId');

        if (!customerId || !this.data.customers[customerId]) {
            this.showNotification('العميل غير موجود', 'error');
            return;
        }

        // التحقق من البيانات المطلوبة
        if (!formData.get('name') || !formData.get('phone')) {
            this.showNotification('يرجى ملء الحقول المطلوبة (الاسم والهاتف)', 'warning');
            return;
        }

        try {
            // تحديث بيانات العميل
            const customer = this.data.customers[customerId];
            customer.name = formData.get('name');
            customer.type = formData.get('type');
            customer.email = formData.get('email');
            customer.phone = formData.get('phone');
            customer.city = formData.get('city');
            customer.taxNumber = formData.get('taxNumber');
            customer.address = formData.get('address');
            customer.creditLimit = parseFloat(formData.get('creditLimit')) || 0;
            customer.paymentTerms = parseInt(formData.get('paymentTerms')) || 30;
            customer.status = formData.get('status');
            customer.notes = formData.get('notes');
            customer.updatedAt = new Date().toISOString();

            this.saveSalesData();

            // إغلاق النافذة
            const modalElement = document.getElementById('editCustomerModal');
            if (modalElement) {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                }
            }

            this.showNotification('تم تحديث بيانات العميل بنجاح', 'success');

            // تحديث العرض إذا كنا في صفحة العملاء
            if (this.data.currentView === 'customers') {
                this.render({ view: 'customers' });
            }

        } catch (error) {
            console.error('خطأ في تحديث العميل:', error);
            this.showNotification('حدث خطأ أثناء تحديث العميل', 'error');
        }
    },

    viewProduct: function(productId) {
        const product = this.data.products[productId];
        if (!product) {
            this.showNotification('المنتج غير موجود', 'error');
            return;
        }

        const modalHTML = `
            <div class="modal fade" id="viewProductModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-box me-2"></i>تفاصيل المنتج: ${product.name}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-info-circle me-2"></i>المعلومات الأساسية</h6>
                                        </div>
                                        <div class="card-body">
                                            <p><strong>الاسم:</strong> ${product.name}</p>
                                            <p><strong>الكود:</strong> ${product.code}</p>
                                            <p><strong>الفئة:</strong> ${product.category || 'غير محدد'}</p>
                                            <p><strong>النوع:</strong> ${product.type === 'service' ? 'خدمة' : 'منتج'}</p>
                                            <p><strong>وحدة القياس:</strong> ${product.unit}</p>
                                            <p><strong>المورد:</strong> ${product.supplier || 'غير محدد'}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-dollar-sign me-2"></i>الأسعار والمخزون</h6>
                                        </div>
                                        <div class="card-body">
                                            <p><strong>السعر:</strong> ${this.formatAmount(product.price, product.currency)}</p>
                                            <p><strong>التكلفة:</strong> ${this.formatAmount(product.cost || 0, product.currency)}</p>
                                            <p><strong>الربح:</strong> ${this.formatAmount((product.price - (product.cost || 0)), product.currency)}</p>
                                            <p><strong>العملة:</strong> ${this.getCurrencyName(product.currency)}</p>
                                            <p><strong>الكمية المتاحة:</strong> ${product.quantity}</p>
                                            <p><strong>الحد الأدنى:</strong> ${product.minStock}</p>
                                            <p><strong>الكمية المباعة:</strong> ${product.soldQuantity || 0}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            ${product.description ? `
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h6><i class="fas fa-file-text me-2"></i>الوصف</h6>
                                    </div>
                                    <div class="card-body">
                                        <p>${product.description}</p>
                                    </div>
                                </div>
                            ` : ''}

                            <div class="card mt-3">
                                <div class="card-header">
                                    <h6><i class="fas fa-chart-line me-2"></i>الحالة والإحصائيات</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>الحالة:</strong>
                                                <span class="badge bg-${this.getProductStatusColor(product)}">
                                                    ${this.getProductStatusLabel(product)}
                                                </span>
                                            </p>
                                            <p><strong>تاريخ الإنشاء:</strong> ${new Date(product.createdAt).toLocaleDateString('ar-SA')}</p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>آخر تحديث:</strong> ${new Date(product.updatedAt).toLocaleDateString('ar-SA')}</p>
                                            <p><strong>إجمالي المبيعات:</strong> ${this.formatAmount((product.soldQuantity || 0) * product.price)}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            ${product.notes ? `
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h6><i class="fas fa-sticky-note me-2"></i>ملاحظات</h6>
                                    </div>
                                    <div class="card-body">
                                        <p>${product.notes}</p>
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.editProduct('${productId}')">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('viewProductModal'));
        modal.show();

        document.getElementById('viewProductModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    editProduct: function(productId) {
        const product = this.data.products[productId];
        if (!product) {
            this.showNotification('المنتج غير موجود', 'error');
            return;
        }

        // إغلاق نافذة العرض إذا كانت مفتوحة
        const viewModal = document.getElementById('viewProductModal');
        if (viewModal) {
            const modal = bootstrap.Modal.getInstance(viewModal);
            if (modal) modal.hide();
        }

        const modalHTML = `
            <div class="modal fade" id="editProductModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-box-edit me-2"></i>تعديل المنتج: ${product.name}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="editProductForm">
                                <input type="hidden" name="productId" value="${productId}">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label class="form-label">اسم المنتج *</label>
                                            <input type="text" class="form-control" name="name" value="${product.name}" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">كود المنتج</label>
                                            <input type="text" class="form-control" name="code" value="${product.code}">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الفئة</label>
                                            <select class="form-control" name="category">
                                                <option value="">اختر الفئة</option>
                                                <option value="travel" ${product.category === 'travel' ? 'selected' : ''}>خدمات سفر</option>
                                                <option value="accommodation" ${product.category === 'accommodation' ? 'selected' : ''}>إقامة</option>
                                                <option value="transportation" ${product.category === 'transportation' ? 'selected' : ''}>نقل</option>
                                                <option value="tours" ${product.category === 'tours' ? 'selected' : ''}>جولات سياحية</option>
                                                <option value="visa" ${product.category === 'visa' ? 'selected' : ''}>تأشيرات</option>
                                                <option value="insurance" ${product.category === 'insurance' ? 'selected' : ''}>تأمين</option>
                                                <option value="other" ${product.category === 'other' ? 'selected' : ''}>أخرى</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">نوع المنتج</label>
                                            <select class="form-control" name="type">
                                                <option value="service" ${product.type === 'service' ? 'selected' : ''}>خدمة</option>
                                                <option value="product" ${product.type === 'product' ? 'selected' : ''}>منتج</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">وصف المنتج</label>
                                    <textarea class="form-control" name="description" rows="3">${product.description || ''}</textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">السعر *</label>
                                            <input type="number" class="form-control" name="price" value="${product.price}" step="0.01" min="0" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">تكلفة الشراء</label>
                                            <input type="number" class="form-control" name="cost" value="${product.cost || 0}" step="0.01" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">الكمية المتاحة</label>
                                            <input type="number" class="form-control" name="quantity" value="${product.quantity}" min="0">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الحد الأدنى للمخزون</label>
                                            <input type="number" class="form-control" name="minStock" value="${product.minStock}" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">وحدة القياس</label>
                                            <select class="form-control" name="unit">
                                                <option value="piece" ${product.unit === 'piece' ? 'selected' : ''}>قطعة</option>
                                                <option value="service" ${product.unit === 'service' ? 'selected' : ''}>خدمة</option>
                                                <option value="night" ${product.unit === 'night' ? 'selected' : ''}>ليلة</option>
                                                <option value="day" ${product.unit === 'day' ? 'selected' : ''}>يوم</option>
                                                <option value="person" ${product.unit === 'person' ? 'selected' : ''}>شخص</option>
                                                <option value="kg" ${product.unit === 'kg' ? 'selected' : ''}>كيلوجرام</option>
                                                <option value="liter" ${product.unit === 'liter' ? 'selected' : ''}>لتر</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">المورد</label>
                                            <input type="text" class="form-control" name="supplier" value="${product.supplier || ''}">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الحالة</label>
                                            <select class="form-control" name="status">
                                                <option value="active" ${product.status === 'active' ? 'selected' : ''}>نشط</option>
                                                <option value="inactive" ${product.status === 'inactive' ? 'selected' : ''}>غير نشط</option>
                                                <option value="discontinued" ${product.status === 'discontinued' ? 'selected' : ''}>متوقف</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="2">${product.notes || ''}</textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.updateProduct()">
                                <i class="fas fa-save me-1"></i>حفظ التغييرات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('editProductModal'));
        modal.show();

        document.getElementById('editProductModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * تحديث بيانات المنتج
     */
    updateProduct: function() {
        const form = document.getElementById('editProductForm');
        const formData = new FormData(form);
        const productId = formData.get('productId');

        if (!productId || !this.data.products[productId]) {
            this.showNotification('المنتج غير موجود', 'error');
            return;
        }

        // التحقق من البيانات المطلوبة
        if (!formData.get('name') || !formData.get('price')) {
            this.showNotification('يرجى ملء الحقول المطلوبة (الاسم والسعر)', 'warning');
            return;
        }

        try {
            // تحديث بيانات المنتج
            const product = this.data.products[productId];
            product.name = formData.get('name');
            product.code = formData.get('code');
            product.category = formData.get('category');
            product.type = formData.get('type');
            product.description = formData.get('description');
            product.price = parseFloat(formData.get('price'));
            product.cost = parseFloat(formData.get('cost')) || 0;
            product.quantity = parseInt(formData.get('quantity')) || 0;
            product.minStock = parseInt(formData.get('minStock')) || 0;
            product.unit = formData.get('unit');
            product.supplier = formData.get('supplier');
            product.status = formData.get('status');
            product.notes = formData.get('notes');
            product.updatedAt = new Date().toISOString();

            this.saveSalesData();

            // إغلاق النافذة
            const modalElement = document.getElementById('editProductModal');
            if (modalElement) {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                }
            }

            this.showNotification('تم تحديث بيانات المنتج بنجاح', 'success');

            // تحديث العرض إذا كنا في صفحة المنتجات
            if (this.data.currentView === 'products') {
                this.render({ view: 'products' });
            }

        } catch (error) {
            console.error('خطأ في تحديث المنتج:', error);
            this.showNotification('حدث خطأ أثناء تحديث المنتج', 'error');
        }
    },

    /**
     * الحصول على لون حالة المنتج
     */
    getProductStatusColor: function(product) {
        if (product.status === 'inactive' || product.status === 'discontinued') {
            return 'secondary';
        }
        if (product.quantity <= product.minStock) {
            return 'warning';
        }
        if (product.quantity === 0) {
            return 'danger';
        }
        return 'success';
    },

    /**
     * الحصول على تسمية حالة المنتج
     */
    getProductStatusLabel: function(product) {
        if (product.status === 'inactive') return 'غير نشط';
        if (product.status === 'discontinued') return 'متوقف';
        if (product.quantity === 0) return 'نفد المخزون';
        if (product.quantity <= product.minStock) return 'مخزون منخفض';
        return 'متاح';
    },

    /**
     * تحديث التقارير
     */
    refreshReports: function() {
        this.showNotification('جاري تحديث التقارير...', 'info');

        // إعادة حساب البيانات
        this.loadSalesData();

        // إعادة عرض صفحة التقارير
        this.render({ view: 'reports' });

        this.showNotification('تم تحديث التقارير بنجاح', 'success');
    },

    /**
     * تصدير التقارير
     */
    exportReport: function(format) {
        try {
            const reportData = this.generateReportData();

            if (format === 'pdf') {
                this.exportToPDF(reportData);
            } else if (format === 'excel') {
                this.exportToExcel(reportData);
            }

            this.showNotification(`جاري تحضير التقرير بصيغة ${format.toUpperCase()}...`, 'info');
        } catch (error) {
            console.error('خطأ في تصدير التقرير:', error);
            this.showNotification('حدث خطأ أثناء تصدير التقرير', 'error');
        }
    },

    /**
     * إنشاء بيانات التقرير
     */
    generateReportData: function() {
        const invoices = Object.values(this.data.invoices || {});
        const customers = Object.values(this.data.customers || {});
        const products = Object.values(this.data.products || {});

        return {
            summary: {
                totalSales: invoices.filter(inv => inv.status === 'paid').reduce((sum, inv) => sum + inv.total, 0),
                pendingSales: invoices.filter(inv => inv.status === 'pending').reduce((sum, inv) => sum + inv.total, 0),
                totalInvoices: invoices.length,
                totalCustomers: customers.length,
                totalProducts: products.length
            },
            invoices: invoices,
            customers: customers,
            products: products,
            generatedAt: new Date().toISOString()
        };
    },

    /**
     * تصدير إلى PDF (مبسط)
     */
    exportToPDF: function(data) {
        // هذه وظيفة مبسطة - في التطبيق الحقيقي ستحتاج مكتبة PDF
        const content = `
تقرير المبيعات
================

إجمالي المبيعات: ${this.formatAmount(data.summary.totalSales)}
المبيعات المعلقة: ${this.formatAmount(data.summary.pendingSales)}
عدد الفواتير: ${data.summary.totalInvoices}
عدد العملاء: ${data.summary.totalCustomers}
عدد المنتجات: ${data.summary.totalProducts}

تاريخ الإنشاء: ${new Date(data.generatedAt).toLocaleString('ar-SA')}
        `;

        // إنشاء ملف نصي مؤقت
        const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `sales_report_${new Date().toISOString().split('T')[0]}.txt`;
        a.click();
        URL.revokeObjectURL(url);

        this.showNotification('تم تحميل التقرير النصي', 'success');
    },

    /**
     * تصدير إلى Excel (مبسط)
     */
    exportToExcel: function(data) {
        // هذه وظيفة مبسطة - في التطبيق الحقيقي ستحتاج مكتبة Excel
        let csvContent = "البيان,القيمة\n";
        csvContent += `إجمالي المبيعات,${data.summary.totalSales}\n`;
        csvContent += `المبيعات المعلقة,${data.summary.pendingSales}\n`;
        csvContent += `عدد الفواتير,${data.summary.totalInvoices}\n`;
        csvContent += `عدد العملاء,${data.summary.totalCustomers}\n`;
        csvContent += `عدد المنتجات,${data.summary.totalProducts}\n`;

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `sales_report_${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        URL.revokeObjectURL(url);

        this.showNotification('تم تحميل التقرير CSV', 'success');
    },

    /**
     * إظهار نافذة إضافة دفعة
     */
    showAddPaymentModal: function(invoiceId = null) {
        const invoices = Object.values(this.data.invoices || {}).filter(inv => inv.status === 'pending');

        const modalHTML = `
            <div class="modal fade" id="addPaymentModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-credit-card me-2"></i>تسجيل دفعة جديدة
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="addPaymentForm">
                                <div class="mb-3">
                                    <label class="form-label">الفاتورة *</label>
                                    <select class="form-control" name="invoiceId" required onchange="window.SalesComponent.updatePaymentAmount(this.value)">
                                        <option value="">اختر الفاتورة</option>
                                        ${invoices.map(invoice => {
                                            const customer = this.data.customers[invoice.customerId];
                                            return `
                                                <option value="${invoice.id}" ${invoiceId === invoice.id ? 'selected' : ''}
                                                        data-amount="${invoice.total}">
                                                    ${invoice.number} - ${customer ? customer.name : 'غير محدد'} - ${this.formatAmount(invoice.total)}
                                                </option>
                                            `;
                                        }).join('')}
                                    </select>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">المبلغ المدفوع *</label>
                                            <input type="number" class="form-control" name="amount" step="0.01" min="0" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">تاريخ الدفع *</label>
                                            <input type="date" class="form-control" name="date" value="${new Date().toISOString().split('T')[0]}" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">طريقة الدفع *</label>
                                            <select class="form-control" name="method" required>
                                                <option value="cash">نقداً</option>
                                                <option value="bank_transfer">تحويل بنكي</option>
                                                <option value="credit_card">بطاقة ائتمان</option>
                                                <option value="check">شيك</option>
                                                <option value="other">أخرى</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رقم المرجع</label>
                                            <input type="text" class="form-control" name="reference" placeholder="رقم الشيك أو التحويل">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="2"></textarea>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="confirmed" id="paymentConfirmed" checked>
                                        <label class="form-check-label" for="paymentConfirmed">
                                            تأكيد استلام الدفعة
                                        </label>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.savePayment()">
                                <i class="fas fa-save me-1"></i>حفظ الدفعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('addPaymentModal'));
        modal.show();

        // تحديث المبلغ إذا تم تحديد فاتورة
        if (invoiceId) {
            setTimeout(() => {
                this.updatePaymentAmount(invoiceId);
            }, 100);
        }

        document.getElementById('addPaymentModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * تحديث مبلغ الدفعة حسب الفاتورة المختارة
     */
    updatePaymentAmount: function(invoiceId) {
        if (!invoiceId) return;

        const invoice = this.data.invoices[invoiceId];
        if (!invoice) return;

        const amountInput = document.querySelector('input[name="amount"]');
        if (amountInput) {
            amountInput.value = invoice.total;
        }
    },

    /**
     * حفظ الدفعة
     */
    savePayment: function() {
        const form = document.getElementById('addPaymentForm');
        const formData = new FormData(form);

        // التحقق من البيانات المطلوبة
        if (!formData.get('invoiceId') || !formData.get('amount') || !formData.get('date') || !formData.get('method')) {
            this.showNotification('يرجى ملء جميع الحقول المطلوبة', 'warning');
            return;
        }

        const amount = parseFloat(formData.get('amount'));
        if (amount <= 0) {
            this.showNotification('يجب أن يكون المبلغ أكبر من صفر', 'warning');
            return;
        }

        try {
            // إنشاء الدفعة
            const paymentId = 'payment_' + Date.now();
            const payment = {
                id: paymentId,
                invoiceId: formData.get('invoiceId'),
                amount: amount,
                date: formData.get('date'),
                method: formData.get('method'),
                reference: formData.get('reference'),
                notes: formData.get('notes'),
                status: formData.get('confirmed') ? 'confirmed' : 'pending',
                createdAt: new Date().toISOString()
            };

            // حفظ الدفعة
            if (!this.data.payments) this.data.payments = {};
            this.data.payments[paymentId] = payment;

            // تحديث حالة الفاتورة
            const invoice = this.data.invoices[payment.invoiceId];
            if (invoice && payment.amount >= invoice.total) {
                invoice.status = 'paid';
                invoice.paidAt = payment.date;
            }

            this.saveSalesData();

            // إغلاق النافذة
            const modalElement = document.getElementById('addPaymentModal');
            if (modalElement) {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                }
            }

            this.showNotification('تم تسجيل الدفعة بنجاح', 'success');

            // تحديث العرض
            if (this.data.currentView === 'payments') {
                this.render({ view: 'payments' });
            }

        } catch (error) {
            console.error('خطأ في حفظ الدفعة:', error);
            this.showNotification('حدث خطأ أثناء حفظ الدفعة', 'error');
        }
    },

    /**
     * تحديث المدفوعات
     */
    refreshPayments: function() {
        this.showNotification('جاري تحديث المدفوعات...', 'info');
        this.render({ view: 'payments' });
        this.showNotification('تم تحديث المدفوعات بنجاح', 'success');
    },

    /**
     * الحصول على تسمية طريقة الدفع
     */
    getPaymentMethodLabel: function(method) {
        const methods = {
            cash: 'نقداً',
            bank_transfer: 'تحويل بنكي',
            credit_card: 'بطاقة ائتمان',
            check: 'شيك',
            other: 'أخرى'
        };
        return methods[method] || method;
    },

    /**
     * إنشاء نسخة احتياطية من البيانات
     */
    createBackup: function() {
        try {
            const backupData = {
                version: '1.0',
                timestamp: new Date().toISOString(),
                data: {
                    customers: this.data.customers,
                    products: this.data.products,
                    invoices: this.data.invoices,
                    payments: this.data.payments,
                    settings: this.data.settings
                },
                metadata: {
                    totalCustomers: Object.keys(this.data.customers || {}).length,
                    totalProducts: Object.keys(this.data.products || {}).length,
                    totalInvoices: Object.keys(this.data.invoices || {}).length,
                    totalPayments: Object.keys(this.data.payments || {}).length,
                    createdBy: 'نظام المبيعات',
                    systemInfo: {
                        userAgent: navigator.userAgent,
                        url: window.location.href
                    }
                }
            };

            const backupJson = JSON.stringify(backupData, null, 2);
            const blob = new Blob([backupJson], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `sales_backup_${new Date().toISOString().split('T')[0]}_${Date.now()}.json`;
            a.click();

            URL.revokeObjectURL(url);

            this.showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');

            // حفظ معلومات النسخة الاحتياطية
            this.saveBackupInfo(backupData.metadata);

            return true;
        } catch (error) {
            console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
            this.showNotification('فشل في إنشاء النسخة الاحتياطية', 'error');
            return false;
        }
    },

    /**
     * استعادة البيانات من نسخة احتياطية
     */
    restoreBackup: function(file) {
        return new Promise((resolve, reject) => {
            if (!file) {
                reject(new Error('لم يتم تحديد ملف'));
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const backupData = JSON.parse(e.target.result);

                    // التحقق من صحة النسخة الاحتياطية
                    if (!this.validateBackup(backupData)) {
                        reject(new Error('ملف النسخة الاحتياطية غير صالح'));
                        return;
                    }

                    // إنشاء نسخة احتياطية من البيانات الحالية قبل الاستعادة
                    const currentBackup = {
                        ...this.data,
                        timestamp: new Date().toISOString(),
                        note: 'نسخة احتياطية تلقائية قبل الاستعادة'
                    };
                    localStorage.setItem('salesData_preRestore', JSON.stringify(currentBackup));

                    // استعادة البيانات
                    this.data.customers = backupData.data.customers || {};
                    this.data.products = backupData.data.products || {};
                    this.data.invoices = backupData.data.invoices || {};
                    this.data.payments = backupData.data.payments || {};
                    this.data.settings = { ...this.data.settings, ...backupData.data.settings };

                    // حفظ البيانات المستعادة
                    this.saveSalesData();

                    this.showNotification('تم استعادة البيانات بنجاح', 'success');

                    // تحديث العرض
                    this.render({ view: this.data.currentView });

                    resolve(backupData.metadata);
                } catch (error) {
                    reject(error);
                }
            };

            reader.onerror = () => {
                reject(new Error('فشل في قراءة الملف'));
            };

            reader.readAsText(file);
        });
    },

    /**
     * التحقق من صحة النسخة الاحتياطية
     */
    validateBackup: function(backupData) {
        if (!backupData || typeof backupData !== 'object') {
            return false;
        }

        // التحقق من وجود الحقول المطلوبة
        if (!backupData.version || !backupData.timestamp || !backupData.data) {
            return false;
        }

        // التحقق من صحة البيانات
        const data = backupData.data;
        if (typeof data !== 'object') {
            return false;
        }

        // التحقق من صحة كل قسم
        const sections = ['customers', 'products', 'invoices', 'payments'];
        for (const section of sections) {
            if (data[section] && typeof data[section] !== 'object') {
                return false;
            }
        }

        return true;
    },

    /**
     * حفظ معلومات النسخة الاحتياطية
     */
    saveBackupInfo: function(metadata) {
        try {
            const backupHistory = JSON.parse(localStorage.getItem('salesBackupHistory') || '[]');
            backupHistory.push({
                ...metadata,
                id: Date.now(),
                timestamp: new Date().toISOString()
            });

            // الاحتفاظ بآخر 10 نسخ احتياطية فقط
            if (backupHistory.length > 10) {
                backupHistory.splice(0, backupHistory.length - 10);
            }

            localStorage.setItem('salesBackupHistory', JSON.stringify(backupHistory));
        } catch (error) {
            console.error('خطأ في حفظ معلومات النسخة الاحتياطية:', error);
        }
    },

    /**
     * عرض نافذة إدارة النسخ الاحتياطية
     */
    showBackupModal: function() {
        const backupHistory = JSON.parse(localStorage.getItem('salesBackupHistory') || '[]');

        const modalHTML = `
            <div class="modal fade" id="backupModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-database me-2"></i>إدارة النسخ الاحتياطية
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-download me-2"></i>إنشاء نسخة احتياطية</h6>
                                        </div>
                                        <div class="card-body">
                                            <p class="text-muted">إنشاء نسخة احتياطية من جميع بيانات المبيعات</p>
                                            <button class="btn btn-primary w-100" onclick="window.SalesComponent.createBackup()">
                                                <i class="fas fa-download me-2"></i>إنشاء نسخة احتياطية
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-upload me-2"></i>استعادة من نسخة احتياطية</h6>
                                        </div>
                                        <div class="card-body">
                                            <p class="text-muted">استعادة البيانات من ملف نسخة احتياطية</p>
                                            <input type="file" class="form-control mb-2" id="backupFileInput" accept=".json">
                                            <button class="btn btn-warning w-100" onclick="window.SalesComponent.handleBackupRestore()">
                                                <i class="fas fa-upload me-2"></i>استعادة البيانات
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="card">
                                <div class="card-header">
                                    <h6><i class="fas fa-history me-2"></i>سجل النسخ الاحتياطية</h6>
                                </div>
                                <div class="card-body">
                                    ${backupHistory.length > 0 ? `
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>التاريخ</th>
                                                        <th>العملاء</th>
                                                        <th>المنتجات</th>
                                                        <th>الفواتير</th>
                                                        <th>المدفوعات</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    ${backupHistory.slice(-5).reverse().map(backup => `
                                                        <tr>
                                                            <td>${new Date(backup.timestamp).toLocaleString('ar-SA')}</td>
                                                            <td>${backup.totalCustomers || 0}</td>
                                                            <td>${backup.totalProducts || 0}</td>
                                                            <td>${backup.totalInvoices || 0}</td>
                                                            <td>${backup.totalPayments || 0}</td>
                                                        </tr>
                                                    `).join('')}
                                                </tbody>
                                            </table>
                                        </div>
                                    ` : `
                                        <div class="text-center py-3">
                                            <i class="fas fa-database fa-3x text-muted mb-3"></i>
                                            <p class="text-muted">لا توجد نسخ احتياطية</p>
                                        </div>
                                    `}
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('backupModal'));
        modal.show();

        document.getElementById('backupModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * معالجة استعادة النسخة الاحتياطية
     */
    handleBackupRestore: function() {
        const fileInput = document.getElementById('backupFileInput');
        const file = fileInput.files[0];

        if (!file) {
            this.showNotification('يرجى اختيار ملف النسخة الاحتياطية', 'warning');
            return;
        }

        // تأكيد الاستعادة
        if (!confirm('هل أنت متأكد من استعادة البيانات؟ سيتم استبدال جميع البيانات الحالية.')) {
            return;
        }

        this.restoreBackup(file)
            .then((metadata) => {
                // إغلاق النافذة
                const modalElement = document.getElementById('backupModal');
                if (modalElement) {
                    const modal = bootstrap.Modal.getInstance(modalElement);
                    if (modal) {
                        modal.hide();
                    }
                }
            })
            .catch((error) => {
                console.error('خطأ في استعادة النسخة الاحتياطية:', error);
                this.showNotification('فشل في استعادة النسخة الاحتياطية: ' + error.message, 'error');
            });
    },

    /**
     * اختبار النظام
     */
    test: function() {
        console.log('🧪 اختبار نظام المبيعات...');

        try {
            // اختبار تحميل البيانات
            this.loadSalesData();
            console.log('✅ تحميل البيانات');

            // اختبار إنشاء البيانات التجريبية
            this.createSampleData();
            console.log('✅ إنشاء البيانات التجريبية');

            // اختبار عرض لوحة التحكم
            const dashboard = this.renderDashboard();
            if (dashboard && dashboard.length > 0) {
                console.log('✅ عرض لوحة التحكم');
            } else {
                console.log('❌ فشل في عرض لوحة التحكم');
            }

            // اختبار الوظائف المساعدة
            const amount = this.formatAmount(1000);
            if (amount) {
                console.log('✅ تنسيق المبالغ:', amount);
            }

            // اختبار حفظ البيانات
            this.saveSalesData();
            console.log('✅ حفظ البيانات');

            console.log('🎉 جميع الاختبارات نجحت!');
            return true;

        } catch (error) {
            console.error('❌ فشل في الاختبار:', error);
            return false;
        }
    },

    /**
     * إعادة تعيين النظام
     */
    reset: function() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
            localStorage.removeItem('salesData');
            this.data = {
                currentView: 'dashboard',
                customers: {},
                products: {},
                invoices: {},
                quotes: {},
                payments: {},
                inventory: {},
                stockAdjustments: {},
                filters: {
                    dateFrom: '',
                    dateTo: '',
                    status: '',
                    customer: '',
                    quickSearch: ''
                },
                quoteFilters: {
                    dateFrom: '',
                    dateTo: '',
                    status: '',
                    customer: ''
                },
                settings: {
                    taxRate: 0.15,
                    currency: 'SAR',
                    invoicePrefix: 'INV-',
                    nextInvoiceNumber: 1,
                    nextQuoteNumber: 1,
                    companyName: 'قمة الوعد للسفريات',
                    companyAddress: 'المملكة العربية السعودية',
                    companyTaxNumber: '*********',
                    companyEmail: '<EMAIL>',
                    companyPhone: '+966501234567',
                    companyWebsite: 'https://qimat-alwaed.com',
                    language: 'ar',
                    timezone: 'Asia/Riyadh',
                    dateFormat: 'ar-SA',
                    autoSave: true,
                    showNotifications: true,
                    enableBackup: true,
                    autoCalculateTax: true,
                    defaultDueDays: 30
                }
            };
            this.createSampleData();
            this.render();
            alert('تم إعادة تعيين النظام بنجاح');
        }
    }
};

// تصدير المكون للاستخدام العام
window.SalesComponent = SalesComponent;
