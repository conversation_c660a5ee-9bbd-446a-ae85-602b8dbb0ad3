/**
 * مكون نظام المبيعات المتقدم
 * يوفر إدارة شاملة للفواتير، العملاء، المنتجات، عروض الأسعار، المدفوعات، والمخزون
 */

const SalesComponent = {
    // بيانات النظام
    data: {
        currentView: 'dashboard',
        customers: {},
        products: {},
        invoices: {},
        quotes: {},
        payments: {},
        inventory: {},
        stockAdjustments: {},
        filters: {
            dateFrom: '',
            dateTo: '',
            status: '',
            customer: '',
            quickSearch: ''
        },
        quoteFilters: {
            dateFrom: '',
            dateTo: '',
            status: '',
            customer: ''
        },
        settings: {
            taxRate: 0.15,
            currency: 'SAR',
            invoicePrefix: 'INV-',
            nextInvoiceNumber: 1,
            nextQuoteNumber: 1,
            companyName: 'قمة الوعد للسفريات',
            companyAddress: 'المملكة العربية السعودية',
            companyTaxNumber: '*********',
            companyEmail: '<EMAIL>',
            companyPhone: '+966501234567',
            companyWebsite: 'https://qimat-alwaed.com',
            language: 'ar',
            timezone: 'Asia/Riyadh',
            dateFormat: 'ar-SA',
            autoSave: true,
            showNotifications: true,
            enableBackup: true,
            autoCalculateTax: true,
            defaultDueDays: 30
        }
    },

    /**
     * تهيئة المكون مع معالجة أخطاء شاملة
     */
    init: function() {
        try {
            console.log('🔧 بدء تهيئة مكون المبيعات...');

            // تهيئة معالج الأخطاء
            this.initErrorHandler();

            // تحميل البيانات
            this.loadSalesData();

            // إنشاء البيانات التجريبية إذا لم تكن موجودة
            this.createSampleData();

            // تسجيل نجاح التهيئة
            console.log('✅ تم تهيئة مكون المبيعات بنجاح');
            this.showNotification('تم تهيئة نظام المبيعات بنجاح', 'success');

        } catch (error) {
            console.error('❌ خطأ في تهيئة مكون المبيعات:', error);
            this.handleError('فشل في تهيئة نظام المبيعات', error);
        }
    },

    /**
     * تهيئة معالج الأخطاء
     */
    initErrorHandler: function() {
        // إضافة معالج أخطاء عام للنافذة
        if (!window.salesErrorHandlerInitialized) {
            window.addEventListener('error', (event) => {
                if (event.filename && event.filename.includes('sales.js')) {
                    this.handleError('خطأ في نظام المبيعات', event.error);
                }
            });

            window.addEventListener('unhandledrejection', (event) => {
                this.handleError('خطأ غير معالج في نظام المبيعات', event.reason);
            });

            window.salesErrorHandlerInitialized = true;
        }
    },

    /**
     * معالج الأخطاء الرئيسي
     */
    handleError: function(message, error = null) {
        const errorInfo = {
            message: message,
            error: error ? error.message : 'خطأ غير محدد',
            stack: error ? error.stack : null,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };

        // تسجيل الخطأ
        console.error('🚨 خطأ في نظام المبيعات:', errorInfo);

        // حفظ الخطأ في التخزين المحلي للمراجعة
        this.logError(errorInfo);

        // إظهار رسالة للمستخدم
        this.showErrorNotification(message, error);

        // محاولة الاسترداد التلقائي
        this.attemptAutoRecovery(errorInfo);
    },

    /**
     * تسجيل الأخطاء في التخزين المحلي
     */
    logError: function(errorInfo) {
        try {
            const errorLog = JSON.parse(localStorage.getItem('salesErrorLog') || '[]');
            errorLog.push(errorInfo);

            // الاحتفاظ بآخر 50 خطأ فقط
            if (errorLog.length > 50) {
                errorLog.splice(0, errorLog.length - 50);
            }

            localStorage.setItem('salesErrorLog', JSON.stringify(errorLog));
        } catch (e) {
            console.error('فشل في حفظ سجل الأخطاء:', e);
        }
    },

    /**
     * إظهار إشعار خطأ للمستخدم
     */
    showErrorNotification: function(message, error = null) {
        const container = document.getElementById('main-content');
        if (!container) return;

        const errorDetails = error ? `<br><small class="text-muted">التفاصيل: ${error.message}</small>` : '';

        const errorHtml = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <h4 class="alert-heading">
                    <i class="fas fa-exclamation-triangle me-2"></i>خطأ في نظام المبيعات
                </h4>
                <p class="mb-0">${message}${errorDetails}</p>
                <hr>
                <div class="d-flex gap-2 mt-3">
                    <button class="btn btn-outline-primary btn-sm" onclick="location.reload()">
                        <i class="fas fa-refresh me-1"></i>تحديث الصفحة
                    </button>
                    <button class="btn btn-outline-info btn-sm" onclick="window.SalesComponent.attemptAutoRecovery()">
                        <i class="fas fa-wrench me-1"></i>محاولة الإصلاح
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="window.SalesComponent.showErrorLog()">
                        <i class="fas fa-list me-1"></i>سجل الأخطاء
                    </button>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // إدراج الخطأ في أعلى المحتوى
        container.insertAdjacentHTML('afterbegin', errorHtml);
    },

    /**
     * إظهار إشعار نجاح
     */
    showNotification: function(message, type = 'info') {
        if (!this.data.settings.showNotifications) return;

        const colors = {
            success: 'alert-success',
            info: 'alert-info',
            warning: 'alert-warning',
            error: 'alert-danger'
        };

        const icons = {
            success: 'fas fa-check-circle',
            info: 'fas fa-info-circle',
            warning: 'fas fa-exclamation-triangle',
            error: 'fas fa-times-circle'
        };

        const notification = document.createElement('div');
        notification.className = `alert ${colors[type]} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        notification.innerHTML = `
            <i class="${icons[type]} me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // إزالة الإشعار تلقائياً بعد 5 ثوان
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    },

    /**
     * محاولة الاسترداد التلقائي
     */
    attemptAutoRecovery: function(errorInfo = null) {
        console.log('🔄 محاولة الاسترداد التلقائي...');

        try {
            // إعادة تهيئة البيانات
            this.data = this.getDefaultData();

            // إعادة تحميل البيانات
            this.loadSalesData();

            // إنشاء البيانات التجريبية
            this.createSampleData();

            // إعادة عرض لوحة التحكم
            this.render({ view: 'dashboard' });

            console.log('✅ تم الاسترداد التلقائي بنجاح');
            this.showNotification('تم إصلاح النظام تلقائياً', 'success');

            return true;
        } catch (error) {
            console.error('❌ فشل في الاسترداد التلقائي:', error);
            this.showNotification('فشل في الإصلاح التلقائي، يرجى تحديث الصفحة', 'error');
            return false;
        }
    },

    /**
     * عرض سجل الأخطاء
     */
    showErrorLog: function() {
        try {
            const errorLog = JSON.parse(localStorage.getItem('salesErrorLog') || '[]');

            if (errorLog.length === 0) {
                this.showNotification('لا توجد أخطاء مسجلة', 'info');
                return;
            }

            const logHtml = errorLog.map((error, index) => `
                <div class="card mb-2">
                    <div class="card-body">
                        <h6 class="card-title text-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>خطأ #${index + 1}
                        </h6>
                        <p class="card-text">${error.message}</p>
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>${new Date(error.timestamp).toLocaleString('ar-SA')}
                        </small>
                        ${error.error ? `<br><small class="text-muted">التفاصيل: ${error.error}</small>` : ''}
                    </div>
                </div>
            `).join('');

            const container = document.getElementById('main-content');
            if (container) {
                container.innerHTML = `
                    <div class="container-fluid">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2><i class="fas fa-list me-2"></i>سجل الأخطاء</h2>
                            <div class="btn-group">
                                <button class="btn btn-outline-danger" onclick="window.SalesComponent.clearErrorLog()">
                                    <i class="fas fa-trash me-1"></i>مسح السجل
                                </button>
                                <button class="btn btn-outline-primary" onclick="window.SalesComponent.render({view: 'dashboard'})">
                                    <i class="fas fa-arrow-left me-1"></i>العودة
                                </button>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            يحتوي هذا السجل على آخر ${errorLog.length} خطأ في نظام المبيعات
                        </div>

                        ${logHtml}
                    </div>
                `;
            }
        } catch (error) {
            console.error('خطأ في عرض سجل الأخطاء:', error);
            this.showNotification('فشل في عرض سجل الأخطاء', 'error');
        }
    },

    /**
     * مسح سجل الأخطاء
     */
    clearErrorLog: function() {
        try {
            localStorage.removeItem('salesErrorLog');
            this.showNotification('تم مسح سجل الأخطاء', 'success');
            this.render({ view: 'dashboard' });
        } catch (error) {
            console.error('خطأ في مسح سجل الأخطاء:', error);
            this.showNotification('فشل في مسح سجل الأخطاء', 'error');
        }
    },

    /**
     * عرض المكون مع معالجة أخطاء محسنة
     */
    render: function(params = {}) {
        try {
            this.data.currentView = params.view || 'dashboard';

            const container = document.getElementById('main-content');
            if (!container) {
                throw new Error('عنصر العرض الرئيسي غير موجود');
            }

            // تحديث شريط التنقل
            this.updateNavigation();

            // عرض المحتوى حسب العرض المختار
            let content = '';

            switch (this.data.currentView) {
                case 'dashboard':
                    content = this.renderDashboard();
                    break;
                case 'invoices':
                    content = this.renderInvoicesView();
                    break;
                case 'quotes':
                    content = this.renderQuotesView();
                    break;
                case 'customers':
                    content = this.renderCustomersView();
                    break;
                case 'products':
                    content = this.renderProductsView();
                    break;
                case 'inventory':
                    content = this.renderInventoryView();
                    break;
                case 'payments':
                    content = this.renderPaymentsView();
                    break;
                case 'reports':
                    content = this.renderReportsView();
                    break;
                case 'settings':
                    content = this.renderSettingsView();
                    break;
                default:
                    content = this.renderDashboard();
            }

            // التحقق من وجود المحتوى
            if (!content || content.trim().length === 0) {
                throw new Error(`فشل في إنشاء محتوى للعرض: ${this.data.currentView}`);
            }

            // عرض المحتوى
            container.innerHTML = content;

            // تسجيل نجاح العرض
            console.log(`✅ تم عرض ${this.data.currentView} بنجاح`);

        } catch (error) {
            console.error(`❌ خطأ في عرض ${this.data.currentView}:`, error);
            this.handleError(`فشل في عرض ${this.getViewDisplayName(this.data.currentView)}`, error);

            // محاولة عرض صفحة خطأ
            this.renderErrorPage(error);
        }
    },

    /**
     * عرض صفحة خطأ
     */
    renderErrorPage: function(error) {
        const container = document.getElementById('main-content');
        if (!container) return;

        container.innerHTML = `
            <div class="container-fluid">
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h4 class="mb-0">
                                    <i class="fas fa-exclamation-triangle me-2"></i>خطأ في النظام
                                </h4>
                            </div>
                            <div class="card-body">
                                <div class="text-center mb-4">
                                    <i class="fas fa-bug fa-4x text-danger mb-3"></i>
                                    <h5>عذراً، حدث خطأ في عرض هذه الصفحة</h5>
                                    <p class="text-muted">نعتذر عن الإزعاج، يرجى المحاولة مرة أخرى</p>
                                </div>

                                <div class="alert alert-light">
                                    <strong>تفاصيل الخطأ:</strong><br>
                                    <code>${error.message}</code>
                                </div>

                                <div class="d-flex justify-content-center gap-3">
                                    <button class="btn btn-primary" onclick="window.SalesComponent.render({view: 'dashboard'})">
                                        <i class="fas fa-home me-2"></i>العودة للرئيسية
                                    </button>
                                    <button class="btn btn-outline-info" onclick="window.SalesComponent.attemptAutoRecovery()">
                                        <i class="fas fa-wrench me-2"></i>محاولة الإصلاح
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="location.reload()">
                                        <i class="fas fa-refresh me-2"></i>تحديث الصفحة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * الحصول على اسم العرض للعرض
     */
    getViewDisplayName: function(view) {
        const names = {
            dashboard: 'لوحة التحكم',
            invoices: 'الفواتير',
            customers: 'العملاء',
            products: 'المنتجات',
            reports: 'التقارير',
            quotes: 'عروض الأسعار',
            inventory: 'المخزون',
            payments: 'المدفوعات',
            settings: 'الإعدادات'
        };
        return names[view] || view;

        // تحديث الرسوم البيانية إذا كانت في لوحة التحكم
        if (this.data.currentView === 'dashboard') {
            setTimeout(() => {
                this.updateCharts();
            }, 100);
        }

        // تحديث التقارير إذا كانت في صفحة التقارير
        if (this.data.currentView === 'reports') {
            setTimeout(() => {
                this.updateReports();
            }, 100);
        }
    },

    /**
     * تحميل بيانات المبيعات من التخزين المحلي
     */
    loadSalesData: function() {
        try {
            const savedData = localStorage.getItem('salesData');
            if (savedData) {
                const parsedData = JSON.parse(savedData);
                Object.assign(this.data, parsedData);
            }

            // التأكد من وجود البيانات الأساسية
            if (!this.data.customers) this.data.customers = {};
            if (!this.data.products) this.data.products = {};
            if (!this.data.invoices) this.data.invoices = {};
            if (!this.data.quotes) this.data.quotes = {};
            if (!this.data.payments) this.data.payments = {};
            if (!this.data.inventory) this.data.inventory = {};
            if (!this.data.stockAdjustments) this.data.stockAdjustments = {};
            if (!this.data.filters) this.data.filters = {
                dateFrom: '',
                dateTo: '',
                status: '',
                customer: '',
                quickSearch: ''
            };
            if (!this.data.quoteFilters) this.data.quoteFilters = {
                dateFrom: '',
                dateTo: '',
                status: '',
                customer: ''
            };
            if (!this.data.settings) this.data.settings = {
                taxRate: 0.15,
                currency: 'SAR',
                invoicePrefix: 'INV-',
                nextInvoiceNumber: 1,
                nextQuoteNumber: 1,
                companyName: 'قمة الوعد للسفريات',
                companyAddress: 'المملكة العربية السعودية',
                companyTaxNumber: '*********',
                companyEmail: '<EMAIL>',
                companyPhone: '+966501234567',
                companyWebsite: 'https://qimat-alwaed.com',
                language: 'ar',
                timezone: 'Asia/Riyadh',
                dateFormat: 'ar-SA',
                autoSave: true,
                showNotifications: true,
                enableBackup: true,
                autoCalculateTax: true,
                defaultDueDays: 30
            };
        } catch (error) {
            console.error('خطأ في تحميل بيانات المبيعات:', error);
            // إعادة تهيئة البيانات في حالة الخطأ
            this.data = {
                currentView: 'dashboard',
                customers: {},
                products: {},
                invoices: {},
                quotes: {},
                payments: {},
                inventory: {},
                stockAdjustments: {},
                filters: {
                    dateFrom: '',
                    dateTo: '',
                    status: '',
                    customer: '',
                    quickSearch: ''
                },
                quoteFilters: {
                    dateFrom: '',
                    dateTo: '',
                    status: '',
                    customer: ''
                },
                settings: {
                    taxRate: 0.15,
                    currency: 'SAR',
                    invoicePrefix: 'INV-',
                    nextInvoiceNumber: 1,
                    nextQuoteNumber: 1,
                    companyName: 'قمة الوعد للسفريات',
                    companyAddress: 'المملكة العربية السعودية',
                    companyTaxNumber: '*********',
                    companyEmail: '<EMAIL>',
                    companyPhone: '+966501234567',
                    companyWebsite: 'https://qimat-alwaed.com',
                    language: 'ar',
                    timezone: 'Asia/Riyadh',
                    dateFormat: 'ar-SA',
                    autoSave: true,
                    showNotifications: true,
                    enableBackup: true,
                    autoCalculateTax: true,
                    defaultDueDays: 30
                }
            };
        }
    },

    /**
     * حفظ بيانات المبيعات في التخزين المحلي
     */
    saveSalesData: function() {
        try {
            localStorage.setItem('salesData', JSON.stringify(this.data));
        } catch (error) {
            console.error('خطأ في حفظ بيانات المبيعات:', error);
        }
    },

    /**
     * إنشاء بيانات تجريبية
     */
    createSampleData: function() {
        // التحقق من وجود بيانات - إنشاء البيانات المفقودة فقط
        const hasData = Object.keys(this.data.customers || {}).length > 0 &&
                       Object.keys(this.data.products || {}).length > 0 &&
                       Object.keys(this.data.invoices || {}).length > 0;

        if (hasData) {
            return;
        }

        // تهيئة البيانات إذا لم تكن موجودة
        if (!this.data.customers) this.data.customers = {};
        if (!this.data.products) this.data.products = {};
        if (!this.data.invoices) this.data.invoices = {};
        if (!this.data.quotes) this.data.quotes = {};
        if (!this.data.payments) this.data.payments = {};
        if (!this.data.inventory) this.data.inventory = {};
        if (!this.data.stockAdjustments) this.data.stockAdjustments = {};
        if (!this.data.quoteFilters) {
            this.data.quoteFilters = {
                dateFrom: '',
                dateTo: '',
                status: '',
                customer: ''
            };
        }
        if (!this.data.filters) {
            this.data.filters = {
                dateFrom: '',
                dateTo: '',
                status: '',
                customer: '',
                quickSearch: ''
            };
        }

        // إنشاء عملاء تجريبيين
        this.data.customers = {
            'customer1': {
                id: 'customer1',
                name: 'أحمد محمد السعيد',
                email: '<EMAIL>',
                phone: '+966501234567',
                address: 'الرياض، المملكة العربية السعودية',
                taxNumber: '*********',
                status: 'active',
                createdAt: new Date().toISOString()
            },
            'customer2': {
                id: 'customer2',
                name: 'فاطمة علي الزهراني',
                email: '<EMAIL>',
                phone: '+966507654321',
                address: 'جدة، المملكة العربية السعودية',
                taxNumber: '*********',
                status: 'active',
                createdAt: new Date().toISOString()
            }
        };

        // إنشاء منتجات تجريبية
        this.data.products = {
            'product1': {
                id: 'product1',
                name: 'تذكرة طيران - الرياض إلى دبي',
                description: 'تذكرة طيران ذهاب وإياب من الرياض إلى دبي',
                price: 1200,
                category: 'تذاكر طيران',
                sku: 'FLIGHT-RUH-DXB',
                status: 'active',
                createdAt: new Date().toISOString()
            },
            'product2': {
                id: 'product2',
                name: 'حجز فندق - دبي 5 نجوم',
                description: 'حجز فندق 5 نجوم في دبي لمدة 3 ليالي',
                price: 800,
                category: 'حجوزات فنادق',
                sku: 'HOTEL-DXB-5STAR',
                status: 'active',
                createdAt: new Date().toISOString()
            }
        };

        // إنشاء فاتورة تجريبية
        this.data.invoices = {
            'invoice1': {
                id: 'invoice1',
                number: 'INV-001',
                date: new Date().toISOString().split('T')[0],
                dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                customerId: 'customer1',
                reference: 'REF-001',
                items: [
                    {
                        productId: 'product1',
                        name: 'تذكرة طيران - الرياض إلى دبي',
                        quantity: 2,
                        price: 1200,
                        total: 2400
                    }
                ],
                subtotal: 2400,
                tax: 360,
                total: 2760,
                status: 'draft',
                notes: 'فاتورة تجريبية',
                createdAt: new Date().toISOString()
            }
        };

        // إنشاء عروض أسعار تجريبية
        this.data.quotes = {
            'quote1': {
                id: 'quote1',
                number: 'QUO-001',
                date: new Date().toISOString().split('T')[0],
                validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                customerId: 'customer1',
                title: 'عرض سعر رحلة عائلية',
                items: [
                    {
                        productId: 'product1',
                        name: 'تذكرة طيران - الرياض إلى دبي',
                        quantity: 4,
                        price: 1200,
                        total: 4800
                    }
                ],
                subtotal: 4800,
                tax: 720,
                total: 5520,
                status: 'pending',
                notes: 'عرض خاص للعائلات - صالح لمدة 30 يوم',
                createdAt: new Date().toISOString()
            }
        };

        // إنشاء مدفوعات تجريبية
        this.data.payments = {};

        // إنشاء مخزون تجريبي
        this.data.inventory = {
            'product1': 25,
            'product2': 15
        };

        // إنشاء سجل تعديلات المخزون
        this.data.stockAdjustments = {};

        // تحديث الإعدادات لتشمل عروض الأسعار
        if (!this.data.settings.nextQuoteNumber) {
            this.data.settings.nextQuoteNumber = 2;
        }

        this.saveSalesData();
    },

    /**
     * تحديث شريط التنقل
     */
    updateNavigation: function() {
        const navItems = document.querySelectorAll('.nav-link[data-view]');
        navItems.forEach(item => {
            const view = item.getAttribute('data-view');
            if (view === this.data.currentView) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
    },

    /**
     * التبديل بين العروض
     */
    switchView: function(view) {
        this.data.currentView = view;
        this.render();
    },

    /**
     * تحديث البيانات
     */
    refreshData: function() {
        this.render();
    },

    /**
     * وظيفة بديلة لإنشاء فاتورة جديدة (للتوافق مع الاستدعاءات القديمة)
     */
    showNewInvoiceModal: function() {
        this.showCreateInvoiceModal();
    },

    /**
     * تشخيص حالة المكون
     */
    diagnose: function() {
        console.log('🔍 تشخيص مكون المبيعات:');
        console.log('- البيانات محملة:', !!this.data);
        console.log('- عدد العملاء:', Object.keys(this.data.customers || {}).length);
        console.log('- عدد المنتجات:', Object.keys(this.data.products || {}).length);
        console.log('- عدد الفواتير:', Object.keys(this.data.invoices || {}).length);
        console.log('- الإعدادات:', !!this.data.settings);
        console.log('- العرض الحالي:', this.data.currentView);

        // اختبار وظيفة إنشاء الفاتورة
        try {
            console.log('- اختبار وظيفة إنشاء الفاتورة...');
            if (typeof this.showCreateInvoiceModal === 'function') {
                console.log('✅ وظيفة showCreateInvoiceModal متاحة');
            } else {
                console.log('❌ وظيفة showCreateInvoiceModal غير متاحة');
            }
        } catch (error) {
            console.log('❌ خطأ في اختبار وظيفة إنشاء الفاتورة:', error);
        }

        return {
            dataLoaded: !!this.data,
            customersCount: Object.keys(this.data.customers || {}).length,
            productsCount: Object.keys(this.data.products || {}).length,
            invoicesCount: Object.keys(this.data.invoices || {}).length,
            hasSettings: !!this.data.settings,
            currentView: this.data.currentView
        };
    },

    /**
     * تحديث البيانات
     */
    refreshData: function() {
        this.render();
    },

    /**
     * وظائف مساعدة أساسية
     */
    formatAmount: function(amount) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: this.data.settings.currency || 'SAR'
        }).format(amount || 0);
    },

    getStatusLabel: function(status) {
        const labels = {
            draft: 'مسودة',
            sent: 'مرسلة',
            paid: 'مدفوعة',
            overdue: 'متأخرة',
            cancelled: 'ملغية',
            pending: 'معلق',
            accepted: 'مقبول',
            rejected: 'مرفوض',
            expired: 'منتهي الصلاحية',
            active: 'نشط',
            inactive: 'غير نشط'
        };
        return labels[status] || status;
    },

    getStatusColor: function(status) {
        const colors = {
            draft: 'secondary',
            sent: 'primary',
            paid: 'success',
            overdue: 'danger',
            cancelled: 'dark',
            pending: 'warning',
            accepted: 'success',
            rejected: 'danger',
            expired: 'secondary',
            active: 'success',
            inactive: 'secondary'
        };
        return colors[status] || 'secondary';
    },

    /**
     * عرض لوحة التحكم
     */
    renderDashboard: function() {
        const totalInvoices = Object.keys(this.data.invoices || {}).length;
        const totalCustomers = Object.keys(this.data.customers || {}).length;
        const totalProducts = Object.keys(this.data.products || {}).length;

        const totalSales = Object.values(this.data.invoices || {})
            .filter(inv => inv.status === 'paid')
            .reduce((sum, inv) => sum + inv.total, 0);

        return `
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم</h2>
                    <button class="btn btn-primary" onclick="window.SalesComponent.showCreateInvoiceModal()">
                        <i class="fas fa-plus me-1"></i>فاتورة جديدة
                    </button>
                </div>

                <!-- الإحصائيات الرئيسية -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${totalInvoices}</h4>
                                        <p class="mb-0">إجمالي الفواتير</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-file-invoice fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${this.formatAmount(totalSales)}</h4>
                                        <p class="mb-0">إجمالي المبيعات</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-dollar-sign fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${totalCustomers}</h4>
                                        <p class="mb-0">العملاء</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${totalProducts}</h4>
                                        <p class="mb-0">المنتجات</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-box fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الإجراءات السريعة -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>الإجراءات السريعة</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-2">
                                        <button class="btn btn-outline-primary w-100 mb-2" onclick="window.SalesComponent.showCreateInvoiceModal()">
                                            <i class="fas fa-file-invoice d-block mb-1"></i>فاتورة جديدة
                                        </button>
                                    </div>
                                    <div class="col-md-2">
                                        <button class="btn btn-outline-success w-100 mb-2" onclick="window.SalesComponent.showCreateCustomerModal()">
                                            <i class="fas fa-user-plus d-block mb-1"></i>عميل جديد
                                        </button>
                                    </div>
                                    <div class="col-md-2">
                                        <button class="btn btn-outline-info w-100 mb-2" onclick="window.SalesComponent.showCreateProductModal()">
                                            <i class="fas fa-plus-circle d-block mb-1"></i>منتج جديد
                                        </button>
                                    </div>
                                    <div class="col-md-2">
                                        <button class="btn btn-outline-warning w-100 mb-2" onclick="window.SalesComponent.showReportsModal()">
                                            <i class="fas fa-chart-line d-block mb-1"></i>التقارير
                                        </button>
                                    </div>
                                    <div class="col-md-2">
                                        <button class="btn btn-outline-secondary w-100 mb-2" onclick="window.SalesComponent.switchView('invoices')">
                                            <i class="fas fa-list d-block mb-1"></i>عرض الفواتير
                                        </button>
                                    </div>
                                    <div class="col-md-2">
                                        <button class="btn btn-outline-dark w-100 mb-2" onclick="window.SalesComponent.switchView('settings')">
                                            <i class="fas fa-cog d-block mb-1"></i>الإعدادات
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الفواتير الأخيرة -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-file-invoice me-2"></i>الفواتير الأخيرة</h5>
                            </div>
                            <div class="card-body">
                                ${this.renderRecentInvoices()}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>توزيع المبيعات</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="salesChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض الفواتير الأخيرة
     */
    renderRecentInvoices: function() {
        const recentInvoices = Object.values(this.data.invoices || {})
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, 5);

        if (recentInvoices.length === 0) {
            return '<p class="text-muted text-center">لا توجد فواتير</p>';
        }

        return `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>المبلغ</th>
                            <th>الحالة</th>
                            <th>التاريخ</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${recentInvoices.map(invoice => {
                            const customer = this.data.customers[invoice.customerId];
                            return `
                                <tr>
                                    <td><strong>${invoice.number}</strong></td>
                                    <td>${customer?.name || 'غير محدد'}</td>
                                    <td>${this.formatAmount(invoice.total)}</td>
                                    <td><span class="badge bg-${this.getStatusColor(invoice.status)}">${this.getStatusLabel(invoice.status)}</span></td>
                                    <td>${new Date(invoice.date).toLocaleDateString('ar-SA')}</td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
        `;
    },

    /**
     * تحديث الرسوم البيانية
     */
    updateCharts: function() {
        // رسم بياني بسيط للمبيعات
        const canvas = document.getElementById('salesChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');

        // بيانات المبيعات حسب الحالة
        const invoicesByStatus = Object.values(this.data.invoices || {}).reduce((acc, invoice) => {
            acc[invoice.status] = (acc[invoice.status] || 0) + 1;
            return acc;
        }, {});

        // ألوان للرسم البياني
        const colors = ['#007bff', '#28a745', '#ffc107', '#dc3545', '#6c757d'];
        const labels = Object.keys(invoicesByStatus);
        const data = Object.values(invoicesByStatus);

        // رسم دائري بسيط
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const radius = Math.min(centerX, centerY) - 20;

        ctx.clearRect(0, 0, canvas.width, canvas.height);

        let currentAngle = 0;
        const total = data.reduce((sum, value) => sum + value, 0);

        data.forEach((value, index) => {
            const sliceAngle = (value / total) * 2 * Math.PI;

            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
            ctx.closePath();
            ctx.fillStyle = colors[index % colors.length];
            ctx.fill();

            currentAngle += sliceAngle;
        });

        // إضافة وسيلة إيضاح بسيطة
        let legendY = 10;
        labels.forEach((label, index) => {
            ctx.fillStyle = colors[index % colors.length];
            ctx.fillRect(10, legendY, 15, 15);
            ctx.fillStyle = '#000';
            ctx.font = '12px Arial';
            ctx.fillText(`${this.getStatusLabel(label)}: ${data[index]}`, 30, legendY + 12);
            legendY += 20;
        });
    },

    /**
     * عرض نافذة إنشاء فاتورة جديدة
     */
    showCreateInvoiceModal: function() {
        // التأكد من تحميل البيانات
        if (!this.data || Object.keys(this.data).length === 0) {
            this.loadSalesData();
            this.createSampleData();
        }

        const customers = Object.values(this.data.customers || {});
        const products = Object.values(this.data.products || {});

        if (customers.length === 0) {
            alert('يجب إضافة عميل واحد على الأقل قبل إنشاء فاتورة. سيتم إنشاء بيانات تجريبية.');
            this.createSampleData();
            // إعادة تحميل البيانات بعد إنشاء البيانات التجريبية
            const customersAfter = Object.values(this.data.customers || {});
            const productsAfter = Object.values(this.data.products || {});
            if (customersAfter.length === 0) {
                alert('فشل في تحميل البيانات. يرجى إعادة تحميل الصفحة.');
                return;
            }
        }

        if (products.length === 0) {
            alert('يجب إضافة منتج واحد على الأقل قبل إنشاء فاتورة');
            return;
        }

        const modalHTML = `
            <div class="modal fade" id="createInvoiceModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-file-invoice me-2"></i>إنشاء فاتورة جديدة
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="createInvoiceForm">
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label class="form-label">رقم الفاتورة</label>
                                        <input type="text" class="form-control" name="number"
                                               value="${this.data.settings.invoicePrefix}${this.data.settings.nextInvoiceNumber}" readonly>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">تاريخ الفاتورة</label>
                                        <input type="date" class="form-control" name="date"
                                               value="${new Date().toISOString().split('T')[0]}" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">تاريخ الاستحقاق</label>
                                        <input type="date" class="form-control" name="dueDate"
                                               value="${new Date(Date.now() + this.data.settings.defaultDueDays * 24 * 60 * 60 * 1000).toISOString().split('T')[0]}" required>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">العميل *</label>
                                        <select class="form-control" name="customerId" required>
                                            <option value="">اختر العميل</option>
                                            ${customers.map(customer => `
                                                <option value="${customer.id}">${customer.name}</option>
                                            `).join('')}
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">رقم المرجع</label>
                                        <input type="text" class="form-control" name="reference" placeholder="رقم المرجع (اختياري)">
                                    </div>
                                </div>

                                <!-- عناصر الفاتورة -->
                                <div class="card mb-3">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">عناصر الفاتورة</h6>
                                        <button type="button" class="btn btn-sm btn-primary" onclick="window.SalesComponent.addInvoiceItem()">
                                            <i class="fas fa-plus me-1"></i>إضافة عنصر
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row mb-2">
                                            <div class="col-md-4"><strong>المنتج</strong></div>
                                            <div class="col-md-2"><strong>الكمية</strong></div>
                                            <div class="col-md-2"><strong>السعر</strong></div>
                                            <div class="col-md-2"><strong>الإجمالي</strong></div>
                                            <div class="col-md-2"><strong>إجراءات</strong></div>
                                        </div>
                                        <div id="invoiceItems">
                                            <!-- سيتم إضافة العناصر هنا -->
                                        </div>
                                    </div>
                                </div>

                                <!-- الإجماليات -->
                                <div class="row">
                                    <div class="col-md-8">
                                        <label class="form-label">ملاحظات</label>
                                        <textarea class="form-control" name="notes" rows="3" placeholder="ملاحظات إضافية"></textarea>
                                    </div>
                                    <div class="col-md-4">
                                        <table class="table table-sm">
                                            <tr>
                                                <td><strong>المجموع الفرعي:</strong></td>
                                                <td class="text-end" id="invoiceSubtotal">0.00 ر.س</td>
                                            </tr>
                                            <tr>
                                                <td><strong>الضريبة (${(this.data.settings.taxRate * 100).toFixed(0)}%):</strong></td>
                                                <td class="text-end" id="invoiceTax">0.00 ر.س</td>
                                            </tr>
                                            <tr class="table-primary">
                                                <td><strong>الإجمالي:</strong></td>
                                                <td class="text-end" id="invoiceTotal"><strong>0.00 ر.س</strong></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveInvoice()">
                                <i class="fas fa-save me-1"></i>حفظ الفاتورة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('createInvoiceModal'));
        modal.show();

        // إضافة عنصر افتراضي
        setTimeout(() => {
            this.addInvoiceItem();
        }, 100);

        document.getElementById('createInvoiceModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * إضافة عنصر جديد للفاتورة
     */
    addInvoiceItem: function() {
        const itemsContainer = document.getElementById('invoiceItems');
        if (!itemsContainer) return;

        const itemCount = itemsContainer.children.length;
        const products = Object.values(this.data.products || {});

        const itemHTML = `
            <div class="invoice-item row mb-2">
                <div class="col-md-4">
                    <select class="form-control item-product" name="items[${itemCount}][productId]" onchange="window.SalesComponent.updateItemFromProduct(${itemCount}, this.value)">
                        <option value="">اختر المنتج</option>
                        ${products.map(product => `
                            <option value="${product.id}" data-price="${product.price}">${product.name}</option>
                        `).join('')}
                    </select>
                </div>
                <div class="col-md-2">
                    <input type="number" class="form-control item-quantity" name="items[${itemCount}][quantity]"
                           placeholder="الكمية" value="1" min="1" onchange="window.SalesComponent.calculateItemTotal(${itemCount})">
                </div>
                <div class="col-md-2">
                    <input type="number" class="form-control item-price" name="items[${itemCount}][price]"
                           placeholder="السعر" step="0.01" onchange="window.SalesComponent.calculateItemTotal(${itemCount})">
                </div>
                <div class="col-md-2">
                    <input type="number" class="form-control item-total" name="items[${itemCount}][total]"
                           placeholder="الإجمالي" readonly>
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="window.SalesComponent.removeInvoiceItem(${itemCount})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;

        itemsContainer.insertAdjacentHTML('beforeend', itemHTML);
    },

    /**
     * إزالة عنصر من الفاتورة
     */
    removeInvoiceItem: function(itemIndex) {
        const itemsContainer = document.getElementById('invoiceItems');
        if (!itemsContainer) return;

        const items = itemsContainer.children;

        if (items.length > 1 && items[itemIndex]) {
            items[itemIndex].remove();
            this.calculateInvoiceTotal();
        } else {
            alert('يجب أن تحتوي الفاتورة على عنصر واحد على الأقل');
        }
    },

    /**
     * تحديث عنصر من المنتج المختار
     */
    updateItemFromProduct: function(itemIndex, productId) {
        if (!productId) return;

        const product = this.data.products?.[productId];
        if (!product) return;

        const itemsContainer = document.getElementById('invoiceItems');
        if (!itemsContainer || !itemsContainer.children[itemIndex]) return;

        const item = itemsContainer.children[itemIndex];
        const priceInput = item.querySelector('.item-price');

        if (priceInput) {
            priceInput.value = product.price;
            this.calculateItemTotal(itemIndex);
        }
    },

    /**
     * حساب إجمالي العنصر
     */
    calculateItemTotal: function(itemIndex) {
        const itemsContainer = document.getElementById('invoiceItems');
        if (!itemsContainer || !itemsContainer.children[itemIndex]) return;

        const item = itemsContainer.children[itemIndex];

        const quantityInput = item.querySelector('.item-quantity');
        const priceInput = item.querySelector('.item-price');
        const totalInput = item.querySelector('.item-total');

        if (!quantityInput || !priceInput || !totalInput) return;

        const quantity = parseFloat(quantityInput.value) || 0;
        const price = parseFloat(priceInput.value) || 0;
        const total = quantity * price;

        totalInput.value = total.toFixed(2);

        this.calculateInvoiceTotal();
    },

    /**
     * حساب إجمالي الفاتورة
     */
    calculateInvoiceTotal: function() {
        const itemsContainer = document.getElementById('invoiceItems');
        if (!itemsContainer) return;

        let subtotal = 0;

        Array.from(itemsContainer.children).forEach(item => {
            const totalInput = item.querySelector('.item-total');
            if (totalInput) {
                const total = parseFloat(totalInput.value) || 0;
                subtotal += total;
            }
        });

        const taxRate = this.data.settings?.taxRate || 0.15;
        const tax = subtotal * taxRate;
        const total = subtotal + tax;

        const subtotalElement = document.getElementById('invoiceSubtotal');
        const taxElement = document.getElementById('invoiceTax');
        const totalElement = document.getElementById('invoiceTotal');

        if (subtotalElement) subtotalElement.textContent = this.formatAmount(subtotal);
        if (taxElement) taxElement.textContent = this.formatAmount(tax);
        if (totalElement) totalElement.textContent = this.formatAmount(total);
    },

    /**
     * حفظ الفاتورة
     */
    saveInvoice: function() {
        const form = document.getElementById('createInvoiceForm');
        const formData = new FormData(form);

        // التحقق من البيانات المطلوبة
        if (!formData.get('customerId')) {
            alert('يرجى اختيار العميل');
            return;
        }

        // جمع عناصر الفاتورة
        const items = [];
        const itemsContainer = document.getElementById('invoiceItems');

        if (!itemsContainer) {
            alert('خطأ في تحميل عناصر الفاتورة');
            return;
        }

        Array.from(itemsContainer.children).forEach((item, index) => {
            const productSelect = item.querySelector('.item-product');
            const quantityInput = item.querySelector('.item-quantity');
            const priceInput = item.querySelector('.item-price');
            const totalInput = item.querySelector('.item-total');

            if (productSelect && quantityInput && priceInput && totalInput &&
                productSelect.value && quantityInput.value && priceInput.value) {
                const product = this.data.products?.[productSelect.value];
                items.push({
                    productId: productSelect.value,
                    name: product ? product.name : 'منتج غير محدد',
                    quantity: parseInt(quantityInput.value),
                    price: parseFloat(priceInput.value),
                    total: parseFloat(totalInput.value)
                });
            }
        });

        if (items.length === 0) {
            alert('يرجى إضافة عنصر واحد على الأقل للفاتورة');
            return;
        }

        // حساب الإجماليات
        const subtotal = items.reduce((sum, item) => sum + item.total, 0);
        const taxRate = this.data.settings?.taxRate || 0.15;
        const tax = subtotal * taxRate;
        const total = subtotal + tax;

        // إنشاء الفاتورة
        const invoiceId = 'invoice_' + Date.now();
        const invoice = {
            id: invoiceId,
            number: formData.get('number'),
            date: formData.get('date'),
            dueDate: formData.get('dueDate'),
            customerId: formData.get('customerId'),
            reference: formData.get('reference'),
            items: items,
            subtotal: subtotal,
            tax: tax,
            total: total,
            status: 'draft',
            notes: formData.get('notes'),
            createdAt: new Date().toISOString()
        };

        // حفظ الفاتورة
        try {
            if (!this.data.invoices) this.data.invoices = {};
            this.data.invoices[invoiceId] = invoice;
            if (!this.data.settings.nextInvoiceNumber) this.data.settings.nextInvoiceNumber = 1;
            this.data.settings.nextInvoiceNumber++;
            this.saveSalesData();

            // إغلاق النافذة وتحديث العرض
            const modalElement = document.getElementById('createInvoiceModal');
            if (modalElement) {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                }
            }

            this.refreshData();
            alert('تم إنشاء الفاتورة بنجاح');
        } catch (error) {
            console.error('خطأ في حفظ الفاتورة:', error);
            alert('حدث خطأ أثناء حفظ الفاتورة. يرجى المحاولة مرة أخرى.');
        }
    },

    /**
     * عرض نافذة إنشاء عميل جديد
     */
    showCreateCustomerModal: function() {
        const modalHTML = `
            <div class="modal fade" id="createCustomerModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-user-plus me-2"></i>إضافة عميل جديد
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="createCustomerForm">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">اسم العميل *</label>
                                        <input type="text" class="form-control" name="name" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" name="email">
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" name="phone">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">الرقم الضريبي</label>
                                        <input type="text" class="form-control" name="taxNumber">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea class="form-control" name="address" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveCustomer()">
                                <i class="fas fa-save me-1"></i>حفظ العميل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('createCustomerModal'));
        modal.show();

        document.getElementById('createCustomerModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * حفظ العميل الجديد
     */
    saveCustomer: function() {
        const form = document.getElementById('createCustomerForm');
        const formData = new FormData(form);

        if (!formData.get('name')) {
            alert('يرجى إدخال اسم العميل');
            return;
        }

        const customerId = 'customer_' + Date.now();
        const customer = {
            id: customerId,
            name: formData.get('name'),
            email: formData.get('email'),
            phone: formData.get('phone'),
            address: formData.get('address'),
            taxNumber: formData.get('taxNumber'),
            status: 'active',
            createdAt: new Date().toISOString()
        };

        if (!this.data.customers) this.data.customers = {};
        this.data.customers[customerId] = customer;
        this.saveSalesData();

        const modalElement = document.getElementById('createCustomerModal');
        if (modalElement) {
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.hide();
            }
        }

        alert('تم إضافة العميل بنجاح');
    },

    /**
     * عرض نافذة إنشاء منتج جديد
     */
    showCreateProductModal: function() {
        const modalHTML = `
            <div class="modal fade" id="createProductModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-plus-circle me-2"></i>إضافة منتج جديد
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="createProductForm">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">اسم المنتج *</label>
                                        <input type="text" class="form-control" name="name" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">السعر *</label>
                                        <input type="number" class="form-control" name="price" step="0.01" required>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">الفئة</label>
                                        <input type="text" class="form-control" name="category">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">رمز المنتج</label>
                                        <input type="text" class="form-control" name="sku">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الوصف</label>
                                    <textarea class="form-control" name="description" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveProduct()">
                                <i class="fas fa-save me-1"></i>حفظ المنتج
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('createProductModal'));
        modal.show();

        document.getElementById('createProductModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * حفظ المنتج الجديد
     */
    saveProduct: function() {
        const form = document.getElementById('createProductForm');
        const formData = new FormData(form);

        if (!formData.get('name') || !formData.get('price')) {
            alert('يرجى إدخال اسم المنتج والسعر');
            return;
        }

        const productId = 'product_' + Date.now();
        const product = {
            id: productId,
            name: formData.get('name'),
            description: formData.get('description'),
            price: parseFloat(formData.get('price')),
            category: formData.get('category'),
            sku: formData.get('sku'),
            status: 'active',
            createdAt: new Date().toISOString()
        };

        if (!this.data.products) this.data.products = {};
        this.data.products[productId] = product;
        this.saveSalesData();

        const modalElement = document.getElementById('createProductModal');
        if (modalElement) {
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.hide();
            }
        }

        alert('تم إضافة المنتج بنجاح');
    },

    /**
     * عرض نافذة التقارير
     */
    showReportsModal: function() {
        alert('ميزة التقارير متاحة في النسخة الكاملة');
    },

    /**
     * عرض صفحة الفواتير
     */
    renderInvoicesView: function() {
        return `
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-file-invoice me-2"></i>إدارة الفواتير</h2>
                    <button class="btn btn-primary" onclick="window.SalesComponent.showCreateInvoiceModal()">
                        <i class="fas fa-plus me-1"></i>فاتورة جديدة
                    </button>
                </div>

                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>التاريخ</th>
                                        <th>العميل</th>
                                        <th>المبلغ</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${this.renderInvoicesRows()}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض صفوف الفواتير
     */
    renderInvoicesRows: function() {
        const invoices = Object.values(this.data.invoices || {});

        if (invoices.length === 0) {
            return '<tr><td colspan="6" class="text-center text-muted">لا توجد فواتير</td></tr>';
        }

        return invoices.map(invoice => {
            const customer = this.data.customers[invoice.customerId];
            return `
                <tr>
                    <td><strong>${invoice.number}</strong></td>
                    <td>${new Date(invoice.date).toLocaleDateString('ar-SA')}</td>
                    <td>${customer?.name || 'غير محدد'}</td>
                    <td>${this.formatAmount(invoice.total)}</td>
                    <td><span class="badge bg-${this.getStatusColor(invoice.status)}">${this.getStatusLabel(invoice.status)}</span></td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="window.SalesComponent.viewInvoice('${invoice.id}')">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-success" onclick="window.SalesComponent.printInvoice('${invoice.id}')">
                                <i class="fas fa-print"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
    },

    /**
     * عرض تفاصيل الفاتورة
     */
    viewInvoice: function(invoiceId) {
        alert('عرض تفاصيل الفاتورة: ' + invoiceId);
    },

    /**
     * طباعة الفاتورة
     */
    printInvoice: function(invoiceId) {
        alert('طباعة الفاتورة: ' + invoiceId);
    },

    /**
     * عرض صفحة العملاء
     */
    renderCustomersView: function() {
        const customers = Object.values(this.data.customers || {});

        return `
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-users me-2"></i>إدارة العملاء</h2>
                    <button class="btn btn-primary" onclick="window.SalesComponent.showCreateCustomerModal()">
                        <i class="fas fa-plus me-1"></i>عميل جديد
                    </button>
                </div>

                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الهاتف</th>
                                        <th>المدينة</th>
                                        <th>إجمالي المشتريات</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${customers.map(customer => `
                                        <tr>
                                            <td><strong>${customer.name}</strong></td>
                                            <td>${customer.email || 'غير محدد'}</td>
                                            <td>${customer.phone || 'غير محدد'}</td>
                                            <td>${customer.city || 'غير محدد'}</td>
                                            <td>${this.formatAmount(customer.totalPurchases || 0)}</td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="window.SalesComponent.viewCustomer('${customer.id}')">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-success" onclick="window.SalesComponent.editCustomer('${customer.id}')">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>

                        ${customers.length === 0 ? `
                            <div class="text-center py-4">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد عملاء مسجلين</p>
                                <button class="btn btn-primary" onclick="window.SalesComponent.showCreateCustomerModal()">
                                    <i class="fas fa-plus me-1"></i>إضافة عميل جديد
                                </button>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض صفحة المنتجات
     */
    renderProductsView: function() {
        const products = Object.values(this.data.products || {});

        return `
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-box me-2"></i>إدارة المنتجات والخدمات</h2>
                    <button class="btn btn-primary" onclick="window.SalesComponent.showCreateProductModal()">
                        <i class="fas fa-plus me-1"></i>منتج جديد
                    </button>
                </div>

                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>اسم المنتج</th>
                                        <th>الفئة</th>
                                        <th>السعر</th>
                                        <th>الكمية المتاحة</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${products.map(product => `
                                        <tr>
                                            <td><strong>${product.name}</strong></td>
                                            <td>${product.category || 'غير محدد'}</td>
                                            <td>${this.formatAmount(product.price)}</td>
                                            <td>${product.quantity || 0}</td>
                                            <td>
                                                <span class="badge bg-${product.quantity > 0 ? 'success' : 'warning'}">
                                                    ${product.quantity > 0 ? 'متاح' : 'نفد المخزون'}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="window.SalesComponent.viewProduct('${product.id}')">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-success" onclick="window.SalesComponent.editProduct('${product.id}')">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>

                        ${products.length === 0 ? `
                            <div class="text-center py-4">
                                <i class="fas fa-box fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد منتجات مسجلة</p>
                                <button class="btn btn-primary" onclick="window.SalesComponent.showCreateProductModal()">
                                    <i class="fas fa-plus me-1"></i>إضافة منتج جديد
                                </button>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض صفحة التقارير
     */
    renderReportsView: function() {
        const invoices = Object.values(this.data.invoices || {});
        const customers = Object.values(this.data.customers || {});
        const products = Object.values(this.data.products || {});

        // حساب الإحصائيات
        const totalSales = invoices.filter(inv => inv.status === 'paid').reduce((sum, inv) => sum + inv.total, 0);
        const pendingSales = invoices.filter(inv => inv.status === 'pending').reduce((sum, inv) => sum + inv.total, 0);
        const thisMonthSales = invoices.filter(inv => {
            const invDate = new Date(inv.date);
            const now = new Date();
            return invDate.getMonth() === now.getMonth() && invDate.getFullYear() === now.getFullYear();
        }).reduce((sum, inv) => sum + inv.total, 0);

        return `
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-chart-bar me-2"></i>تقارير المبيعات</h2>
                    <div class="btn-group">
                        <button class="btn btn-outline-primary" onclick="window.SalesComponent.exportReport('pdf')">
                            <i class="fas fa-file-pdf me-1"></i>تصدير PDF
                        </button>
                        <button class="btn btn-outline-success" onclick="window.SalesComponent.exportReport('excel')">
                            <i class="fas fa-file-excel me-1"></i>تصدير Excel
                        </button>
                    </div>
                </div>

                <!-- الإحصائيات الرئيسية -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h4>${this.formatAmount(totalSales)}</h4>
                                <p class="mb-0">إجمالي المبيعات المحصلة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body">
                                <h4>${this.formatAmount(pendingSales)}</h4>
                                <p class="mb-0">المبيعات المعلقة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <h4>${this.formatAmount(thisMonthSales)}</h4>
                                <p class="mb-0">مبيعات هذا الشهر</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <h4>${invoices.length}</h4>
                                <p class="mb-0">إجمالي الفواتير</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تقارير تفصيلية -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-users me-2"></i>أفضل العملاء</h5>
                            </div>
                            <div class="card-body">
                                ${customers.slice(0, 5).map(customer => `
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>${customer.name}</span>
                                        <span class="badge bg-primary">${this.formatAmount(customer.totalPurchases || 0)}</span>
                                    </div>
                                `).join('')}
                                ${customers.length === 0 ? '<p class="text-muted">لا توجد بيانات عملاء</p>' : ''}
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-box me-2"></i>أكثر المنتجات مبيعاً</h5>
                            </div>
                            <div class="card-body">
                                ${products.slice(0, 5).map(product => `
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>${product.name}</span>
                                        <span class="badge bg-success">${product.soldQuantity || 0} وحدة</span>
                                    </div>
                                `).join('')}
                                ${products.length === 0 ? '<p class="text-muted">لا توجد بيانات منتجات</p>' : ''}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول الفواتير الأخيرة -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-file-invoice me-2"></i>آخر الفواتير</h5>
                    </div>
                    <div class="card-body">
                        ${invoices.length > 0 ? `
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>رقم الفاتورة</th>
                                            <th>التاريخ</th>
                                            <th>العميل</th>
                                            <th>المبلغ</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${invoices.slice(0, 10).map(invoice => {
                                            const customer = customers.find(c => c.id === invoice.customerId);
                                            return `
                                                <tr>
                                                    <td>${invoice.number}</td>
                                                    <td>${new Date(invoice.date).toLocaleDateString('ar-SA')}</td>
                                                    <td>${customer?.name || 'غير محدد'}</td>
                                                    <td>${this.formatAmount(invoice.total)}</td>
                                                    <td><span class="badge bg-${this.getStatusColor(invoice.status)}">${this.getStatusLabel(invoice.status)}</span></td>
                                                </tr>
                                            `;
                                        }).join('')}
                                    </tbody>
                                </table>
                            </div>
                        ` : `
                            <div class="text-center py-4">
                                <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد فواتير</p>
                            </div>
                        `}
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * العروض الأخرى (مبسطة)
     */
    renderQuotesView: function() { return '<div class="alert alert-info">عرض عروض الأسعار متاح في النسخة الكاملة</div>'; },
    renderInventoryView: function() { return '<div class="alert alert-info">عرض المخزون متاح في النسخة الكاملة</div>'; },
    renderPaymentsView: function() { return '<div class="alert alert-info">عرض المدفوعات متاح في النسخة الكاملة</div>'; },
    renderSettingsView: function() { return '<div class="alert alert-info">عرض الإعدادات متاح في النسخة الكاملة</div>'; },
    updateReports: function() { /* وظيفة فارغة للتوافق */ },

    /**
     * وظائف مساعدة للعملاء والمنتجات
     */
    showCreateCustomerModal: function() {
        alert('وظيفة إضافة عميل جديد ستكون متاحة قريباً');
    },

    showCreateProductModal: function() {
        alert('وظيفة إضافة منتج جديد ستكون متاحة قريباً');
    },

    viewCustomer: function(customerId) {
        const customer = this.data.customers[customerId];
        if (customer) {
            alert(`عرض تفاصيل العميل: ${customer.name}`);
        }
    },

    editCustomer: function(customerId) {
        const customer = this.data.customers[customerId];
        if (customer) {
            alert(`تعديل العميل: ${customer.name}`);
        }
    },

    viewProduct: function(productId) {
        const product = this.data.products[productId];
        if (product) {
            alert(`عرض تفاصيل المنتج: ${product.name}`);
        }
    },

    editProduct: function(productId) {
        const product = this.data.products[productId];
        if (product) {
            alert(`تعديل المنتج: ${product.name}`);
        }
    },

    exportReport: function(format) {
        alert(`تصدير التقرير بصيغة ${format} سيكون متاحاً قريباً`);
    },

    /**
     * اختبار النظام
     */
    test: function() {
        console.log('🧪 اختبار نظام المبيعات...');

        try {
            // اختبار تحميل البيانات
            this.loadSalesData();
            console.log('✅ تحميل البيانات');

            // اختبار إنشاء البيانات التجريبية
            this.createSampleData();
            console.log('✅ إنشاء البيانات التجريبية');

            // اختبار عرض لوحة التحكم
            const dashboard = this.renderDashboard();
            if (dashboard && dashboard.length > 0) {
                console.log('✅ عرض لوحة التحكم');
            } else {
                console.log('❌ فشل في عرض لوحة التحكم');
            }

            // اختبار الوظائف المساعدة
            const amount = this.formatAmount(1000);
            if (amount) {
                console.log('✅ تنسيق المبالغ:', amount);
            }

            // اختبار حفظ البيانات
            this.saveSalesData();
            console.log('✅ حفظ البيانات');

            console.log('🎉 جميع الاختبارات نجحت!');
            return true;

        } catch (error) {
            console.error('❌ فشل في الاختبار:', error);
            return false;
        }
    },

    /**
     * إعادة تعيين النظام
     */
    reset: function() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
            localStorage.removeItem('salesData');
            this.data = {
                currentView: 'dashboard',
                customers: {},
                products: {},
                invoices: {},
                quotes: {},
                payments: {},
                inventory: {},
                stockAdjustments: {},
                filters: {
                    dateFrom: '',
                    dateTo: '',
                    status: '',
                    customer: '',
                    quickSearch: ''
                },
                quoteFilters: {
                    dateFrom: '',
                    dateTo: '',
                    status: '',
                    customer: ''
                },
                settings: {
                    taxRate: 0.15,
                    currency: 'SAR',
                    invoicePrefix: 'INV-',
                    nextInvoiceNumber: 1,
                    nextQuoteNumber: 1,
                    companyName: 'قمة الوعد للسفريات',
                    companyAddress: 'المملكة العربية السعودية',
                    companyTaxNumber: '*********',
                    companyEmail: '<EMAIL>',
                    companyPhone: '+966501234567',
                    companyWebsite: 'https://qimat-alwaed.com',
                    language: 'ar',
                    timezone: 'Asia/Riyadh',
                    dateFormat: 'ar-SA',
                    autoSave: true,
                    showNotifications: true,
                    enableBackup: true,
                    autoCalculateTax: true,
                    defaultDueDays: 30
                }
            };
            this.createSampleData();
            this.render();
            alert('تم إعادة تعيين النظام بنجاح');
        }
    }
};

// تصدير المكون للاستخدام العام
window.SalesComponent = SalesComponent;
