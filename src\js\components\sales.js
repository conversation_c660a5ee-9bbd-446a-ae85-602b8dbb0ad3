/**
 * ===================================
 * مكون المبيعات - Sales Component
 * ===================================
 */

const SalesComponent = {
    // بيانات المكون
    data: {
        currentView: 'invoices',
        invoices: {},
        customers: {},
        products: {},
        deletedInvoices: {},
        selectedInvoice: null,
        selectedCustomer: null,
        selectedProduct: null,
        filters: {
            dateFrom: '',
            dateTo: '',
            status: '',
            customer: ''
        },
        settings: {
            taxRate: 0.15,
            currency: 'SAR',
            invoicePrefix: 'INV-',
            nextInvoiceNumber: 1,
            companyName: 'قمة الوعد للسفريات',
            companyAddress: 'المملكة العربية السعودية',
            companyTaxNumber: '*********',
            companyEmail: '<EMAIL>',
            companyPhone: '+966501234567',
            companyWebsite: 'https://qimat-alwaed.com',
            language: 'ar',
            timezone: 'Asia/Riyadh',
            dateFormat: 'ar-SA',
            autoSave: true,
            showNotifications: true,
            enableBackup: true,
            autoCalculateTax: true,
            defaultDueDays: 30,

        }
    },

    /**
     * تهيئة المكون
     */
    init: function() {
        this.loadSalesData();
        this.createSampleData();
    },

    /**
     * عرض المكون الرئيسي
     */
    render: function(params = {}) {
        this.data.currentView = params.view || 'invoices';
        
        return `
            <div class="sales-container">
                ${this.renderHeader()}
                ${this.renderNavigation()}
                <div class="sales-content">
                    ${this.renderCurrentView()}
                </div>
            </div>
        `;
    },

    /**
     * عرض رأس الصفحة
     */
    renderHeader: function() {
        return `
            <div class="sales-header">
                <div class="header-title">
                    <h2><i class="fas fa-receipt me-2"></i>نظام المبيعات</h2>
                    <p class="text-muted">إدارة شاملة للفواتير والعملاء والمنتجات</p>
                </div>
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="window.SalesComponent.showCreateInvoiceModal()">
                        <i class="fas fa-plus me-1"></i>فاتورة جديدة
                    </button>
                    <button class="btn btn-outline-success" onclick="window.SalesComponent.showCreateCustomerModal()">
                        <i class="fas fa-user-plus me-1"></i>عميل جديد
                    </button>
                    <button class="btn btn-outline-info" onclick="window.SalesComponent.showCreateProductModal()">
                        <i class="fas fa-box me-1"></i>منتج جديد
                    </button>
                </div>
            </div>
        `;
    },

    /**
     * عرض شريط التنقل
     */
    renderNavigation: function() {
        const navItems = [
            { id: 'invoices', label: 'الفواتير', icon: 'fas fa-file-invoice' },
            { id: 'customers', label: 'العملاء', icon: 'fas fa-users' },
            { id: 'products', label: 'المنتجات', icon: 'fas fa-boxes' },
            { id: 'reports', label: 'التقارير', icon: 'fas fa-chart-bar' },
            { id: 'settings', label: 'الإعدادات', icon: 'fas fa-cog' }
        ];

        return `
            <div class="sales-navigation">
                <ul class="nav nav-tabs">
                    ${navItems.map(item => `
                        <li class="nav-item">
                            <a class="nav-link ${this.data.currentView === item.id ? 'active' : ''}" 
                               href="#" onclick="window.SalesComponent.switchView('${item.id}')">
                                <i class="${item.icon} me-1"></i>${item.label}
                            </a>
                        </li>
                    `).join('')}
                </ul>
            </div>
        `;
    },

    /**
     * عرض المحتوى الحالي
     */
    renderCurrentView: function() {
        switch (this.data.currentView) {
            case 'invoices':
                return this.renderInvoicesView();
            case 'customers':
                return this.renderCustomersView();
            case 'products':
                return this.renderProductsView();
            case 'reports':
                return this.renderReportsView();
            case 'settings':
                return this.renderSettingsView();
            default:
                return this.renderInvoicesView();
        }
    },

    /**
     * تبديل العرض
     */
    switchView: function(view) {
        this.data.currentView = view;
        const content = document.getElementById('main-content');
        if (content) {
            content.innerHTML = this.renderCurrentView();
        }
        
        // تحديث التقارير إذا كان في قسم التقارير
        if (view === 'reports') {
            setTimeout(() => {
                this.updateReports();
            }, 100);
        }
    },

    /**
     * تحميل بيانات المبيعات
     */
    loadSalesData: function() {
        try {
            const savedData = localStorage.getItem('salesData');
            if (savedData) {
                const parsedData = JSON.parse(savedData);
                Object.assign(this.data, parsedData);
            }
        } catch (error) {
            console.error('خطأ في تحميل بيانات المبيعات:', error);
        }
    },

    /**
     * حفظ بيانات المبيعات
     */
    saveSalesData: function() {
        try {
            localStorage.setItem('salesData', JSON.stringify(this.data));
        } catch (error) {
            console.error('خطأ في حفظ بيانات المبيعات:', error);
        }
    },

    /**
     * إنشاء بيانات تجريبية
     */
    createSampleData: function() {
        // التحقق من وجود بيانات
        if (Object.keys(this.data.customers).length > 0) {
            return;
        }

        // إنشاء عملاء تجريبيين
        this.data.customers = {
            'customer1': {
                id: 'customer1',
                name: 'أحمد محمد السعيد',
                email: '<EMAIL>',
                phone: '+966501234567',
                address: 'الرياض، المملكة العربية السعودية',
                taxNumber: '*********',
                createdAt: new Date().toISOString()
            },
            'customer2': {
                id: 'customer2',
                name: 'شركة النور للتجارة',
                email: '<EMAIL>',
                phone: '+966509876543',
                address: 'جدة، المملكة العربية السعودية',
                taxNumber: '*********',
                createdAt: new Date().toISOString()
            }
        };

        // إنشاء منتجات تجريبية
        this.data.products = {
            'product1': {
                id: 'product1',
                name: 'تذكرة طيران - الرياض إلى دبي',
                description: 'تذكرة طيران ذهاب وإياب',
                price: 1200,
                category: 'طيران',
                sku: 'FLT-RUH-DXB',
                createdAt: new Date().toISOString()
            },
            'product2': {
                id: 'product2',
                name: 'حجز فندق - 3 ليالي',
                description: 'إقامة في فندق 4 نجوم',
                price: 800,
                category: 'إقامة',
                sku: 'HTL-3N-4S',
                createdAt: new Date().toISOString()
            }
        };

        this.saveSalesData();
    },

    /**
     * عرض قسم الفواتير
     */
    renderInvoicesView: function() {
        const invoices = Object.values(this.data.invoices || {});

        return `
            <div class="invoices-management">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4><i class="fas fa-file-invoice me-2"></i>إدارة الفواتير</h4>
                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="window.SalesComponent.showCreateInvoiceModal()">
                            <i class="fas fa-plus me-1"></i>فاتورة جديدة
                        </button>
                        <button class="btn btn-outline-secondary" onclick="window.SalesComponent.exportInvoicesCSV()">
                            <i class="fas fa-download me-1"></i>تصدير CSV
                        </button>
                    </div>
                </div>

                <!-- فلاتر البحث -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="filterDateFrom"
                                       value="${this.data.filters.dateFrom}"
                                       onchange="window.SalesComponent.updateFilter('dateFrom', this.value)">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="filterDateTo"
                                       value="${this.data.filters.dateTo}"
                                       onchange="window.SalesComponent.updateFilter('dateTo', this.value)">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الحالة</label>
                                <select class="form-control" id="filterStatus"
                                        onchange="window.SalesComponent.updateFilter('status', this.value)">
                                    <option value="">جميع الحالات</option>
                                    <option value="draft" ${this.data.filters.status === 'draft' ? 'selected' : ''}>مسودة</option>
                                    <option value="sent" ${this.data.filters.status === 'sent' ? 'selected' : ''}>مرسلة</option>
                                    <option value="paid" ${this.data.filters.status === 'paid' ? 'selected' : ''}>مدفوعة</option>
                                    <option value="overdue" ${this.data.filters.status === 'overdue' ? 'selected' : ''}>متأخرة</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">العميل</label>
                                <select class="form-control" id="filterCustomer"
                                        onchange="window.SalesComponent.updateFilter('customer', this.value)">
                                    <option value="">جميع العملاء</option>
                                    ${Object.values(this.data.customers || {}).map(customer => `
                                        <option value="${customer.id}" ${this.data.filters.customer === customer.id ? 'selected' : ''}>
                                            ${customer.name}
                                        </option>
                                    `).join('')}
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول الفواتير -->
                <div class="card">
                    <div class="card-body">
                        ${invoices.length === 0 ? `
                            <div class="text-center py-5">
                                <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد فواتير</h5>
                                <p class="text-muted">ابدأ بإنشاء فاتورة جديدة</p>
                                <button class="btn btn-primary" onclick="window.SalesComponent.showCreateInvoiceModal()">
                                    <i class="fas fa-plus me-1"></i>إنشاء فاتورة
                                </button>
                            </div>
                        ` : `
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>رقم الفاتورة</th>
                                            <th>العميل</th>
                                            <th>التاريخ</th>
                                            <th>الإجمالي</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${this.getFilteredInvoices().map(invoice => `
                                            <tr>
                                                <td>
                                                    <strong>${invoice.number}</strong>
                                                </td>
                                                <td>
                                                    ${this.data.customers[invoice.customerId]?.name || 'عميل غير محدد'}
                                                </td>
                                                <td>
                                                    ${new Date(invoice.date).toLocaleDateString('ar-SA')}
                                                </td>
                                                <td>
                                                    <strong>${this.formatAmount(invoice.total)}</strong>
                                                </td>
                                                <td>
                                                    <span class="badge bg-${this.getInvoiceStatusColor(invoice.status)}">
                                                        ${this.getInvoiceStatusLabel(invoice.status)}
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-primary" onclick="window.SalesComponent.viewInvoice('${invoice.id}')" title="عرض">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="btn btn-outline-success" onclick="window.SalesComponent.editInvoice('${invoice.id}')" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-outline-info" onclick="window.SalesComponent.printInvoice('${invoice.id}')" title="طباعة">
                                                            <i class="fas fa-print"></i>
                                                        </button>
                                                        <button class="btn btn-outline-danger" onclick="window.SalesComponent.deleteInvoice('${invoice.id}')" title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        `}
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض قسم العملاء
     */
    renderCustomersView: function() {
        const customers = Object.values(this.data.customers || {});

        return `
            <div class="customers-management">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4><i class="fas fa-users me-2"></i>إدارة العملاء</h4>
                    <button class="btn btn-primary" onclick="window.SalesComponent.showCreateCustomerModal()">
                        <i class="fas fa-user-plus me-1"></i>عميل جديد
                    </button>
                </div>

                <div class="row">
                    ${customers.length === 0 ? `
                        <div class="col-12">
                            <div class="text-center py-5">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا يوجد عملاء</h5>
                                <p class="text-muted">ابدأ بإضافة عميل جديد</p>
                                <button class="btn btn-primary" onclick="window.SalesComponent.showCreateCustomerModal()">
                                    <i class="fas fa-user-plus me-1"></i>إضافة عميل
                                </button>
                            </div>
                        </div>
                    ` : customers.map(customer => `
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card customer-card">
                                <div class="card-body">
                                    <h6 class="card-title">${customer.name}</h6>
                                    <p class="card-text">
                                        <small class="text-muted">
                                            <i class="fas fa-envelope me-1"></i>${customer.email}<br>
                                            <i class="fas fa-phone me-1"></i>${customer.phone}
                                        </small>
                                    </p>
                                    <div class="btn-group btn-group-sm w-100">
                                        <button class="btn btn-outline-primary" onclick="window.SalesComponent.viewCustomer('${customer.id}')">
                                            <i class="fas fa-eye me-1"></i>عرض
                                        </button>
                                        <button class="btn btn-outline-success" onclick="window.SalesComponent.editCustomer('${customer.id}')">
                                            <i class="fas fa-edit me-1"></i>تعديل
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="window.SalesComponent.deleteCustomer('${customer.id}')">
                                            <i class="fas fa-trash me-1"></i>حذف
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    },

    /**
     * الحصول على الفواتير المفلترة
     */
    getFilteredInvoices: function() {
        let invoices = Object.values(this.data.invoices || {});

        // فلترة حسب التاريخ
        if (this.data.filters.dateFrom) {
            invoices = invoices.filter(invoice => invoice.date >= this.data.filters.dateFrom);
        }
        if (this.data.filters.dateTo) {
            invoices = invoices.filter(invoice => invoice.date <= this.data.filters.dateTo);
        }

        // فلترة حسب الحالة
        if (this.data.filters.status) {
            invoices = invoices.filter(invoice => invoice.status === this.data.filters.status);
        }

        // فلترة حسب العميل
        if (this.data.filters.customer) {
            invoices = invoices.filter(invoice => invoice.customerId === this.data.filters.customer);
        }

        return invoices.sort((a, b) => new Date(b.date) - new Date(a.date));
    },

    /**
     * تحديث الفلتر
     */
    updateFilter: function(filterName, value) {
        this.data.filters[filterName] = value;
        this.saveSalesData();
        this.refreshData();
    },

    /**
     * تنسيق المبلغ
     */
    formatAmount: function(amount) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR'
        }).format(amount || 0);
    },

    /**
     * الحصول على لون حالة الفاتورة
     */
    getInvoiceStatusColor: function(status) {
        const colors = {
            draft: 'secondary',
            sent: 'info',
            paid: 'success',
            overdue: 'danger',
            cancelled: 'dark'
        };
        return colors[status] || 'secondary';
    },

    /**
     * الحصول على تسمية حالة الفاتورة
     */
    getInvoiceStatusLabel: function(status) {
        const labels = {
            draft: 'مسودة',
            sent: 'مرسلة',
            paid: 'مدفوعة',
            overdue: 'متأخرة',
            cancelled: 'ملغية'
        };
        return labels[status] || 'غير محدد';
    },

    /**
     * عرض نافذة إنشاء فاتورة
     */
    showCreateInvoiceModal: function() {
        const customers = Object.values(this.data.customers || {});
        const products = Object.values(this.data.products || {});

        if (customers.length === 0) {
            alert('يجب إضافة عميل واحد على الأقل قبل إنشاء فاتورة');
            return;
        }

        const modalHTML = `
            <div class="modal fade" id="createInvoiceModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-file-invoice me-2"></i>إنشاء فاتورة جديدة
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="createInvoiceForm">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">رقم الفاتورة</label>
                                        <input type="text" class="form-control" name="number"
                                               value="${this.data.settings.invoicePrefix}${this.data.settings.nextInvoiceNumber}" readonly>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">تاريخ الفاتورة</label>
                                        <input type="date" class="form-control" name="date"
                                               value="${new Date().toISOString().split('T')[0]}" required>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">العميل *</label>
                                        <select class="form-control" name="customerId" required>
                                            <option value="">اختر العميل</option>
                                            ${customers.map(customer => `
                                                <option value="${customer.id}">${customer.name}</option>
                                            `).join('')}
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">تاريخ الاستحقاق</label>
                                        <input type="date" class="form-control" name="dueDate"
                                               value="${new Date(Date.now() + this.data.settings.defaultDueDays * 24 * 60 * 60 * 1000).toISOString().split('T')[0]}">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">المرجع (اختياري)</label>
                                    <input type="text" class="form-control" name="reference" placeholder="رقم المرجع أو الطلب">
                                </div>

                                <!-- عناصر الفاتورة -->
                                <div class="card mb-3">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">عناصر الفاتورة</h6>
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="window.SalesComponent.addInvoiceItem()">
                                            <i class="fas fa-plus me-1"></i>إضافة عنصر
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div id="invoiceItems">
                                            <div class="invoice-item row mb-2">
                                                <div class="col-md-4">
                                                    <select class="form-control item-product" name="items[0][productId]" onchange="window.SalesComponent.updateItemFromProduct(0, this.value)">
                                                        <option value="">اختر المنتج</option>
                                                        ${products.map(product => `
                                                            <option value="${product.id}" data-price="${product.price}">${product.name}</option>
                                                        `).join('')}
                                                    </select>
                                                </div>
                                                <div class="col-md-2">
                                                    <input type="number" class="form-control item-quantity" name="items[0][quantity]"
                                                           placeholder="الكمية" value="1" min="1" onchange="window.SalesComponent.calculateItemTotal(0)">
                                                </div>
                                                <div class="col-md-2">
                                                    <input type="number" class="form-control item-price" name="items[0][price]"
                                                           placeholder="السعر" step="0.01" onchange="window.SalesComponent.calculateItemTotal(0)">
                                                </div>
                                                <div class="col-md-2">
                                                    <input type="number" class="form-control item-total" name="items[0][total]"
                                                           placeholder="الإجمالي" readonly>
                                                </div>
                                                <div class="col-md-2">
                                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="window.SalesComponent.removeInvoiceItem(0)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- الإجماليات -->
                                <div class="card">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-8">
                                                <label class="form-label">ملاحظات</label>
                                                <textarea class="form-control" name="notes" rows="3" placeholder="ملاحظات إضافية"></textarea>
                                            </div>
                                            <div class="col-md-4">
                                                <table class="table table-sm">
                                                    <tr>
                                                        <td>المجموع الفرعي:</td>
                                                        <td class="text-end"><span id="invoiceSubtotal">0.00 ر.س</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td>الضريبة (${(this.data.settings.taxRate * 100)}%):</td>
                                                        <td class="text-end"><span id="invoiceTax">0.00 ر.س</span></td>
                                                    </tr>
                                                    <tr class="table-primary">
                                                        <td><strong>الإجمالي:</strong></td>
                                                        <td class="text-end"><strong><span id="invoiceTotal">0.00 ر.س</span></strong></td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveInvoice()">
                                <i class="fas fa-save me-1"></i>حفظ الفاتورة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('createInvoiceModal'));
        modal.show();

        // إزالة النافذة عند الإغلاق
        document.getElementById('createInvoiceModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * إضافة عنصر جديد للفاتورة
     */
    addInvoiceItem: function() {
        const itemsContainer = document.getElementById('invoiceItems');
        const itemCount = itemsContainer.children.length;
        const products = Object.values(this.data.products || {});

        const itemHTML = `
            <div class="invoice-item row mb-2">
                <div class="col-md-4">
                    <select class="form-control item-product" name="items[${itemCount}][productId]" onchange="window.SalesComponent.updateItemFromProduct(${itemCount}, this.value)">
                        <option value="">اختر المنتج</option>
                        ${products.map(product => `
                            <option value="${product.id}" data-price="${product.price}">${product.name}</option>
                        `).join('')}
                    </select>
                </div>
                <div class="col-md-2">
                    <input type="number" class="form-control item-quantity" name="items[${itemCount}][quantity]"
                           placeholder="الكمية" value="1" min="1" onchange="window.SalesComponent.calculateItemTotal(${itemCount})">
                </div>
                <div class="col-md-2">
                    <input type="number" class="form-control item-price" name="items[${itemCount}][price]"
                           placeholder="السعر" step="0.01" onchange="window.SalesComponent.calculateItemTotal(${itemCount})">
                </div>
                <div class="col-md-2">
                    <input type="number" class="form-control item-total" name="items[${itemCount}][total]"
                           placeholder="الإجمالي" readonly>
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="window.SalesComponent.removeInvoiceItem(${itemCount})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;

        itemsContainer.insertAdjacentHTML('beforeend', itemHTML);
    },

    /**
     * إزالة عنصر من الفاتورة
     */
    removeInvoiceItem: function(itemIndex) {
        const itemsContainer = document.getElementById('invoiceItems');
        const items = itemsContainer.children;

        if (items.length > 1) {
            items[itemIndex].remove();
            this.calculateInvoiceTotal();
        } else {
            alert('يجب أن تحتوي الفاتورة على عنصر واحد على الأقل');
        }
    },

    /**
     * تحديث عنصر من المنتج المختار
     */
    updateItemFromProduct: function(itemIndex, productId) {
        if (!productId) return;

        const product = this.data.products[productId];
        if (!product) return;

        const itemsContainer = document.getElementById('invoiceItems');
        const item = itemsContainer.children[itemIndex];

        const priceInput = item.querySelector('.item-price');
        priceInput.value = product.price;

        this.calculateItemTotal(itemIndex);
    },

    /**
     * حساب إجمالي العنصر
     */
    calculateItemTotal: function(itemIndex) {
        const itemsContainer = document.getElementById('invoiceItems');
        const item = itemsContainer.children[itemIndex];

        const quantity = parseFloat(item.querySelector('.item-quantity').value) || 0;
        const price = parseFloat(item.querySelector('.item-price').value) || 0;
        const total = quantity * price;

        item.querySelector('.item-total').value = total.toFixed(2);

        this.calculateInvoiceTotal();
    },

    /**
     * حساب إجمالي الفاتورة
     */
    calculateInvoiceTotal: function() {
        const itemsContainer = document.getElementById('invoiceItems');
        let subtotal = 0;

        Array.from(itemsContainer.children).forEach(item => {
            const total = parseFloat(item.querySelector('.item-total').value) || 0;
            subtotal += total;
        });

        const tax = subtotal * this.data.settings.taxRate;
        const total = subtotal + tax;

        document.getElementById('invoiceSubtotal').textContent = this.formatAmount(subtotal);
        document.getElementById('invoiceTax').textContent = this.formatAmount(tax);
        document.getElementById('invoiceTotal').textContent = this.formatAmount(total);
    },

    /**
     * حفظ الفاتورة
     */
    saveInvoice: function() {
        const form = document.getElementById('createInvoiceForm');
        const formData = new FormData(form);

        // التحقق من البيانات المطلوبة
        if (!formData.get('customerId')) {
            alert('يرجى اختيار العميل');
            return;
        }

        // جمع عناصر الفاتورة
        const items = [];
        const itemsContainer = document.getElementById('invoiceItems');

        Array.from(itemsContainer.children).forEach((item, index) => {
            const productSelect = item.querySelector('.item-product');
            const quantityInput = item.querySelector('.item-quantity');
            const priceInput = item.querySelector('.item-price');
            const totalInput = item.querySelector('.item-total');

            if (productSelect.value && quantityInput.value && priceInput.value) {
                const product = this.data.products[productSelect.value];
                items.push({
                    productId: productSelect.value,
                    name: product ? product.name : 'منتج غير محدد',
                    quantity: parseInt(quantityInput.value),
                    price: parseFloat(priceInput.value),
                    total: parseFloat(totalInput.value)
                });
            }
        });

        if (items.length === 0) {
            alert('يرجى إضافة عنصر واحد على الأقل للفاتورة');
            return;
        }

        // حساب الإجماليات
        const subtotal = items.reduce((sum, item) => sum + item.total, 0);
        const tax = subtotal * this.data.settings.taxRate;
        const total = subtotal + tax;

        // إنشاء الفاتورة
        const invoiceId = 'invoice_' + Date.now();
        const invoice = {
            id: invoiceId,
            number: formData.get('number'),
            date: formData.get('date'),
            dueDate: formData.get('dueDate'),
            customerId: formData.get('customerId'),
            reference: formData.get('reference'),
            items: items,
            subtotal: subtotal,
            tax: tax,
            total: total,
            status: 'draft',
            notes: formData.get('notes'),
            createdAt: new Date().toISOString()
        };

        // حفظ الفاتورة
        this.data.invoices[invoiceId] = invoice;
        this.data.settings.nextInvoiceNumber++;
        this.saveSalesData();

        // إغلاق النافذة وتحديث العرض
        const modal = bootstrap.Modal.getInstance(document.getElementById('createInvoiceModal'));
        modal.hide();

        this.refreshData();
        alert('تم إنشاء الفاتورة بنجاح');
    },

    /**
     * عرض نافذة إنشاء عميل
     */
    showCreateCustomerModal: function() {
        const modalHTML = `
            <div class="modal fade" id="createCustomerModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-user-plus me-2"></i>إضافة عميل جديد
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="createCustomerForm">
                                <div class="mb-3">
                                    <label class="form-label">اسم العميل *</label>
                                    <input type="text" class="form-control" name="name" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني *</label>
                                    <input type="email" class="form-control" name="email" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف *</label>
                                    <input type="tel" class="form-control" name="phone" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea class="form-control" name="address" rows="3"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الرقم الضريبي</label>
                                    <input type="text" class="form-control" name="taxNumber">
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveCustomer()">
                                <i class="fas fa-save me-1"></i>حفظ العميل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('createCustomerModal'));
        modal.show();

        document.getElementById('createCustomerModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * حفظ العميل
     */
    saveCustomer: function() {
        const form = document.getElementById('createCustomerForm');
        const formData = new FormData(form);

        // التحقق من البيانات المطلوبة
        if (!formData.get('name') || !formData.get('email') || !formData.get('phone')) {
            alert('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        // إنشاء العميل
        const customerId = 'customer_' + Date.now();
        const customer = {
            id: customerId,
            name: formData.get('name'),
            email: formData.get('email'),
            phone: formData.get('phone'),
            address: formData.get('address'),
            taxNumber: formData.get('taxNumber'),
            createdAt: new Date().toISOString()
        };

        // حفظ العميل
        this.data.customers[customerId] = customer;
        this.saveSalesData();

        // إغلاق النافذة وتحديث العرض
        const modal = bootstrap.Modal.getInstance(document.getElementById('createCustomerModal'));
        modal.hide();

        this.refreshData();
        alert('تم إضافة العميل بنجاح');
    },

    /**
     * عرض نافذة إنشاء منتج
     */
    showCreateProductModal: function() {
        const modalHTML = `
            <div class="modal fade" id="createProductModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-box me-2"></i>إضافة منتج جديد
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="createProductForm">
                                <div class="mb-3">
                                    <label class="form-label">اسم المنتج *</label>
                                    <input type="text" class="form-control" name="name" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الوصف</label>
                                    <textarea class="form-control" name="description" rows="3"></textarea>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">السعر *</label>
                                            <input type="number" class="form-control" name="price" step="0.01" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الفئة</label>
                                            <select class="form-control" name="category">
                                                <option value="">اختر الفئة</option>
                                                <option value="طيران">طيران</option>
                                                <option value="إقامة">إقامة</option>
                                                <option value="نقل">نقل</option>
                                                <option value="جولات">جولات</option>
                                                <option value="تأشيرات">تأشيرات</option>
                                                <option value="أخرى">أخرى</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">رمز المنتج (SKU)</label>
                                    <input type="text" class="form-control" name="sku">
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveProduct()">
                                <i class="fas fa-save me-1"></i>حفظ المنتج
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('createProductModal'));
        modal.show();

        document.getElementById('createProductModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * حفظ المنتج
     */
    saveProduct: function() {
        const form = document.getElementById('createProductForm');
        const formData = new FormData(form);

        // التحقق من البيانات المطلوبة
        if (!formData.get('name') || !formData.get('price')) {
            alert('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        // إنشاء المنتج
        const productId = 'product_' + Date.now();
        const product = {
            id: productId,
            name: formData.get('name'),
            description: formData.get('description'),
            price: parseFloat(formData.get('price')),
            category: formData.get('category'),
            sku: formData.get('sku'),
            createdAt: new Date().toISOString()
        };

        // حفظ المنتج
        this.data.products[productId] = product;
        this.saveSalesData();

        // إغلاق النافذة وتحديث العرض
        const modal = bootstrap.Modal.getInstance(document.getElementById('createProductModal'));
        modal.hide();

        this.refreshData();
        alert('تم إضافة المنتج بنجاح');
    },

    /**
     * عرض قسم المنتجات
     */
    renderProductsView: function() {
        const products = Object.values(this.data.products || {});

        return `
            <div class="products-management">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4><i class="fas fa-boxes me-2"></i>إدارة المنتجات</h4>
                    <button class="btn btn-primary" onclick="window.SalesComponent.showCreateProductModal()">
                        <i class="fas fa-box me-1"></i>منتج جديد
                    </button>
                </div>

                <div class="row">
                    ${products.length === 0 ? `
                        <div class="col-12">
                            <div class="text-center py-5">
                                <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد منتجات</h5>
                                <p class="text-muted">ابدأ بإضافة منتج جديد</p>
                                <button class="btn btn-primary" onclick="window.SalesComponent.showCreateProductModal()">
                                    <i class="fas fa-box me-1"></i>إضافة منتج
                                </button>
                            </div>
                        </div>
                    ` : products.map(product => `
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card product-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="card-title">${product.name}</h6>
                                        ${product.category ? `<span class="badge bg-secondary">${product.category}</span>` : ''}
                                    </div>
                                    ${product.description ? `<p class="card-text text-muted small">${product.description}</p>` : ''}
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <strong class="text-primary">${this.formatAmount(product.price)}</strong>
                                        ${product.sku ? `<small class="text-muted">SKU: ${product.sku}</small>` : ''}
                                    </div>
                                    <div class="btn-group btn-group-sm w-100">
                                        <button class="btn btn-outline-primary" onclick="window.SalesComponent.viewProduct('${product.id}')">
                                            <i class="fas fa-eye me-1"></i>عرض
                                        </button>
                                        <button class="btn btn-outline-success" onclick="window.SalesComponent.editProduct('${product.id}')">
                                            <i class="fas fa-edit me-1"></i>تعديل
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="window.SalesComponent.deleteProduct('${product.id}')">
                                            <i class="fas fa-trash me-1"></i>حذف
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    },

    /**
     * عرض قسم التقارير
     */
    renderReportsView: function() {
        return `
            <div class="reports-management">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4><i class="fas fa-chart-bar me-2"></i>التقارير</h4>
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    قسم التقارير قيد التطوير
                </div>
            </div>
        `;
    },

    /**
     * عرض قسم الإعدادات
     */
    renderSettingsView: function() {
        return `
            <div class="settings-management">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4><i class="fas fa-cog me-2"></i>الإعدادات</h4>
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    قسم الإعدادات قيد التطوير
                </div>
            </div>
        `;
    },

    /**
     * تصدير الفواتير CSV
     */
    exportInvoicesCSV: function() {
        alert('ميزة التصدير قيد التطوير');
    },

    /**
     * عرض المنتج
     */
    viewProduct: function(productId) {
        const product = this.data.products[productId];
        if (!product) {
            alert('المنتج غير موجود');
            return;
        }

        const modalHTML = `
            <div class="modal fade" id="viewProductModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-box me-2"></i>تفاصيل المنتج
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <table class="table">
                                <tr>
                                    <td><strong>اسم المنتج:</strong></td>
                                    <td>${product.name}</td>
                                </tr>
                                ${product.description ? `
                                <tr>
                                    <td><strong>الوصف:</strong></td>
                                    <td>${product.description}</td>
                                </tr>
                                ` : ''}
                                <tr>
                                    <td><strong>السعر:</strong></td>
                                    <td>${this.formatAmount(product.price)}</td>
                                </tr>
                                ${product.category ? `
                                <tr>
                                    <td><strong>الفئة:</strong></td>
                                    <td>${product.category}</td>
                                </tr>
                                ` : ''}
                                ${product.sku ? `
                                <tr>
                                    <td><strong>رمز المنتج:</strong></td>
                                    <td>${product.sku}</td>
                                </tr>
                                ` : ''}
                                <tr>
                                    <td><strong>تاريخ الإنشاء:</strong></td>
                                    <td>${new Date(product.createdAt).toLocaleDateString('ar-SA')}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.editProduct('${product.id}')">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('viewProductModal'));
        modal.show();

        document.getElementById('viewProductModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * تعديل المنتج
     */
    editProduct: function(productId) {
        alert('ميزة تعديل المنتج قيد التطوير');
    },

    /**
     * حذف المنتج
     */
    deleteProduct: function(productId) {
        if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
            delete this.data.products[productId];
            this.saveSalesData();
            this.refreshData();
            alert('تم حذف المنتج بنجاح');
        }
    },

    /**
     * عرض الفاتورة
     */
    viewInvoice: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            alert('الفاتورة غير موجودة');
            return;
        }

        const customer = this.data.customers[invoice.customerId];

        const modalHTML = `
            <div class="modal fade" id="viewInvoiceModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-file-invoice me-2"></i>فاتورة رقم: ${invoice.number}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="invoice-preview">
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <h6>معلومات الفاتورة</h6>
                                        <table class="table table-sm">
                                            <tr>
                                                <td><strong>رقم الفاتورة:</strong></td>
                                                <td>${invoice.number}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>التاريخ:</strong></td>
                                                <td>${new Date(invoice.date).toLocaleDateString('ar-SA')}</td>
                                            </tr>
                                            ${invoice.dueDate ? `
                                            <tr>
                                                <td><strong>تاريخ الاستحقاق:</strong></td>
                                                <td>${new Date(invoice.dueDate).toLocaleDateString('ar-SA')}</td>
                                            </tr>
                                            ` : ''}
                                            ${invoice.reference ? `
                                            <tr>
                                                <td><strong>المرجع:</strong></td>
                                                <td>${invoice.reference}</td>
                                            </tr>
                                            ` : ''}
                                            <tr>
                                                <td><strong>الحالة:</strong></td>
                                                <td>
                                                    <span class="badge bg-${this.getInvoiceStatusColor(invoice.status)}">
                                                        ${this.getInvoiceStatusLabel(invoice.status)}
                                                    </span>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>معلومات العميل</h6>
                                        ${customer ? `
                                            <table class="table table-sm">
                                                <tr>
                                                    <td><strong>الاسم:</strong></td>
                                                    <td>${customer.name}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>الإيميل:</strong></td>
                                                    <td>${customer.email}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>الهاتف:</strong></td>
                                                    <td>${customer.phone}</td>
                                                </tr>
                                                ${customer.address ? `
                                                <tr>
                                                    <td><strong>العنوان:</strong></td>
                                                    <td>${customer.address}</td>
                                                </tr>
                                                ` : ''}
                                            </table>
                                        ` : '<p class="text-muted">عميل غير محدد</p>'}
                                    </div>
                                </div>

                                <h6>عناصر الفاتورة</h6>
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>المنتج</th>
                                                <th>الكمية</th>
                                                <th>السعر</th>
                                                <th>الإجمالي</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${invoice.items.map(item => `
                                                <tr>
                                                    <td>${item.name}</td>
                                                    <td>${item.quantity}</td>
                                                    <td>${this.formatAmount(item.price)}</td>
                                                    <td>${this.formatAmount(item.total)}</td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                                <td colspan="3"><strong>المجموع الفرعي:</strong></td>
                                                <td><strong>${this.formatAmount(invoice.subtotal)}</strong></td>
                                            </tr>
                                            <tr>
                                                <td colspan="3"><strong>الضريبة:</strong></td>
                                                <td><strong>${this.formatAmount(invoice.tax)}</strong></td>
                                            </tr>
                                            <tr class="table-primary">
                                                <td colspan="3"><strong>الإجمالي:</strong></td>
                                                <td><strong>${this.formatAmount(invoice.total)}</strong></td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>

                                ${invoice.notes ? `
                                    <div class="mt-3">
                                        <h6>ملاحظات</h6>
                                        <p>${invoice.notes}</p>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-info" onclick="window.SalesComponent.printInvoice('${invoice.id}')">
                                <i class="fas fa-print me-1"></i>طباعة
                            </button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.editInvoice('${invoice.id}')">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('viewInvoiceModal'));
        modal.show();

        document.getElementById('viewInvoiceModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * تعديل الفاتورة
     */
    editInvoice: function(invoiceId) {
        alert('ميزة تعديل الفاتورة قيد التطوير');
    },

    /**
     * طباعة الفاتورة
     */
    printInvoice: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            alert('الفاتورة غير موجودة');
            return;
        }

        const customer = this.data.customers[invoice.customerId];

        const printHTML = `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>فاتورة رقم ${invoice.number}</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        margin: 20px;
                        direction: rtl;
                        color: #333;
                    }
                    .invoice-header {
                        text-align: center;
                        margin-bottom: 30px;
                        border-bottom: 2px solid #007bff;
                        padding-bottom: 20px;
                    }
                    .company-name {
                        font-size: 24px;
                        font-weight: bold;
                        color: #007bff;
                        margin-bottom: 10px;
                    }
                    .invoice-title {
                        font-size: 20px;
                        margin: 10px 0;
                    }
                    .invoice-info {
                        display: flex;
                        justify-content: space-between;
                        margin: 20px 0;
                    }
                    .info-section {
                        width: 48%;
                    }
                    .info-section h4 {
                        color: #007bff;
                        border-bottom: 1px solid #007bff;
                        padding-bottom: 5px;
                        margin-bottom: 10px;
                    }
                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin: 20px 0;
                    }
                    th, td {
                        border: 1px solid #ddd;
                        padding: 12px;
                        text-align: right;
                    }
                    th {
                        background-color: #007bff;
                        color: white;
                        font-weight: bold;
                    }
                    .total-row {
                        background-color: #f8f9fa;
                        font-weight: bold;
                    }
                    .final-total {
                        background-color: #007bff;
                        color: white;
                        font-weight: bold;
                    }
                    .notes {
                        margin-top: 20px;
                        padding: 15px;
                        background-color: #f8f9fa;
                        border-radius: 5px;
                    }
                    @media print {
                        body { margin: 0; }
                        .no-print { display: none; }
                    }
                </style>
            </head>
            <body>
                <div class="invoice-header">
                    <div class="company-name">${this.data.settings.companyName}</div>
                    <div>${this.data.settings.companyAddress}</div>
                    <div>هاتف: ${this.data.settings.companyPhone} | إيميل: ${this.data.settings.companyEmail}</div>
                    ${this.data.settings.companyTaxNumber ? `<div>الرقم الضريبي: ${this.data.settings.companyTaxNumber}</div>` : ''}
                    <div class="invoice-title">فاتورة رقم: ${invoice.number}</div>
                </div>

                <div class="invoice-info">
                    <div class="info-section">
                        <h4>معلومات الفاتورة</h4>
                        <p><strong>رقم الفاتورة:</strong> ${invoice.number}</p>
                        <p><strong>التاريخ:</strong> ${new Date(invoice.date).toLocaleDateString('ar-SA')}</p>
                        ${invoice.dueDate ? `<p><strong>تاريخ الاستحقاق:</strong> ${new Date(invoice.dueDate).toLocaleDateString('ar-SA')}</p>` : ''}
                        ${invoice.reference ? `<p><strong>المرجع:</strong> ${invoice.reference}</p>` : ''}
                        <p><strong>الحالة:</strong> ${this.getInvoiceStatusLabel(invoice.status)}</p>
                    </div>
                    <div class="info-section">
                        <h4>معلومات العميل</h4>
                        ${customer ? `
                            <p><strong>الاسم:</strong> ${customer.name}</p>
                            <p><strong>الإيميل:</strong> ${customer.email}</p>
                            <p><strong>الهاتف:</strong> ${customer.phone}</p>
                            ${customer.address ? `<p><strong>العنوان:</strong> ${customer.address}</p>` : ''}
                            ${customer.taxNumber ? `<p><strong>الرقم الضريبي:</strong> ${customer.taxNumber}</p>` : ''}
                        ` : '<p>عميل غير محدد</p>'}
                    </div>
                </div>

                <table>
                    <thead>
                        <tr>
                            <th>المنتج/الخدمة</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${invoice.items.map(item => `
                            <tr>
                                <td>${item.name}</td>
                                <td>${item.quantity}</td>
                                <td>${this.formatAmount(item.price)}</td>
                                <td>${this.formatAmount(item.total)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                    <tfoot>
                        <tr class="total-row">
                            <td colspan="3">المجموع الفرعي</td>
                            <td>${this.formatAmount(invoice.subtotal)}</td>
                        </tr>
                        <tr class="total-row">
                            <td colspan="3">الضريبة (${(this.data.settings.taxRate * 100)}%)</td>
                            <td>${this.formatAmount(invoice.tax)}</td>
                        </tr>
                        <tr class="final-total">
                            <td colspan="3">الإجمالي النهائي</td>
                            <td>${this.formatAmount(invoice.total)}</td>
                        </tr>
                    </tfoot>
                </table>

                ${invoice.notes ? `
                    <div class="notes">
                        <h4>ملاحظات:</h4>
                        <p>${invoice.notes}</p>
                    </div>
                ` : ''}

                <div style="margin-top: 40px; text-align: center; color: #666; font-size: 12px;">
                    <p>شكراً لتعاملكم معنا</p>
                    <p>تم إنشاء هذه الفاتورة بواسطة نظام إدارة السفريات</p>
                </div>
            </body>
            </html>
        `;

        // فتح نافذة جديدة للطباعة
        const printWindow = window.open('', '_blank');
        printWindow.document.write(printHTML);
        printWindow.document.close();
        printWindow.focus();

        // طباعة تلقائية
        setTimeout(() => {
            printWindow.print();
        }, 500);
    },

    /**
     * حذف الفاتورة
     */
    deleteInvoice: function(invoiceId) {
        if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
            delete this.data.invoices[invoiceId];
            this.saveSalesData();
            this.refreshData();
            alert('تم حذف الفاتورة بنجاح');
        }
    },

    /**
     * عرض العميل
     */
    viewCustomer: function(customerId) {
        alert('ميزة عرض العميل قيد التطوير');
    },

    /**
     * تعديل العميل
     */
    editCustomer: function(customerId) {
        alert('ميزة تعديل العميل قيد التطوير');
    },

    /**
     * حذف العميل
     */
    deleteCustomer: function(customerId) {
        if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
            delete this.data.customers[customerId];
            this.saveSalesData();
            this.refreshData();
            alert('تم حذف العميل بنجاح');
        }
    },

    /**
     * تحديث التقارير
     */
    updateReports: function() {
        // وظيفة فارغة للتوافق
    },

    /**
     * تحديث البيانات
     */
    refreshData: function() {
        this.loadSalesData();
        const content = document.getElementById('main-content');
        if (content) {
            content.innerHTML = this.renderCurrentView();
        }

        // تحديث التقارير إذا كان في قسم التقارير
        if (this.data.currentView === 'reports') {
            setTimeout(() => {
                this.updateReports();
            }, 100);
        }
    }
};

// تصدير المكون للاستخدام العام
window.SalesComponent = SalesComponent;
