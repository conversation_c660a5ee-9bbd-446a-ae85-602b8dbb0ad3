/**
 * مكون نظام المبيعات المتقدم
 * يوفر إدارة شاملة للفواتير، العملاء، المنتجات، عروض الأسعار، المدفوعات، والمخزون
 */

const SalesComponent = {
    // بيانات النظام
    data: {
        currentView: 'dashboard',
        customers: {},
        products: {},
        invoices: {},
        quotes: {},
        payments: {},
        inventory: {},
        stockAdjustments: {},
        filters: {
            dateFrom: '',
            dateTo: '',
            status: '',
            customer: '',
            quickSearch: ''
        },
        quoteFilters: {
            dateFrom: '',
            dateTo: '',
            status: '',
            customer: ''
        },
        settings: {
            taxRate: 0.15,
            currency: 'SAR',
            invoicePrefix: 'INV-',
            nextInvoiceNumber: 1,
            nextQuoteNumber: 1,
            companyName: 'قمة الوعد للسفريات',
            companyAddress: 'المملكة العربية السعودية',
            companyTaxNumber: '*********',
            companyEmail: '<EMAIL>',
            companyPhone: '+966501234567',
            companyWebsite: 'https://qimat-alwaed.com',
            language: 'ar',
            timezone: 'Asia/Riyadh',
            dateFormat: 'ar-SA',
            autoSave: true,
            showNotifications: true,
            enableBackup: true,
            autoCalculateTax: true,
            defaultDueDays: 30
        }
    },

    /**
     * تهيئة المكون مع معالجة أخطاء شاملة
     */
    init: function() {
        try {
            console.log('🔧 بدء تهيئة مكون المبيعات...');

            // تهيئة معالج الأخطاء
            this.initErrorHandler();

            // تحميل البيانات
            this.loadSalesData();

            // إنشاء البيانات التجريبية إذا لم تكن موجودة
            this.createSampleData();

            // تسجيل نجاح التهيئة
            console.log('✅ تم تهيئة مكون المبيعات بنجاح');
            this.showNotification('تم تهيئة نظام المبيعات بنجاح', 'success');

        } catch (error) {
            console.error('❌ خطأ في تهيئة مكون المبيعات:', error);
            this.handleError('فشل في تهيئة نظام المبيعات', error);
        }
    },

    /**
     * تهيئة معالج الأخطاء
     */
    initErrorHandler: function() {
        // إضافة معالج أخطاء عام للنافذة
        if (!window.salesErrorHandlerInitialized) {
            window.addEventListener('error', (event) => {
                if (event.filename && event.filename.includes('sales.js')) {
                    this.handleError('خطأ في نظام المبيعات', event.error);
                }
            });

            window.addEventListener('unhandledrejection', (event) => {
                this.handleError('خطأ غير معالج في نظام المبيعات', event.reason);
            });

            window.salesErrorHandlerInitialized = true;
        }
    },

    /**
     * معالج الأخطاء الرئيسي
     */
    handleError: function(message, error = null) {
        const errorInfo = {
            message: message,
            error: error ? error.message : 'خطأ غير محدد',
            stack: error ? error.stack : null,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };

        // تسجيل الخطأ
        console.error('🚨 خطأ في نظام المبيعات:', errorInfo);

        // حفظ الخطأ في التخزين المحلي للمراجعة
        this.logError(errorInfo);

        // إظهار رسالة للمستخدم
        this.showErrorNotification(message, error);

        // محاولة الاسترداد التلقائي
        this.attemptAutoRecovery(errorInfo);
    },

    /**
     * تسجيل الأخطاء في التخزين المحلي
     */
    logError: function(errorInfo) {
        try {
            const errorLog = JSON.parse(localStorage.getItem('salesErrorLog') || '[]');
            errorLog.push(errorInfo);

            // الاحتفاظ بآخر 50 خطأ فقط
            if (errorLog.length > 50) {
                errorLog.splice(0, errorLog.length - 50);
            }

            localStorage.setItem('salesErrorLog', JSON.stringify(errorLog));
        } catch (e) {
            console.error('فشل في حفظ سجل الأخطاء:', e);
        }
    },

    /**
     * إظهار إشعار خطأ للمستخدم
     */
    showErrorNotification: function(message, error = null) {
        const container = document.getElementById('main-content');
        if (!container) return;

        const errorDetails = error ? `<br><small class="text-muted">التفاصيل: ${error.message}</small>` : '';

        const errorHtml = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <h4 class="alert-heading">
                    <i class="fas fa-exclamation-triangle me-2"></i>خطأ في نظام المبيعات
                </h4>
                <p class="mb-0">${message}${errorDetails}</p>
                <hr>
                <div class="d-flex gap-2 mt-3">
                    <button class="btn btn-outline-primary btn-sm" onclick="location.reload()">
                        <i class="fas fa-refresh me-1"></i>تحديث الصفحة
                    </button>
                    <button class="btn btn-outline-info btn-sm" onclick="window.SalesComponent.attemptAutoRecovery()">
                        <i class="fas fa-wrench me-1"></i>محاولة الإصلاح
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="window.SalesComponent.showErrorLog()">
                        <i class="fas fa-list me-1"></i>سجل الأخطاء
                    </button>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // إدراج الخطأ في أعلى المحتوى
        container.insertAdjacentHTML('afterbegin', errorHtml);
    },

    /**
     * إظهار إشعار نجاح
     */
    showNotification: function(message, type = 'info') {
        if (!this.data.settings.showNotifications) return;

        const colors = {
            success: 'alert-success',
            info: 'alert-info',
            warning: 'alert-warning',
            error: 'alert-danger'
        };

        const icons = {
            success: 'fas fa-check-circle',
            info: 'fas fa-info-circle',
            warning: 'fas fa-exclamation-triangle',
            error: 'fas fa-times-circle'
        };

        const notification = document.createElement('div');
        notification.className = `alert ${colors[type]} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        notification.innerHTML = `
            <i class="${icons[type]} me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // إزالة الإشعار تلقائياً بعد 5 ثوان
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    },

    /**
     * محاولة الاسترداد التلقائي
     */
    attemptAutoRecovery: function(errorInfo = null) {
        console.log('🔄 محاولة الاسترداد التلقائي...');

        try {
            // إعادة تهيئة البيانات
            this.data = this.getDefaultData();

            // إعادة تحميل البيانات
            this.loadSalesData();

            // إنشاء البيانات التجريبية
            this.createSampleData();

            // إعادة عرض لوحة التحكم
            this.render({ view: 'dashboard' });

            console.log('✅ تم الاسترداد التلقائي بنجاح');
            this.showNotification('تم إصلاح النظام تلقائياً', 'success');

            return true;
        } catch (error) {
            console.error('❌ فشل في الاسترداد التلقائي:', error);
            this.showNotification('فشل في الإصلاح التلقائي، يرجى تحديث الصفحة', 'error');
            return false;
        }
    },

    /**
     * عرض سجل الأخطاء
     */
    showErrorLog: function() {
        try {
            const errorLog = JSON.parse(localStorage.getItem('salesErrorLog') || '[]');

            if (errorLog.length === 0) {
                this.showNotification('لا توجد أخطاء مسجلة', 'info');
                return;
            }

            const logHtml = errorLog.map((error, index) => `
                <div class="card mb-2">
                    <div class="card-body">
                        <h6 class="card-title text-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>خطأ #${index + 1}
                        </h6>
                        <p class="card-text">${error.message}</p>
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>${new Date(error.timestamp).toLocaleString('ar-SA')}
                        </small>
                        ${error.error ? `<br><small class="text-muted">التفاصيل: ${error.error}</small>` : ''}
                    </div>
                </div>
            `).join('');

            const container = document.getElementById('main-content');
            if (container) {
                container.innerHTML = `
                    <div class="container-fluid">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2><i class="fas fa-list me-2"></i>سجل الأخطاء</h2>
                            <div class="btn-group">
                                <button class="btn btn-outline-danger" onclick="window.SalesComponent.clearErrorLog()">
                                    <i class="fas fa-trash me-1"></i>مسح السجل
                                </button>
                                <button class="btn btn-outline-primary" onclick="window.SalesComponent.render({view: 'dashboard'})">
                                    <i class="fas fa-arrow-left me-1"></i>العودة
                                </button>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            يحتوي هذا السجل على آخر ${errorLog.length} خطأ في نظام المبيعات
                        </div>

                        ${logHtml}
                    </div>
                `;
            }
        } catch (error) {
            console.error('خطأ في عرض سجل الأخطاء:', error);
            this.showNotification('فشل في عرض سجل الأخطاء', 'error');
        }
    },

    /**
     * مسح سجل الأخطاء
     */
    clearErrorLog: function() {
        try {
            localStorage.removeItem('salesErrorLog');
            this.showNotification('تم مسح سجل الأخطاء', 'success');
            this.render({ view: 'dashboard' });
        } catch (error) {
            console.error('خطأ في مسح سجل الأخطاء:', error);
            this.showNotification('فشل في مسح سجل الأخطاء', 'error');
        }
    },

    /**
     * عرض المكون مع معالجة أخطاء محسنة
     */
    render: function(params = {}) {
        try {
            this.data.currentView = params.view || 'dashboard';

            const container = document.getElementById('main-content');
            if (!container) {
                throw new Error('عنصر العرض الرئيسي غير موجود');
            }

            // تحديث شريط التنقل
            this.updateNavigation();

            // عرض المحتوى حسب العرض المختار
            let content = '';

            switch (this.data.currentView) {
                case 'dashboard':
                    content = this.renderDashboard();
                    break;
                case 'invoices':
                    content = this.renderInvoicesView();
                    break;
                case 'quotes':
                    content = this.renderQuotesView();
                    break;
                case 'customers':
                    content = this.renderCustomersView();
                    break;
                case 'products':
                    content = this.renderProductsView();
                    break;
                case 'inventory':
                    content = this.renderInventoryView();
                    break;
                case 'payments':
                    content = this.renderPaymentsView();
                    break;
                case 'reports':
                    content = this.renderReportsView();
                    break;
                case 'settings':
                    content = this.renderSettingsView();
                    break;
                default:
                    content = this.renderDashboard();
            }

            // التحقق من وجود المحتوى
            if (!content || content.trim().length === 0) {
                throw new Error(`فشل في إنشاء محتوى للعرض: ${this.data.currentView}`);
            }

            // عرض المحتوى
            container.innerHTML = content;

            // تسجيل نجاح العرض
            console.log(`✅ تم عرض ${this.data.currentView} بنجاح`);

        } catch (error) {
            console.error(`❌ خطأ في عرض ${this.data.currentView}:`, error);
            this.handleError(`فشل في عرض ${this.getViewDisplayName(this.data.currentView)}`, error);

            // محاولة عرض صفحة خطأ
            this.renderErrorPage(error);
        }
    },

    /**
     * عرض صفحة خطأ
     */
    renderErrorPage: function(error) {
        const container = document.getElementById('main-content');
        if (!container) return;

        container.innerHTML = `
            <div class="container-fluid">
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h4 class="mb-0">
                                    <i class="fas fa-exclamation-triangle me-2"></i>خطأ في النظام
                                </h4>
                            </div>
                            <div class="card-body">
                                <div class="text-center mb-4">
                                    <i class="fas fa-bug fa-4x text-danger mb-3"></i>
                                    <h5>عذراً، حدث خطأ في عرض هذه الصفحة</h5>
                                    <p class="text-muted">نعتذر عن الإزعاج، يرجى المحاولة مرة أخرى</p>
                                </div>

                                <div class="alert alert-light">
                                    <strong>تفاصيل الخطأ:</strong><br>
                                    <code>${error.message}</code>
                                </div>

                                <div class="d-flex justify-content-center gap-3">
                                    <button class="btn btn-primary" onclick="window.SalesComponent.render({view: 'dashboard'})">
                                        <i class="fas fa-home me-2"></i>العودة للرئيسية
                                    </button>
                                    <button class="btn btn-outline-info" onclick="window.SalesComponent.attemptAutoRecovery()">
                                        <i class="fas fa-wrench me-2"></i>محاولة الإصلاح
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="location.reload()">
                                        <i class="fas fa-refresh me-2"></i>تحديث الصفحة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * الحصول على اسم العرض للعرض
     */
    getViewDisplayName: function(view) {
        const names = {
            dashboard: 'لوحة التحكم',
            invoices: 'الفواتير',
            customers: 'العملاء',
            products: 'المنتجات',
            reports: 'التقارير',
            quotes: 'عروض الأسعار',
            inventory: 'المخزون',
            payments: 'المدفوعات',
            settings: 'الإعدادات'
        };
        return names[view] || view;

        // تحديث الرسوم البيانية إذا كانت في لوحة التحكم
        if (this.data.currentView === 'dashboard') {
            setTimeout(() => {
                this.updateCharts();
            }, 100);
        }

        // تحديث التقارير إذا كانت في صفحة التقارير
        if (this.data.currentView === 'reports') {
            setTimeout(() => {
                this.updateReports();
            }, 100);
        }
    },

    /**
     * تحميل بيانات المبيعات من التخزين المحلي
     */
    loadSalesData: function() {
        try {
            const savedData = localStorage.getItem('salesData');
            if (savedData) {
                const parsedData = JSON.parse(savedData);
                Object.assign(this.data, parsedData);
            }

            // التأكد من وجود البيانات الأساسية
            if (!this.data.customers) this.data.customers = {};
            if (!this.data.products) this.data.products = {};
            if (!this.data.invoices) this.data.invoices = {};
            if (!this.data.quotes) this.data.quotes = {};
            if (!this.data.payments) this.data.payments = {};
            if (!this.data.inventory) this.data.inventory = {};
            if (!this.data.stockAdjustments) this.data.stockAdjustments = {};
            if (!this.data.filters) this.data.filters = {
                dateFrom: '',
                dateTo: '',
                status: '',
                customer: '',
                quickSearch: ''
            };
            if (!this.data.quoteFilters) this.data.quoteFilters = {
                dateFrom: '',
                dateTo: '',
                status: '',
                customer: ''
            };
            if (!this.data.settings) this.data.settings = {
                taxRate: 0.15,
                currency: 'SAR',
                invoicePrefix: 'INV-',
                nextInvoiceNumber: 1,
                nextQuoteNumber: 1,
                companyName: 'قمة الوعد للسفريات',
                companyAddress: 'المملكة العربية السعودية',
                companyTaxNumber: '*********',
                companyEmail: '<EMAIL>',
                companyPhone: '+966501234567',
                companyWebsite: 'https://qimat-alwaed.com',
                language: 'ar',
                timezone: 'Asia/Riyadh',
                dateFormat: 'ar-SA',
                autoSave: true,
                showNotifications: true,
                enableBackup: true,
                autoCalculateTax: true,
                defaultDueDays: 30
            };
        } catch (error) {
            console.error('خطأ في تحميل بيانات المبيعات:', error);
            // إعادة تهيئة البيانات في حالة الخطأ
            this.data = {
                currentView: 'dashboard',
                customers: {},
                products: {},
                invoices: {},
                quotes: {},
                payments: {},
                inventory: {},
                stockAdjustments: {},
                filters: {
                    dateFrom: '',
                    dateTo: '',
                    status: '',
                    customer: '',
                    quickSearch: ''
                },
                quoteFilters: {
                    dateFrom: '',
                    dateTo: '',
                    status: '',
                    customer: ''
                },
                settings: {
                    taxRate: 0.15,
                    currency: 'SAR',
                    invoicePrefix: 'INV-',
                    nextInvoiceNumber: 1,
                    nextQuoteNumber: 1,
                    companyName: 'قمة الوعد للسفريات',
                    companyAddress: 'المملكة العربية السعودية',
                    companyTaxNumber: '*********',
                    companyEmail: '<EMAIL>',
                    companyPhone: '+966501234567',
                    companyWebsite: 'https://qimat-alwaed.com',
                    language: 'ar',
                    timezone: 'Asia/Riyadh',
                    dateFormat: 'ar-SA',
                    autoSave: true,
                    showNotifications: true,
                    enableBackup: true,
                    autoCalculateTax: true,
                    defaultDueDays: 30
                }
            };
        }
    },

    /**
     * حفظ بيانات المبيعات في التخزين المحلي
     */
    saveSalesData: function() {
        try {
            localStorage.setItem('salesData', JSON.stringify(this.data));
        } catch (error) {
            console.error('خطأ في حفظ بيانات المبيعات:', error);
        }
    },

    /**
     * إنشاء بيانات تجريبية
     */
    createSampleData: function() {
        // التحقق من وجود بيانات - إنشاء البيانات المفقودة فقط
        const hasData = Object.keys(this.data.customers || {}).length > 0 &&
                       Object.keys(this.data.products || {}).length > 0 &&
                       Object.keys(this.data.invoices || {}).length > 0;

        if (hasData) {
            return;
        }

        // تهيئة البيانات إذا لم تكن موجودة
        if (!this.data.customers) this.data.customers = {};
        if (!this.data.products) this.data.products = {};
        if (!this.data.invoices) this.data.invoices = {};
        if (!this.data.quotes) this.data.quotes = {};
        if (!this.data.payments) this.data.payments = {};
        if (!this.data.inventory) this.data.inventory = {};
        if (!this.data.stockAdjustments) this.data.stockAdjustments = {};
        if (!this.data.quoteFilters) {
            this.data.quoteFilters = {
                dateFrom: '',
                dateTo: '',
                status: '',
                customer: ''
            };
        }
        if (!this.data.filters) {
            this.data.filters = {
                dateFrom: '',
                dateTo: '',
                status: '',
                customer: '',
                quickSearch: ''
            };
        }

        // إنشاء عملاء تجريبيين
        this.data.customers = {
            'customer1': {
                id: 'customer1',
                name: 'أحمد محمد السعيد',
                email: '<EMAIL>',
                phone: '+966501234567',
                address: 'الرياض، المملكة العربية السعودية',
                taxNumber: '*********',
                status: 'active',
                createdAt: new Date().toISOString()
            },
            'customer2': {
                id: 'customer2',
                name: 'فاطمة علي الزهراني',
                email: '<EMAIL>',
                phone: '+966507654321',
                address: 'جدة، المملكة العربية السعودية',
                taxNumber: '*********',
                status: 'active',
                createdAt: new Date().toISOString()
            }
        };

        // إنشاء منتجات تجريبية
        this.data.products = {
            'product1': {
                id: 'product1',
                name: 'تذكرة طيران - الرياض إلى دبي',
                description: 'تذكرة طيران ذهاب وإياب من الرياض إلى دبي',
                price: 1200,
                category: 'تذاكر طيران',
                sku: 'FLIGHT-RUH-DXB',
                status: 'active',
                createdAt: new Date().toISOString()
            },
            'product2': {
                id: 'product2',
                name: 'حجز فندق - دبي 5 نجوم',
                description: 'حجز فندق 5 نجوم في دبي لمدة 3 ليالي',
                price: 800,
                category: 'حجوزات فنادق',
                sku: 'HOTEL-DXB-5STAR',
                status: 'active',
                createdAt: new Date().toISOString()
            }
        };

        // إنشاء فاتورة تجريبية
        this.data.invoices = {
            'invoice1': {
                id: 'invoice1',
                number: 'INV-001',
                date: new Date().toISOString().split('T')[0],
                dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                customerId: 'customer1',
                reference: 'REF-001',
                items: [
                    {
                        productId: 'product1',
                        name: 'تذكرة طيران - الرياض إلى دبي',
                        quantity: 2,
                        price: 1200,
                        total: 2400
                    }
                ],
                subtotal: 2400,
                tax: 360,
                total: 2760,
                status: 'draft',
                notes: 'فاتورة تجريبية',
                createdAt: new Date().toISOString()
            }
        };

        // إنشاء عروض أسعار تجريبية
        this.data.quotes = {
            'quote1': {
                id: 'quote1',
                number: 'QUO-001',
                date: new Date().toISOString().split('T')[0],
                validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                customerId: 'customer1',
                title: 'عرض سعر رحلة عائلية',
                items: [
                    {
                        productId: 'product1',
                        name: 'تذكرة طيران - الرياض إلى دبي',
                        quantity: 4,
                        price: 1200,
                        total: 4800
                    }
                ],
                subtotal: 4800,
                tax: 720,
                total: 5520,
                status: 'pending',
                notes: 'عرض خاص للعائلات - صالح لمدة 30 يوم',
                createdAt: new Date().toISOString()
            }
        };

        // إنشاء مدفوعات تجريبية
        this.data.payments = {};

        // إنشاء مخزون تجريبي
        this.data.inventory = {
            'product1': 25,
            'product2': 15
        };

        // إنشاء سجل تعديلات المخزون
        this.data.stockAdjustments = {};

        // تحديث الإعدادات لتشمل عروض الأسعار
        if (!this.data.settings.nextQuoteNumber) {
            this.data.settings.nextQuoteNumber = 2;
        }

        this.saveSalesData();
    },

    /**
     * تحديث شريط التنقل
     */
    updateNavigation: function() {
        const navItems = document.querySelectorAll('.nav-link[data-view]');
        navItems.forEach(item => {
            const view = item.getAttribute('data-view');
            if (view === this.data.currentView) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
    },

    /**
     * التبديل بين العروض
     */
    switchView: function(view) {
        this.data.currentView = view;
        this.render();
    },

    /**
     * تحديث البيانات
     */
    refreshData: function() {
        this.render();
    },

    /**
     * وظيفة بديلة لإنشاء فاتورة جديدة (للتوافق مع الاستدعاءات القديمة)
     */
    showNewInvoiceModal: function() {
        this.showCreateInvoiceModal();
    },

    /**
     * تشخيص حالة المكون
     */
    diagnose: function() {
        console.log('🔍 تشخيص مكون المبيعات:');
        console.log('- البيانات محملة:', !!this.data);
        console.log('- عدد العملاء:', Object.keys(this.data.customers || {}).length);
        console.log('- عدد المنتجات:', Object.keys(this.data.products || {}).length);
        console.log('- عدد الفواتير:', Object.keys(this.data.invoices || {}).length);
        console.log('- الإعدادات:', !!this.data.settings);
        console.log('- العرض الحالي:', this.data.currentView);

        // اختبار وظيفة إنشاء الفاتورة
        try {
            console.log('- اختبار وظيفة إنشاء الفاتورة...');
            if (typeof this.showCreateInvoiceModal === 'function') {
                console.log('✅ وظيفة showCreateInvoiceModal متاحة');
            } else {
                console.log('❌ وظيفة showCreateInvoiceModal غير متاحة');
            }
        } catch (error) {
            console.log('❌ خطأ في اختبار وظيفة إنشاء الفاتورة:', error);
        }

        return {
            dataLoaded: !!this.data,
            customersCount: Object.keys(this.data.customers || {}).length,
            productsCount: Object.keys(this.data.products || {}).length,
            invoicesCount: Object.keys(this.data.invoices || {}).length,
            hasSettings: !!this.data.settings,
            currentView: this.data.currentView
        };
    },

    /**
     * تحديث البيانات
     */
    refreshData: function() {
        this.render();
    },

    /**
     * وظائف مساعدة أساسية
     */
    formatAmount: function(amount) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: this.data.settings.currency || 'SAR'
        }).format(amount || 0);
    },

    getStatusLabel: function(status) {
        const labels = {
            draft: 'مسودة',
            sent: 'مرسلة',
            paid: 'مدفوعة',
            overdue: 'متأخرة',
            cancelled: 'ملغية',
            pending: 'معلق',
            accepted: 'مقبول',
            rejected: 'مرفوض',
            expired: 'منتهي الصلاحية',
            active: 'نشط',
            inactive: 'غير نشط'
        };
        return labels[status] || status;
    },

    getStatusColor: function(status) {
        const colors = {
            draft: 'secondary',
            sent: 'primary',
            paid: 'success',
            overdue: 'danger',
            cancelled: 'dark',
            pending: 'warning',
            accepted: 'success',
            rejected: 'danger',
            expired: 'secondary',
            active: 'success',
            inactive: 'secondary'
        };
        return colors[status] || 'secondary';
    },

    /**
     * عرض لوحة التحكم
     */
    renderDashboard: function() {
        const totalInvoices = Object.keys(this.data.invoices || {}).length;
        const totalCustomers = Object.keys(this.data.customers || {}).length;
        const totalProducts = Object.keys(this.data.products || {}).length;

        const totalSales = Object.values(this.data.invoices || {})
            .filter(inv => inv.status === 'paid')
            .reduce((sum, inv) => sum + inv.total, 0);

        return `
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم</h2>
                    <button class="btn btn-primary" onclick="window.SalesComponent.showCreateInvoiceModal()">
                        <i class="fas fa-plus me-1"></i>فاتورة جديدة
                    </button>
                </div>

                <!-- الإحصائيات الرئيسية -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${totalInvoices}</h4>
                                        <p class="mb-0">إجمالي الفواتير</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-file-invoice fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${this.formatAmount(totalSales)}</h4>
                                        <p class="mb-0">إجمالي المبيعات</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-dollar-sign fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${totalCustomers}</h4>
                                        <p class="mb-0">العملاء</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${totalProducts}</h4>
                                        <p class="mb-0">المنتجات</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-box fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الإجراءات السريعة -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>الإجراءات السريعة</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-2">
                                        <button class="btn btn-outline-primary w-100 mb-2" onclick="window.SalesComponent.showCreateInvoiceModal()">
                                            <i class="fas fa-file-invoice d-block mb-1"></i>فاتورة جديدة
                                        </button>
                                    </div>
                                    <div class="col-md-2">
                                        <button class="btn btn-outline-success w-100 mb-2" onclick="window.SalesComponent.showCreateCustomerModal()">
                                            <i class="fas fa-user-plus d-block mb-1"></i>عميل جديد
                                        </button>
                                    </div>
                                    <div class="col-md-2">
                                        <button class="btn btn-outline-info w-100 mb-2" onclick="window.SalesComponent.showCreateProductModal()">
                                            <i class="fas fa-plus-circle d-block mb-1"></i>منتج جديد
                                        </button>
                                    </div>
                                    <div class="col-md-2">
                                        <button class="btn btn-outline-warning w-100 mb-2" onclick="window.SalesComponent.showReportsModal()">
                                            <i class="fas fa-chart-line d-block mb-1"></i>التقارير
                                        </button>
                                    </div>
                                    <div class="col-md-2">
                                        <button class="btn btn-outline-secondary w-100 mb-2" onclick="window.SalesComponent.switchView('invoices')">
                                            <i class="fas fa-list d-block mb-1"></i>عرض الفواتير
                                        </button>
                                    </div>
                                    <div class="col-md-2">
                                        <button class="btn btn-outline-dark w-100 mb-2" onclick="window.SalesComponent.switchView('settings')">
                                            <i class="fas fa-cog d-block mb-1"></i>الإعدادات
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الفواتير الأخيرة -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-file-invoice me-2"></i>الفواتير الأخيرة</h5>
                            </div>
                            <div class="card-body">
                                ${this.renderRecentInvoices()}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>توزيع المبيعات</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="salesChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض الفواتير الأخيرة
     */
    renderRecentInvoices: function() {
        const recentInvoices = Object.values(this.data.invoices || {})
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, 5);

        if (recentInvoices.length === 0) {
            return '<p class="text-muted text-center">لا توجد فواتير</p>';
        }

        return `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>المبلغ</th>
                            <th>الحالة</th>
                            <th>التاريخ</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${recentInvoices.map(invoice => {
                            const customer = this.data.customers[invoice.customerId];
                            return `
                                <tr>
                                    <td><strong>${invoice.number}</strong></td>
                                    <td>${customer?.name || 'غير محدد'}</td>
                                    <td>${this.formatAmount(invoice.total)}</td>
                                    <td><span class="badge bg-${this.getStatusColor(invoice.status)}">${this.getStatusLabel(invoice.status)}</span></td>
                                    <td>${new Date(invoice.date).toLocaleDateString('ar-SA')}</td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
        `;
    },

    /**
     * تحديث الرسوم البيانية
     */
    updateCharts: function() {
        // رسم بياني بسيط للمبيعات
        const canvas = document.getElementById('salesChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');

        // بيانات المبيعات حسب الحالة
        const invoicesByStatus = Object.values(this.data.invoices || {}).reduce((acc, invoice) => {
            acc[invoice.status] = (acc[invoice.status] || 0) + 1;
            return acc;
        }, {});

        // ألوان للرسم البياني
        const colors = ['#007bff', '#28a745', '#ffc107', '#dc3545', '#6c757d'];
        const labels = Object.keys(invoicesByStatus);
        const data = Object.values(invoicesByStatus);

        // رسم دائري بسيط
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const radius = Math.min(centerX, centerY) - 20;

        ctx.clearRect(0, 0, canvas.width, canvas.height);

        let currentAngle = 0;
        const total = data.reduce((sum, value) => sum + value, 0);

        data.forEach((value, index) => {
            const sliceAngle = (value / total) * 2 * Math.PI;

            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
            ctx.closePath();
            ctx.fillStyle = colors[index % colors.length];
            ctx.fill();

            currentAngle += sliceAngle;
        });

        // إضافة وسيلة إيضاح بسيطة
        let legendY = 10;
        labels.forEach((label, index) => {
            ctx.fillStyle = colors[index % colors.length];
            ctx.fillRect(10, legendY, 15, 15);
            ctx.fillStyle = '#000';
            ctx.font = '12px Arial';
            ctx.fillText(`${this.getStatusLabel(label)}: ${data[index]}`, 30, legendY + 12);
            legendY += 20;
        });
    },

    /**
     * عرض نافذة إنشاء فاتورة جديدة
     */
    showCreateInvoiceModal: function() {
        // التأكد من تحميل البيانات
        if (!this.data || Object.keys(this.data).length === 0) {
            this.loadSalesData();
            this.createSampleData();
        }

        const customers = Object.values(this.data.customers || {});
        const products = Object.values(this.data.products || {});

        if (customers.length === 0) {
            alert('يجب إضافة عميل واحد على الأقل قبل إنشاء فاتورة. سيتم إنشاء بيانات تجريبية.');
            this.createSampleData();
            // إعادة تحميل البيانات بعد إنشاء البيانات التجريبية
            const customersAfter = Object.values(this.data.customers || {});
            const productsAfter = Object.values(this.data.products || {});
            if (customersAfter.length === 0) {
                alert('فشل في تحميل البيانات. يرجى إعادة تحميل الصفحة.');
                return;
            }
        }

        if (products.length === 0) {
            alert('يجب إضافة منتج واحد على الأقل قبل إنشاء فاتورة');
            return;
        }

        const modalHTML = `
            <div class="modal fade" id="createInvoiceModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-file-invoice me-2"></i>إنشاء فاتورة جديدة
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="createInvoiceForm">
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label class="form-label">رقم الفاتورة</label>
                                        <input type="text" class="form-control" name="number"
                                               value="${this.data.settings.invoicePrefix}${this.data.settings.nextInvoiceNumber}" readonly>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">تاريخ الفاتورة</label>
                                        <input type="date" class="form-control" name="date"
                                               value="${new Date().toISOString().split('T')[0]}" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">تاريخ الاستحقاق</label>
                                        <input type="date" class="form-control" name="dueDate"
                                               value="${new Date(Date.now() + this.data.settings.defaultDueDays * 24 * 60 * 60 * 1000).toISOString().split('T')[0]}" required>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">العميل *</label>
                                        <select class="form-control" name="customerId" required>
                                            <option value="">اختر العميل</option>
                                            ${customers.map(customer => `
                                                <option value="${customer.id}">${customer.name}</option>
                                            `).join('')}
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">رقم المرجع</label>
                                        <input type="text" class="form-control" name="reference" placeholder="رقم المرجع (اختياري)">
                                    </div>
                                </div>

                                <!-- عناصر الفاتورة -->
                                <div class="card mb-3">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">عناصر الفاتورة</h6>
                                        <button type="button" class="btn btn-sm btn-primary" onclick="window.SalesComponent.addInvoiceItem()">
                                            <i class="fas fa-plus me-1"></i>إضافة عنصر
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row mb-2">
                                            <div class="col-md-4"><strong>المنتج</strong></div>
                                            <div class="col-md-2"><strong>الكمية</strong></div>
                                            <div class="col-md-2"><strong>السعر</strong></div>
                                            <div class="col-md-2"><strong>الإجمالي</strong></div>
                                            <div class="col-md-2"><strong>إجراءات</strong></div>
                                        </div>
                                        <div id="invoiceItems">
                                            <!-- سيتم إضافة العناصر هنا -->
                                        </div>
                                    </div>
                                </div>

                                <!-- الإجماليات -->
                                <div class="row">
                                    <div class="col-md-8">
                                        <label class="form-label">ملاحظات</label>
                                        <textarea class="form-control" name="notes" rows="3" placeholder="ملاحظات إضافية"></textarea>
                                    </div>
                                    <div class="col-md-4">
                                        <table class="table table-sm">
                                            <tr>
                                                <td><strong>المجموع الفرعي:</strong></td>
                                                <td class="text-end" id="invoiceSubtotal">0.00 ر.س</td>
                                            </tr>
                                            <tr>
                                                <td><strong>الضريبة (${(this.data.settings.taxRate * 100).toFixed(0)}%):</strong></td>
                                                <td class="text-end" id="invoiceTax">0.00 ر.س</td>
                                            </tr>
                                            <tr class="table-primary">
                                                <td><strong>الإجمالي:</strong></td>
                                                <td class="text-end" id="invoiceTotal"><strong>0.00 ر.س</strong></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveInvoice()">
                                <i class="fas fa-save me-1"></i>حفظ الفاتورة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('createInvoiceModal'));
        modal.show();

        // إضافة عنصر افتراضي
        setTimeout(() => {
            this.addInvoiceItem();
        }, 100);

        document.getElementById('createInvoiceModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * إضافة عنصر جديد للفاتورة
     */
    addInvoiceItem: function() {
        const itemsContainer = document.getElementById('invoiceItems');
        if (!itemsContainer) {
            console.error('عنصر invoiceItems غير موجود');
            return;
        }

        const itemCount = itemsContainer.children.length;
        const products = Object.values(this.data.products || {});
        const itemId = 'item_' + Date.now() + '_' + itemCount;

        const itemHTML = `
            <div class="invoice-item row mb-2" data-item-id="${itemId}">
                <div class="col-md-4">
                    <select class="form-control item-product" name="items[${itemCount}][productId]"
                            onchange="window.SalesComponent.updateItemFromProduct(this)">
                        <option value="">اختر المنتج</option>
                        ${products.map(product => `
                            <option value="${product.id}" data-price="${product.price}" data-name="${product.name}">
                                ${product.name} - ${this.formatAmount(product.price)}
                            </option>
                        `).join('')}
                    </select>
                </div>
                <div class="col-md-2">
                    <input type="number" class="form-control item-quantity" name="items[${itemCount}][quantity]"
                           placeholder="الكمية" value="1" min="1"
                           onchange="window.SalesComponent.calculateItemTotal(this)">
                </div>
                <div class="col-md-2">
                    <input type="number" class="form-control item-price" name="items[${itemCount}][price]"
                           placeholder="السعر" step="0.01" min="0"
                           onchange="window.SalesComponent.calculateItemTotal(this)">
                </div>
                <div class="col-md-2">
                    <input type="number" class="form-control item-total" name="items[${itemCount}][total]"
                           placeholder="الإجمالي" readonly>
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-outline-danger btn-sm"
                            onclick="window.SalesComponent.removeInvoiceItem(this.closest('.invoice-item'))"
                            title="حذف العنصر">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;

        itemsContainer.insertAdjacentHTML('beforeend', itemHTML);

        // التركيز على حقل المنتج الجديد
        const newItem = itemsContainer.lastElementChild;
        const productSelect = newItem.querySelector('.item-product');
        if (productSelect) {
            productSelect.focus();
        }

        this.showNotification('تم إضافة عنصر جديد', 'success');
    },

    /**
     * إزالة عنصر من الفاتورة
     */
    removeInvoiceItem: function(itemIndex) {
        const itemsContainer = document.getElementById('invoiceItems');
        if (!itemsContainer) return;

        const items = Array.from(itemsContainer.children);

        if (items.length > 1) {
            // البحث عن العنصر بالفهرس أو بالعنصر نفسه
            let itemToRemove = null;

            if (typeof itemIndex === 'number') {
                itemToRemove = items[itemIndex];
            } else {
                // إذا تم تمرير العنصر نفسه
                itemToRemove = itemIndex;
            }

            if (itemToRemove && itemToRemove.parentNode === itemsContainer) {
                itemToRemove.remove();
                this.calculateInvoiceTotal();
                this.showNotification('تم حذف العنصر', 'success');
            }
        } else {
            this.showNotification('يجب أن تحتوي الفاتورة على عنصر واحد على الأقل', 'warning');
        }
    },

    /**
     * تحديث عنصر من المنتج المختار
     */
    updateItemFromProduct: function(selectElement) {
        const productId = selectElement.value;
        if (!productId) return;

        const product = this.data.products?.[productId];
        if (!product) return;

        const item = selectElement.closest('.invoice-item');
        if (!item) return;

        const priceInput = item.querySelector('.item-price');
        if (priceInput) {
            priceInput.value = product.price;
            this.calculateItemTotal(priceInput);
        }
    },

    /**
     * حساب إجمالي العنصر
     */
    calculateItemTotal: function(inputElement) {
        const item = inputElement.closest('.invoice-item');
        if (!item) return;

        const quantityInput = item.querySelector('.item-quantity');
        const priceInput = item.querySelector('.item-price');
        const totalInput = item.querySelector('.item-total');

        if (!quantityInput || !priceInput || !totalInput) return;

        const quantity = parseFloat(quantityInput.value) || 0;
        const price = parseFloat(priceInput.value) || 0;
        const total = quantity * price;

        totalInput.value = total.toFixed(2);

        // إضافة تأثير بصري للتحديث
        totalInput.classList.add('bg-success', 'text-white');
        setTimeout(() => {
            totalInput.classList.remove('bg-success', 'text-white');
        }, 500);

        this.calculateInvoiceTotal();
    },

    /**
     * حساب إجمالي الفاتورة
     */
    calculateInvoiceTotal: function() {
        const itemsContainer = document.getElementById('invoiceItems');
        if (!itemsContainer) return;

        let subtotal = 0;
        let itemCount = 0;

        Array.from(itemsContainer.children).forEach(item => {
            const totalInput = item.querySelector('.item-total');
            if (totalInput && totalInput.value) {
                const total = parseFloat(totalInput.value) || 0;
                subtotal += total;
                itemCount++;
            }
        });

        const taxRate = this.data.settings?.taxRate || 0.15;
        const tax = subtotal * taxRate;
        const total = subtotal + tax;

        // تحديث عناصر العرض
        const subtotalElement = document.getElementById('invoiceSubtotal');
        const taxElement = document.getElementById('invoiceTax');
        const totalElement = document.getElementById('invoiceTotal');

        if (subtotalElement) {
            subtotalElement.textContent = this.formatAmount(subtotal);
            this.animateValue(subtotalElement);
        }

        if (taxElement) {
            taxElement.textContent = this.formatAmount(tax);
            this.animateValue(taxElement);
        }

        if (totalElement) {
            totalElement.textContent = this.formatAmount(total);
            this.animateValue(totalElement);
        }

        // تحديث عداد العناصر إذا كان موجوداً
        const itemCountElement = document.getElementById('invoiceItemCount');
        if (itemCountElement) {
            itemCountElement.textContent = itemCount;
        }

        return { subtotal, tax, total, itemCount };
    },

    /**
     * تأثير بصري لتحديث القيم
     */
    animateValue: function(element) {
        if (!element) return;

        element.classList.add('bg-primary', 'text-white');
        setTimeout(() => {
            element.classList.remove('bg-primary', 'text-white');
        }, 300);
    },

    /**
     * حفظ الفاتورة
     */
    saveInvoice: function() {
        const form = document.getElementById('createInvoiceForm');
        const formData = new FormData(form);

        // التحقق من البيانات المطلوبة
        if (!formData.get('customerId')) {
            alert('يرجى اختيار العميل');
            return;
        }

        // جمع عناصر الفاتورة
        const items = [];
        const itemsContainer = document.getElementById('invoiceItems');

        if (!itemsContainer) {
            alert('خطأ في تحميل عناصر الفاتورة');
            return;
        }

        Array.from(itemsContainer.children).forEach((item, index) => {
            const productSelect = item.querySelector('.item-product');
            const quantityInput = item.querySelector('.item-quantity');
            const priceInput = item.querySelector('.item-price');
            const totalInput = item.querySelector('.item-total');

            if (productSelect && quantityInput && priceInput && totalInput &&
                productSelect.value && quantityInput.value && priceInput.value) {
                const product = this.data.products?.[productSelect.value];
                items.push({
                    productId: productSelect.value,
                    name: product ? product.name : 'منتج غير محدد',
                    quantity: parseInt(quantityInput.value),
                    price: parseFloat(priceInput.value),
                    total: parseFloat(totalInput.value)
                });
            }
        });

        if (items.length === 0) {
            alert('يرجى إضافة عنصر واحد على الأقل للفاتورة');
            return;
        }

        // حساب الإجماليات
        const subtotal = items.reduce((sum, item) => sum + item.total, 0);
        const taxRate = this.data.settings?.taxRate || 0.15;
        const tax = subtotal * taxRate;
        const total = subtotal + tax;

        // إنشاء الفاتورة
        const invoiceId = 'invoice_' + Date.now();
        const invoice = {
            id: invoiceId,
            number: formData.get('number'),
            date: formData.get('date'),
            dueDate: formData.get('dueDate'),
            customerId: formData.get('customerId'),
            reference: formData.get('reference'),
            items: items,
            subtotal: subtotal,
            tax: tax,
            total: total,
            status: 'draft',
            notes: formData.get('notes'),
            createdAt: new Date().toISOString()
        };

        // حفظ الفاتورة
        try {
            if (!this.data.invoices) this.data.invoices = {};
            this.data.invoices[invoiceId] = invoice;
            if (!this.data.settings.nextInvoiceNumber) this.data.settings.nextInvoiceNumber = 1;
            this.data.settings.nextInvoiceNumber++;
            this.saveSalesData();

            // إغلاق النافذة وتحديث العرض
            const modalElement = document.getElementById('createInvoiceModal');
            if (modalElement) {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                }
            }

            this.refreshData();
            alert('تم إنشاء الفاتورة بنجاح');
        } catch (error) {
            console.error('خطأ في حفظ الفاتورة:', error);
            alert('حدث خطأ أثناء حفظ الفاتورة. يرجى المحاولة مرة أخرى.');
        }
    },

    /**
     * عرض نافذة إنشاء عميل جديد
     */
    showCreateCustomerModal: function() {
        const modalHTML = `
            <div class="modal fade" id="createCustomerModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-user-plus me-2"></i>إضافة عميل جديد
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="createCustomerForm">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">اسم العميل *</label>
                                        <input type="text" class="form-control" name="name" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" name="email">
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" name="phone">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">الرقم الضريبي</label>
                                        <input type="text" class="form-control" name="taxNumber">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea class="form-control" name="address" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveCustomer()">
                                <i class="fas fa-save me-1"></i>حفظ العميل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('createCustomerModal'));
        modal.show();

        document.getElementById('createCustomerModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * حفظ العميل الجديد
     */
    saveCustomer: function() {
        const form = document.getElementById('createCustomerForm');
        const formData = new FormData(form);

        if (!formData.get('name')) {
            alert('يرجى إدخال اسم العميل');
            return;
        }

        const customerId = 'customer_' + Date.now();
        const customer = {
            id: customerId,
            name: formData.get('name'),
            email: formData.get('email'),
            phone: formData.get('phone'),
            address: formData.get('address'),
            taxNumber: formData.get('taxNumber'),
            status: 'active',
            createdAt: new Date().toISOString()
        };

        if (!this.data.customers) this.data.customers = {};
        this.data.customers[customerId] = customer;
        this.saveSalesData();

        const modalElement = document.getElementById('createCustomerModal');
        if (modalElement) {
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.hide();
            }
        }

        alert('تم إضافة العميل بنجاح');
    },

    /**
     * عرض نافذة إنشاء منتج جديد
     */
    showCreateProductModal: function() {
        const modalHTML = `
            <div class="modal fade" id="createProductModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-plus-circle me-2"></i>إضافة منتج جديد
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="createProductForm">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">اسم المنتج *</label>
                                        <input type="text" class="form-control" name="name" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">السعر *</label>
                                        <input type="number" class="form-control" name="price" step="0.01" required>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">الفئة</label>
                                        <input type="text" class="form-control" name="category">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">رمز المنتج</label>
                                        <input type="text" class="form-control" name="sku">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الوصف</label>
                                    <textarea class="form-control" name="description" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveProduct()">
                                <i class="fas fa-save me-1"></i>حفظ المنتج
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('createProductModal'));
        modal.show();

        document.getElementById('createProductModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * حفظ المنتج الجديد
     */
    saveProduct: function() {
        const form = document.getElementById('createProductForm');
        const formData = new FormData(form);

        if (!formData.get('name') || !formData.get('price')) {
            alert('يرجى إدخال اسم المنتج والسعر');
            return;
        }

        const productId = 'product_' + Date.now();
        const product = {
            id: productId,
            name: formData.get('name'),
            description: formData.get('description'),
            price: parseFloat(formData.get('price')),
            category: formData.get('category'),
            sku: formData.get('sku'),
            status: 'active',
            createdAt: new Date().toISOString()
        };

        if (!this.data.products) this.data.products = {};
        this.data.products[productId] = product;
        this.saveSalesData();

        const modalElement = document.getElementById('createProductModal');
        if (modalElement) {
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.hide();
            }
        }

        alert('تم إضافة المنتج بنجاح');
    },

    /**
     * عرض نافذة التقارير
     */
    showReportsModal: function() {
        alert('ميزة التقارير متاحة في النسخة الكاملة');
    },

    /**
     * عرض صفحة الفواتير
     */
    renderInvoicesView: function() {
        return `
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-file-invoice me-2"></i>إدارة الفواتير</h2>
                    <button class="btn btn-primary" onclick="window.SalesComponent.showCreateInvoiceModal()">
                        <i class="fas fa-plus me-1"></i>فاتورة جديدة
                    </button>
                </div>

                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>التاريخ</th>
                                        <th>العميل</th>
                                        <th>المبلغ</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${this.renderInvoicesRows()}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض صفوف الفواتير
     */
    renderInvoicesRows: function() {
        const invoices = Object.values(this.data.invoices || {});

        if (invoices.length === 0) {
            return '<tr><td colspan="6" class="text-center text-muted">لا توجد فواتير</td></tr>';
        }

        return invoices.map(invoice => {
            const customer = this.data.customers[invoice.customerId];
            return `
                <tr>
                    <td><strong>${invoice.number}</strong></td>
                    <td>${new Date(invoice.date).toLocaleDateString('ar-SA')}</td>
                    <td>${customer?.name || 'غير محدد'}</td>
                    <td>${this.formatAmount(invoice.total)}</td>
                    <td><span class="badge bg-${this.getStatusColor(invoice.status)}">${this.getStatusLabel(invoice.status)}</span></td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="window.SalesComponent.viewInvoice('${invoice.id}')">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-success" onclick="window.SalesComponent.printInvoice('${invoice.id}')">
                                <i class="fas fa-print"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
    },

    /**
     * عرض تفاصيل الفاتورة
     */
    viewInvoice: function(invoiceId) {
        alert('عرض تفاصيل الفاتورة: ' + invoiceId);
    },

    /**
     * طباعة الفاتورة
     */
    printInvoice: function(invoiceId) {
        alert('طباعة الفاتورة: ' + invoiceId);
    },

    /**
     * عرض صفحة العملاء
     */
    renderCustomersView: function() {
        const customers = Object.values(this.data.customers || {});

        return `
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-users me-2"></i>إدارة العملاء</h2>
                    <button class="btn btn-primary" onclick="window.SalesComponent.showCreateCustomerModal()">
                        <i class="fas fa-plus me-1"></i>عميل جديد
                    </button>
                </div>

                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الهاتف</th>
                                        <th>المدينة</th>
                                        <th>إجمالي المشتريات</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${customers.map(customer => `
                                        <tr>
                                            <td><strong>${customer.name}</strong></td>
                                            <td>${customer.email || 'غير محدد'}</td>
                                            <td>${customer.phone || 'غير محدد'}</td>
                                            <td>${customer.city || 'غير محدد'}</td>
                                            <td>${this.formatAmount(customer.totalPurchases || 0)}</td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="window.SalesComponent.viewCustomer('${customer.id}')">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-success" onclick="window.SalesComponent.editCustomer('${customer.id}')">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>

                        ${customers.length === 0 ? `
                            <div class="text-center py-4">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد عملاء مسجلين</p>
                                <button class="btn btn-primary" onclick="window.SalesComponent.showCreateCustomerModal()">
                                    <i class="fas fa-plus me-1"></i>إضافة عميل جديد
                                </button>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض صفحة المنتجات
     */
    renderProductsView: function() {
        const products = Object.values(this.data.products || {});

        return `
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-box me-2"></i>إدارة المنتجات والخدمات</h2>
                    <button class="btn btn-primary" onclick="window.SalesComponent.showCreateProductModal()">
                        <i class="fas fa-plus me-1"></i>منتج جديد
                    </button>
                </div>

                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>اسم المنتج</th>
                                        <th>الفئة</th>
                                        <th>السعر</th>
                                        <th>الكمية المتاحة</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${products.map(product => `
                                        <tr>
                                            <td><strong>${product.name}</strong></td>
                                            <td>${product.category || 'غير محدد'}</td>
                                            <td>${this.formatAmount(product.price)}</td>
                                            <td>${product.quantity || 0}</td>
                                            <td>
                                                <span class="badge bg-${product.quantity > 0 ? 'success' : 'warning'}">
                                                    ${product.quantity > 0 ? 'متاح' : 'نفد المخزون'}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="window.SalesComponent.viewProduct('${product.id}')">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-success" onclick="window.SalesComponent.editProduct('${product.id}')">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>

                        ${products.length === 0 ? `
                            <div class="text-center py-4">
                                <i class="fas fa-box fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد منتجات مسجلة</p>
                                <button class="btn btn-primary" onclick="window.SalesComponent.showCreateProductModal()">
                                    <i class="fas fa-plus me-1"></i>إضافة منتج جديد
                                </button>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض صفحة التقارير المحسنة
     */
    renderReportsView: function() {
        const invoices = Object.values(this.data.invoices || {});
        const customers = Object.values(this.data.customers || {});
        const products = Object.values(this.data.products || {});

        // حساب الإحصائيات المتقدمة
        const totalSales = invoices.filter(inv => inv.status === 'paid').reduce((sum, inv) => sum + inv.total, 0);
        const pendingSales = invoices.filter(inv => inv.status === 'pending').reduce((sum, inv) => sum + inv.total, 0);
        const draftSales = invoices.filter(inv => inv.status === 'draft').reduce((sum, inv) => sum + inv.total, 0);

        // إحصائيات شهرية
        const now = new Date();
        const thisMonthSales = invoices.filter(inv => {
            const invDate = new Date(inv.date);
            return invDate.getMonth() === now.getMonth() && invDate.getFullYear() === now.getFullYear();
        }).reduce((sum, inv) => sum + inv.total, 0);

        const lastMonthSales = invoices.filter(inv => {
            const invDate = new Date(inv.date);
            const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
            return invDate.getMonth() === lastMonth.getMonth() && invDate.getFullYear() === lastMonth.getFullYear();
        }).reduce((sum, inv) => sum + inv.total, 0);

        // حساب النمو
        const growthRate = lastMonthSales > 0 ? ((thisMonthSales - lastMonthSales) / lastMonthSales * 100) : 0;

        // أفضل العملاء (مرتبين حسب المشتريات)
        const topCustomers = customers
            .sort((a, b) => (b.totalPurchases || 0) - (a.totalPurchases || 0))
            .slice(0, 5);

        // أكثر المنتجات مبيعاً
        const topProducts = products
            .sort((a, b) => (b.soldQuantity || 0) - (a.soldQuantity || 0))
            .slice(0, 5);

        // إحصائيات المخزون
        const lowStockProducts = products.filter(p => p.quantity <= p.minStock && p.status === 'active');
        const outOfStockProducts = products.filter(p => p.quantity === 0 && p.status === 'active');

        return `
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-chart-bar me-2"></i>تقارير المبيعات المتقدمة</h2>
                    <div class="btn-group">
                        <button class="btn btn-outline-primary" onclick="window.SalesComponent.exportReport('pdf')">
                            <i class="fas fa-file-pdf me-1"></i>تصدير PDF
                        </button>
                        <button class="btn btn-outline-success" onclick="window.SalesComponent.exportReport('excel')">
                            <i class="fas fa-file-excel me-1"></i>تصدير Excel
                        </button>
                        <button class="btn btn-outline-info" onclick="window.SalesComponent.refreshReports()">
                            <i class="fas fa-sync me-1"></i>تحديث
                        </button>
                    </div>
                </div>

                <!-- الإحصائيات الرئيسية المحسنة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${this.formatAmount(totalSales)}</h4>
                                        <p class="mb-0">إجمالي المبيعات المحصلة</p>
                                    </div>
                                    <i class="fas fa-money-bill-wave fa-2x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${this.formatAmount(pendingSales)}</h4>
                                        <p class="mb-0">المبيعات المعلقة</p>
                                    </div>
                                    <i class="fas fa-clock fa-2x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${this.formatAmount(thisMonthSales)}</h4>
                                        <p class="mb-0">مبيعات هذا الشهر</p>
                                        <small class="text-light">
                                            ${growthRate >= 0 ? '↗️' : '↘️'} ${Math.abs(growthRate).toFixed(1)}%
                                        </small>
                                    </div>
                                    <i class="fas fa-calendar-month fa-2x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${invoices.length}</h4>
                                        <p class="mb-0">إجمالي الفواتير</p>
                                        <small class="text-light">${draftSales > 0 ? this.formatAmount(draftSales) + ' مسودات' : 'لا توجد مسودات'}</small>
                                    </div>
                                    <i class="fas fa-file-invoice fa-2x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تحذيرات المخزون -->
                ${(lowStockProducts.length > 0 || outOfStockProducts.length > 0) ? `
                    <div class="alert alert-warning mb-4">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>تنبيهات المخزون</h6>
                        ${outOfStockProducts.length > 0 ? `<p class="mb-1"><strong>نفد المخزون:</strong> ${outOfStockProducts.length} منتج</p>` : ''}
                        ${lowStockProducts.length > 0 ? `<p class="mb-0"><strong>مخزون منخفض:</strong> ${lowStockProducts.length} منتج</p>` : ''}
                    </div>
                ` : ''}

                <!-- تقارير تفصيلية -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-users me-2"></i>أفضل العملاء</h5>
                            </div>
                            <div class="card-body">
                                ${customers.slice(0, 5).map(customer => `
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>${customer.name}</span>
                                        <span class="badge bg-primary">${this.formatAmount(customer.totalPurchases || 0)}</span>
                                    </div>
                                `).join('')}
                                ${customers.length === 0 ? '<p class="text-muted">لا توجد بيانات عملاء</p>' : ''}
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-box me-2"></i>أكثر المنتجات مبيعاً</h5>
                            </div>
                            <div class="card-body">
                                ${products.slice(0, 5).map(product => `
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>${product.name}</span>
                                        <span class="badge bg-success">${product.soldQuantity || 0} وحدة</span>
                                    </div>
                                `).join('')}
                                ${products.length === 0 ? '<p class="text-muted">لا توجد بيانات منتجات</p>' : ''}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسوم البيانية -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-line me-2"></i>اتجاه المبيعات</h5>
                            </div>
                            <div class="card-body">
                                <div id="salesChart" style="height: 300px; display: flex; align-items: center; justify-content: center; background: #f8f9fa; border-radius: 8px;">
                                    <div class="text-center">
                                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">رسم بياني لاتجاه المبيعات</p>
                                        <small class="text-muted">يتطلب مكتبة Chart.js للعرض الكامل</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-pie me-2"></i>توزيع الفواتير</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>مدفوعة</span>
                                        <span class="badge bg-success">${invoices.filter(i => i.status === 'paid').length}</span>
                                    </div>
                                    <div class="progress mb-2">
                                        <div class="progress-bar bg-success" style="width: ${invoices.length > 0 ? (invoices.filter(i => i.status === 'paid').length / invoices.length * 100) : 0}%"></div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>معلقة</span>
                                        <span class="badge bg-warning">${invoices.filter(i => i.status === 'pending').length}</span>
                                    </div>
                                    <div class="progress mb-2">
                                        <div class="progress-bar bg-warning" style="width: ${invoices.length > 0 ? (invoices.filter(i => i.status === 'pending').length / invoices.length * 100) : 0}%"></div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>مسودات</span>
                                        <span class="badge bg-secondary">${invoices.filter(i => i.status === 'draft').length}</span>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar bg-secondary" style="width: ${invoices.length > 0 ? (invoices.filter(i => i.status === 'draft').length / invoices.length * 100) : 0}%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول الفواتير الأخيرة -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-file-invoice me-2"></i>آخر الفواتير</h5>
                    </div>
                    <div class="card-body">
                        ${invoices.length > 0 ? `
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>رقم الفاتورة</th>
                                            <th>التاريخ</th>
                                            <th>العميل</th>
                                            <th>المبلغ</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${invoices.slice(0, 10).map(invoice => {
                                            const customer = customers.find(c => c.id === invoice.customerId);
                                            return `
                                                <tr>
                                                    <td>${invoice.number}</td>
                                                    <td>${new Date(invoice.date).toLocaleDateString('ar-SA')}</td>
                                                    <td>${customer?.name || 'غير محدد'}</td>
                                                    <td>${this.formatAmount(invoice.total)}</td>
                                                    <td><span class="badge bg-${this.getStatusColor(invoice.status)}">${this.getStatusLabel(invoice.status)}</span></td>
                                                </tr>
                                            `;
                                        }).join('')}
                                    </tbody>
                                </table>
                            </div>
                        ` : `
                            <div class="text-center py-4">
                                <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد فواتير</p>
                            </div>
                        `}
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * العروض الأخرى (مبسطة)
     */
    renderQuotesView: function() { return '<div class="alert alert-info">عرض عروض الأسعار متاح في النسخة الكاملة</div>'; },
    renderInventoryView: function() { return '<div class="alert alert-info">عرض المخزون متاح في النسخة الكاملة</div>'; },
    renderPaymentsView: function() {
        const invoices = Object.values(this.data.invoices || {});
        const payments = Object.values(this.data.payments || {});

        // حساب الإحصائيات
        const totalReceived = payments.reduce((sum, payment) => sum + payment.amount, 0);
        const totalPending = invoices.filter(inv => inv.status === 'pending').reduce((sum, inv) => sum + inv.total, 0);
        const totalOverdue = invoices.filter(inv => {
            if (inv.status !== 'pending') return false;
            const dueDate = new Date(inv.dueDate);
            return dueDate < new Date();
        }).reduce((sum, inv) => sum + inv.total, 0);

        // آخر المدفوعات
        const recentPayments = payments
            .sort((a, b) => new Date(b.date) - new Date(a.date))
            .slice(0, 10);

        // الفواتير المستحقة
        const overdueInvoices = invoices.filter(inv => {
            if (inv.status !== 'pending') return false;
            const dueDate = new Date(inv.dueDate);
            return dueDate < new Date();
        }).sort((a, b) => new Date(a.dueDate) - new Date(b.dueDate));

        return `
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-credit-card me-2"></i>إدارة المدفوعات</h2>
                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="window.SalesComponent.showAddPaymentModal()">
                            <i class="fas fa-plus me-1"></i>تسجيل دفعة
                        </button>
                        <button class="btn btn-outline-info" onclick="window.SalesComponent.refreshPayments()">
                            <i class="fas fa-sync me-1"></i>تحديث
                        </button>
                    </div>
                </div>

                <!-- إحصائيات المدفوعات -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${this.formatAmount(totalReceived)}</h4>
                                        <p class="mb-0">إجمالي المحصل</p>
                                    </div>
                                    <i class="fas fa-money-bill-wave fa-2x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${this.formatAmount(totalPending)}</h4>
                                        <p class="mb-0">المبالغ المعلقة</p>
                                    </div>
                                    <i class="fas fa-clock fa-2x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${this.formatAmount(totalOverdue)}</h4>
                                        <p class="mb-0">المتأخرات</p>
                                    </div>
                                    <i class="fas fa-exclamation-triangle fa-2x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>${payments.length}</h4>
                                        <p class="mb-0">عدد المدفوعات</p>
                                    </div>
                                    <i class="fas fa-list fa-2x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تحذيرات المتأخرات -->
                ${overdueInvoices.length > 0 ? `
                    <div class="alert alert-danger mb-4">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>فواتير متأخرة السداد</h6>
                        <p class="mb-0">يوجد ${overdueInvoices.length} فاتورة متأخرة بإجمالي ${this.formatAmount(totalOverdue)}</p>
                    </div>
                ` : ''}

                <div class="row">
                    <!-- آخر المدفوعات -->
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5><i class="fas fa-history me-2"></i>آخر المدفوعات</h5>
                                <span class="badge bg-primary">${recentPayments.length}</span>
                            </div>
                            <div class="card-body">
                                ${recentPayments.length > 0 ? `
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>التاريخ</th>
                                                    <th>رقم الفاتورة</th>
                                                    <th>العميل</th>
                                                    <th>المبلغ</th>
                                                    <th>طريقة الدفع</th>
                                                    <th>الحالة</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${recentPayments.map(payment => {
                                                    const invoice = this.data.invoices[payment.invoiceId];
                                                    const customer = invoice ? this.data.customers[invoice.customerId] : null;
                                                    return `
                                                        <tr>
                                                            <td>${new Date(payment.date).toLocaleDateString('ar-SA')}</td>
                                                            <td>${invoice ? invoice.number : 'غير محدد'}</td>
                                                            <td>${customer ? customer.name : 'غير محدد'}</td>
                                                            <td>${this.formatAmount(payment.amount)}</td>
                                                            <td>
                                                                <span class="badge bg-secondary">${this.getPaymentMethodLabel(payment.method)}</span>
                                                            </td>
                                                            <td>
                                                                <span class="badge bg-${payment.status === 'confirmed' ? 'success' : 'warning'}">
                                                                    ${payment.status === 'confirmed' ? 'مؤكد' : 'معلق'}
                                                                </span>
                                                            </td>
                                                        </tr>
                                                    `;
                                                }).join('')}
                                            </tbody>
                                        </table>
                                    </div>
                                ` : `
                                    <div class="text-center py-4">
                                        <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد مدفوعات مسجلة</p>
                                        <button class="btn btn-primary" onclick="window.SalesComponent.showAddPaymentModal()">
                                            <i class="fas fa-plus me-1"></i>تسجيل أول دفعة
                                        </button>
                                    </div>
                                `}
                            </div>
                        </div>
                    </div>

                    <!-- الفواتير المستحقة -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5><i class="fas fa-exclamation-circle me-2"></i>فواتير مستحقة</h5>
                                <span class="badge bg-warning">${overdueInvoices.length}</span>
                            </div>
                            <div class="card-body">
                                ${overdueInvoices.length > 0 ? overdueInvoices.slice(0, 5).map(invoice => {
                                    const customer = this.data.customers[invoice.customerId];
                                    const daysPastDue = Math.floor((new Date() - new Date(invoice.dueDate)) / (1000 * 60 * 60 * 24));
                                    return `
                                        <div class="d-flex justify-content-between align-items-center mb-3 p-2 border rounded">
                                            <div>
                                                <strong>${invoice.number}</strong>
                                                <br><small class="text-muted">${customer ? customer.name : 'غير محدد'}</small>
                                                <br><small class="text-danger">متأخر ${daysPastDue} يوم</small>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge bg-danger">${this.formatAmount(invoice.total)}</span>
                                                <br><button class="btn btn-sm btn-outline-primary mt-1" onclick="window.SalesComponent.showAddPaymentModal('${invoice.id}')">
                                                    دفع
                                                </button>
                                            </div>
                                        </div>
                                    `;
                                }).join('') : `
                                    <div class="text-center py-3">
                                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                        <p class="text-muted mb-0">لا توجد فواتير متأخرة</p>
                                    </div>
                                `}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },
    renderSettingsView: function() { return '<div class="alert alert-info">عرض الإعدادات متاح في النسخة الكاملة</div>'; },
    updateReports: function() { /* وظيفة فارغة للتوافق */ },

    /**
     * وظائف مساعدة للعملاء والمنتجات
     */
    showCreateCustomerModal: function() {
        const modalHTML = `
            <div class="modal fade" id="createCustomerModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-user-plus me-2"></i>إضافة عميل جديد
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="createCustomerForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">اسم العميل *</label>
                                            <input type="text" class="form-control" name="name" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">نوع العميل</label>
                                            <select class="form-control" name="type">
                                                <option value="individual">فرد</option>
                                                <option value="company">شركة</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">البريد الإلكتروني</label>
                                            <input type="email" class="form-control" name="email">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رقم الهاتف *</label>
                                            <input type="tel" class="form-control" name="phone" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">المدينة</label>
                                            <input type="text" class="form-control" name="city">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الرقم الضريبي</label>
                                            <input type="text" class="form-control" name="taxNumber">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea class="form-control" name="address" rows="3"></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">حد الائتمان</label>
                                            <input type="number" class="form-control" name="creditLimit" step="0.01" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">مدة السداد (أيام)</label>
                                            <input type="number" class="form-control" name="paymentTerms" value="30" min="0">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="2"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveCustomer()">
                                <i class="fas fa-save me-1"></i>حفظ العميل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('createCustomerModal'));
        modal.show();

        document.getElementById('createCustomerModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * حفظ العميل الجديد
     */
    saveCustomer: function() {
        const form = document.getElementById('createCustomerForm');
        const formData = new FormData(form);

        // التحقق من البيانات المطلوبة
        if (!formData.get('name') || !formData.get('phone')) {
            this.showNotification('يرجى ملء الحقول المطلوبة (الاسم والهاتف)', 'warning');
            return;
        }

        // إنشاء العميل
        const customerId = 'customer_' + Date.now();
        const customer = {
            id: customerId,
            name: formData.get('name'),
            type: formData.get('type') || 'individual',
            email: formData.get('email'),
            phone: formData.get('phone'),
            city: formData.get('city'),
            taxNumber: formData.get('taxNumber'),
            address: formData.get('address'),
            creditLimit: parseFloat(formData.get('creditLimit')) || 0,
            paymentTerms: parseInt(formData.get('paymentTerms')) || 30,
            notes: formData.get('notes'),
            totalPurchases: 0,
            outstandingBalance: 0,
            createdAt: new Date().toISOString(),
            lastPurchase: null,
            status: 'active'
        };

        try {
            // حفظ العميل
            if (!this.data.customers) this.data.customers = {};
            this.data.customers[customerId] = customer;
            this.saveSalesData();

            // إغلاق النافذة وتحديث العرض
            const modalElement = document.getElementById('createCustomerModal');
            if (modalElement) {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                }
            }

            this.showNotification('تم إضافة العميل بنجاح', 'success');

            // تحديث العرض إذا كنا في صفحة العملاء
            if (this.data.currentView === 'customers') {
                this.render({ view: 'customers' });
            }

        } catch (error) {
            console.error('خطأ في حفظ العميل:', error);
            this.showNotification('حدث خطأ أثناء حفظ العميل', 'error');
        }
    },

    showCreateProductModal: function() {
        const modalHTML = `
            <div class="modal fade" id="createProductModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-box me-2"></i>إضافة منتج جديد
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="createProductForm">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label class="form-label">اسم المنتج *</label>
                                            <input type="text" class="form-control" name="name" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">كود المنتج</label>
                                            <input type="text" class="form-control" name="code" placeholder="اختياري">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الفئة</label>
                                            <select class="form-control" name="category">
                                                <option value="">اختر الفئة</option>
                                                <option value="travel">خدمات سفر</option>
                                                <option value="accommodation">إقامة</option>
                                                <option value="transportation">نقل</option>
                                                <option value="tours">جولات سياحية</option>
                                                <option value="visa">تأشيرات</option>
                                                <option value="insurance">تأمين</option>
                                                <option value="other">أخرى</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">نوع المنتج</label>
                                            <select class="form-control" name="type">
                                                <option value="service">خدمة</option>
                                                <option value="product">منتج</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">وصف المنتج</label>
                                    <textarea class="form-control" name="description" rows="3"></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">السعر *</label>
                                            <input type="number" class="form-control" name="price" step="0.01" min="0" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">تكلفة الشراء</label>
                                            <input type="number" class="form-control" name="cost" step="0.01" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">الكمية المتاحة</label>
                                            <input type="number" class="form-control" name="quantity" min="0" value="0">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الحد الأدنى للمخزون</label>
                                            <input type="number" class="form-control" name="minStock" min="0" value="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">وحدة القياس</label>
                                            <select class="form-control" name="unit">
                                                <option value="piece">قطعة</option>
                                                <option value="service">خدمة</option>
                                                <option value="night">ليلة</option>
                                                <option value="day">يوم</option>
                                                <option value="person">شخص</option>
                                                <option value="kg">كيلوجرام</option>
                                                <option value="liter">لتر</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">المورد</label>
                                            <input type="text" class="form-control" name="supplier">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الحالة</label>
                                            <select class="form-control" name="status">
                                                <option value="active">نشط</option>
                                                <option value="inactive">غير نشط</option>
                                                <option value="discontinued">متوقف</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="2"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveProduct()">
                                <i class="fas fa-save me-1"></i>حفظ المنتج
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('createProductModal'));
        modal.show();

        document.getElementById('createProductModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * حفظ المنتج الجديد
     */
    saveProduct: function() {
        const form = document.getElementById('createProductForm');
        const formData = new FormData(form);

        // التحقق من البيانات المطلوبة
        if (!formData.get('name') || !formData.get('price')) {
            this.showNotification('يرجى ملء الحقول المطلوبة (الاسم والسعر)', 'warning');
            return;
        }

        // إنشاء المنتج
        const productId = 'product_' + Date.now();
        const product = {
            id: productId,
            name: formData.get('name'),
            code: formData.get('code') || productId,
            category: formData.get('category'),
            type: formData.get('type') || 'service',
            description: formData.get('description'),
            price: parseFloat(formData.get('price')),
            cost: parseFloat(formData.get('cost')) || 0,
            quantity: parseInt(formData.get('quantity')) || 0,
            minStock: parseInt(formData.get('minStock')) || 0,
            unit: formData.get('unit') || 'piece',
            supplier: formData.get('supplier'),
            status: formData.get('status') || 'active',
            notes: formData.get('notes'),
            soldQuantity: 0,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        try {
            // حفظ المنتج
            if (!this.data.products) this.data.products = {};
            this.data.products[productId] = product;
            this.saveSalesData();

            // إغلاق النافذة وتحديث العرض
            const modalElement = document.getElementById('createProductModal');
            if (modalElement) {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                }
            }

            this.showNotification('تم إضافة المنتج بنجاح', 'success');

            // تحديث العرض إذا كنا في صفحة المنتجات
            if (this.data.currentView === 'products') {
                this.render({ view: 'products' });
            }

        } catch (error) {
            console.error('خطأ في حفظ المنتج:', error);
            this.showNotification('حدث خطأ أثناء حفظ المنتج', 'error');
        }
    },

    viewCustomer: function(customerId) {
        const customer = this.data.customers[customerId];
        if (!customer) {
            this.showNotification('العميل غير موجود', 'error');
            return;
        }

        const modalHTML = `
            <div class="modal fade" id="viewCustomerModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-user me-2"></i>تفاصيل العميل: ${customer.name}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-info-circle me-2"></i>المعلومات الأساسية</h6>
                                        </div>
                                        <div class="card-body">
                                            <p><strong>الاسم:</strong> ${customer.name}</p>
                                            <p><strong>النوع:</strong> ${customer.type === 'individual' ? 'فرد' : 'شركة'}</p>
                                            <p><strong>البريد الإلكتروني:</strong> ${customer.email || 'غير محدد'}</p>
                                            <p><strong>الهاتف:</strong> ${customer.phone}</p>
                                            <p><strong>المدينة:</strong> ${customer.city || 'غير محدد'}</p>
                                            <p><strong>الرقم الضريبي:</strong> ${customer.taxNumber || 'غير محدد'}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-chart-line me-2"></i>الإحصائيات</h6>
                                        </div>
                                        <div class="card-body">
                                            <p><strong>إجمالي المشتريات:</strong> ${this.formatAmount(customer.totalPurchases || 0)}</p>
                                            <p><strong>الرصيد المستحق:</strong> ${this.formatAmount(customer.outstandingBalance || 0)}</p>
                                            <p><strong>حد الائتمان:</strong> ${this.formatAmount(customer.creditLimit || 0)}</p>
                                            <p><strong>مدة السداد:</strong> ${customer.paymentTerms || 30} يوم</p>
                                            <p><strong>تاريخ الإنشاء:</strong> ${new Date(customer.createdAt).toLocaleDateString('ar-SA')}</p>
                                            <p><strong>الحالة:</strong>
                                                <span class="badge bg-${customer.status === 'active' ? 'success' : 'warning'}">
                                                    ${customer.status === 'active' ? 'نشط' : 'غير نشط'}
                                                </span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            ${customer.address ? `
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h6><i class="fas fa-map-marker-alt me-2"></i>العنوان</h6>
                                    </div>
                                    <div class="card-body">
                                        <p>${customer.address}</p>
                                    </div>
                                </div>
                            ` : ''}

                            ${customer.notes ? `
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h6><i class="fas fa-sticky-note me-2"></i>ملاحظات</h6>
                                    </div>
                                    <div class="card-body">
                                        <p>${customer.notes}</p>
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.editCustomer('${customerId}')">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('viewCustomerModal'));
        modal.show();

        document.getElementById('viewCustomerModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    editCustomer: function(customerId) {
        const customer = this.data.customers[customerId];
        if (!customer) {
            this.showNotification('العميل غير موجود', 'error');
            return;
        }

        // إغلاق نافذة العرض إذا كانت مفتوحة
        const viewModal = document.getElementById('viewCustomerModal');
        if (viewModal) {
            const modal = bootstrap.Modal.getInstance(viewModal);
            if (modal) modal.hide();
        }

        const modalHTML = `
            <div class="modal fade" id="editCustomerModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-user-edit me-2"></i>تعديل العميل: ${customer.name}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="editCustomerForm">
                                <input type="hidden" name="customerId" value="${customerId}">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">اسم العميل *</label>
                                            <input type="text" class="form-control" name="name" value="${customer.name}" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">نوع العميل</label>
                                            <select class="form-control" name="type">
                                                <option value="individual" ${customer.type === 'individual' ? 'selected' : ''}>فرد</option>
                                                <option value="company" ${customer.type === 'company' ? 'selected' : ''}>شركة</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">البريد الإلكتروني</label>
                                            <input type="email" class="form-control" name="email" value="${customer.email || ''}">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رقم الهاتف *</label>
                                            <input type="tel" class="form-control" name="phone" value="${customer.phone}" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">المدينة</label>
                                            <input type="text" class="form-control" name="city" value="${customer.city || ''}">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الرقم الضريبي</label>
                                            <input type="text" class="form-control" name="taxNumber" value="${customer.taxNumber || ''}">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea class="form-control" name="address" rows="3">${customer.address || ''}</textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">حد الائتمان</label>
                                            <input type="number" class="form-control" name="creditLimit" value="${customer.creditLimit || 0}" step="0.01" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">مدة السداد (أيام)</label>
                                            <input type="number" class="form-control" name="paymentTerms" value="${customer.paymentTerms || 30}" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">الحالة</label>
                                            <select class="form-control" name="status">
                                                <option value="active" ${customer.status === 'active' ? 'selected' : ''}>نشط</option>
                                                <option value="inactive" ${customer.status === 'inactive' ? 'selected' : ''}>غير نشط</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="2">${customer.notes || ''}</textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.updateCustomer()">
                                <i class="fas fa-save me-1"></i>حفظ التغييرات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('editCustomerModal'));
        modal.show();

        document.getElementById('editCustomerModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * تحديث بيانات العميل
     */
    updateCustomer: function() {
        const form = document.getElementById('editCustomerForm');
        const formData = new FormData(form);
        const customerId = formData.get('customerId');

        if (!customerId || !this.data.customers[customerId]) {
            this.showNotification('العميل غير موجود', 'error');
            return;
        }

        // التحقق من البيانات المطلوبة
        if (!formData.get('name') || !formData.get('phone')) {
            this.showNotification('يرجى ملء الحقول المطلوبة (الاسم والهاتف)', 'warning');
            return;
        }

        try {
            // تحديث بيانات العميل
            const customer = this.data.customers[customerId];
            customer.name = formData.get('name');
            customer.type = formData.get('type');
            customer.email = formData.get('email');
            customer.phone = formData.get('phone');
            customer.city = formData.get('city');
            customer.taxNumber = formData.get('taxNumber');
            customer.address = formData.get('address');
            customer.creditLimit = parseFloat(formData.get('creditLimit')) || 0;
            customer.paymentTerms = parseInt(formData.get('paymentTerms')) || 30;
            customer.status = formData.get('status');
            customer.notes = formData.get('notes');
            customer.updatedAt = new Date().toISOString();

            this.saveSalesData();

            // إغلاق النافذة
            const modalElement = document.getElementById('editCustomerModal');
            if (modalElement) {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                }
            }

            this.showNotification('تم تحديث بيانات العميل بنجاح', 'success');

            // تحديث العرض إذا كنا في صفحة العملاء
            if (this.data.currentView === 'customers') {
                this.render({ view: 'customers' });
            }

        } catch (error) {
            console.error('خطأ في تحديث العميل:', error);
            this.showNotification('حدث خطأ أثناء تحديث العميل', 'error');
        }
    },

    viewProduct: function(productId) {
        const product = this.data.products[productId];
        if (!product) {
            this.showNotification('المنتج غير موجود', 'error');
            return;
        }

        const modalHTML = `
            <div class="modal fade" id="viewProductModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-box me-2"></i>تفاصيل المنتج: ${product.name}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-info-circle me-2"></i>المعلومات الأساسية</h6>
                                        </div>
                                        <div class="card-body">
                                            <p><strong>الاسم:</strong> ${product.name}</p>
                                            <p><strong>الكود:</strong> ${product.code}</p>
                                            <p><strong>الفئة:</strong> ${product.category || 'غير محدد'}</p>
                                            <p><strong>النوع:</strong> ${product.type === 'service' ? 'خدمة' : 'منتج'}</p>
                                            <p><strong>وحدة القياس:</strong> ${product.unit}</p>
                                            <p><strong>المورد:</strong> ${product.supplier || 'غير محدد'}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-dollar-sign me-2"></i>الأسعار والمخزون</h6>
                                        </div>
                                        <div class="card-body">
                                            <p><strong>السعر:</strong> ${this.formatAmount(product.price)}</p>
                                            <p><strong>التكلفة:</strong> ${this.formatAmount(product.cost || 0)}</p>
                                            <p><strong>الربح:</strong> ${this.formatAmount((product.price - (product.cost || 0)))}</p>
                                            <p><strong>الكمية المتاحة:</strong> ${product.quantity}</p>
                                            <p><strong>الحد الأدنى:</strong> ${product.minStock}</p>
                                            <p><strong>الكمية المباعة:</strong> ${product.soldQuantity || 0}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            ${product.description ? `
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h6><i class="fas fa-file-text me-2"></i>الوصف</h6>
                                    </div>
                                    <div class="card-body">
                                        <p>${product.description}</p>
                                    </div>
                                </div>
                            ` : ''}

                            <div class="card mt-3">
                                <div class="card-header">
                                    <h6><i class="fas fa-chart-line me-2"></i>الحالة والإحصائيات</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>الحالة:</strong>
                                                <span class="badge bg-${this.getProductStatusColor(product)}">
                                                    ${this.getProductStatusLabel(product)}
                                                </span>
                                            </p>
                                            <p><strong>تاريخ الإنشاء:</strong> ${new Date(product.createdAt).toLocaleDateString('ar-SA')}</p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>آخر تحديث:</strong> ${new Date(product.updatedAt).toLocaleDateString('ar-SA')}</p>
                                            <p><strong>إجمالي المبيعات:</strong> ${this.formatAmount((product.soldQuantity || 0) * product.price)}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            ${product.notes ? `
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h6><i class="fas fa-sticky-note me-2"></i>ملاحظات</h6>
                                    </div>
                                    <div class="card-body">
                                        <p>${product.notes}</p>
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.editProduct('${productId}')">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('viewProductModal'));
        modal.show();

        document.getElementById('viewProductModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    editProduct: function(productId) {
        const product = this.data.products[productId];
        if (!product) {
            this.showNotification('المنتج غير موجود', 'error');
            return;
        }

        // إغلاق نافذة العرض إذا كانت مفتوحة
        const viewModal = document.getElementById('viewProductModal');
        if (viewModal) {
            const modal = bootstrap.Modal.getInstance(viewModal);
            if (modal) modal.hide();
        }

        const modalHTML = `
            <div class="modal fade" id="editProductModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-box-edit me-2"></i>تعديل المنتج: ${product.name}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="editProductForm">
                                <input type="hidden" name="productId" value="${productId}">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label class="form-label">اسم المنتج *</label>
                                            <input type="text" class="form-control" name="name" value="${product.name}" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">كود المنتج</label>
                                            <input type="text" class="form-control" name="code" value="${product.code}">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الفئة</label>
                                            <select class="form-control" name="category">
                                                <option value="">اختر الفئة</option>
                                                <option value="travel" ${product.category === 'travel' ? 'selected' : ''}>خدمات سفر</option>
                                                <option value="accommodation" ${product.category === 'accommodation' ? 'selected' : ''}>إقامة</option>
                                                <option value="transportation" ${product.category === 'transportation' ? 'selected' : ''}>نقل</option>
                                                <option value="tours" ${product.category === 'tours' ? 'selected' : ''}>جولات سياحية</option>
                                                <option value="visa" ${product.category === 'visa' ? 'selected' : ''}>تأشيرات</option>
                                                <option value="insurance" ${product.category === 'insurance' ? 'selected' : ''}>تأمين</option>
                                                <option value="other" ${product.category === 'other' ? 'selected' : ''}>أخرى</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">نوع المنتج</label>
                                            <select class="form-control" name="type">
                                                <option value="service" ${product.type === 'service' ? 'selected' : ''}>خدمة</option>
                                                <option value="product" ${product.type === 'product' ? 'selected' : ''}>منتج</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">وصف المنتج</label>
                                    <textarea class="form-control" name="description" rows="3">${product.description || ''}</textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">السعر *</label>
                                            <input type="number" class="form-control" name="price" value="${product.price}" step="0.01" min="0" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">تكلفة الشراء</label>
                                            <input type="number" class="form-control" name="cost" value="${product.cost || 0}" step="0.01" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">الكمية المتاحة</label>
                                            <input type="number" class="form-control" name="quantity" value="${product.quantity}" min="0">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الحد الأدنى للمخزون</label>
                                            <input type="number" class="form-control" name="minStock" value="${product.minStock}" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">وحدة القياس</label>
                                            <select class="form-control" name="unit">
                                                <option value="piece" ${product.unit === 'piece' ? 'selected' : ''}>قطعة</option>
                                                <option value="service" ${product.unit === 'service' ? 'selected' : ''}>خدمة</option>
                                                <option value="night" ${product.unit === 'night' ? 'selected' : ''}>ليلة</option>
                                                <option value="day" ${product.unit === 'day' ? 'selected' : ''}>يوم</option>
                                                <option value="person" ${product.unit === 'person' ? 'selected' : ''}>شخص</option>
                                                <option value="kg" ${product.unit === 'kg' ? 'selected' : ''}>كيلوجرام</option>
                                                <option value="liter" ${product.unit === 'liter' ? 'selected' : ''}>لتر</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">المورد</label>
                                            <input type="text" class="form-control" name="supplier" value="${product.supplier || ''}">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الحالة</label>
                                            <select class="form-control" name="status">
                                                <option value="active" ${product.status === 'active' ? 'selected' : ''}>نشط</option>
                                                <option value="inactive" ${product.status === 'inactive' ? 'selected' : ''}>غير نشط</option>
                                                <option value="discontinued" ${product.status === 'discontinued' ? 'selected' : ''}>متوقف</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="2">${product.notes || ''}</textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.updateProduct()">
                                <i class="fas fa-save me-1"></i>حفظ التغييرات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('editProductModal'));
        modal.show();

        document.getElementById('editProductModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * تحديث بيانات المنتج
     */
    updateProduct: function() {
        const form = document.getElementById('editProductForm');
        const formData = new FormData(form);
        const productId = formData.get('productId');

        if (!productId || !this.data.products[productId]) {
            this.showNotification('المنتج غير موجود', 'error');
            return;
        }

        // التحقق من البيانات المطلوبة
        if (!formData.get('name') || !formData.get('price')) {
            this.showNotification('يرجى ملء الحقول المطلوبة (الاسم والسعر)', 'warning');
            return;
        }

        try {
            // تحديث بيانات المنتج
            const product = this.data.products[productId];
            product.name = formData.get('name');
            product.code = formData.get('code');
            product.category = formData.get('category');
            product.type = formData.get('type');
            product.description = formData.get('description');
            product.price = parseFloat(formData.get('price'));
            product.cost = parseFloat(formData.get('cost')) || 0;
            product.quantity = parseInt(formData.get('quantity')) || 0;
            product.minStock = parseInt(formData.get('minStock')) || 0;
            product.unit = formData.get('unit');
            product.supplier = formData.get('supplier');
            product.status = formData.get('status');
            product.notes = formData.get('notes');
            product.updatedAt = new Date().toISOString();

            this.saveSalesData();

            // إغلاق النافذة
            const modalElement = document.getElementById('editProductModal');
            if (modalElement) {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                }
            }

            this.showNotification('تم تحديث بيانات المنتج بنجاح', 'success');

            // تحديث العرض إذا كنا في صفحة المنتجات
            if (this.data.currentView === 'products') {
                this.render({ view: 'products' });
            }

        } catch (error) {
            console.error('خطأ في تحديث المنتج:', error);
            this.showNotification('حدث خطأ أثناء تحديث المنتج', 'error');
        }
    },

    /**
     * الحصول على لون حالة المنتج
     */
    getProductStatusColor: function(product) {
        if (product.status === 'inactive' || product.status === 'discontinued') {
            return 'secondary';
        }
        if (product.quantity <= product.minStock) {
            return 'warning';
        }
        if (product.quantity === 0) {
            return 'danger';
        }
        return 'success';
    },

    /**
     * الحصول على تسمية حالة المنتج
     */
    getProductStatusLabel: function(product) {
        if (product.status === 'inactive') return 'غير نشط';
        if (product.status === 'discontinued') return 'متوقف';
        if (product.quantity === 0) return 'نفد المخزون';
        if (product.quantity <= product.minStock) return 'مخزون منخفض';
        return 'متاح';
    },

    /**
     * تحديث التقارير
     */
    refreshReports: function() {
        this.showNotification('جاري تحديث التقارير...', 'info');

        // إعادة حساب البيانات
        this.loadSalesData();

        // إعادة عرض صفحة التقارير
        this.render({ view: 'reports' });

        this.showNotification('تم تحديث التقارير بنجاح', 'success');
    },

    /**
     * تصدير التقارير
     */
    exportReport: function(format) {
        try {
            const reportData = this.generateReportData();

            if (format === 'pdf') {
                this.exportToPDF(reportData);
            } else if (format === 'excel') {
                this.exportToExcel(reportData);
            }

            this.showNotification(`جاري تحضير التقرير بصيغة ${format.toUpperCase()}...`, 'info');
        } catch (error) {
            console.error('خطأ في تصدير التقرير:', error);
            this.showNotification('حدث خطأ أثناء تصدير التقرير', 'error');
        }
    },

    /**
     * إنشاء بيانات التقرير
     */
    generateReportData: function() {
        const invoices = Object.values(this.data.invoices || {});
        const customers = Object.values(this.data.customers || {});
        const products = Object.values(this.data.products || {});

        return {
            summary: {
                totalSales: invoices.filter(inv => inv.status === 'paid').reduce((sum, inv) => sum + inv.total, 0),
                pendingSales: invoices.filter(inv => inv.status === 'pending').reduce((sum, inv) => sum + inv.total, 0),
                totalInvoices: invoices.length,
                totalCustomers: customers.length,
                totalProducts: products.length
            },
            invoices: invoices,
            customers: customers,
            products: products,
            generatedAt: new Date().toISOString()
        };
    },

    /**
     * تصدير إلى PDF (مبسط)
     */
    exportToPDF: function(data) {
        // هذه وظيفة مبسطة - في التطبيق الحقيقي ستحتاج مكتبة PDF
        const content = `
تقرير المبيعات
================

إجمالي المبيعات: ${this.formatAmount(data.summary.totalSales)}
المبيعات المعلقة: ${this.formatAmount(data.summary.pendingSales)}
عدد الفواتير: ${data.summary.totalInvoices}
عدد العملاء: ${data.summary.totalCustomers}
عدد المنتجات: ${data.summary.totalProducts}

تاريخ الإنشاء: ${new Date(data.generatedAt).toLocaleString('ar-SA')}
        `;

        // إنشاء ملف نصي مؤقت
        const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `sales_report_${new Date().toISOString().split('T')[0]}.txt`;
        a.click();
        URL.revokeObjectURL(url);

        this.showNotification('تم تحميل التقرير النصي', 'success');
    },

    /**
     * تصدير إلى Excel (مبسط)
     */
    exportToExcel: function(data) {
        // هذه وظيفة مبسطة - في التطبيق الحقيقي ستحتاج مكتبة Excel
        let csvContent = "البيان,القيمة\n";
        csvContent += `إجمالي المبيعات,${data.summary.totalSales}\n`;
        csvContent += `المبيعات المعلقة,${data.summary.pendingSales}\n`;
        csvContent += `عدد الفواتير,${data.summary.totalInvoices}\n`;
        csvContent += `عدد العملاء,${data.summary.totalCustomers}\n`;
        csvContent += `عدد المنتجات,${data.summary.totalProducts}\n`;

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `sales_report_${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        URL.revokeObjectURL(url);

        this.showNotification('تم تحميل التقرير CSV', 'success');
    },

    /**
     * إظهار نافذة إضافة دفعة
     */
    showAddPaymentModal: function(invoiceId = null) {
        const invoices = Object.values(this.data.invoices || {}).filter(inv => inv.status === 'pending');

        const modalHTML = `
            <div class="modal fade" id="addPaymentModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-credit-card me-2"></i>تسجيل دفعة جديدة
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="addPaymentForm">
                                <div class="mb-3">
                                    <label class="form-label">الفاتورة *</label>
                                    <select class="form-control" name="invoiceId" required onchange="window.SalesComponent.updatePaymentAmount(this.value)">
                                        <option value="">اختر الفاتورة</option>
                                        ${invoices.map(invoice => {
                                            const customer = this.data.customers[invoice.customerId];
                                            return `
                                                <option value="${invoice.id}" ${invoiceId === invoice.id ? 'selected' : ''}
                                                        data-amount="${invoice.total}">
                                                    ${invoice.number} - ${customer ? customer.name : 'غير محدد'} - ${this.formatAmount(invoice.total)}
                                                </option>
                                            `;
                                        }).join('')}
                                    </select>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">المبلغ المدفوع *</label>
                                            <input type="number" class="form-control" name="amount" step="0.01" min="0" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">تاريخ الدفع *</label>
                                            <input type="date" class="form-control" name="date" value="${new Date().toISOString().split('T')[0]}" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">طريقة الدفع *</label>
                                            <select class="form-control" name="method" required>
                                                <option value="cash">نقداً</option>
                                                <option value="bank_transfer">تحويل بنكي</option>
                                                <option value="credit_card">بطاقة ائتمان</option>
                                                <option value="check">شيك</option>
                                                <option value="other">أخرى</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رقم المرجع</label>
                                            <input type="text" class="form-control" name="reference" placeholder="رقم الشيك أو التحويل">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="2"></textarea>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="confirmed" id="paymentConfirmed" checked>
                                        <label class="form-check-label" for="paymentConfirmed">
                                            تأكيد استلام الدفعة
                                        </label>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.savePayment()">
                                <i class="fas fa-save me-1"></i>حفظ الدفعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('addPaymentModal'));
        modal.show();

        // تحديث المبلغ إذا تم تحديد فاتورة
        if (invoiceId) {
            setTimeout(() => {
                this.updatePaymentAmount(invoiceId);
            }, 100);
        }

        document.getElementById('addPaymentModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * تحديث مبلغ الدفعة حسب الفاتورة المختارة
     */
    updatePaymentAmount: function(invoiceId) {
        if (!invoiceId) return;

        const invoice = this.data.invoices[invoiceId];
        if (!invoice) return;

        const amountInput = document.querySelector('input[name="amount"]');
        if (amountInput) {
            amountInput.value = invoice.total;
        }
    },

    /**
     * حفظ الدفعة
     */
    savePayment: function() {
        const form = document.getElementById('addPaymentForm');
        const formData = new FormData(form);

        // التحقق من البيانات المطلوبة
        if (!formData.get('invoiceId') || !formData.get('amount') || !formData.get('date') || !formData.get('method')) {
            this.showNotification('يرجى ملء جميع الحقول المطلوبة', 'warning');
            return;
        }

        const amount = parseFloat(formData.get('amount'));
        if (amount <= 0) {
            this.showNotification('يجب أن يكون المبلغ أكبر من صفر', 'warning');
            return;
        }

        try {
            // إنشاء الدفعة
            const paymentId = 'payment_' + Date.now();
            const payment = {
                id: paymentId,
                invoiceId: formData.get('invoiceId'),
                amount: amount,
                date: formData.get('date'),
                method: formData.get('method'),
                reference: formData.get('reference'),
                notes: formData.get('notes'),
                status: formData.get('confirmed') ? 'confirmed' : 'pending',
                createdAt: new Date().toISOString()
            };

            // حفظ الدفعة
            if (!this.data.payments) this.data.payments = {};
            this.data.payments[paymentId] = payment;

            // تحديث حالة الفاتورة
            const invoice = this.data.invoices[payment.invoiceId];
            if (invoice && payment.amount >= invoice.total) {
                invoice.status = 'paid';
                invoice.paidAt = payment.date;
            }

            this.saveSalesData();

            // إغلاق النافذة
            const modalElement = document.getElementById('addPaymentModal');
            if (modalElement) {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                }
            }

            this.showNotification('تم تسجيل الدفعة بنجاح', 'success');

            // تحديث العرض
            if (this.data.currentView === 'payments') {
                this.render({ view: 'payments' });
            }

        } catch (error) {
            console.error('خطأ في حفظ الدفعة:', error);
            this.showNotification('حدث خطأ أثناء حفظ الدفعة', 'error');
        }
    },

    /**
     * تحديث المدفوعات
     */
    refreshPayments: function() {
        this.showNotification('جاري تحديث المدفوعات...', 'info');
        this.render({ view: 'payments' });
        this.showNotification('تم تحديث المدفوعات بنجاح', 'success');
    },

    /**
     * الحصول على تسمية طريقة الدفع
     */
    getPaymentMethodLabel: function(method) {
        const methods = {
            cash: 'نقداً',
            bank_transfer: 'تحويل بنكي',
            credit_card: 'بطاقة ائتمان',
            check: 'شيك',
            other: 'أخرى'
        };
        return methods[method] || method;
    },

    /**
     * إنشاء نسخة احتياطية من البيانات
     */
    createBackup: function() {
        try {
            const backupData = {
                version: '1.0',
                timestamp: new Date().toISOString(),
                data: {
                    customers: this.data.customers,
                    products: this.data.products,
                    invoices: this.data.invoices,
                    payments: this.data.payments,
                    settings: this.data.settings
                },
                metadata: {
                    totalCustomers: Object.keys(this.data.customers || {}).length,
                    totalProducts: Object.keys(this.data.products || {}).length,
                    totalInvoices: Object.keys(this.data.invoices || {}).length,
                    totalPayments: Object.keys(this.data.payments || {}).length,
                    createdBy: 'نظام المبيعات',
                    systemInfo: {
                        userAgent: navigator.userAgent,
                        url: window.location.href
                    }
                }
            };

            const backupJson = JSON.stringify(backupData, null, 2);
            const blob = new Blob([backupJson], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `sales_backup_${new Date().toISOString().split('T')[0]}_${Date.now()}.json`;
            a.click();

            URL.revokeObjectURL(url);

            this.showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');

            // حفظ معلومات النسخة الاحتياطية
            this.saveBackupInfo(backupData.metadata);

            return true;
        } catch (error) {
            console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
            this.showNotification('فشل في إنشاء النسخة الاحتياطية', 'error');
            return false;
        }
    },

    /**
     * استعادة البيانات من نسخة احتياطية
     */
    restoreBackup: function(file) {
        return new Promise((resolve, reject) => {
            if (!file) {
                reject(new Error('لم يتم تحديد ملف'));
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const backupData = JSON.parse(e.target.result);

                    // التحقق من صحة النسخة الاحتياطية
                    if (!this.validateBackup(backupData)) {
                        reject(new Error('ملف النسخة الاحتياطية غير صالح'));
                        return;
                    }

                    // إنشاء نسخة احتياطية من البيانات الحالية قبل الاستعادة
                    const currentBackup = {
                        ...this.data,
                        timestamp: new Date().toISOString(),
                        note: 'نسخة احتياطية تلقائية قبل الاستعادة'
                    };
                    localStorage.setItem('salesData_preRestore', JSON.stringify(currentBackup));

                    // استعادة البيانات
                    this.data.customers = backupData.data.customers || {};
                    this.data.products = backupData.data.products || {};
                    this.data.invoices = backupData.data.invoices || {};
                    this.data.payments = backupData.data.payments || {};
                    this.data.settings = { ...this.data.settings, ...backupData.data.settings };

                    // حفظ البيانات المستعادة
                    this.saveSalesData();

                    this.showNotification('تم استعادة البيانات بنجاح', 'success');

                    // تحديث العرض
                    this.render({ view: this.data.currentView });

                    resolve(backupData.metadata);
                } catch (error) {
                    reject(error);
                }
            };

            reader.onerror = () => {
                reject(new Error('فشل في قراءة الملف'));
            };

            reader.readAsText(file);
        });
    },

    /**
     * التحقق من صحة النسخة الاحتياطية
     */
    validateBackup: function(backupData) {
        if (!backupData || typeof backupData !== 'object') {
            return false;
        }

        // التحقق من وجود الحقول المطلوبة
        if (!backupData.version || !backupData.timestamp || !backupData.data) {
            return false;
        }

        // التحقق من صحة البيانات
        const data = backupData.data;
        if (typeof data !== 'object') {
            return false;
        }

        // التحقق من صحة كل قسم
        const sections = ['customers', 'products', 'invoices', 'payments'];
        for (const section of sections) {
            if (data[section] && typeof data[section] !== 'object') {
                return false;
            }
        }

        return true;
    },

    /**
     * حفظ معلومات النسخة الاحتياطية
     */
    saveBackupInfo: function(metadata) {
        try {
            const backupHistory = JSON.parse(localStorage.getItem('salesBackupHistory') || '[]');
            backupHistory.push({
                ...metadata,
                id: Date.now(),
                timestamp: new Date().toISOString()
            });

            // الاحتفاظ بآخر 10 نسخ احتياطية فقط
            if (backupHistory.length > 10) {
                backupHistory.splice(0, backupHistory.length - 10);
            }

            localStorage.setItem('salesBackupHistory', JSON.stringify(backupHistory));
        } catch (error) {
            console.error('خطأ في حفظ معلومات النسخة الاحتياطية:', error);
        }
    },

    /**
     * عرض نافذة إدارة النسخ الاحتياطية
     */
    showBackupModal: function() {
        const backupHistory = JSON.parse(localStorage.getItem('salesBackupHistory') || '[]');

        const modalHTML = `
            <div class="modal fade" id="backupModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-database me-2"></i>إدارة النسخ الاحتياطية
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-download me-2"></i>إنشاء نسخة احتياطية</h6>
                                        </div>
                                        <div class="card-body">
                                            <p class="text-muted">إنشاء نسخة احتياطية من جميع بيانات المبيعات</p>
                                            <button class="btn btn-primary w-100" onclick="window.SalesComponent.createBackup()">
                                                <i class="fas fa-download me-2"></i>إنشاء نسخة احتياطية
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-upload me-2"></i>استعادة من نسخة احتياطية</h6>
                                        </div>
                                        <div class="card-body">
                                            <p class="text-muted">استعادة البيانات من ملف نسخة احتياطية</p>
                                            <input type="file" class="form-control mb-2" id="backupFileInput" accept=".json">
                                            <button class="btn btn-warning w-100" onclick="window.SalesComponent.handleBackupRestore()">
                                                <i class="fas fa-upload me-2"></i>استعادة البيانات
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="card">
                                <div class="card-header">
                                    <h6><i class="fas fa-history me-2"></i>سجل النسخ الاحتياطية</h6>
                                </div>
                                <div class="card-body">
                                    ${backupHistory.length > 0 ? `
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>التاريخ</th>
                                                        <th>العملاء</th>
                                                        <th>المنتجات</th>
                                                        <th>الفواتير</th>
                                                        <th>المدفوعات</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    ${backupHistory.slice(-5).reverse().map(backup => `
                                                        <tr>
                                                            <td>${new Date(backup.timestamp).toLocaleString('ar-SA')}</td>
                                                            <td>${backup.totalCustomers || 0}</td>
                                                            <td>${backup.totalProducts || 0}</td>
                                                            <td>${backup.totalInvoices || 0}</td>
                                                            <td>${backup.totalPayments || 0}</td>
                                                        </tr>
                                                    `).join('')}
                                                </tbody>
                                            </table>
                                        </div>
                                    ` : `
                                        <div class="text-center py-3">
                                            <i class="fas fa-database fa-3x text-muted mb-3"></i>
                                            <p class="text-muted">لا توجد نسخ احتياطية</p>
                                        </div>
                                    `}
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('backupModal'));
        modal.show();

        document.getElementById('backupModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * معالجة استعادة النسخة الاحتياطية
     */
    handleBackupRestore: function() {
        const fileInput = document.getElementById('backupFileInput');
        const file = fileInput.files[0];

        if (!file) {
            this.showNotification('يرجى اختيار ملف النسخة الاحتياطية', 'warning');
            return;
        }

        // تأكيد الاستعادة
        if (!confirm('هل أنت متأكد من استعادة البيانات؟ سيتم استبدال جميع البيانات الحالية.')) {
            return;
        }

        this.restoreBackup(file)
            .then((metadata) => {
                // إغلاق النافذة
                const modalElement = document.getElementById('backupModal');
                if (modalElement) {
                    const modal = bootstrap.Modal.getInstance(modalElement);
                    if (modal) {
                        modal.hide();
                    }
                }
            })
            .catch((error) => {
                console.error('خطأ في استعادة النسخة الاحتياطية:', error);
                this.showNotification('فشل في استعادة النسخة الاحتياطية: ' + error.message, 'error');
            });
    },

    /**
     * اختبار النظام
     */
    test: function() {
        console.log('🧪 اختبار نظام المبيعات...');

        try {
            // اختبار تحميل البيانات
            this.loadSalesData();
            console.log('✅ تحميل البيانات');

            // اختبار إنشاء البيانات التجريبية
            this.createSampleData();
            console.log('✅ إنشاء البيانات التجريبية');

            // اختبار عرض لوحة التحكم
            const dashboard = this.renderDashboard();
            if (dashboard && dashboard.length > 0) {
                console.log('✅ عرض لوحة التحكم');
            } else {
                console.log('❌ فشل في عرض لوحة التحكم');
            }

            // اختبار الوظائف المساعدة
            const amount = this.formatAmount(1000);
            if (amount) {
                console.log('✅ تنسيق المبالغ:', amount);
            }

            // اختبار حفظ البيانات
            this.saveSalesData();
            console.log('✅ حفظ البيانات');

            console.log('🎉 جميع الاختبارات نجحت!');
            return true;

        } catch (error) {
            console.error('❌ فشل في الاختبار:', error);
            return false;
        }
    },

    /**
     * إعادة تعيين النظام
     */
    reset: function() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
            localStorage.removeItem('salesData');
            this.data = {
                currentView: 'dashboard',
                customers: {},
                products: {},
                invoices: {},
                quotes: {},
                payments: {},
                inventory: {},
                stockAdjustments: {},
                filters: {
                    dateFrom: '',
                    dateTo: '',
                    status: '',
                    customer: '',
                    quickSearch: ''
                },
                quoteFilters: {
                    dateFrom: '',
                    dateTo: '',
                    status: '',
                    customer: ''
                },
                settings: {
                    taxRate: 0.15,
                    currency: 'SAR',
                    invoicePrefix: 'INV-',
                    nextInvoiceNumber: 1,
                    nextQuoteNumber: 1,
                    companyName: 'قمة الوعد للسفريات',
                    companyAddress: 'المملكة العربية السعودية',
                    companyTaxNumber: '*********',
                    companyEmail: '<EMAIL>',
                    companyPhone: '+966501234567',
                    companyWebsite: 'https://qimat-alwaed.com',
                    language: 'ar',
                    timezone: 'Asia/Riyadh',
                    dateFormat: 'ar-SA',
                    autoSave: true,
                    showNotifications: true,
                    enableBackup: true,
                    autoCalculateTax: true,
                    defaultDueDays: 30
                }
            };
            this.createSampleData();
            this.render();
            alert('تم إعادة تعيين النظام بنجاح');
        }
    }
};

// تصدير المكون للاستخدام العام
window.SalesComponent = SalesComponent;
