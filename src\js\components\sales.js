/**
 * مكون نظام المبيعات
 * Sales Management Component
 */

const SalesComponent = {
    // بيانات النظام
    data: {
        currentView: 'dashboard',
        customers: {},
        products: {},
        invoices: {},
        salesTransactions: {},
        settings: {
            taxRate: 0.15, // ضريبة القيمة المضافة 15%
            currency: 'SAR',
            invoicePrefix: 'INV-',
            nextInvoiceNumber: 1
        }
    },

    // إعدادات العرض
    views: [
        { key: 'dashboard', label: 'لوحة التحكم', icon: 'fas fa-tachometer-alt' },
        { key: 'invoices', label: 'الفواتير', icon: 'fas fa-file-invoice' },
        { key: 'customers', label: 'العملاء', icon: 'fas fa-users' },
        { key: 'products', label: 'المنتجات والخدمات', icon: 'fas fa-box' },
        { key: 'reports', label: 'التقارير', icon: 'fas fa-chart-bar' },
        { key: 'settings', label: 'الإعدادات', icon: 'fas fa-cog' }
    ],

    /**
     * تهيئة مكون المبيعات
     */
    init: function() {
        this.loadSalesData();
        this.setupEventListeners();
        console.log('✅ تم تهيئة نظام المبيعات');
    },

    /**
     * تحميل بيانات المبيعات
     */
    loadSalesData: function() {
        try {
            // تحميل البيانات من localStorage
            const savedData = localStorage.getItem('salesData');
            if (savedData) {
                const data = JSON.parse(savedData);
                this.data = { ...this.data, ...data };
            } else {
                // إنشاء بيانات تجريبية
                this.createSampleData();
            }
        } catch (error) {
            console.error('خطأ في تحميل بيانات المبيعات:', error);
            this.createSampleData();
        }
    },

    /**
     * حفظ بيانات المبيعات
     */
    saveSalesData: function() {
        try {
            localStorage.setItem('salesData', JSON.stringify(this.data));
        } catch (error) {
            console.error('خطأ في حفظ بيانات المبيعات:', error);
        }
    },

    /**
     * إنشاء بيانات تجريبية
     */
    createSampleData: function() {
        // عملاء تجريبيون
        this.data.customers = {
            'CUST001': {
                id: 'CUST001',
                name: 'شركة الرياض للسفر',
                email: '<EMAIL>',
                phone: '+966501234567',
                address: 'الرياض، المملكة العربية السعودية',
                taxNumber: '*********',
                type: 'corporate',
                creditLimit: 50000,
                balance: 0,
                isActive: true,
                createdAt: new Date().toISOString()
            },
            'CUST002': {
                id: 'CUST002',
                name: 'أحمد محمد العلي',
                email: '<EMAIL>',
                phone: '+966509876543',
                address: 'جدة، المملكة العربية السعودية',
                taxNumber: '',
                type: 'individual',
                creditLimit: 10000,
                balance: 0,
                isActive: true,
                createdAt: new Date().toISOString()
            }
        };

        // منتجات وخدمات تجريبية
        this.data.products = {
            'PROD001': {
                id: 'PROD001',
                name: 'تذكرة طيران داخلي',
                nameEn: 'Domestic Flight Ticket',
                category: 'flights',
                type: 'service',
                price: 500,
                cost: 400,
                taxable: true,
                isActive: true,
                description: 'تذكرة طيران داخلي - درجة اقتصادية',
                createdAt: new Date().toISOString()
            },
            'PROD002': {
                id: 'PROD002',
                name: 'حجز فندقي - ليلة واحدة',
                nameEn: 'Hotel Booking - One Night',
                category: 'hotels',
                type: 'service',
                price: 300,
                cost: 250,
                taxable: true,
                isActive: true,
                description: 'حجز فندقي لليلة واحدة - غرفة مفردة',
                createdAt: new Date().toISOString()
            },
            'PROD003': {
                id: 'PROD003',
                name: 'تأشيرة سياحية',
                nameEn: 'Tourist Visa',
                category: 'visas',
                type: 'service',
                price: 200,
                cost: 150,
                taxable: false,
                isActive: true,
                description: 'خدمة استخراج تأشيرة سياحية',
                createdAt: new Date().toISOString()
            }
        };

        this.saveSalesData();
    },

    /**
     * تبديل العرض
     */
    switchView: function(view) {
        this.data.currentView = view;
        const content = document.getElementById('main-content');
        if (content) {
            content.innerHTML = this.renderCurrentView();
        }
    },

    /**
     * عرض المحتوى الحالي
     */
    renderCurrentView: function() {
        switch (this.data.currentView) {
            case 'dashboard':
                return this.renderDashboard();
            case 'invoices':
                return this.renderInvoices();
            case 'customers':
                return this.renderCustomers();
            case 'products':
                return this.renderProducts();
            case 'reports':
                return this.renderReports();
            case 'settings':
                return this.renderSettings();
            default:
                return this.renderDashboard();
        }
    },

    /**
     * عرض نافذة المبيعات الرئيسية
     */
    render: function() {
        return `
            <div class="sales-management">
                <!-- شريط التنقل -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h4 class="mb-0">
                                        <i class="fas fa-shopping-cart text-primary me-2"></i>
                                        نظام إدارة المبيعات
                                    </h4>
                                    <div class="btn-group">
                                        <button class="btn btn-primary" onclick="window.SalesComponent.showNewInvoiceModal()">
                                            <i class="fas fa-plus me-1"></i>فاتورة جديدة
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="window.SalesComponent.refreshData()">
                                            <i class="fas fa-sync me-1"></i>تحديث
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- تبويبات التنقل -->
                                <ul class="nav nav-pills nav-fill">
                                    ${this.views.map(item => `
                                        <li class="nav-item">
                                            <a class="nav-link ${this.data.currentView === item.key ? 'active' : ''}" 
                                               href="#" onclick="window.SalesComponent.switchView('${item.key}')">
                                                <i class="${item.icon} me-2"></i>${item.label}
                                            </a>
                                        </li>
                                    `).join('')}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- المحتوى الرئيسي -->
                <div class="row">
                    <div class="col-12">
                        ${this.renderCurrentView()}
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض لوحة التحكم
     */
    renderDashboard: function() {
        const stats = this.calculateDashboardStats();
        
        return `
            <div class="sales-dashboard">
                <!-- بطاقات الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">إجمالي المبيعات اليوم</h6>
                                        <h4>${this.formatAmount(stats.todaySales)}</h4>
                                    </div>
                                    <i class="fas fa-chart-line fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">عدد الفواتير</h6>
                                        <h4>${stats.totalInvoices}</h4>
                                    </div>
                                    <i class="fas fa-file-invoice fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">عدد العملاء</h6>
                                        <h4>${stats.totalCustomers}</h4>
                                    </div>
                                    <i class="fas fa-users fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">متوسط قيمة الفاتورة</h6>
                                        <h4>${this.formatAmount(stats.averageInvoice)}</h4>
                                    </div>
                                    <i class="fas fa-calculator fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسوم البيانية والتقارير السريعة -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-area me-2"></i>مبيعات آخر 7 أيام</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="salesChart" height="100"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-list me-2"></i>آخر الفواتير</h5>
                            </div>
                            <div class="card-body">
                                ${this.renderRecentInvoices()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * حساب إحصائيات لوحة التحكم
     */
    calculateDashboardStats: function() {
        const invoices = Object.values(this.data.invoices || {});
        const customers = Object.values(this.data.customers || {});
        
        const today = new Date().toISOString().split('T')[0];
        const todayInvoices = invoices.filter(inv => inv.date === today);
        
        return {
            todaySales: todayInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0),
            totalInvoices: invoices.length,
            totalCustomers: customers.length,
            averageInvoice: invoices.length > 0 ? 
                invoices.reduce((sum, inv) => sum + (inv.total || 0), 0) / invoices.length : 0
        };
    },

    /**
     * تنسيق المبالغ
     */
    formatAmount: function(amount) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: this.data.settings.currency
        }).format(amount || 0);
    },

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners: function() {
        // سيتم إضافة مستمعي الأحداث هنا
    },

    /**
     * عرض آخر الفواتير
     */
    renderRecentInvoices: function() {
        const invoices = Object.values(this.data.invoices || {})
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, 5);

        if (invoices.length === 0) {
            return `
                <div class="text-center text-muted">
                    <i class="fas fa-file-invoice fa-3x mb-3"></i>
                    <p>لا توجد فواتير حتى الآن</p>
                    <button class="btn btn-primary btn-sm" onclick="window.SalesComponent.showNewInvoiceModal()">
                        <i class="fas fa-plus me-1"></i>إنشاء فاتورة جديدة
                    </button>
                </div>
            `;
        }

        return `
            <div class="list-group list-group-flush">
                ${invoices.map(invoice => `
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">${invoice.number}</h6>
                            <small class="text-muted">${this.data.customers[invoice.customerId]?.name || 'عميل غير محدد'}</small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-${this.getInvoiceStatusColor(invoice.status)} mb-1">${this.getInvoiceStatusLabel(invoice.status)}</span>
                            <div class="fw-bold">${this.formatAmount(invoice.total)}</div>
                        </div>
                    </div>
                `).join('')}
            </div>
            <div class="text-center mt-3">
                <button class="btn btn-outline-primary btn-sm" onclick="window.SalesComponent.switchView('invoices')">
                    عرض جميع الفواتير
                </button>
            </div>
        `;
    },

    /**
     * الحصول على لون حالة الفاتورة
     */
    getInvoiceStatusColor: function(status) {
        const colors = {
            'draft': 'secondary',
            'sent': 'primary',
            'paid': 'success',
            'overdue': 'danger',
            'cancelled': 'dark'
        };
        return colors[status] || 'secondary';
    },

    /**
     * الحصول على تسمية حالة الفاتورة
     */
    getInvoiceStatusLabel: function(status) {
        const labels = {
            'draft': 'مسودة',
            'sent': 'مرسلة',
            'paid': 'مدفوعة',
            'overdue': 'متأخرة',
            'cancelled': 'ملغية'
        };
        return labels[status] || 'غير محدد';
    },

    /**
     * عرض نافذة فاتورة جديدة
     */
    showNewInvoiceModal: function() {
        const modalHTML = `
            <div class="modal fade" id="newInvoiceModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-file-invoice me-2"></i>فاتورة جديدة
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${this.renderInvoiceForm()}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-outline-primary" onclick="window.SalesComponent.saveInvoiceAsDraft()">
                                <i class="fas fa-save me-1"></i>حفظ كمسودة
                            </button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveAndSendInvoice()">
                                <i class="fas fa-paper-plane me-1"></i>حفظ وإرسال
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('newInvoiceModal'));
        modal.show();

        document.getElementById('newInvoiceModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });

        // تهيئة النموذج
        this.initializeInvoiceForm();
    },

    /**
     * عرض نموذج الفاتورة
     */
    renderInvoiceForm: function() {
        const customers = Object.values(this.data.customers || {});
        const products = Object.values(this.data.products || {});

        return `
            <form id="invoiceForm">
                <div class="row">
                    <!-- معلومات الفاتورة -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-info-circle me-2"></i>معلومات الفاتورة</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">رقم الفاتورة</label>
                                    <input type="text" class="form-control" name="invoiceNumber"
                                           value="${this.data.settings.invoicePrefix}${this.data.settings.nextInvoiceNumber.toString().padStart(4, '0')}" readonly>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">تاريخ الفاتورة *</label>
                                    <input type="date" class="form-control" name="invoiceDate"
                                           value="${new Date().toISOString().split('T')[0]}" required>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">تاريخ الاستحقاق</label>
                                    <input type="date" class="form-control" name="dueDate">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">مرجع الطلب</label>
                                    <input type="text" class="form-control" name="reference" placeholder="رقم الطلب أو المرجع">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات العميل -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-user me-2"></i>معلومات العميل</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">العميل *</label>
                                    <select class="form-control" name="customerId" required onchange="window.SalesComponent.updateCustomerInfo()">
                                        <option value="">اختر العميل</option>
                                        ${customers.map(customer => `
                                            <option value="${customer.id}">${customer.name}</option>
                                        `).join('')}
                                    </select>
                                </div>

                                <div id="customerInfo" class="d-none">
                                    <div class="mb-2">
                                        <small class="text-muted">البريد الإلكتروني:</small>
                                        <div id="customerEmail"></div>
                                    </div>
                                    <div class="mb-2">
                                        <small class="text-muted">الهاتف:</small>
                                        <div id="customerPhone"></div>
                                    </div>
                                    <div class="mb-2">
                                        <small class="text-muted">العنوان:</small>
                                        <div id="customerAddress"></div>
                                    </div>
                                </div>

                                <div class="mt-3">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="window.SalesComponent.showNewCustomerModal()">
                                        <i class="fas fa-plus me-1"></i>عميل جديد
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- عناصر الفاتورة -->
                <div class="card mt-3">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6><i class="fas fa-list me-2"></i>عناصر الفاتورة</h6>
                            <button type="button" class="btn btn-primary btn-sm" onclick="window.SalesComponent.addInvoiceItem()">
                                <i class="fas fa-plus me-1"></i>إضافة عنصر
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th width="30%">المنتج/الخدمة</th>
                                        <th width="15%">الكمية</th>
                                        <th width="15%">السعر</th>
                                        <th width="15%">الخصم</th>
                                        <th width="15%">المجموع</th>
                                        <th width="10%">إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="invoiceItems">
                                    <!-- سيتم إضافة العناصر هنا -->
                                </tbody>
                            </table>
                        </div>

                        <!-- ملخص الفاتورة -->
                        <div class="row mt-3">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="3" placeholder="ملاحظات إضافية للفاتورة"></textarea>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>المجموع الفرعي:</span>
                                            <span id="subtotal">0.00 ر.س</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>الخصم:</span>
                                            <span id="totalDiscount">0.00 ر.س</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>الضريبة (${(this.data.settings.taxRate * 100)}%):</span>
                                            <span id="taxAmount">0.00 ر.س</span>
                                        </div>
                                        <hr>
                                        <div class="d-flex justify-content-between fw-bold">
                                            <span>الإجمالي:</span>
                                            <span id="grandTotal">0.00 ر.س</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        `;
    },

    /**
     * تهيئة نموذج الفاتورة
     */
    initializeInvoiceForm: function() {
        // إضافة عنصر افتراضي
        this.addInvoiceItem();
    },

    /**
     * إضافة عنصر للفاتورة
     */
    addInvoiceItem: function() {
        const tbody = document.getElementById('invoiceItems');
        const products = Object.values(this.data.products || {});
        const itemIndex = tbody.children.length;

        const itemHTML = `
            <tr data-item-index="${itemIndex}">
                <td>
                    <select class="form-control" name="items[${itemIndex}][productId]" onchange="window.SalesComponent.updateItemPrice(${itemIndex})" required>
                        <option value="">اختر المنتج/الخدمة</option>
                        ${products.map(product => `
                            <option value="${product.id}" data-price="${product.price}">${product.name}</option>
                        `).join('')}
                    </select>
                </td>
                <td>
                    <input type="number" class="form-control" name="items[${itemIndex}][quantity]"
                           value="1" min="1" step="0.01" onchange="window.SalesComponent.calculateItemTotal(${itemIndex})" required>
                </td>
                <td>
                    <input type="number" class="form-control" name="items[${itemIndex}][price]"
                           step="0.01" min="0" onchange="window.SalesComponent.calculateItemTotal(${itemIndex})" required>
                </td>
                <td>
                    <input type="number" class="form-control" name="items[${itemIndex}][discount]"
                           value="0" step="0.01" min="0" onchange="window.SalesComponent.calculateItemTotal(${itemIndex})">
                </td>
                <td>
                    <input type="number" class="form-control" name="items[${itemIndex}][total]" readonly>
                </td>
                <td>
                    <button type="button" class="btn btn-danger btn-sm" onclick="window.SalesComponent.removeInvoiceItem(${itemIndex})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;

        tbody.insertAdjacentHTML('beforeend', itemHTML);
    },

    /**
     * تحديث سعر العنصر عند اختيار المنتج
     */
    updateItemPrice: function(itemIndex) {
        const select = document.querySelector(`select[name="items[${itemIndex}][productId]"]`);
        const priceInput = document.querySelector(`input[name="items[${itemIndex}][price]"]`);

        if (select.selectedOptions.length > 0) {
            const price = select.selectedOptions[0].getAttribute('data-price');
            priceInput.value = price || 0;
            this.calculateItemTotal(itemIndex);
        }
    },

    /**
     * حساب إجمالي العنصر
     */
    calculateItemTotal: function(itemIndex) {
        const quantity = parseFloat(document.querySelector(`input[name="items[${itemIndex}][quantity]"]`).value) || 0;
        const price = parseFloat(document.querySelector(`input[name="items[${itemIndex}][price]"]`).value) || 0;
        const discount = parseFloat(document.querySelector(`input[name="items[${itemIndex}][discount]"]`).value) || 0;

        const total = (quantity * price) - discount;
        document.querySelector(`input[name="items[${itemIndex}][total]"]`).value = total.toFixed(2);

        this.calculateInvoiceTotal();
    },

    /**
     * حساب إجمالي الفاتورة
     */
    calculateInvoiceTotal: function() {
        const itemTotals = document.querySelectorAll('input[name*="[total]"]');
        let subtotal = 0;
        let totalDiscount = 0;

        itemTotals.forEach(input => {
            subtotal += parseFloat(input.value) || 0;
        });

        // حساب إجمالي الخصومات
        const discountInputs = document.querySelectorAll('input[name*="[discount]"]');
        discountInputs.forEach(input => {
            totalDiscount += parseFloat(input.value) || 0;
        });

        const taxAmount = subtotal * this.data.settings.taxRate;
        const grandTotal = subtotal + taxAmount;

        // تحديث العرض
        document.getElementById('subtotal').textContent = this.formatAmount(subtotal);
        document.getElementById('totalDiscount').textContent = this.formatAmount(totalDiscount);
        document.getElementById('taxAmount').textContent = this.formatAmount(taxAmount);
        document.getElementById('grandTotal').textContent = this.formatAmount(grandTotal);
    },

    /**
     * حذف عنصر من الفاتورة
     */
    removeInvoiceItem: function(itemIndex) {
        const row = document.querySelector(`tr[data-item-index="${itemIndex}"]`);
        if (row) {
            row.remove();
            this.calculateInvoiceTotal();
        }
    },

    /**
     * تحديث معلومات العميل
     */
    updateCustomerInfo: function() {
        const select = document.querySelector('select[name="customerId"]');
        const customerInfo = document.getElementById('customerInfo');

        if (select.value) {
            const customer = this.data.customers[select.value];
            if (customer) {
                document.getElementById('customerEmail').textContent = customer.email || '-';
                document.getElementById('customerPhone').textContent = customer.phone || '-';
                document.getElementById('customerAddress').textContent = customer.address || '-';
                customerInfo.classList.remove('d-none');
            }
        } else {
            customerInfo.classList.add('d-none');
        }
    },

    /**
     * حفظ الفاتورة كمسودة
     */
    saveInvoiceAsDraft: function() {
        this.saveInvoice('draft');
    },

    /**
     * حفظ وإرسال الفاتورة
     */
    saveAndSendInvoice: function() {
        this.saveInvoice('sent');
    },

    /**
     * حفظ الفاتورة
     */
    saveInvoice: function(status = 'draft') {
        const form = document.getElementById('invoiceForm');
        const formData = new FormData(form);

        try {
            // جمع بيانات الفاتورة
            const invoiceData = {
                id: this.generateId(),
                number: formData.get('invoiceNumber'),
                date: formData.get('invoiceDate'),
                dueDate: formData.get('dueDate'),
                customerId: formData.get('customerId'),
                reference: formData.get('reference'),
                notes: formData.get('notes'),
                status: status,
                items: [],
                subtotal: 0,
                totalDiscount: 0,
                taxAmount: 0,
                total: 0,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // التحقق من صحة البيانات
            if (!invoiceData.customerId) {
                throw new Error('يرجى اختيار العميل');
            }

            // جمع عناصر الفاتورة
            const itemRows = document.querySelectorAll('#invoiceItems tr');
            itemRows.forEach((row, index) => {
                const productId = row.querySelector(`select[name="items[${index}][productId]"]`)?.value;
                const quantity = parseFloat(row.querySelector(`input[name="items[${index}][quantity]"]`)?.value) || 0;
                const price = parseFloat(row.querySelector(`input[name="items[${index}][price]"]`)?.value) || 0;
                const discount = parseFloat(row.querySelector(`input[name="items[${index}][discount]"]`)?.value) || 0;
                const total = parseFloat(row.querySelector(`input[name="items[${index}][total]"]`)?.value) || 0;

                if (productId && quantity > 0 && price > 0) {
                    invoiceData.items.push({
                        productId,
                        quantity,
                        price,
                        discount,
                        total
                    });
                }
            });

            if (invoiceData.items.length === 0) {
                throw new Error('يرجى إضافة عنصر واحد على الأقل للفاتورة');
            }

            // حساب الإجماليات
            invoiceData.subtotal = invoiceData.items.reduce((sum, item) => sum + (item.quantity * item.price), 0);
            invoiceData.totalDiscount = invoiceData.items.reduce((sum, item) => sum + item.discount, 0);
            invoiceData.taxAmount = (invoiceData.subtotal - invoiceData.totalDiscount) * this.data.settings.taxRate;
            invoiceData.total = invoiceData.subtotal - invoiceData.totalDiscount + invoiceData.taxAmount;

            // حفظ الفاتورة
            this.data.invoices[invoiceData.id] = invoiceData;

            // تحديث رقم الفاتورة التالي
            this.data.settings.nextInvoiceNumber++;

            // حفظ البيانات
            this.saveSalesData();

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('newInvoiceModal'));
            modal.hide();

            // عرض رسالة نجاح
            this.showMessage(`تم ${status === 'draft' ? 'حفظ' : 'حفظ وإرسال'} الفاتورة بنجاح`, 'success');

            // تحديث العرض
            this.refreshData();

        } catch (error) {
            this.showMessage('خطأ في حفظ الفاتورة: ' + error.message, 'error');
        }
    },

    /**
     * توليد معرف فريد
     */
    generateId: function() {
        return 'ID_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    },

    /**
     * عرض رسالة للمستخدم
     */
    showMessage: function(message, type = 'info') {
        // يمكن تحسين هذا لاحقاً باستخدام نظام إشعارات أفضل
        const alertClass = type === 'success' ? 'alert-success' :
                          type === 'error' ? 'alert-danger' : 'alert-info';

        const alertHTML = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // إضافة الرسالة في أعلى الصفحة
        const container = document.querySelector('.sales-management') || document.body;
        container.insertAdjacentHTML('afterbegin', alertHTML);

        // إزالة الرسالة تلقائياً بعد 5 ثوان
        setTimeout(() => {
            const alert = container.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    },

    /**
     * عرض قائمة الفواتير
     */
    renderInvoices: function() {
        const invoices = Object.values(this.data.invoices || {})
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        return `
            <div class="invoices-management">
                <!-- شريط الأدوات -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-0">
                                    <i class="fas fa-file-invoice me-2"></i>إدارة الفواتير
                                </h5>
                            </div>
                            <div class="col-md-6 text-end">
                                <button class="btn btn-primary" onclick="window.SalesComponent.showNewInvoiceModal()">
                                    <i class="fas fa-plus me-1"></i>فاتورة جديدة
                                </button>
                                <button class="btn btn-outline-secondary" onclick="window.SalesComponent.exportInvoices()">
                                    <i class="fas fa-download me-1"></i>تصدير
                                </button>
                            </div>
                        </div>

                        <!-- فلاتر البحث -->
                        <div class="row mt-3">
                            <div class="col-md-3">
                                <input type="text" class="form-control" placeholder="البحث برقم الفاتورة..."
                                       onkeyup="window.SalesComponent.filterInvoices()">
                            </div>
                            <div class="col-md-3">
                                <select class="form-control" onchange="window.SalesComponent.filterInvoices()">
                                    <option value="">جميع الحالات</option>
                                    <option value="draft">مسودة</option>
                                    <option value="sent">مرسلة</option>
                                    <option value="paid">مدفوعة</option>
                                    <option value="overdue">متأخرة</option>
                                    <option value="cancelled">ملغية</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <input type="date" class="form-control" onchange="window.SalesComponent.filterInvoices()">
                            </div>
                            <div class="col-md-3">
                                <select class="form-control" onchange="window.SalesComponent.filterInvoices()">
                                    <option value="">جميع العملاء</option>
                                    ${Object.values(this.data.customers || {}).map(customer => `
                                        <option value="${customer.id}">${customer.name}</option>
                                    `).join('')}
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول الفواتير -->
                <div class="card">
                    <div class="card-body">
                        ${invoices.length === 0 ? this.renderEmptyInvoices() : this.renderInvoicesTable(invoices)}
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض رسالة عدم وجود فواتير
     */
    renderEmptyInvoices: function() {
        return `
            <div class="text-center py-5">
                <i class="fas fa-file-invoice fa-5x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد فواتير حتى الآن</h4>
                <p class="text-muted">ابدأ بإنشاء أول فاتورة لك</p>
                <button class="btn btn-primary btn-lg" onclick="window.SalesComponent.showNewInvoiceModal()">
                    <i class="fas fa-plus me-2"></i>إنشاء فاتورة جديدة
                </button>
            </div>
        `;
    },

    /**
     * عرض جدول الفواتير
     */
    renderInvoicesTable: function(invoices) {
        return `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>التاريخ</th>
                            <th>الإجمالي</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${invoices.map(invoice => `
                            <tr>
                                <td>
                                    <strong>${invoice.number}</strong>
                                    ${invoice.reference ? `<br><small class="text-muted">${invoice.reference}</small>` : ''}
                                </td>
                                <td>
                                    ${this.data.customers[invoice.customerId]?.name || 'عميل غير محدد'}
                                    <br><small class="text-muted">${this.data.customers[invoice.customerId]?.email || ''}</small>
                                </td>
                                <td>
                                    ${new Date(invoice.date).toLocaleDateString('ar-SA')}
                                    ${invoice.dueDate ? `<br><small class="text-muted">الاستحقاق: ${new Date(invoice.dueDate).toLocaleDateString('ar-SA')}</small>` : ''}
                                </td>
                                <td>
                                    <strong>${this.formatAmount(invoice.total)}</strong>
                                    <br><small class="text-muted">${invoice.items?.length || 0} عنصر</small>
                                </td>
                                <td>
                                    <span class="badge bg-${this.getInvoiceStatusColor(invoice.status)}">
                                        ${this.getInvoiceStatusLabel(invoice.status)}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="window.SalesComponent.viewInvoice('${invoice.id}')" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="window.SalesComponent.editInvoice('${invoice.id}')" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-info" onclick="window.SalesComponent.printInvoice('${invoice.id}')" title="طباعة">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="window.SalesComponent.deleteInvoice('${invoice.id}')" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    },

    /**
     * عرض تفاصيل الفاتورة
     */
    viewInvoice: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            this.showMessage('الفاتورة غير موجودة', 'error');
            return;
        }

        const customer = this.data.customers[invoice.customerId];
        const modalHTML = `
            <div class="modal fade" id="viewInvoiceModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-file-invoice me-2"></i>تفاصيل الفاتورة ${invoice.number}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${this.renderInvoiceDetails(invoice, customer)}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-outline-primary" onclick="window.SalesComponent.editInvoice('${invoice.id}')">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </button>
                            <button type="button" class="btn btn-info" onclick="window.SalesComponent.printInvoice('${invoice.id}')">
                                <i class="fas fa-print me-1"></i>طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('viewInvoiceModal'));
        modal.show();

        document.getElementById('viewInvoiceModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * عرض تفاصيل الفاتورة
     */
    renderInvoiceDetails: function(invoice, customer) {
        return `
            <div class="invoice-details">
                <!-- رأس الفاتورة -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h4>فاتورة رقم: ${invoice.number}</h4>
                        <p class="text-muted mb-1">تاريخ الإصدار: ${new Date(invoice.date).toLocaleDateString('ar-SA')}</p>
                        ${invoice.dueDate ? `<p class="text-muted mb-1">تاريخ الاستحقاق: ${new Date(invoice.dueDate).toLocaleDateString('ar-SA')}</p>` : ''}
                        ${invoice.reference ? `<p class="text-muted">المرجع: ${invoice.reference}</p>` : ''}
                    </div>
                    <div class="col-md-6 text-end">
                        <span class="badge bg-${this.getInvoiceStatusColor(invoice.status)} fs-6 mb-2">
                            ${this.getInvoiceStatusLabel(invoice.status)}
                        </span>
                        <h3 class="text-primary">${this.formatAmount(invoice.total)}</h3>
                    </div>
                </div>

                <!-- معلومات العميل -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-user me-2"></i>معلومات العميل</h6>
                            </div>
                            <div class="card-body">
                                <h6>${customer?.name || 'عميل غير محدد'}</h6>
                                ${customer?.email ? `<p class="mb-1"><i class="fas fa-envelope me-2"></i>${customer.email}</p>` : ''}
                                ${customer?.phone ? `<p class="mb-1"><i class="fas fa-phone me-2"></i>${customer.phone}</p>` : ''}
                                ${customer?.address ? `<p class="mb-0"><i class="fas fa-map-marker-alt me-2"></i>${customer.address}</p>` : ''}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-building me-2"></i>معلومات الشركة</h6>
                            </div>
                            <div class="card-body">
                                <h6>قمة الوعد للسفريات</h6>
                                <p class="mb-1">المملكة العربية السعودية</p>
                                <p class="mb-1">الرقم الضريبي: *********</p>
                                <p class="mb-0"><EMAIL></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- عناصر الفاتورة -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6><i class="fas fa-list me-2"></i>عناصر الفاتورة</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th>المنتج/الخدمة</th>
                                        <th>الكمية</th>
                                        <th>السعر</th>
                                        <th>الخصم</th>
                                        <th>المجموع</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${invoice.items?.map(item => {
                                        const product = this.data.products[item.productId];
                                        return `
                                            <tr>
                                                <td>${product?.name || 'منتج غير محدد'}</td>
                                                <td>${item.quantity}</td>
                                                <td>${this.formatAmount(item.price)}</td>
                                                <td>${this.formatAmount(item.discount)}</td>
                                                <td>${this.formatAmount(item.total)}</td>
                                            </tr>
                                        `;
                                    }).join('') || '<tr><td colspan="5" class="text-center">لا توجد عناصر</td></tr>'}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- ملخص الفاتورة -->
                <div class="row">
                    <div class="col-md-8">
                        ${invoice.notes ? `
                            <div class="card">
                                <div class="card-header">
                                    <h6><i class="fas fa-sticky-note me-2"></i>ملاحظات</h6>
                                </div>
                                <div class="card-body">
                                    <p>${invoice.notes}</p>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>المجموع الفرعي:</span>
                                    <span>${this.formatAmount(invoice.subtotal)}</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>الخصم:</span>
                                    <span>${this.formatAmount(invoice.totalDiscount)}</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>الضريبة:</span>
                                    <span>${this.formatAmount(invoice.taxAmount)}</span>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between fw-bold fs-5">
                                    <span>الإجمالي:</span>
                                    <span class="text-primary">${this.formatAmount(invoice.total)}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض قائمة الفواتير المتقدمة
     */
    renderInvoices: function() {
        const invoices = Object.values(this.data.invoices || {})
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        return `
            <div class="invoices-management">
                <!-- شريط الأدوات المتقدم -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row align-items-center mb-3">
                            <div class="col-md-6">
                                <h5 class="mb-0">
                                    <i class="fas fa-file-invoice me-2"></i>إدارة الفواتير المتقدمة
                                </h5>
                                <small class="text-muted">إجمالي ${invoices.length} فاتورة</small>
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="btn-group me-2">
                                    <button class="btn btn-primary" onclick="window.SalesComponent.showNewInvoiceModal()">
                                        <i class="fas fa-plus me-1"></i>فاتورة جديدة
                                    </button>
                                    <button class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="fas fa-cog"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.bulkInvoiceActions()">
                                            <i class="fas fa-tasks me-2"></i>إجراءات مجمعة
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.invoiceTemplates()">
                                            <i class="fas fa-file-alt me-2"></i>قوالب الفواتير
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.invoiceSettings()">
                                            <i class="fas fa-cog me-2"></i>إعدادات الفواتير
                                        </a></li>
                                    </ul>
                                </div>
                                <div class="btn-group">
                                    <button class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="fas fa-download me-1"></i>تصدير
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.exportInvoicesCSV()">
                                            <i class="fas fa-file-csv me-2"></i>تصدير CSV
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.exportInvoicesExcel()">
                                            <i class="fas fa-file-excel me-2"></i>تصدير Excel
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.exportInvoicesPDF()">
                                            <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.printAllInvoices()">
                                            <i class="fas fa-print me-2"></i>طباعة الكل
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- إحصائيات سريعة -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <div class="d-flex align-items-center p-2 bg-light rounded">
                                    <i class="fas fa-file-invoice fa-2x text-primary me-3"></i>
                                    <div>
                                        <h6 class="mb-0">${invoices.length}</h6>
                                        <small class="text-muted">إجمالي الفواتير</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="d-flex align-items-center p-2 bg-light rounded">
                                    <i class="fas fa-check-circle fa-2x text-success me-3"></i>
                                    <div>
                                        <h6 class="mb-0">${invoices.filter(inv => inv.status === 'paid').length}</h6>
                                        <small class="text-muted">فواتير مدفوعة</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="d-flex align-items-center p-2 bg-light rounded">
                                    <i class="fas fa-clock fa-2x text-warning me-3"></i>
                                    <div>
                                        <h6 class="mb-0">${invoices.filter(inv => inv.status === 'sent').length}</h6>
                                        <small class="text-muted">فواتير معلقة</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="d-flex align-items-center p-2 bg-light rounded">
                                    <i class="fas fa-money-bill-wave fa-2x text-info me-3"></i>
                                    <div>
                                        <h6 class="mb-0">${this.formatAmount(invoices.reduce((sum, inv) => sum + (inv.total || 0), 0))}</h6>
                                        <small class="text-muted">إجمالي القيمة</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- فلاتر البحث المتقدمة -->
                        <div class="card bg-light">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label class="form-label small">البحث</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" placeholder="رقم الفاتورة أو المرجع..."
                                                   id="invoiceSearch" onkeyup="window.SalesComponent.filterInvoices()">
                                            <button class="btn btn-outline-secondary" type="button" onclick="window.SalesComponent.clearSearch()">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label small">الحالة</label>
                                        <select class="form-control" id="statusFilter" onchange="window.SalesComponent.filterInvoices()">
                                            <option value="">جميع الحالات</option>
                                            <option value="draft">مسودة</option>
                                            <option value="sent">مرسلة</option>
                                            <option value="paid">مدفوعة</option>
                                            <option value="overdue">متأخرة</option>
                                            <option value="cancelled">ملغية</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label small">من تاريخ</label>
                                        <input type="date" class="form-control" id="dateFromFilter" onchange="window.SalesComponent.filterInvoices()">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label small">إلى تاريخ</label>
                                        <input type="date" class="form-control" id="dateToFilter" onchange="window.SalesComponent.filterInvoices()">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label small">العميل</label>
                                        <select class="form-control" id="customerFilter" onchange="window.SalesComponent.filterInvoices()">
                                            <option value="">جميع العملاء</option>
                                            ${Object.values(this.data.customers || {}).map(customer => `
                                                <option value="${customer.id}">${customer.name}</option>
                                            `).join('')}
                                        </select>
                                    </div>
                                    <div class="col-md-1">
                                        <label class="form-label small">&nbsp;</label>
                                        <button class="btn btn-outline-primary w-100" onclick="window.SalesComponent.clearAllFilters()" title="مسح الفلاتر">
                                            <i class="fas fa-eraser"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- فلاتر إضافية -->
                                <div class="row mt-2">
                                    <div class="col-md-2">
                                        <label class="form-label small">المبلغ من</label>
                                        <input type="number" class="form-control" placeholder="0" id="amountFromFilter" onchange="window.SalesComponent.filterInvoices()">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label small">المبلغ إلى</label>
                                        <input type="number" class="form-control" placeholder="999999" id="amountToFilter" onchange="window.SalesComponent.filterInvoices()">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label small">ترتيب حسب</label>
                                        <select class="form-control" id="sortBy" onchange="window.SalesComponent.filterInvoices()">
                                            <option value="date_desc">التاريخ (الأحدث)</option>
                                            <option value="date_asc">التاريخ (الأقدم)</option>
                                            <option value="amount_desc">المبلغ (الأعلى)</option>
                                            <option value="amount_asc">المبلغ (الأقل)</option>
                                            <option value="customer">اسم العميل</option>
                                            <option value="status">الحالة</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label small">عرض</label>
                                        <select class="form-control" id="itemsPerPage" onchange="window.SalesComponent.changeItemsPerPage()">
                                            <option value="10">10 فواتير</option>
                                            <option value="25" selected>25 فاتورة</option>
                                            <option value="50">50 فاتورة</option>
                                            <option value="100">100 فاتورة</option>
                                            <option value="all">الكل</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label small">عرض</label>
                                        <div class="btn-group w-100">
                                            <button class="btn btn-outline-secondary btn-sm active" onclick="window.SalesComponent.changeViewMode('table')" id="tableViewBtn">
                                                <i class="fas fa-table"></i>
                                            </button>
                                            <button class="btn btn-outline-secondary btn-sm" onclick="window.SalesComponent.changeViewMode('cards')" id="cardsViewBtn">
                                                <i class="fas fa-th-large"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label small">إجراءات</label>
                                        <button class="btn btn-outline-info w-100" onclick="window.SalesComponent.showAdvancedSearch()">
                                            <i class="fas fa-search-plus"></i> متقدم
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول الفواتير المتطور -->
                <div class="card mt-3">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAllInvoices" onchange="window.SalesComponent.toggleSelectAll()">
                                    <label class="form-check-label" for="selectAllInvoices">
                                        تحديد الكل
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 text-end">
                                <div id="bulkActionsContainer" style="display: none;">
                                    <span class="text-muted me-2">المحدد: <span id="selectedCount">0</span></span>
                                    <div class="btn-group">
                                        <button class="btn btn-outline-success btn-sm" onclick="window.SalesComponent.bulkMarkAsPaid()">
                                            <i class="fas fa-check me-1"></i>تحديد كمدفوع
                                        </button>
                                        <button class="btn btn-outline-warning btn-sm" onclick="window.SalesComponent.bulkMarkAsSent()">
                                            <i class="fas fa-paper-plane me-1"></i>تحديد كمرسل
                                        </button>
                                        <button class="btn btn-outline-info btn-sm" onclick="window.SalesComponent.bulkPrint()">
                                            <i class="fas fa-print me-1"></i>طباعة المحدد
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm" onclick="window.SalesComponent.bulkDelete()">
                                            <i class="fas fa-trash me-1"></i>حذف المحدد
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div id="invoicesTableContainer">
                            ${invoices.length === 0 ? this.renderEmptyInvoices() : this.renderInvoicesTableAdvanced(invoices)}
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-between align-items-center p-3 border-top">
                            <div class="text-muted">
                                عرض <span id="showingFrom">1</span> إلى <span id="showingTo">${Math.min(25, invoices.length)}</span>
                                من <span id="totalItems">${invoices.length}</span> فاتورة
                            </div>
                            <nav>
                                <ul class="pagination pagination-sm mb-0" id="invoicesPagination">
                                    <!-- سيتم إنشاء الصفحات هنا -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض رسالة عدم وجود فواتير
     */
    renderEmptyInvoices: function() {
        return `
            <div class="text-center py-5">
                <i class="fas fa-file-invoice fa-5x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد فواتير حتى الآن</h4>
                <p class="text-muted">ابدأ بإنشاء أول فاتورة لك</p>
                <button class="btn btn-primary btn-lg" onclick="window.SalesComponent.showNewInvoiceModal()">
                    <i class="fas fa-plus me-2"></i>إنشاء فاتورة جديدة
                </button>
            </div>
        `;
    },

    /**
     * عرض جدول الفواتير المتطور
     */
    renderInvoicesTableAdvanced: function(invoices) {
        return `
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="3%">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAllInvoicesHeader">
                                </div>
                            </th>
                            <th width="15%">
                                <div class="d-flex align-items-center">
                                    رقم الفاتورة
                                    <button class="btn btn-sm ms-1" onclick="window.SalesComponent.sortInvoices('number')">
                                        <i class="fas fa-sort"></i>
                                    </button>
                                </div>
                            </th>
                            <th width="20%">
                                <div class="d-flex align-items-center">
                                    العميل
                                    <button class="btn btn-sm ms-1" onclick="window.SalesComponent.sortInvoices('customer')">
                                        <i class="fas fa-sort"></i>
                                    </button>
                                </div>
                            </th>
                            <th width="15%">
                                <div class="d-flex align-items-center">
                                    التاريخ
                                    <button class="btn btn-sm ms-1" onclick="window.SalesComponent.sortInvoices('date')">
                                        <i class="fas fa-sort"></i>
                                    </button>
                                </div>
                            </th>
                            <th width="15%">
                                <div class="d-flex align-items-center">
                                    الإجمالي
                                    <button class="btn btn-sm ms-1" onclick="window.SalesComponent.sortInvoices('amount')">
                                        <i class="fas fa-sort"></i>
                                    </button>
                                </div>
                            </th>
                            <th width="12%">الحالة</th>
                            <th width="20%">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${invoices.map(invoice => `
                            <tr class="invoice-row" data-invoice-id="${invoice.id}">
                                <td>
                                    <div class="form-check">
                                        <input class="form-check-input invoice-checkbox" type="checkbox"
                                               value="${invoice.id}" onchange="window.SalesComponent.updateBulkActions()">
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div>
                                            <strong class="text-primary">${invoice.number}</strong>
                                            ${invoice.reference ? `<br><small class="text-muted"><i class="fas fa-tag me-1"></i>${invoice.reference}</small>` : ''}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="me-2">
                                            <i class="fas fa-${this.data.customers[invoice.customerId]?.type === 'corporate' ? 'building' : 'user'} text-muted"></i>
                                        </div>
                                        <div>
                                            <strong>${this.data.customers[invoice.customerId]?.name || 'عميل غير محدد'}</strong>
                                            ${this.data.customers[invoice.customerId]?.email ? `<br><small class="text-muted">${this.data.customers[invoice.customerId].email}</small>` : ''}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong>${new Date(invoice.date).toLocaleDateString('ar-SA')}</strong>
                                        <br><small class="text-muted">${this.getTimeAgo(invoice.createdAt)}</small>
                                        ${invoice.dueDate ? `<br><small class="text-${this.isOverdue(invoice.dueDate) ? 'danger' : 'warning'}">
                                            <i class="fas fa-clock me-1"></i>الاستحقاق: ${new Date(invoice.dueDate).toLocaleDateString('ar-SA')}
                                        </small>` : ''}
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong class="fs-6">${this.formatAmount(invoice.total)}</strong>
                                        <br><small class="text-muted">${invoice.items?.length || 0} عنصر</small>
                                        ${invoice.taxAmount > 0 ? `<br><small class="text-info">ضريبة: ${this.formatAmount(invoice.taxAmount)}</small>` : ''}
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex flex-column">
                                        <span class="badge bg-${this.getInvoiceStatusColor(invoice.status)} mb-1">
                                            ${this.getInvoiceStatusLabel(invoice.status)}
                                        </span>
                                        ${this.getInvoiceStatusActions(invoice)}
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="window.SalesComponent.viewInvoice('${invoice.id}')" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="window.SalesComponent.editInvoice('${invoice.id}')" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <div class="btn-group">
                                            <button class="btn btn-outline-info dropdown-toggle" data-bs-toggle="dropdown" title="المزيد">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.printInvoice('${invoice.id}')">
                                                    <i class="fas fa-print me-2"></i>طباعة
                                                </a></li>
                                                <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.duplicateInvoice('${invoice.id}')">
                                                    <i class="fas fa-copy me-2"></i>نسخ
                                                </a></li>
                                                <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.sendInvoiceEmail('${invoice.id}')">
                                                    <i class="fas fa-envelope me-2"></i>إرسال بالبريد
                                                </a></li>
                                                <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.downloadInvoicePDF('${invoice.id}')">
                                                    <i class="fas fa-file-pdf me-2"></i>تحميل PDF
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#" onclick="window.SalesComponent.deleteInvoice('${invoice.id}')">
                                                    <i class="fas fa-trash me-2"></i>حذف
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    },

    /**
     * الحصول على إجراءات سريعة حسب حالة الفاتورة
     */
    getInvoiceStatusActions: function(invoice) {
        switch (invoice.status) {
            case 'draft':
                return `<button class="btn btn-outline-primary btn-xs" onclick="window.SalesComponent.sendInvoice('${invoice.id}')">
                    <i class="fas fa-paper-plane"></i> إرسال
                </button>`;
            case 'sent':
                return `<button class="btn btn-outline-success btn-xs" onclick="window.SalesComponent.markAsPaid('${invoice.id}')">
                    <i class="fas fa-check"></i> تحديد كمدفوع
                </button>`;
            case 'overdue':
                return `<button class="btn btn-outline-warning btn-xs" onclick="window.SalesComponent.sendReminder('${invoice.id}')">
                    <i class="fas fa-bell"></i> تذكير
                </button>`;
            default:
                return '';
        }
    },

    /**
     * التحقق من انتهاء الاستحقاق
     */
    isOverdue: function(dueDate) {
        if (!dueDate) return false;
        return new Date(dueDate) < new Date();
    },

    /**
     * حساب الوقت المنقضي
     */
    getTimeAgo: function(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays === 1) return 'أمس';
        if (diffDays < 7) return `منذ ${diffDays} أيام`;
        if (diffDays < 30) return `منذ ${Math.ceil(diffDays / 7)} أسابيع`;
        return `منذ ${Math.ceil(diffDays / 30)} شهور`;
    },

    /**
     * فلترة الفواتير المتقدمة
     */
    filterInvoices: function() {
        const searchTerm = document.getElementById('invoiceSearch')?.value.toLowerCase() || '';
        const statusFilter = document.getElementById('statusFilter')?.value || '';
        const dateFromFilter = document.getElementById('dateFromFilter')?.value || '';
        const dateToFilter = document.getElementById('dateToFilter')?.value || '';
        const customerFilter = document.getElementById('customerFilter')?.value || '';
        const amountFromFilter = parseFloat(document.getElementById('amountFromFilter')?.value) || 0;
        const amountToFilter = parseFloat(document.getElementById('amountToFilter')?.value) || Infinity;
        const sortBy = document.getElementById('sortBy')?.value || 'date_desc';

        let filteredInvoices = Object.values(this.data.invoices || {});

        // فلترة بالبحث
        if (searchTerm) {
            filteredInvoices = filteredInvoices.filter(invoice =>
                invoice.number.toLowerCase().includes(searchTerm) ||
                invoice.reference?.toLowerCase().includes(searchTerm) ||
                this.data.customers[invoice.customerId]?.name.toLowerCase().includes(searchTerm)
            );
        }

        // فلترة بالحالة
        if (statusFilter) {
            filteredInvoices = filteredInvoices.filter(invoice => invoice.status === statusFilter);
        }

        // فلترة بالتاريخ من
        if (dateFromFilter) {
            filteredInvoices = filteredInvoices.filter(invoice => invoice.date >= dateFromFilter);
        }

        // فلترة بالتاريخ إلى
        if (dateToFilter) {
            filteredInvoices = filteredInvoices.filter(invoice => invoice.date <= dateToFilter);
        }

        // فلترة بالعميل
        if (customerFilter) {
            filteredInvoices = filteredInvoices.filter(invoice => invoice.customerId === customerFilter);
        }

        // فلترة بالمبلغ
        filteredInvoices = filteredInvoices.filter(invoice =>
            invoice.total >= amountFromFilter && invoice.total <= amountToFilter
        );

        // ترتيب النتائج
        this.sortInvoicesArray(filteredInvoices, sortBy);

        // تحديث الجدول
        this.updateInvoicesDisplay(filteredInvoices);
    },

    /**
     * ترتيب مصفوفة الفواتير
     */
    sortInvoicesArray: function(invoices, sortBy) {
        switch (sortBy) {
            case 'date_desc':
                invoices.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
                break;
            case 'date_asc':
                invoices.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
                break;
            case 'amount_desc':
                invoices.sort((a, b) => b.total - a.total);
                break;
            case 'amount_asc':
                invoices.sort((a, b) => a.total - b.total);
                break;
            case 'customer':
                invoices.sort((a, b) => {
                    const nameA = this.data.customers[a.customerId]?.name || '';
                    const nameB = this.data.customers[b.customerId]?.name || '';
                    return nameA.localeCompare(nameB);
                });
                break;
            case 'status':
                invoices.sort((a, b) => a.status.localeCompare(b.status));
                break;
        }
    },

    /**
     * تحديث عرض الفواتير
     */
    updateInvoicesDisplay: function(filteredInvoices) {
        const container = document.getElementById('invoicesTableContainer');
        if (container) {
            container.innerHTML = filteredInvoices.length === 0 ?
                '<div class="text-center py-4"><p class="text-muted">لا توجد فواتير تطابق معايير البحث</p></div>' :
                this.renderInvoicesTableAdvanced(filteredInvoices);
        }

        // تحديث معلومات الصفحات
        this.updatePaginationInfo(filteredInvoices.length);
    },

    /**
     * مسح جميع الفلاتر
     */
    clearAllFilters: function() {
        document.getElementById('invoiceSearch').value = '';
        document.getElementById('statusFilter').value = '';
        document.getElementById('dateFromFilter').value = '';
        document.getElementById('dateToFilter').value = '';
        document.getElementById('customerFilter').value = '';
        document.getElementById('amountFromFilter').value = '';
        document.getElementById('amountToFilter').value = '';
        document.getElementById('sortBy').value = 'date_desc';
        this.filterInvoices();
    },

    /**
     * مسح البحث
     */
    clearSearch: function() {
        document.getElementById('invoiceSearch').value = '';
        this.filterInvoices();
    },

    /**
     * تبديل تحديد الكل
     */
    toggleSelectAll: function() {
        const selectAllCheckbox = document.getElementById('selectAllInvoices');
        const invoiceCheckboxes = document.querySelectorAll('.invoice-checkbox');

        invoiceCheckboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
        });

        this.updateBulkActions();
    },

    /**
     * تحديث إجراءات التحديد المجمع
     */
    updateBulkActions: function() {
        const selectedCheckboxes = document.querySelectorAll('.invoice-checkbox:checked');
        const bulkActionsContainer = document.getElementById('bulkActionsContainer');
        const selectedCountSpan = document.getElementById('selectedCount');

        if (selectedCheckboxes.length > 0) {
            bulkActionsContainer.style.display = 'block';
            selectedCountSpan.textContent = selectedCheckboxes.length;
        } else {
            bulkActionsContainer.style.display = 'none';
        }

        // تحديث حالة تحديد الكل
        const selectAllCheckbox = document.getElementById('selectAllInvoices');
        const allCheckboxes = document.querySelectorAll('.invoice-checkbox');
        selectAllCheckbox.checked = selectedCheckboxes.length === allCheckboxes.length;
    },

    /**
     * تحديد المحدد كمدفوع
     */
    bulkMarkAsPaid: function() {
        const selectedInvoices = this.getSelectedInvoices();
        if (selectedInvoices.length === 0) {
            this.showMessage('يرجى تحديد فاتورة واحدة على الأقل', 'warning');
            return;
        }

        if (confirm(`هل تريد تحديد ${selectedInvoices.length} فاتورة كمدفوعة؟`)) {
            selectedInvoices.forEach(invoiceId => {
                if (this.data.invoices[invoiceId]) {
                    this.data.invoices[invoiceId].status = 'paid';
                    this.data.invoices[invoiceId].updatedAt = new Date().toISOString();
                }
            });

            this.saveSalesData();
            this.showMessage(`تم تحديد ${selectedInvoices.length} فاتورة كمدفوعة`, 'success');
            this.refreshData();
        }
    },

    /**
     * تحديد المحدد كمرسل
     */
    bulkMarkAsSent: function() {
        const selectedInvoices = this.getSelectedInvoices();
        if (selectedInvoices.length === 0) {
            this.showMessage('يرجى تحديد فاتورة واحدة على الأقل', 'warning');
            return;
        }

        if (confirm(`هل تريد تحديد ${selectedInvoices.length} فاتورة كمرسلة؟`)) {
            selectedInvoices.forEach(invoiceId => {
                if (this.data.invoices[invoiceId]) {
                    this.data.invoices[invoiceId].status = 'sent';
                    this.data.invoices[invoiceId].updatedAt = new Date().toISOString();
                }
            });

            this.saveSalesData();
            this.showMessage(`تم تحديد ${selectedInvoices.length} فاتورة كمرسلة`, 'success');
            this.refreshData();
        }
    },

    /**
     * الحصول على الفواتير المحددة
     */
    getSelectedInvoices: function() {
        const selectedCheckboxes = document.querySelectorAll('.invoice-checkbox:checked');
        return Array.from(selectedCheckboxes).map(checkbox => checkbox.value);
    },

    /**
     * عرض تفاصيل الفاتورة
     */
    viewInvoice: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            this.showMessage('الفاتورة غير موجودة', 'error');
            return;
        }

        const customer = this.data.customers[invoice.customerId];
        const modalHTML = `
            <div class="modal fade" id="viewInvoiceModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-file-invoice me-2"></i>تفاصيل الفاتورة ${invoice.number}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${this.renderInvoiceDetails(invoice, customer)}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-outline-primary" onclick="window.SalesComponent.editInvoice('${invoice.id}'); bootstrap.Modal.getInstance(document.getElementById('viewInvoiceModal')).hide();">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </button>
                            <button type="button" class="btn btn-info" onclick="window.SalesComponent.printInvoice('${invoice.id}')">
                                <i class="fas fa-print me-1"></i>طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('viewInvoiceModal'));
        modal.show();

        document.getElementById('viewInvoiceModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * عرض تفاصيل الفاتورة
     */
    renderInvoiceDetails: function(invoice, customer) {
        return `
            <div class="invoice-details">
                <!-- رأس الفاتورة -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h4>فاتورة رقم: ${invoice.number}</h4>
                        <p class="text-muted mb-1">تاريخ الإصدار: ${new Date(invoice.date).toLocaleDateString('ar-SA')}</p>
                        ${invoice.dueDate ? `<p class="text-muted mb-1">تاريخ الاستحقاق: ${new Date(invoice.dueDate).toLocaleDateString('ar-SA')}</p>` : ''}
                        ${invoice.reference ? `<p class="text-muted">المرجع: ${invoice.reference}</p>` : ''}
                    </div>
                    <div class="col-md-6 text-end">
                        <span class="badge bg-${this.getInvoiceStatusColor(invoice.status)} fs-6 mb-2">
                            ${this.getInvoiceStatusLabel(invoice.status)}
                        </span>
                        <h3 class="text-primary">${this.formatAmount(invoice.total)}</h3>
                    </div>
                </div>

                <!-- معلومات العميل -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-user me-2"></i>معلومات العميل</h6>
                            </div>
                            <div class="card-body">
                                <h6>${customer?.name || 'عميل غير محدد'}</h6>
                                ${customer?.email ? `<p class="mb-1"><i class="fas fa-envelope me-2"></i>${customer.email}</p>` : ''}
                                ${customer?.phone ? `<p class="mb-1"><i class="fas fa-phone me-2"></i>${customer.phone}</p>` : ''}
                                ${customer?.address ? `<p class="mb-0"><i class="fas fa-map-marker-alt me-2"></i>${customer.address}</p>` : ''}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-building me-2"></i>معلومات الشركة</h6>
                            </div>
                            <div class="card-body">
                                <h6>قمة الوعد للسفريات</h6>
                                <p class="mb-1">المملكة العربية السعودية</p>
                                <p class="mb-1">الرقم الضريبي: *********</p>
                                <p class="mb-0"><EMAIL></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- عناصر الفاتورة -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6><i class="fas fa-list me-2"></i>عناصر الفاتورة</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th>المنتج/الخدمة</th>
                                        <th>الكمية</th>
                                        <th>السعر</th>
                                        <th>الخصم</th>
                                        <th>المجموع</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${invoice.items?.map(item => {
                                        const product = this.data.products[item.productId];
                                        return `
                                            <tr>
                                                <td>${product?.name || 'منتج غير محدد'}</td>
                                                <td>${item.quantity}</td>
                                                <td>${this.formatAmount(item.price)}</td>
                                                <td>${this.formatAmount(item.discount)}</td>
                                                <td>${this.formatAmount(item.total)}</td>
                                            </tr>
                                        `;
                                    }).join('') || '<tr><td colspan="5" class="text-center">لا توجد عناصر</td></tr>'}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- ملخص الفاتورة -->
                <div class="row">
                    <div class="col-md-8">
                        ${invoice.notes ? `
                            <div class="card">
                                <div class="card-header">
                                    <h6><i class="fas fa-sticky-note me-2"></i>ملاحظات</h6>
                                </div>
                                <div class="card-body">
                                    <p>${invoice.notes}</p>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>المجموع الفرعي:</span>
                                    <span>${this.formatAmount(invoice.subtotal)}</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>الخصم:</span>
                                    <span>${this.formatAmount(invoice.totalDiscount)}</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>الضريبة:</span>
                                    <span>${this.formatAmount(invoice.taxAmount)}</span>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between fw-bold fs-5">
                                    <span>الإجمالي:</span>
                                    <span class="text-primary">${this.formatAmount(invoice.total)}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * تعديل الفاتورة
     */
    editInvoice: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            this.showMessage('الفاتورة غير موجودة', 'error');
            return;
        }

        // إغلاق أي نافذة مفتوحة
        const existingModal = document.getElementById('viewInvoiceModal');
        if (existingModal) {
            bootstrap.Modal.getInstance(existingModal)?.hide();
        }

        // عرض نافذة التعديل
        this.showEditInvoiceModal(invoice);
    },

    /**
     * حذف الفاتورة
     */
    deleteInvoice: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            this.showMessage('الفاتورة غير موجودة', 'error');
            return;
        }

        if (confirm(`هل أنت متأكد من حذف الفاتورة ${invoice.number}؟`)) {
            delete this.data.invoices[invoiceId];
            this.saveSalesData();
            this.showMessage('تم حذف الفاتورة بنجاح', 'success');
            this.refreshData();
        }
    },

    /**
     * طباعة الفاتورة
     */
    printInvoice: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            this.showMessage('الفاتورة غير موجودة', 'error');
            return;
        }

        const customer = this.data.customers[invoice.customerId];
        const printWindow = window.open('', '_blank');

        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>فاتورة ${invoice.number}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .header { text-align: center; margin-bottom: 30px; }
                    .invoice-info { margin-bottom: 20px; }
                    .customer-info { margin-bottom: 20px; }
                    table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                    th { background-color: #f5f5f5; }
                    .total-section { text-align: left; margin-top: 20px; }
                    .total-row { font-weight: bold; font-size: 1.2em; }
                    @media print { .no-print { display: none; } }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>قمة الوعد للسفريات</h1>
                    <p>المملكة العربية السعودية</p>
                    <p>الرقم الضريبي: *********</p>
                </div>

                <div class="invoice-info">
                    <h2>فاتورة رقم: ${invoice.number}</h2>
                    <p>تاريخ الإصدار: ${new Date(invoice.date).toLocaleDateString('ar-SA')}</p>
                    ${invoice.dueDate ? `<p>تاريخ الاستحقاق: ${new Date(invoice.dueDate).toLocaleDateString('ar-SA')}</p>` : ''}
                    ${invoice.reference ? `<p>المرجع: ${invoice.reference}</p>` : ''}
                </div>

                <div class="customer-info">
                    <h3>معلومات العميل:</h3>
                    <p><strong>${customer?.name || 'عميل غير محدد'}</strong></p>
                    ${customer?.email ? `<p>البريد الإلكتروني: ${customer.email}</p>` : ''}
                    ${customer?.phone ? `<p>الهاتف: ${customer.phone}</p>` : ''}
                    ${customer?.address ? `<p>العنوان: ${customer.address}</p>` : ''}
                </div>

                <table>
                    <thead>
                        <tr>
                            <th>المنتج/الخدمة</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>الخصم</th>
                            <th>المجموع</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${invoice.items?.map(item => {
                            const product = this.data.products[item.productId];
                            return `
                                <tr>
                                    <td>${product?.name || 'منتج غير محدد'}</td>
                                    <td>${item.quantity}</td>
                                    <td>${this.formatAmount(item.price)}</td>
                                    <td>${this.formatAmount(item.discount)}</td>
                                    <td>${this.formatAmount(item.total)}</td>
                                </tr>
                            `;
                        }).join('') || '<tr><td colspan="5">لا توجد عناصر</td></tr>'}
                    </tbody>
                </table>

                <div class="total-section">
                    <p>المجموع الفرعي: ${this.formatAmount(invoice.subtotal)}</p>
                    <p>الخصم: ${this.formatAmount(invoice.totalDiscount)}</p>
                    <p>الضريبة: ${this.formatAmount(invoice.taxAmount)}</p>
                    <p class="total-row">الإجمالي: ${this.formatAmount(invoice.total)}</p>
                </div>

                ${invoice.notes ? `<div><h3>ملاحظات:</h3><p>${invoice.notes}</p></div>` : ''}

                <button class="no-print" onclick="window.print()">طباعة</button>
            </body>
            </html>
        `);

        printWindow.document.close();
        printWindow.focus();
    },

    /**
     * تصدير الفواتير
     */
    exportInvoices: function() {
        const invoices = Object.values(this.data.invoices || {});
        if (invoices.length === 0) {
            this.showMessage('لا توجد فواتير للتصدير', 'error');
            return;
        }

        // تحضير البيانات للتصدير
        const csvData = [
            ['رقم الفاتورة', 'العميل', 'التاريخ', 'الإجمالي', 'الحالة', 'المرجع']
        ];

        invoices.forEach(invoice => {
            const customer = this.data.customers[invoice.customerId];
            csvData.push([
                invoice.number,
                customer?.name || 'عميل غير محدد',
                invoice.date,
                invoice.total,
                this.getInvoiceStatusLabel(invoice.status),
                invoice.reference || ''
            ]);
        });

        // تحويل إلى CSV
        const csvContent = csvData.map(row => row.join(',')).join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

        // تحميل الملف
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `invoices_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();

        this.showMessage('تم تصدير الفواتير بنجاح', 'success');
    },

    /**
     * عرض إدارة العملاء
     */
    renderCustomers: function() {
        const customers = Object.values(this.data.customers || {})
            .sort((a, b) => a.name.localeCompare(b.name));

        return `
            <div class="customers-management">
                <!-- شريط الأدوات -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-0">
                                    <i class="fas fa-users me-2"></i>إدارة العملاء
                                </h5>
                            </div>
                            <div class="col-md-6 text-end">
                                <button class="btn btn-primary" onclick="window.SalesComponent.showNewCustomerModal()">
                                    <i class="fas fa-plus me-1"></i>عميل جديد
                                </button>
                                <button class="btn btn-outline-secondary" onclick="window.SalesComponent.exportCustomers()">
                                    <i class="fas fa-download me-1"></i>تصدير
                                </button>
                            </div>
                        </div>

                        <!-- فلاتر البحث -->
                        <div class="row mt-3">
                            <div class="col-md-4">
                                <input type="text" class="form-control" placeholder="البحث بالاسم أو البريد الإلكتروني..."
                                       id="customerSearch" onkeyup="window.SalesComponent.filterCustomers()">
                            </div>
                            <div class="col-md-3">
                                <select class="form-control" id="customerTypeFilter" onchange="window.SalesComponent.filterCustomers()">
                                    <option value="">جميع الأنواع</option>
                                    <option value="individual">فرد</option>
                                    <option value="corporate">شركة</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-control" id="customerStatusFilter" onchange="window.SalesComponent.filterCustomers()">
                                    <option value="">جميع الحالات</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-outline-primary w-100" onclick="window.SalesComponent.clearCustomerFilters()">
                                    <i class="fas fa-times me-1"></i>مسح
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قائمة العملاء -->
                <div class="row" id="customersContainer">
                    ${customers.length === 0 ? this.renderEmptyCustomers() : this.renderCustomersGrid(customers)}
                </div>
            </div>
        `;
    },

    /**
     * عرض رسالة عدم وجود عملاء
     */
    renderEmptyCustomers: function() {
        return `
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-users fa-5x text-muted mb-3"></i>
                    <h4 class="text-muted">لا يوجد عملاء حتى الآن</h4>
                    <p class="text-muted">ابدأ بإضافة أول عميل لك</p>
                    <button class="btn btn-primary btn-lg" onclick="window.SalesComponent.showNewCustomerModal()">
                        <i class="fas fa-plus me-2"></i>إضافة عميل جديد
                    </button>
                </div>
            </div>
        `;
    },

    /**
     * عرض شبكة العملاء
     */
    renderCustomersGrid: function(customers) {
        return customers.map(customer => `
            <div class="col-md-6 col-lg-4 mb-4 customer-card" data-customer-id="${customer.id}">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div class="customer-avatar">
                                <i class="fas fa-${customer.type === 'corporate' ? 'building' : 'user'} fa-2x text-primary"></i>
                            </div>
                            <div class="customer-status">
                                <span class="badge bg-${customer.isActive ? 'success' : 'secondary'}">
                                    ${customer.isActive ? 'نشط' : 'غير نشط'}
                                </span>
                            </div>
                        </div>

                        <h6 class="card-title">${customer.name}</h6>
                        <p class="text-muted small mb-2">
                            <i class="fas fa-${customer.type === 'corporate' ? 'building' : 'user'} me-1"></i>
                            ${customer.type === 'corporate' ? 'شركة' : 'فرد'}
                        </p>

                        ${customer.email ? `
                            <p class="text-muted small mb-2">
                                <i class="fas fa-envelope me-1"></i>${customer.email}
                            </p>
                        ` : ''}

                        ${customer.phone ? `
                            <p class="text-muted small mb-2">
                                <i class="fas fa-phone me-1"></i>${customer.phone}
                            </p>
                        ` : ''}

                        <div class="customer-stats mt-3">
                            <div class="row text-center">
                                <div class="col-6">
                                    <small class="text-muted">الرصيد</small>
                                    <div class="fw-bold">${this.formatAmount(customer.balance || 0)}</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">الحد الائتماني</small>
                                    <div class="fw-bold">${this.formatAmount(customer.creditLimit || 0)}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer bg-transparent">
                        <div class="btn-group w-100">
                            <button class="btn btn-outline-primary btn-sm" onclick="window.SalesComponent.viewCustomer('${customer.id}')" title="عرض">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="window.SalesComponent.editCustomer('${customer.id}')" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-info btn-sm" onclick="window.SalesComponent.customerInvoices('${customer.id}')" title="الفواتير">
                                <i class="fas fa-file-invoice"></i>
                            </button>
                            <button class="btn btn-outline-danger btn-sm" onclick="window.SalesComponent.deleteCustomer('${customer.id}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    },

    /**
     * عرض نافذة عميل جديد
     */
    showNewCustomerModal: function() {
        const modalHTML = `
            <div class="modal fade" id="newCustomerModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-user-plus me-2"></i>إضافة عميل جديد
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${this.renderCustomerForm()}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveNewCustomer()">
                                <i class="fas fa-save me-1"></i>حفظ العميل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('newCustomerModal'));
        modal.show();

        document.getElementById('newCustomerModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * عرض نموذج العميل
     */
    renderCustomerForm: function(customer = null) {
        const isEdit = customer !== null;

        return `
            <form id="customerForm">
                <div class="row">
                    <!-- المعلومات الأساسية -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-info-circle me-2"></i>المعلومات الأساسية</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">نوع العميل *</label>
                                    <select class="form-control" name="type" required onchange="window.SalesComponent.toggleCustomerFields()">
                                        <option value="individual" ${customer?.type === 'individual' ? 'selected' : ''}>فرد</option>
                                        <option value="corporate" ${customer?.type === 'corporate' ? 'selected' : ''}>شركة</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الاسم *</label>
                                    <input type="text" class="form-control" name="name" value="${customer?.name || ''}" required>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" name="email" value="${customer?.email || ''}">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف *</label>
                                    <input type="tel" class="form-control" name="phone" value="${customer?.phone || ''}" required>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea class="form-control" name="address" rows="3">${customer?.address || ''}</textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- المعلومات المالية -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-money-bill me-2"></i>المعلومات المالية</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3" id="taxNumberField" style="display: ${customer?.type === 'corporate' ? 'block' : 'none'}">
                                    <label class="form-label">الرقم الضريبي</label>
                                    <input type="text" class="form-control" name="taxNumber" value="${customer?.taxNumber || ''}">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الحد الائتماني</label>
                                    <input type="number" class="form-control" name="creditLimit" value="${customer?.creditLimit || 0}" min="0" step="0.01">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الرصيد الحالي</label>
                                    <input type="number" class="form-control" name="balance" value="${customer?.balance || 0}" step="0.01" ${isEdit ? '' : 'readonly'}>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="isActive" ${customer?.isActive !== false ? 'checked' : ''}>
                                        <label class="form-check-label">عميل نشط</label>
                                    </div>
                                </div>

                                ${isEdit ? `<input type="hidden" name="customerId" value="${customer.id}">` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        `;
    },

    /**
     * تبديل حقول العميل حسب النوع
     */
    toggleCustomerFields: function() {
        const typeSelect = document.querySelector('select[name="type"]');
        const taxNumberField = document.getElementById('taxNumberField');

        if (typeSelect && taxNumberField) {
            taxNumberField.style.display = typeSelect.value === 'corporate' ? 'block' : 'none';
        }
    },

    /**
     * حفظ عميل جديد
     */
    saveNewCustomer: function() {
        const form = document.getElementById('customerForm');
        const formData = new FormData(form);

        try {
            const customerData = {
                id: this.generateId(),
                name: formData.get('name'),
                email: formData.get('email'),
                phone: formData.get('phone'),
                address: formData.get('address'),
                type: formData.get('type'),
                taxNumber: formData.get('taxNumber'),
                creditLimit: parseFloat(formData.get('creditLimit')) || 0,
                balance: parseFloat(formData.get('balance')) || 0,
                isActive: formData.has('isActive'),
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // التحقق من صحة البيانات
            if (!customerData.name || !customerData.phone) {
                throw new Error('يرجى ملء جميع الحقول المطلوبة');
            }

            // التحقق من عدم تكرار البريد الإلكتروني
            if (customerData.email) {
                const existingCustomer = Object.values(this.data.customers || {})
                    .find(c => c.email === customerData.email);
                if (existingCustomer) {
                    throw new Error('البريد الإلكتروني مستخدم بالفعل');
                }
            }

            // حفظ العميل
            this.data.customers[customerData.id] = customerData;
            this.saveSalesData();

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('newCustomerModal'));
            modal.hide();

            // عرض رسالة نجاح
            this.showMessage('تم إضافة العميل بنجاح', 'success');

            // تحديث العرض
            this.refreshData();

        } catch (error) {
            this.showMessage('خطأ في حفظ العميل: ' + error.message, 'error');
        }
    },

    /**
     * عرض تفاصيل العميل
     */
    viewCustomer: function(customerId) {
        const customer = this.data.customers[customerId];
        if (!customer) {
            this.showMessage('العميل غير موجود', 'error');
            return;
        }

        // حساب إحصائيات العميل
        const customerInvoices = Object.values(this.data.invoices || {})
            .filter(invoice => invoice.customerId === customerId);

        const totalSales = customerInvoices.reduce((sum, invoice) => sum + (invoice.total || 0), 0);
        const paidInvoices = customerInvoices.filter(invoice => invoice.status === 'paid').length;

        const modalHTML = `
            <div class="modal fade" id="viewCustomerModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-user me-2"></i>تفاصيل العميل: ${customer.name}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <!-- معلومات العميل -->
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-info-circle me-2"></i>المعلومات الأساسية</h6>
                                        </div>
                                        <div class="card-body">
                                            <table class="table table-borderless">
                                                <tr>
                                                    <td><strong>الاسم:</strong></td>
                                                    <td>${customer.name}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>النوع:</strong></td>
                                                    <td>${customer.type === 'corporate' ? 'شركة' : 'فرد'}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>البريد الإلكتروني:</strong></td>
                                                    <td>${customer.email || '-'}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>الهاتف:</strong></td>
                                                    <td>${customer.phone || '-'}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>العنوان:</strong></td>
                                                    <td>${customer.address || '-'}</td>
                                                </tr>
                                                ${customer.taxNumber ? `
                                                <tr>
                                                    <td><strong>الرقم الضريبي:</strong></td>
                                                    <td>${customer.taxNumber}</td>
                                                </tr>
                                                ` : ''}
                                                <tr>
                                                    <td><strong>الحالة:</strong></td>
                                                    <td>
                                                        <span class="badge bg-${customer.isActive ? 'success' : 'secondary'}">
                                                            ${customer.isActive ? 'نشط' : 'غير نشط'}
                                                        </span>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <!-- الإحصائيات المالية -->
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-chart-bar me-2"></i>الإحصائيات المالية</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row text-center">
                                                <div class="col-6 mb-3">
                                                    <div class="border rounded p-3">
                                                        <h4 class="text-primary">${this.formatAmount(customer.balance || 0)}</h4>
                                                        <small class="text-muted">الرصيد الحالي</small>
                                                    </div>
                                                </div>
                                                <div class="col-6 mb-3">
                                                    <div class="border rounded p-3">
                                                        <h4 class="text-info">${this.formatAmount(customer.creditLimit || 0)}</h4>
                                                        <small class="text-muted">الحد الائتماني</small>
                                                    </div>
                                                </div>
                                                <div class="col-6 mb-3">
                                                    <div class="border rounded p-3">
                                                        <h4 class="text-success">${this.formatAmount(totalSales)}</h4>
                                                        <small class="text-muted">إجمالي المبيعات</small>
                                                    </div>
                                                </div>
                                                <div class="col-6 mb-3">
                                                    <div class="border rounded p-3">
                                                        <h4 class="text-warning">${customerInvoices.length}</h4>
                                                        <small class="text-muted">عدد الفواتير</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- آخر الفواتير -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h6><i class="fas fa-file-invoice me-2"></i>آخر الفواتير</h6>
                                </div>
                                <div class="card-body">
                                    ${customerInvoices.length === 0 ?
                                        '<p class="text-muted text-center">لا توجد فواتير لهذا العميل</p>' :
                                        this.renderCustomerInvoicesTable(customerInvoices.slice(0, 5))
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-outline-primary" onclick="window.SalesComponent.editCustomer('${customer.id}'); bootstrap.Modal.getInstance(document.getElementById('viewCustomerModal')).hide();">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.createInvoiceForCustomer('${customer.id}'); bootstrap.Modal.getInstance(document.getElementById('viewCustomerModal')).hide();">
                                <i class="fas fa-plus me-1"></i>فاتورة جديدة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('viewCustomerModal'));
        modal.show();

        document.getElementById('viewCustomerModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * عرض جدول فواتير العميل
     */
    renderCustomerInvoicesTable: function(invoices) {
        return `
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>التاريخ</th>
                            <th>المبلغ</th>
                            <th>الحالة</th>
                            <th>إجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${invoices.map(invoice => `
                            <tr>
                                <td>${invoice.number}</td>
                                <td>${new Date(invoice.date).toLocaleDateString('ar-SA')}</td>
                                <td>${this.formatAmount(invoice.total)}</td>
                                <td>
                                    <span class="badge bg-${this.getInvoiceStatusColor(invoice.status)}">
                                        ${this.getInvoiceStatusLabel(invoice.status)}
                                    </span>
                                </td>
                                <td>
                                    <button class="btn btn-outline-primary btn-sm" onclick="window.SalesComponent.viewInvoice('${invoice.id}')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    },

    /**
     * فلترة العملاء
     */
    filterCustomers: function() {
        const searchTerm = document.getElementById('customerSearch')?.value.toLowerCase() || '';
        const typeFilter = document.getElementById('customerTypeFilter')?.value || '';
        const statusFilter = document.getElementById('customerStatusFilter')?.value || '';

        let filteredCustomers = Object.values(this.data.customers || {});

        // فلترة بالبحث
        if (searchTerm) {
            filteredCustomers = filteredCustomers.filter(customer =>
                customer.name.toLowerCase().includes(searchTerm) ||
                customer.email?.toLowerCase().includes(searchTerm)
            );
        }

        // فلترة بالنوع
        if (typeFilter) {
            filteredCustomers = filteredCustomers.filter(customer => customer.type === typeFilter);
        }

        // فلترة بالحالة
        if (statusFilter) {
            const isActive = statusFilter === 'active';
            filteredCustomers = filteredCustomers.filter(customer => customer.isActive === isActive);
        }

        // ترتيب النتائج
        filteredCustomers.sort((a, b) => a.name.localeCompare(b.name));

        // تحديث العرض
        const container = document.getElementById('customersContainer');
        if (container) {
            container.innerHTML = filteredCustomers.length === 0 ?
                '<div class="col-12"><div class="text-center py-4"><p class="text-muted">لا يوجد عملاء يطابقون معايير البحث</p></div></div>' :
                this.renderCustomersGrid(filteredCustomers);
        }
    },

    /**
     * مسح فلاتر العملاء
     */
    clearCustomerFilters: function() {
        document.getElementById('customerSearch').value = '';
        document.getElementById('customerTypeFilter').value = '';
        document.getElementById('customerStatusFilter').value = '';
        this.filterCustomers();
    },

    /**
     * عرض إدارة المنتجات والخدمات
     */
    renderProducts: function() {
        const products = Object.values(this.data.products || {})
            .sort((a, b) => a.name.localeCompare(b.name));

        return `
            <div class="products-management">
                <!-- شريط الأدوات -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-0">
                                    <i class="fas fa-box me-2"></i>إدارة المنتجات والخدمات
                                </h5>
                            </div>
                            <div class="col-md-6 text-end">
                                <button class="btn btn-primary" onclick="window.SalesComponent.showNewProductModal()">
                                    <i class="fas fa-plus me-1"></i>منتج/خدمة جديدة
                                </button>
                                <button class="btn btn-outline-secondary" onclick="window.SalesComponent.exportProducts()">
                                    <i class="fas fa-download me-1"></i>تصدير
                                </button>
                            </div>
                        </div>

                        <!-- فلاتر البحث -->
                        <div class="row mt-3">
                            <div class="col-md-4">
                                <input type="text" class="form-control" placeholder="البحث بالاسم..."
                                       id="productSearch" onkeyup="window.SalesComponent.filterProducts()">
                            </div>
                            <div class="col-md-3">
                                <select class="form-control" id="categoryFilter" onchange="window.SalesComponent.filterProducts()">
                                    <option value="">جميع التصنيفات</option>
                                    <option value="flights">تذاكر الطيران</option>
                                    <option value="hotels">الفنادق</option>
                                    <option value="visas">التأشيرات</option>
                                    <option value="transport">النقل</option>
                                    <option value="tours">الجولات السياحية</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-control" id="typeFilter" onchange="window.SalesComponent.filterProducts()">
                                    <option value="">جميع الأنواع</option>
                                    <option value="service">خدمة</option>
                                    <option value="product">منتج</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-outline-primary w-100" onclick="window.SalesComponent.clearProductFilters()">
                                    <i class="fas fa-times me-1"></i>مسح
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قائمة المنتجات -->
                <div class="row" id="productsContainer">
                    ${products.length === 0 ? this.renderEmptyProducts() : this.renderProductsGrid(products)}
                </div>
            </div>
        `;
    },

    /**
     * عرض رسالة عدم وجود منتجات
     */
    renderEmptyProducts: function() {
        return `
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-box fa-5x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد منتجات أو خدمات حتى الآن</h4>
                    <p class="text-muted">ابدأ بإضافة أول منتج أو خدمة</p>
                    <button class="btn btn-primary btn-lg" onclick="window.SalesComponent.showNewProductModal()">
                        <i class="fas fa-plus me-2"></i>إضافة منتج/خدمة جديدة
                    </button>
                </div>
            </div>
        `;
    },

    /**
     * عرض شبكة المنتجات
     */
    renderProductsGrid: function(products) {
        return products.map(product => `
            <div class="col-md-6 col-lg-4 mb-4 product-card" data-product-id="${product.id}">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div class="product-icon">
                                <i class="fas fa-${this.getProductIcon(product.category)} fa-2x text-primary"></i>
                            </div>
                            <div class="product-status">
                                <span class="badge bg-${product.isActive ? 'success' : 'secondary'}">
                                    ${product.isActive ? 'نشط' : 'غير نشط'}
                                </span>
                            </div>
                        </div>

                        <h6 class="card-title">${product.name}</h6>
                        <p class="text-muted small mb-2">
                            <i class="fas fa-tag me-1"></i>
                            ${this.getCategoryLabel(product.category)}
                        </p>

                        <p class="text-muted small mb-2">
                            <i class="fas fa-${product.type === 'service' ? 'cogs' : 'box'} me-1"></i>
                            ${product.type === 'service' ? 'خدمة' : 'منتج'}
                        </p>

                        ${product.description ? `
                            <p class="text-muted small mb-3">${product.description.substring(0, 80)}${product.description.length > 80 ? '...' : ''}</p>
                        ` : ''}

                        <div class="product-pricing mt-3">
                            <div class="row text-center">
                                <div class="col-6">
                                    <small class="text-muted">سعر البيع</small>
                                    <div class="fw-bold text-success">${this.formatAmount(product.price || 0)}</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">التكلفة</small>
                                    <div class="fw-bold text-info">${this.formatAmount(product.cost || 0)}</div>
                                </div>
                            </div>
                            <div class="text-center mt-2">
                                <small class="text-muted">الربح: </small>
                                <span class="fw-bold text-primary">${this.formatAmount((product.price || 0) - (product.cost || 0))}</span>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer bg-transparent">
                        <div class="btn-group w-100">
                            <button class="btn btn-outline-primary btn-sm" onclick="window.SalesComponent.viewProduct('${product.id}')" title="عرض">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="window.SalesComponent.editProduct('${product.id}')" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-info btn-sm" onclick="window.SalesComponent.duplicateProduct('${product.id}')" title="نسخ">
                                <i class="fas fa-copy"></i>
                            </button>
                            <button class="btn btn-outline-danger btn-sm" onclick="window.SalesComponent.deleteProduct('${product.id}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    },

    /**
     * الحصول على أيقونة المنتج حسب التصنيف
     */
    getProductIcon: function(category) {
        const icons = {
            'flights': 'plane',
            'hotels': 'bed',
            'visas': 'passport',
            'transport': 'bus',
            'tours': 'map-marked-alt',
            'other': 'box'
        };
        return icons[category] || 'box';
    },

    /**
     * الحصول على تسمية التصنيف
     */
    getCategoryLabel: function(category) {
        const labels = {
            'flights': 'تذاكر الطيران',
            'hotels': 'الفنادق',
            'visas': 'التأشيرات',
            'transport': 'النقل',
            'tours': 'الجولات السياحية',
            'other': 'أخرى'
        };
        return labels[category] || 'غير محدد';
    },

    /**
     * عرض نافذة منتج/خدمة جديدة
     */
    showNewProductModal: function() {
        const modalHTML = `
            <div class="modal fade" id="newProductModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-plus me-2"></i>إضافة منتج/خدمة جديدة
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${this.renderProductForm()}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveNewProduct()">
                                <i class="fas fa-save me-1"></i>حفظ المنتج/الخدمة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('newProductModal'));
        modal.show();

        document.getElementById('newProductModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * عرض نموذج المنتج/الخدمة
     */
    renderProductForm: function(product = null) {
        const isEdit = product !== null;

        return `
            <form id="productForm">
                <div class="row">
                    <!-- المعلومات الأساسية -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-info-circle me-2"></i>المعلومات الأساسية</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">النوع *</label>
                                    <select class="form-control" name="type" required>
                                        <option value="service" ${product?.type === 'service' ? 'selected' : ''}>خدمة</option>
                                        <option value="product" ${product?.type === 'product' ? 'selected' : ''}>منتج</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الاسم بالعربية *</label>
                                    <input type="text" class="form-control" name="name" value="${product?.name || ''}" required>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الاسم بالإنجليزية</label>
                                    <input type="text" class="form-control" name="nameEn" value="${product?.nameEn || ''}">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">التصنيف *</label>
                                    <select class="form-control" name="category" required>
                                        <option value="flights" ${product?.category === 'flights' ? 'selected' : ''}>تذاكر الطيران</option>
                                        <option value="hotels" ${product?.category === 'hotels' ? 'selected' : ''}>الفنادق</option>
                                        <option value="visas" ${product?.category === 'visas' ? 'selected' : ''}>التأشيرات</option>
                                        <option value="transport" ${product?.category === 'transport' ? 'selected' : ''}>النقل</option>
                                        <option value="tours" ${product?.category === 'tours' ? 'selected' : ''}>الجولات السياحية</option>
                                        <option value="other" ${product?.category === 'other' ? 'selected' : ''}>أخرى</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الوصف</label>
                                    <textarea class="form-control" name="description" rows="3">${product?.description || ''}</textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- المعلومات المالية -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-money-bill me-2"></i>المعلومات المالية</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">سعر البيع *</label>
                                    <input type="number" class="form-control" name="price" value="${product?.price || 0}"
                                           min="0" step="0.01" required onchange="window.SalesComponent.calculateProfit()">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">التكلفة</label>
                                    <input type="number" class="form-control" name="cost" value="${product?.cost || 0}"
                                           min="0" step="0.01" onchange="window.SalesComponent.calculateProfit()">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الربح المتوقع</label>
                                    <input type="number" class="form-control" id="profitAmount" readonly>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="taxable" ${product?.taxable !== false ? 'checked' : ''}>
                                        <label class="form-check-label">خاضع للضريبة</label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="isActive" ${product?.isActive !== false ? 'checked' : ''}>
                                        <label class="form-check-label">منتج/خدمة نشطة</label>
                                    </div>
                                </div>

                                ${isEdit ? `<input type="hidden" name="productId" value="${product.id}">` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        `;
    },

    /**
     * حساب الربح
     */
    calculateProfit: function() {
        const priceInput = document.querySelector('input[name="price"]');
        const costInput = document.querySelector('input[name="cost"]');
        const profitInput = document.getElementById('profitAmount');

        if (priceInput && costInput && profitInput) {
            const price = parseFloat(priceInput.value) || 0;
            const cost = parseFloat(costInput.value) || 0;
            const profit = price - cost;
            profitInput.value = profit.toFixed(2);
        }
    },

    /**
     * حفظ منتج/خدمة جديدة
     */
    saveNewProduct: function() {
        const form = document.getElementById('productForm');
        const formData = new FormData(form);

        try {
            const productData = {
                id: this.generateId(),
                name: formData.get('name'),
                nameEn: formData.get('nameEn'),
                type: formData.get('type'),
                category: formData.get('category'),
                description: formData.get('description'),
                price: parseFloat(formData.get('price')) || 0,
                cost: parseFloat(formData.get('cost')) || 0,
                taxable: formData.has('taxable'),
                isActive: formData.has('isActive'),
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // التحقق من صحة البيانات
            if (!productData.name || !productData.category || productData.price <= 0) {
                throw new Error('يرجى ملء جميع الحقول المطلوبة');
            }

            // حفظ المنتج
            this.data.products[productData.id] = productData;
            this.saveSalesData();

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('newProductModal'));
            modal.hide();

            // عرض رسالة نجاح
            this.showMessage('تم إضافة المنتج/الخدمة بنجاح', 'success');

            // تحديث العرض
            this.refreshData();

        } catch (error) {
            this.showMessage('خطأ في حفظ المنتج/الخدمة: ' + error.message, 'error');
        }
    },

    /**
     * فلترة المنتجات
     */
    filterProducts: function() {
        const searchTerm = document.getElementById('productSearch')?.value.toLowerCase() || '';
        const categoryFilter = document.getElementById('categoryFilter')?.value || '';
        const typeFilter = document.getElementById('typeFilter')?.value || '';

        let filteredProducts = Object.values(this.data.products || {});

        // فلترة بالبحث
        if (searchTerm) {
            filteredProducts = filteredProducts.filter(product =>
                product.name.toLowerCase().includes(searchTerm) ||
                product.nameEn?.toLowerCase().includes(searchTerm)
            );
        }

        // فلترة بالتصنيف
        if (categoryFilter) {
            filteredProducts = filteredProducts.filter(product => product.category === categoryFilter);
        }

        // فلترة بالنوع
        if (typeFilter) {
            filteredProducts = filteredProducts.filter(product => product.type === typeFilter);
        }

        // ترتيب النتائج
        filteredProducts.sort((a, b) => a.name.localeCompare(b.name));

        // تحديث العرض
        const container = document.getElementById('productsContainer');
        if (container) {
            container.innerHTML = filteredProducts.length === 0 ?
                '<div class="col-12"><div class="text-center py-4"><p class="text-muted">لا توجد منتجات تطابق معايير البحث</p></div></div>' :
                this.renderProductsGrid(filteredProducts);
        }
    },

    /**
     * مسح فلاتر المنتجات
     */
    clearProductFilters: function() {
        document.getElementById('productSearch').value = '';
        document.getElementById('categoryFilter').value = '';
        document.getElementById('typeFilter').value = '';
        this.filterProducts();
    },

    /**
     * عرض التقارير
     */
    renderReports: function() {
        return `
            <div class="reports-management">
                <!-- شريط الأدوات -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-bar me-2"></i>تقارير المبيعات
                                </h5>
                            </div>
                            <div class="col-md-6 text-end">
                                <button class="btn btn-primary" onclick="window.SalesComponent.generateCustomReport()">
                                    <i class="fas fa-plus me-1"></i>تقرير مخصص
                                </button>
                                <button class="btn btn-outline-secondary" onclick="window.SalesComponent.exportAllReports()">
                                    <i class="fas fa-download me-1"></i>تصدير الكل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- بطاقات التقارير السريعة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-calendar-day fa-3x mb-3"></i>
                                <h5>تقرير يومي</h5>
                                <p>مبيعات اليوم الحالي</p>
                                <button class="btn btn-light btn-sm" onclick="window.SalesComponent.generateDailyReport()">
                                    عرض التقرير
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-calendar-week fa-3x mb-3"></i>
                                <h5>تقرير أسبوعي</h5>
                                <p>مبيعات آخر 7 أيام</p>
                                <button class="btn btn-light btn-sm" onclick="window.SalesComponent.generateWeeklyReport()">
                                    عرض التقرير
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-calendar-alt fa-3x mb-3"></i>
                                <h5>تقرير شهري</h5>
                                <p>مبيعات الشهر الحالي</p>
                                <button class="btn btn-light btn-sm" onclick="window.SalesComponent.generateMonthlyReport()">
                                    عرض التقرير
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-pie fa-3x mb-3"></i>
                                <h5>تحليل المنتجات</h5>
                                <p>أداء المنتجات والخدمات</p>
                                <button class="btn btn-light btn-sm" onclick="window.SalesComponent.generateProductsReport()">
                                    عرض التقرير
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- التقارير التفصيلية -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-users me-2"></i>تقارير العملاء</h6>
                            </div>
                            <div class="card-body">
                                <div class="list-group list-group-flush">
                                    <a href="#" class="list-group-item list-group-item-action" onclick="window.SalesComponent.generateTopCustomersReport()">
                                        <i class="fas fa-crown me-2"></i>أفضل العملاء
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action" onclick="window.SalesComponent.generateCustomerBalancesReport()">
                                        <i class="fas fa-balance-scale me-2"></i>أرصدة العملاء
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action" onclick="window.SalesComponent.generateNewCustomersReport()">
                                        <i class="fas fa-user-plus me-2"></i>العملاء الجدد
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-file-invoice me-2"></i>تقارير الفواتير</h6>
                            </div>
                            <div class="card-body">
                                <div class="list-group list-group-flush">
                                    <a href="#" class="list-group-item list-group-item-action" onclick="window.SalesComponent.generatePaidInvoicesReport()">
                                        <i class="fas fa-check-circle me-2"></i>الفواتير المدفوعة
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action" onclick="window.SalesComponent.generatePendingInvoicesReport()">
                                        <i class="fas fa-clock me-2"></i>الفواتير المعلقة
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action" onclick="window.SalesComponent.generateOverdueInvoicesReport()">
                                        <i class="fas fa-exclamation-triangle me-2"></i>الفواتير المتأخرة
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- منطقة عرض التقرير -->
                <div class="card mt-4" id="reportContainer" style="display: none;">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 id="reportTitle">تقرير المبيعات</h6>
                            <div>
                                <button class="btn btn-outline-primary btn-sm" onclick="window.SalesComponent.printReport()">
                                    <i class="fas fa-print me-1"></i>طباعة
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="window.SalesComponent.exportReport()">
                                    <i class="fas fa-download me-1"></i>تصدير
                                </button>
                                <button class="btn btn-outline-danger btn-sm" onclick="window.SalesComponent.closeReport()">
                                    <i class="fas fa-times me-1"></i>إغلاق
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body" id="reportContent">
                        <!-- محتوى التقرير سيظهر هنا -->
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * إنشاء تقرير يومي
     */
    generateDailyReport: function() {
        const today = new Date().toISOString().split('T')[0];
        const todayInvoices = Object.values(this.data.invoices || {})
            .filter(invoice => invoice.date === today);

        const reportData = {
            title: `تقرير المبيعات اليومية - ${new Date().toLocaleDateString('ar-SA')}`,
            totalSales: todayInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0),
            totalInvoices: todayInvoices.length,
            paidInvoices: todayInvoices.filter(inv => inv.status === 'paid').length,
            pendingInvoices: todayInvoices.filter(inv => inv.status === 'sent').length,
            invoices: todayInvoices
        };

        this.displayReport(reportData.title, this.renderDailyReportContent(reportData));
    },

    /**
     * عرض محتوى التقرير اليومي
     */
    renderDailyReportContent: function(data) {
        return `
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="text-center p-3 border rounded">
                        <h4 class="text-primary">${this.formatAmount(data.totalSales)}</h4>
                        <small class="text-muted">إجمالي المبيعات</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center p-3 border rounded">
                        <h4 class="text-info">${data.totalInvoices}</h4>
                        <small class="text-muted">عدد الفواتير</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center p-3 border rounded">
                        <h4 class="text-success">${data.paidInvoices}</h4>
                        <small class="text-muted">فواتير مدفوعة</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center p-3 border rounded">
                        <h4 class="text-warning">${data.pendingInvoices}</h4>
                        <small class="text-muted">فواتير معلقة</small>
                    </div>
                </div>
            </div>

            ${data.invoices.length > 0 ? `
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                                <th>الوقت</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${data.invoices.map(invoice => `
                                <tr>
                                    <td>${invoice.number}</td>
                                    <td>${this.data.customers[invoice.customerId]?.name || 'غير محدد'}</td>
                                    <td>${this.formatAmount(invoice.total)}</td>
                                    <td>
                                        <span class="badge bg-${this.getInvoiceStatusColor(invoice.status)}">
                                            ${this.getInvoiceStatusLabel(invoice.status)}
                                        </span>
                                    </td>
                                    <td>${new Date(invoice.createdAt).toLocaleTimeString('ar-SA')}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            ` : '<p class="text-center text-muted">لا توجد مبيعات اليوم</p>'}
        `;
    },

    /**
     * عرض التقرير
     */
    displayReport: function(title, content) {
        document.getElementById('reportTitle').textContent = title;
        document.getElementById('reportContent').innerHTML = content;
        document.getElementById('reportContainer').style.display = 'block';

        // التمرير إلى التقرير
        document.getElementById('reportContainer').scrollIntoView({ behavior: 'smooth' });
    },

    /**
     * إغلاق التقرير
     */
    closeReport: function() {
        document.getElementById('reportContainer').style.display = 'none';
    },

    /**
     * عرض الإعدادات
     */
    renderSettings: function() {
        return `
            <div class="settings-management">
                <!-- شريط الأدوات -->
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="mb-0">
                            <i class="fas fa-cog me-2"></i>إعدادات نظام المبيعات
                        </h5>
                    </div>
                </div>

                <div class="row">
                    <!-- الإعدادات العامة -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-sliders-h me-2"></i>الإعدادات العامة</h6>
                            </div>
                            <div class="card-body">
                                <form id="generalSettingsForm">
                                    <div class="mb-3">
                                        <label class="form-label">العملة</label>
                                        <select class="form-control" name="currency">
                                            <option value="SAR" ${this.data.settings.currency === 'SAR' ? 'selected' : ''}>ريال سعودي (SAR)</option>
                                            <option value="USD" ${this.data.settings.currency === 'USD' ? 'selected' : ''}>دولار أمريكي (USD)</option>
                                            <option value="EUR" ${this.data.settings.currency === 'EUR' ? 'selected' : ''}>يورو (EUR)</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">معدل الضريبة (%)</label>
                                        <input type="number" class="form-control" name="taxRate"
                                               value="${(this.data.settings.taxRate * 100).toFixed(2)}"
                                               min="0" max="100" step="0.01">
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">بادئة رقم الفاتورة</label>
                                        <input type="text" class="form-control" name="invoicePrefix"
                                               value="${this.data.settings.invoicePrefix}">
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">رقم الفاتورة التالي</label>
                                        <input type="number" class="form-control" name="nextInvoiceNumber"
                                               value="${this.data.settings.nextInvoiceNumber}" min="1">
                                    </div>

                                    <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveGeneralSettings()">
                                        <i class="fas fa-save me-1"></i>حفظ الإعدادات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات الشركة -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-building me-2"></i>معلومات الشركة</h6>
                            </div>
                            <div class="card-body">
                                <form id="companySettingsForm">
                                    <div class="mb-3">
                                        <label class="form-label">اسم الشركة</label>
                                        <input type="text" class="form-control" name="companyName"
                                               value="${this.data.settings.companyName || 'قمة الوعد للسفريات'}">
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">عنوان الشركة</label>
                                        <textarea class="form-control" name="companyAddress" rows="3">${this.data.settings.companyAddress || 'المملكة العربية السعودية'}</textarea>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">الرقم الضريبي</label>
                                        <input type="text" class="form-control" name="companyTaxNumber"
                                               value="${this.data.settings.companyTaxNumber || '*********'}">
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" name="companyEmail"
                                               value="${this.data.settings.companyEmail || '<EMAIL>'}">
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" name="companyPhone"
                                               value="${this.data.settings.companyPhone || '+966501234567'}">
                                    </div>

                                    <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveCompanySettings()">
                                        <i class="fas fa-save me-1"></i>حفظ المعلومات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أدوات النظام -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6><i class="fas fa-tools me-2"></i>أدوات النظام</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center p-3 border rounded">
                                    <i class="fas fa-download fa-2x text-primary mb-2"></i>
                                    <h6>نسخ احتياطي</h6>
                                    <button class="btn btn-outline-primary btn-sm" onclick="window.SalesComponent.exportBackup()">
                                        تصدير البيانات
                                    </button>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="text-center p-3 border rounded">
                                    <i class="fas fa-upload fa-2x text-success mb-2"></i>
                                    <h6>استيراد البيانات</h6>
                                    <input type="file" id="importFile" accept=".json" style="display: none;" onchange="window.SalesComponent.importBackup()">
                                    <button class="btn btn-outline-success btn-sm" onclick="document.getElementById('importFile').click()">
                                        استيراد البيانات
                                    </button>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="text-center p-3 border rounded">
                                    <i class="fas fa-trash fa-2x text-warning mb-2"></i>
                                    <h6>مسح البيانات</h6>
                                    <button class="btn btn-outline-warning btn-sm" onclick="window.SalesComponent.clearAllData()">
                                        مسح الكل
                                    </button>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="text-center p-3 border rounded">
                                    <i class="fas fa-sync fa-2x text-info mb-2"></i>
                                    <h6>إعادة تعيين</h6>
                                    <button class="btn btn-outline-info btn-sm" onclick="window.SalesComponent.resetToDefaults()">
                                        القيم الافتراضية
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات النظام -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6><i class="fas fa-chart-bar me-2"></i>إحصائيات النظام</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <h4 class="text-primary">${Object.keys(this.data.invoices || {}).length}</h4>
                                <small class="text-muted">إجمالي الفواتير</small>
                            </div>
                            <div class="col-md-3">
                                <h4 class="text-success">${Object.keys(this.data.customers || {}).length}</h4>
                                <small class="text-muted">إجمالي العملاء</small>
                            </div>
                            <div class="col-md-3">
                                <h4 class="text-info">${Object.keys(this.data.products || {}).length}</h4>
                                <small class="text-muted">إجمالي المنتجات</small>
                            </div>
                            <div class="col-md-3">
                                <h4 class="text-warning">${this.formatAmount(Object.values(this.data.invoices || {}).reduce((sum, inv) => sum + (inv.total || 0), 0))}</h4>
                                <small class="text-muted">إجمالي المبيعات</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * حفظ الإعدادات العامة
     */
    saveGeneralSettings: function() {
        const form = document.getElementById('generalSettingsForm');
        const formData = new FormData(form);

        try {
            this.data.settings.currency = formData.get('currency');
            this.data.settings.taxRate = parseFloat(formData.get('taxRate')) / 100;
            this.data.settings.invoicePrefix = formData.get('invoicePrefix');
            this.data.settings.nextInvoiceNumber = parseInt(formData.get('nextInvoiceNumber'));

            this.saveSalesData();
            this.showMessage('تم حفظ الإعدادات بنجاح', 'success');
        } catch (error) {
            this.showMessage('خطأ في حفظ الإعدادات: ' + error.message, 'error');
        }
    },

    /**
     * حفظ معلومات الشركة
     */
    saveCompanySettings: function() {
        const form = document.getElementById('companySettingsForm');
        const formData = new FormData(form);

        try {
            this.data.settings.companyName = formData.get('companyName');
            this.data.settings.companyAddress = formData.get('companyAddress');
            this.data.settings.companyTaxNumber = formData.get('companyTaxNumber');
            this.data.settings.companyEmail = formData.get('companyEmail');
            this.data.settings.companyPhone = formData.get('companyPhone');

            this.saveSalesData();
            this.showMessage('تم حفظ معلومات الشركة بنجاح', 'success');
        } catch (error) {
            this.showMessage('خطأ في حفظ معلومات الشركة: ' + error.message, 'error');
        }
    },

    /**
     * تصدير نسخة احتياطية
     */
    exportBackup: function() {
        try {
            const backupData = {
                version: '1.0',
                exportDate: new Date().toISOString(),
                data: this.data
            };

            const dataStr = JSON.stringify(backupData, null, 2);
            const blob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `sales_backup_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            this.showMessage('تم تصدير النسخة الاحتياطية بنجاح', 'success');
        } catch (error) {
            this.showMessage('خطأ في تصدير النسخة الاحتياطية: ' + error.message, 'error');
        }
    },

    /**
     * استيراد نسخة احتياطية
     */
    importBackup: function() {
        const fileInput = document.getElementById('importFile');
        const file = fileInput.files[0];

        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const backupData = JSON.parse(e.target.result);

                if (confirm('هل أنت متأكد من استيراد هذه البيانات؟ سيتم استبدال البيانات الحالية.')) {
                    this.data = backupData.data;
                    this.saveSalesData();
                    this.showMessage('تم استيراد البيانات بنجاح', 'success');
                    this.refreshData();
                }
            } catch (error) {
                this.showMessage('خطأ في استيراد البيانات: ' + error.message, 'error');
            }
        };

        reader.readAsText(file);
        fileInput.value = '';
    },

    /**
     * مسح جميع البيانات
     */
    clearAllData: function() {
        if (confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
            if (confirm('تأكيد أخير: سيتم مسح جميع الفواتير والعملاء والمنتجات!')) {
                localStorage.removeItem('salesData');
                this.data = {
                    currentView: 'dashboard',
                    customers: {},
                    products: {},
                    invoices: {},
                    salesTransactions: {},
                    settings: {
                        taxRate: 0.15,
                        currency: 'SAR',
                        invoicePrefix: 'INV-',
                        nextInvoiceNumber: 1
                    }
                };
                this.createSampleData();
                this.showMessage('تم مسح جميع البيانات وإعادة تعيين النظام', 'success');
                this.refreshData();
            }
        }
    },

    /**
     * إعادة تعيين القيم الافتراضية
     */
    resetToDefaults: function() {
        if (confirm('هل تريد إعادة تعيين الإعدادات للقيم الافتراضية؟')) {
            this.data.settings = {
                taxRate: 0.15,
                currency: 'SAR',
                invoicePrefix: 'INV-',
                nextInvoiceNumber: this.data.settings.nextInvoiceNumber || 1,
                companyName: 'قمة الوعد للسفريات',
                companyAddress: 'المملكة العربية السعودية',
                companyTaxNumber: '*********',
                companyEmail: '<EMAIL>',
                companyPhone: '+966501234567'
            };
            this.saveSalesData();
            this.showMessage('تم إعادة تعيين الإعدادات للقيم الافتراضية', 'success');
            this.refreshData();
        }
    },

    /**
     * تعديل العميل
     */
    editCustomer: function(customerId) {
        const customer = this.data.customers[customerId];
        if (!customer) {
            this.showMessage('العميل غير موجود', 'error');
            return;
        }

        const modalHTML = `
            <div class="modal fade" id="editCustomerModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-edit me-2"></i>تعديل العميل: ${customer.name}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${this.renderCustomerForm(customer)}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveEditedCustomer()">
                                <i class="fas fa-save me-1"></i>حفظ التعديلات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('editCustomerModal'));
        modal.show();

        document.getElementById('editCustomerModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });

        // تهيئة النموذج
        setTimeout(() => this.toggleCustomerFields(), 100);
    },

    /**
     * حفظ تعديلات العميل
     */
    saveEditedCustomer: function() {
        const form = document.getElementById('customerForm');
        const formData = new FormData(form);

        try {
            const customerId = formData.get('customerId');
            const customerData = {
                ...this.data.customers[customerId],
                name: formData.get('name'),
                email: formData.get('email'),
                phone: formData.get('phone'),
                address: formData.get('address'),
                type: formData.get('type'),
                taxNumber: formData.get('taxNumber'),
                creditLimit: parseFloat(formData.get('creditLimit')) || 0,
                balance: parseFloat(formData.get('balance')) || 0,
                isActive: formData.has('isActive'),
                updatedAt: new Date().toISOString()
            };

            // التحقق من صحة البيانات
            if (!customerData.name || !customerData.phone) {
                throw new Error('يرجى ملء جميع الحقول المطلوبة');
            }

            // حفظ التعديلات
            this.data.customers[customerId] = customerData;
            this.saveSalesData();

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('editCustomerModal'));
            modal.hide();

            // عرض رسالة نجاح
            this.showMessage('تم تحديث بيانات العميل بنجاح', 'success');

            // تحديث العرض
            this.refreshData();

        } catch (error) {
            this.showMessage('خطأ في تحديث العميل: ' + error.message, 'error');
        }
    },

    /**
     * حذف العميل
     */
    deleteCustomer: function(customerId) {
        const customer = this.data.customers[customerId];
        if (!customer) {
            this.showMessage('العميل غير موجود', 'error');
            return;
        }

        // التحقق من وجود فواتير للعميل
        const customerInvoices = Object.values(this.data.invoices || {})
            .filter(invoice => invoice.customerId === customerId);

        if (customerInvoices.length > 0) {
            this.showMessage('لا يمكن حذف العميل لوجود فواتير مرتبطة به', 'error');
            return;
        }

        if (confirm(`هل أنت متأكد من حذف العميل "${customer.name}"؟`)) {
            delete this.data.customers[customerId];
            this.saveSalesData();
            this.showMessage('تم حذف العميل بنجاح', 'success');
            this.refreshData();
        }
    },

    /**
     * إنشاء فاتورة للعميل
     */
    createInvoiceForCustomer: function(customerId) {
        // تخزين معرف العميل المحدد
        this.selectedCustomerId = customerId;

        // عرض نافذة الفاتورة الجديدة
        this.showNewInvoiceModal();

        // تحديد العميل في النموذج
        setTimeout(() => {
            const customerSelect = document.querySelector('select[name="customerId"]');
            if (customerSelect) {
                customerSelect.value = customerId;
                this.updateCustomerInfo();
            }
        }, 500);
    },

    /**
     * عرض فواتير العميل
     */
    customerInvoices: function(customerId) {
        const customer = this.data.customers[customerId];
        if (!customer) {
            this.showMessage('العميل غير موجود', 'error');
            return;
        }

        const customerInvoices = Object.values(this.data.invoices || {})
            .filter(invoice => invoice.customerId === customerId)
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        const modalHTML = `
            <div class="modal fade" id="customerInvoicesModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-file-invoice me-2"></i>فواتير العميل: ${customer.name}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${customerInvoices.length === 0 ?
                                '<p class="text-center text-muted">لا توجد فواتير لهذا العميل</p>' :
                                this.renderInvoicesTable(customerInvoices)
                            }
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.createInvoiceForCustomer('${customerId}'); bootstrap.Modal.getInstance(document.getElementById('customerInvoicesModal')).hide();">
                                <i class="fas fa-plus me-1"></i>فاتورة جديدة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('customerInvoicesModal'));
        modal.show();

        document.getElementById('customerInvoicesModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * عرض نافذة تعديل الفاتورة
     */
    showEditInvoiceModal: function(invoice) {
        const modalHTML = `
            <div class="modal fade" id="editInvoiceModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-edit me-2"></i>تعديل الفاتورة ${invoice.number}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${this.renderEditInvoiceForm(invoice)}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveEditedInvoice()">
                                <i class="fas fa-save me-1"></i>حفظ التعديلات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('editInvoiceModal'));
        modal.show();

        document.getElementById('editInvoiceModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });

        // تهيئة النموذج
        this.initializeEditInvoiceForm(invoice);
    },

    /**
     * عرض نموذج تعديل الفاتورة
     */
    renderEditInvoiceForm: function(invoice) {
        const customers = Object.values(this.data.customers || {});
        const products = Object.values(this.data.products || {});

        return `
            <form id="editInvoiceForm">
                <input type="hidden" name="invoiceId" value="${invoice.id}">

                <div class="row">
                    <!-- معلومات الفاتورة -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-info-circle me-2"></i>معلومات الفاتورة</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">رقم الفاتورة</label>
                                    <input type="text" class="form-control" name="invoiceNumber" value="${invoice.number}" readonly>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">تاريخ الفاتورة *</label>
                                    <input type="date" class="form-control" name="invoiceDate" value="${invoice.date}" required>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">تاريخ الاستحقاق</label>
                                    <input type="date" class="form-control" name="dueDate" value="${invoice.dueDate || ''}">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">مرجع الطلب</label>
                                    <input type="text" class="form-control" name="reference" value="${invoice.reference || ''}" placeholder="رقم الطلب أو المرجع">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">حالة الفاتورة</label>
                                    <select class="form-control" name="status">
                                        <option value="draft" ${invoice.status === 'draft' ? 'selected' : ''}>مسودة</option>
                                        <option value="sent" ${invoice.status === 'sent' ? 'selected' : ''}>مرسلة</option>
                                        <option value="paid" ${invoice.status === 'paid' ? 'selected' : ''}>مدفوعة</option>
                                        <option value="overdue" ${invoice.status === 'overdue' ? 'selected' : ''}>متأخرة</option>
                                        <option value="cancelled" ${invoice.status === 'cancelled' ? 'selected' : ''}>ملغية</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات العميل -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-user me-2"></i>معلومات العميل</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">العميل *</label>
                                    <select class="form-control" name="customerId" required onchange="window.SalesComponent.updateCustomerInfo()">
                                        <option value="">اختر العميل</option>
                                        ${customers.map(customer => `
                                            <option value="${customer.id}" ${customer.id === invoice.customerId ? 'selected' : ''}>${customer.name}</option>
                                        `).join('')}
                                    </select>
                                </div>

                                <div id="customerInfo" class="${invoice.customerId ? '' : 'd-none'}">
                                    <div class="mb-2">
                                        <small class="text-muted">البريد الإلكتروني:</small>
                                        <div id="customerEmail">${this.data.customers[invoice.customerId]?.email || '-'}</div>
                                    </div>
                                    <div class="mb-2">
                                        <small class="text-muted">الهاتف:</small>
                                        <div id="customerPhone">${this.data.customers[invoice.customerId]?.phone || '-'}</div>
                                    </div>
                                    <div class="mb-2">
                                        <small class="text-muted">العنوان:</small>
                                        <div id="customerAddress">${this.data.customers[invoice.customerId]?.address || '-'}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- عناصر الفاتورة -->
                <div class="card mt-3">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6><i class="fas fa-list me-2"></i>عناصر الفاتورة</h6>
                            <button type="button" class="btn btn-primary btn-sm" onclick="window.SalesComponent.addEditInvoiceItem()">
                                <i class="fas fa-plus me-1"></i>إضافة عنصر
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th width="30%">المنتج/الخدمة</th>
                                        <th width="15%">الكمية</th>
                                        <th width="15%">السعر</th>
                                        <th width="15%">الخصم</th>
                                        <th width="15%">المجموع</th>
                                        <th width="10%">إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="editInvoiceItems">
                                    <!-- سيتم إضافة العناصر هنا -->
                                </tbody>
                            </table>
                        </div>

                        <!-- ملخص الفاتورة -->
                        <div class="row mt-3">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="3" placeholder="ملاحظات إضافية للفاتورة">${invoice.notes || ''}</textarea>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>المجموع الفرعي:</span>
                                            <span id="editSubtotal">${this.formatAmount(invoice.subtotal)}</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>الخصم:</span>
                                            <span id="editTotalDiscount">${this.formatAmount(invoice.totalDiscount)}</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>الضريبة (${(this.data.settings.taxRate * 100)}%):</span>
                                            <span id="editTaxAmount">${this.formatAmount(invoice.taxAmount)}</span>
                                        </div>
                                        <hr>
                                        <div class="d-flex justify-content-between fw-bold">
                                            <span>الإجمالي:</span>
                                            <span id="editGrandTotal">${this.formatAmount(invoice.total)}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        `;
    },

    /**
     * نسخ الفاتورة
     */
    duplicateInvoice: function(invoiceId) {
        const originalInvoice = this.data.invoices[invoiceId];
        if (!originalInvoice) {
            this.showMessage('الفاتورة غير موجودة', 'error');
            return;
        }

        const newInvoice = {
            ...originalInvoice,
            id: this.generateId(),
            number: `${this.data.settings.invoicePrefix}${this.data.settings.nextInvoiceNumber.toString().padStart(4, '0')}`,
            status: 'draft',
            date: new Date().toISOString().split('T')[0],
            dueDate: '',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        // تحديث رقم الفاتورة التالي
        this.data.settings.nextInvoiceNumber++;

        // حفظ الفاتورة الجديدة
        this.data.invoices[newInvoice.id] = newInvoice;
        this.saveSalesData();

        this.showMessage(`تم نسخ الفاتورة بنجاح. رقم الفاتورة الجديدة: ${newInvoice.number}`, 'success');
        this.refreshData();
    },

    /**
     * إرسال الفاتورة بالبريد الإلكتروني
     */
    sendInvoiceEmail: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        const customer = this.data.customers[invoice.customerId];

        if (!customer?.email) {
            this.showMessage('لا يوجد بريد إلكتروني للعميل', 'error');
            return;
        }

        const modalHTML = `
            <div class="modal fade" id="sendEmailModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-envelope me-2"></i>إرسال الفاتورة بالبريد الإلكتروني
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="emailForm">
                                <div class="mb-3">
                                    <label class="form-label">إلى</label>
                                    <input type="email" class="form-control" name="to" value="${customer.email}" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الموضوع</label>
                                    <input type="text" class="form-control" name="subject"
                                           value="فاتورة رقم ${invoice.number}" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الرسالة</label>
                                    <textarea class="form-control" name="message" rows="5" required>
عزيزي ${customer.name}،

نرسل لك فاتورة رقم ${invoice.number} بتاريخ ${new Date(invoice.date).toLocaleDateString('ar-SA')}.

إجمالي المبلغ: ${this.formatAmount(invoice.total)}

يرجى مراجعة الفاتورة المرفقة والدفع في الموعد المحدد.

شكراً لك،
${this.data.settings.companyName || 'قمة الوعد للسفريات'}
                                    </textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.sendEmail('${invoiceId}')">
                                <i class="fas fa-paper-plane me-1"></i>إرسال
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('sendEmailModal'));
        modal.show();

        document.getElementById('sendEmailModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * إرسال البريد الإلكتروني
     */
    sendEmail: function(invoiceId) {
        // في التطبيق الحقيقي، سيتم إرسال البريد عبر API
        // هنا سنقوم بمحاكاة الإرسال

        const modal = bootstrap.Modal.getInstance(document.getElementById('sendEmailModal'));
        modal.hide();

        // تحديث حالة الفاتورة إلى مرسلة
        if (this.data.invoices[invoiceId]) {
            this.data.invoices[invoiceId].status = 'sent';
            this.data.invoices[invoiceId].updatedAt = new Date().toISOString();
            this.saveSalesData();
        }

        this.showMessage('تم إرسال الفاتورة بالبريد الإلكتروني بنجاح', 'success');
        this.refreshData();
    },

    /**
     * تحميل الفاتورة كـ PDF
     */
    downloadInvoicePDF: function(invoiceId) {
        // في التطبيق الحقيقي، سيتم إنشاء PDF باستخدام مكتبة مثل jsPDF
        // هنا سنقوم بمحاكاة التحميل

        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            this.showMessage('الفاتورة غير موجودة', 'error');
            return;
        }

        // محاكاة تحميل PDF
        this.showMessage(`جاري تحضير ملف PDF للفاتورة ${invoice.number}...`, 'info');

        setTimeout(() => {
            this.showMessage(`تم تحضير ملف PDF للفاتورة ${invoice.number}`, 'success');
            // هنا يمكن إضافة كود تحميل الملف الفعلي
        }, 2000);
    },

    /**
     * إرسال تذكير للفاتورة المتأخرة
     */
    sendReminder: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        const customer = this.data.customers[invoice.customerId];

        if (!customer?.email) {
            this.showMessage('لا يوجد بريد إلكتروني للعميل', 'error');
            return;
        }

        if (confirm(`هل تريد إرسال تذكير للعميل ${customer.name} بخصوص الفاتورة ${invoice.number}؟`)) {
            // محاكاة إرسال التذكير
            this.showMessage('تم إرسال تذكير للعميل بنجاح', 'success');
        }
    },

    /**
     * تحديد الفاتورة كمدفوعة
     */
    markAsPaid: function(invoiceId) {
        if (confirm('هل تريد تحديد هذه الفاتورة كمدفوعة؟')) {
            this.data.invoices[invoiceId].status = 'paid';
            this.data.invoices[invoiceId].updatedAt = new Date().toISOString();
            this.saveSalesData();
            this.showMessage('تم تحديد الفاتورة كمدفوعة', 'success');
            this.refreshData();
        }
    },

    /**
     * إرسال الفاتورة
     */
    sendInvoice: function(invoiceId) {
        if (confirm('هل تريد إرسال هذه الفاتورة؟')) {
            this.data.invoices[invoiceId].status = 'sent';
            this.data.invoices[invoiceId].updatedAt = new Date().toISOString();
            this.saveSalesData();
            this.showMessage('تم إرسال الفاتورة', 'success');
            this.refreshData();
        }
    },

    /**
     * تغيير وضع العرض
     */
    changeViewMode: function(mode) {
        // تحديث أزرار العرض
        document.getElementById('tableViewBtn').classList.toggle('active', mode === 'table');
        document.getElementById('cardsViewBtn').classList.toggle('active', mode === 'cards');

        // تحديث العرض
        if (mode === 'cards') {
            this.renderInvoicesCards();
        } else {
            this.filterInvoices(); // إعادة عرض الجدول
        }
    },

    /**
     * عرض الفواتير كبطاقات
     */
    renderInvoicesCards: function() {
        const invoices = Object.values(this.data.invoices || {});
        const container = document.getElementById('invoicesTableContainer');

        container.innerHTML = `
            <div class="row">
                ${invoices.map(invoice => `
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card invoice-card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <strong>${invoice.number}</strong>
                                <span class="badge bg-${this.getInvoiceStatusColor(invoice.status)}">
                                    ${this.getInvoiceStatusLabel(invoice.status)}
                                </span>
                            </div>
                            <div class="card-body">
                                <h6 class="card-title">${this.data.customers[invoice.customerId]?.name || 'عميل غير محدد'}</h6>
                                <p class="card-text">
                                    <small class="text-muted">التاريخ: ${new Date(invoice.date).toLocaleDateString('ar-SA')}</small><br>
                                    <strong class="text-primary">${this.formatAmount(invoice.total)}</strong>
                                </p>
                                <div class="btn-group w-100">
                                    <button class="btn btn-outline-primary btn-sm" onclick="window.SalesComponent.viewInvoice('${invoice.id}')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="window.SalesComponent.editInvoice('${invoice.id}')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-info btn-sm" onclick="window.SalesComponent.printInvoice('${invoice.id}')">
                                        <i class="fas fa-print"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    },

    /**
     * تحديث معلومات الصفحات
     */
    updatePaginationInfo: function(totalItems) {
        document.getElementById('totalItems').textContent = totalItems;
        // يمكن إضافة المزيد من منطق الصفحات هنا
    },

    /**
     * نسخ الفاتورة
     */
    duplicateInvoice: function(invoiceId) {
        const originalInvoice = this.data.invoices[invoiceId];
        if (!originalInvoice) {
            this.showMessage('الفاتورة غير موجودة', 'error');
            return;
        }

        const newInvoice = {
            ...originalInvoice,
            id: this.generateId(),
            number: `${this.data.settings.invoicePrefix}${this.data.settings.nextInvoiceNumber.toString().padStart(4, '0')}`,
            status: 'draft',
            date: new Date().toISOString().split('T')[0],
            dueDate: '',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        // تحديث رقم الفاتورة التالي
        this.data.settings.nextInvoiceNumber++;

        // حفظ الفاتورة الجديدة
        this.data.invoices[newInvoice.id] = newInvoice;
        this.saveSalesData();

        this.showMessage(`تم نسخ الفاتورة بنجاح. رقم الفاتورة الجديدة: ${newInvoice.number}`, 'success');
        this.refreshData();
    },

    /**
     * إرسال الفاتورة بالبريد الإلكتروني
     */
    sendInvoiceEmail: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        const customer = this.data.customers[invoice.customerId];

        if (!customer?.email) {
            this.showMessage('لا يوجد بريد إلكتروني للعميل', 'error');
            return;
        }

        const modalHTML = `
            <div class="modal fade" id="sendEmailModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-envelope me-2"></i>إرسال الفاتورة بالبريد الإلكتروني
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="emailForm">
                                <div class="mb-3">
                                    <label class="form-label">إلى</label>
                                    <input type="email" class="form-control" name="to" value="${customer.email}" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الموضوع</label>
                                    <input type="text" class="form-control" name="subject"
                                           value="فاتورة رقم ${invoice.number}" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الرسالة</label>
                                    <textarea class="form-control" name="message" rows="5" required>عزيزي ${customer.name}،

نرسل لك فاتورة رقم ${invoice.number} بتاريخ ${new Date(invoice.date).toLocaleDateString('ar-SA')}.

إجمالي المبلغ: ${this.formatAmount(invoice.total)}

يرجى مراجعة الفاتورة المرفقة والدفع في الموعد المحدد.

شكراً لك،
${this.data.settings.companyName || 'قمة الوعد للسفريات'}</textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.sendEmail('${invoiceId}')">
                                <i class="fas fa-paper-plane me-1"></i>إرسال
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('sendEmailModal'));
        modal.show();

        document.getElementById('sendEmailModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * إرسال البريد الإلكتروني
     */
    sendEmail: function(invoiceId) {
        const modal = bootstrap.Modal.getInstance(document.getElementById('sendEmailModal'));
        modal.hide();

        // تحديث حالة الفاتورة إلى مرسلة
        if (this.data.invoices[invoiceId]) {
            this.data.invoices[invoiceId].status = 'sent';
            this.data.invoices[invoiceId].updatedAt = new Date().toISOString();
            this.saveSalesData();
        }

        this.showMessage('تم إرسال الفاتورة بالبريد الإلكتروني بنجاح', 'success');
        this.refreshData();
    },

    /**
     * تحميل الفاتورة كـ PDF
     */
    downloadInvoicePDF: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            this.showMessage('الفاتورة غير موجودة', 'error');
            return;
        }

        this.showMessage(`جاري تحضير ملف PDF للفاتورة ${invoice.number}...`, 'info');

        setTimeout(() => {
            this.showMessage(`تم تحضير ملف PDF للفاتورة ${invoice.number}`, 'success');
        }, 2000);
    },

    /**
     * إرسال تذكير للفاتورة المتأخرة
     */
    sendReminder: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        const customer = this.data.customers[invoice.customerId];

        if (!customer?.email) {
            this.showMessage('لا يوجد بريد إلكتروني للعميل', 'error');
            return;
        }

        if (confirm(`هل تريد إرسال تذكير للعميل ${customer.name} بخصوص الفاتورة ${invoice.number}؟`)) {
            this.showMessage('تم إرسال تذكير للعميل بنجاح', 'success');
        }
    },

    /**
     * تحديد الفاتورة كمدفوعة
     */
    markAsPaid: function(invoiceId) {
        if (confirm('هل تريد تحديد هذه الفاتورة كمدفوعة؟')) {
            this.data.invoices[invoiceId].status = 'paid';
            this.data.invoices[invoiceId].updatedAt = new Date().toISOString();
            this.saveSalesData();
            this.showMessage('تم تحديد الفاتورة كمدفوعة', 'success');
            this.refreshData();
        }
    },

    /**
     * إرسال الفاتورة
     */
    sendInvoice: function(invoiceId) {
        if (confirm('هل تريد إرسال هذه الفاتورة؟')) {
            this.data.invoices[invoiceId].status = 'sent';
            this.data.invoices[invoiceId].updatedAt = new Date().toISOString();
            this.saveSalesData();
            this.showMessage('تم إرسال الفاتورة', 'success');
            this.refreshData();
        }
    },

    /**
     * تغيير وضع العرض
     */
    changeViewMode: function(mode) {
        document.getElementById('tableViewBtn').classList.toggle('active', mode === 'table');
        document.getElementById('cardsViewBtn').classList.toggle('active', mode === 'cards');

        if (mode === 'cards') {
            this.renderInvoicesCards();
        } else {
            this.filterInvoices();
        }
    },

    /**
     * عرض الفواتير كبطاقات
     */
    renderInvoicesCards: function() {
        const invoices = Object.values(this.data.invoices || {});
        const container = document.getElementById('invoicesTableContainer');

        container.innerHTML = `
            <div class="row">
                ${invoices.map(invoice => `
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card invoice-card h-100">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <strong>${invoice.number}</strong>
                                <span class="badge bg-${this.getInvoiceStatusColor(invoice.status)}">
                                    ${this.getInvoiceStatusLabel(invoice.status)}
                                </span>
                            </div>
                            <div class="card-body">
                                <h6 class="card-title">${this.data.customers[invoice.customerId]?.name || 'عميل غير محدد'}</h6>
                                <p class="card-text">
                                    <small class="text-muted">التاريخ: ${new Date(invoice.date).toLocaleDateString('ar-SA')}</small><br>
                                    <strong class="text-primary">${this.formatAmount(invoice.total)}</strong>
                                </p>
                            </div>
                            <div class="card-footer">
                                <div class="btn-group w-100">
                                    <button class="btn btn-outline-primary btn-sm" onclick="window.SalesComponent.viewInvoice('${invoice.id}')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="window.SalesComponent.editInvoice('${invoice.id}')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-info btn-sm" onclick="window.SalesComponent.printInvoice('${invoice.id}')">
                                        <i class="fas fa-print"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    },

    /**
     * طباعة المحدد
     */
    bulkPrint: function() {
        const selectedInvoices = this.getSelectedInvoices();
        if (selectedInvoices.length === 0) {
            this.showMessage('يرجى تحديد فاتورة واحدة على الأقل', 'warning');
            return;
        }

        if (confirm(`هل تريد طباعة ${selectedInvoices.length} فاتورة؟`)) {
            selectedInvoices.forEach(invoiceId => {
                setTimeout(() => this.printInvoice(invoiceId), 1000);
            });
        }
    },

    /**
     * حذف المحدد
     */
    bulkDelete: function() {
        const selectedInvoices = this.getSelectedInvoices();
        if (selectedInvoices.length === 0) {
            this.showMessage('يرجى تحديد فاتورة واحدة على الأقل', 'warning');
            return;
        }

        if (confirm(`هل أنت متأكد من حذف ${selectedInvoices.length} فاتورة؟ هذا الإجراء لا يمكن التراجع عنه.`)) {
            selectedInvoices.forEach(invoiceId => {
                delete this.data.invoices[invoiceId];
            });

            this.saveSalesData();
            this.showMessage(`تم حذف ${selectedInvoices.length} فاتورة`, 'success');
            this.refreshData();
        }
    },

    /**
     * تصدير الفواتير المحددة
     */
    exportInvoicesCSV: function() {
        const invoices = Object.values(this.data.invoices || {});
        if (invoices.length === 0) {
            this.showMessage('لا توجد فواتير للتصدير', 'error');
            return;
        }

        const csvData = [
            ['رقم الفاتورة', 'العميل', 'التاريخ', 'تاريخ الاستحقاق', 'الإجمالي', 'الحالة', 'المرجع', 'الملاحظات']
        ];

        invoices.forEach(invoice => {
            const customer = this.data.customers[invoice.customerId];
            csvData.push([
                invoice.number,
                customer?.name || 'عميل غير محدد',
                invoice.date,
                invoice.dueDate || '',
                invoice.total,
                this.getInvoiceStatusLabel(invoice.status),
                invoice.reference || '',
                invoice.notes || ''
            ]);
        });

        const csvContent = csvData.map(row => row.join(',')).join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `invoices_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();

        this.showMessage('تم تصدير الفواتير بنجاح', 'success');
    },

    /**
     * عرض البحث المتقدم
     */
    showAdvancedSearch: function() {
        const modalHTML = `
            <div class="modal fade" id="advancedSearchModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-search-plus me-2"></i>البحث المتقدم في الفواتير
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="advancedSearchForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رقم الفاتورة</label>
                                            <input type="text" class="form-control" name="invoiceNumber" placeholder="INV-0001">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">المرجع</label>
                                            <input type="text" class="form-control" name="reference" placeholder="رقم المرجع">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">العميل</label>
                                            <select class="form-control" name="customerId">
                                                <option value="">جميع العملاء</option>
                                                ${Object.values(this.data.customers || {}).map(customer => `
                                                    <option value="${customer.id}">${customer.name}</option>
                                                `).join('')}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الحالة</label>
                                            <select class="form-control" name="status">
                                                <option value="">جميع الحالات</option>
                                                <option value="draft">مسودة</option>
                                                <option value="sent">مرسلة</option>
                                                <option value="paid">مدفوعة</option>
                                                <option value="overdue">متأخرة</option>
                                                <option value="cancelled">ملغية</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">المبلغ من</label>
                                            <input type="number" class="form-control" name="amountFrom" placeholder="0">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">المبلغ إلى</label>
                                            <input type="number" class="form-control" name="amountTo" placeholder="999999">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">من تاريخ</label>
                                            <input type="date" class="form-control" name="dateFrom">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">إلى تاريخ</label>
                                            <input type="date" class="form-control" name="dateTo">
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات تحتوي على</label>
                                    <input type="text" class="form-control" name="notesContain" placeholder="نص في الملاحظات">
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-outline-warning" onclick="window.SalesComponent.clearAdvancedSearch()">
                                <i class="fas fa-eraser me-1"></i>مسح
                            </button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.performAdvancedSearch()">
                                <i class="fas fa-search me-1"></i>بحث
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('advancedSearchModal'));
        modal.show();

        document.getElementById('advancedSearchModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * تنفيذ البحث المتقدم
     */
    performAdvancedSearch: function() {
        const form = document.getElementById('advancedSearchForm');
        const formData = new FormData(form);

        let filteredInvoices = Object.values(this.data.invoices || {});

        // تطبيق فلاتر البحث المتقدم
        const invoiceNumber = formData.get('invoiceNumber');
        if (invoiceNumber) {
            filteredInvoices = filteredInvoices.filter(inv =>
                inv.number.toLowerCase().includes(invoiceNumber.toLowerCase())
            );
        }

        const reference = formData.get('reference');
        if (reference) {
            filteredInvoices = filteredInvoices.filter(inv =>
                inv.reference?.toLowerCase().includes(reference.toLowerCase())
            );
        }

        const customerId = formData.get('customerId');
        if (customerId) {
            filteredInvoices = filteredInvoices.filter(inv => inv.customerId === customerId);
        }

        const status = formData.get('status');
        if (status) {
            filteredInvoices = filteredInvoices.filter(inv => inv.status === status);
        }

        const amountFrom = parseFloat(formData.get('amountFrom'));
        if (amountFrom) {
            filteredInvoices = filteredInvoices.filter(inv => inv.total >= amountFrom);
        }

        const amountTo = parseFloat(formData.get('amountTo'));
        if (amountTo) {
            filteredInvoices = filteredInvoices.filter(inv => inv.total <= amountTo);
        }

        const dateFrom = formData.get('dateFrom');
        if (dateFrom) {
            filteredInvoices = filteredInvoices.filter(inv => inv.date >= dateFrom);
        }

        const dateTo = formData.get('dateTo');
        if (dateTo) {
            filteredInvoices = filteredInvoices.filter(inv => inv.date <= dateTo);
        }

        const notesContain = formData.get('notesContain');
        if (notesContain) {
            filteredInvoices = filteredInvoices.filter(inv =>
                inv.notes?.toLowerCase().includes(notesContain.toLowerCase())
            );
        }

        // إغلاق النافذة
        const modal = bootstrap.Modal.getInstance(document.getElementById('advancedSearchModal'));
        modal.hide();

        // تحديث العرض
        this.updateInvoicesDisplay(filteredInvoices);
        this.showMessage(`تم العثور على ${filteredInvoices.length} فاتورة`, 'info');
    },

    /**
     * تحديث البيانات
     */
    refreshData: function() {
        this.loadSalesData();
        const content = document.getElementById('main-content');
        if (content) {
            content.innerHTML = this.renderCurrentView();
        }
    }
};

// تصدير المكون للاستخدام العام
window.SalesComponent = SalesComponent;
