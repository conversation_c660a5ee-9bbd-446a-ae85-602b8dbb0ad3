/**
 * ===================================
 * مكون المبيعات - Sales Component
 * ===================================
 */

const SalesComponent = {
    // بيانات المكون
    data: {
        currentView: 'invoices',
        invoices: {},
        customers: {},
        products: {},
        deletedInvoices: {},
        selectedInvoice: null,
        selectedCustomer: null,
        selectedProduct: null,
        filters: {
            dateFrom: '',
            dateTo: '',
            status: '',
            customer: ''
        },
        settings: {
            taxRate: 0.15,
            currency: 'SAR',
            invoicePrefix: 'INV-',
            nextInvoiceNumber: 1,
            companyName: 'قمة الوعد للسفريات',
            companyAddress: 'المملكة العربية السعودية',
            companyTaxNumber: '*********',
            companyEmail: '<EMAIL>',
            companyPhone: '+966501234567',
            companyWebsite: 'https://qimat-alwaed.com',
            language: 'ar',
            timezone: 'Asia/Riyadh',
            dateFormat: 'ar-SA',
            autoSave: true,
            showNotifications: true,
            enableBackup: true,
            autoCalculateTax: true,
            defaultDueDays: 30,

        }
    },

    /**
     * تهيئة المكون
     */
    init: function() {
        console.log('🚀 تهيئة نظام المبيعات المتقدم...');

        // تحميل البيانات
        this.loadSalesData();
        this.createSampleData();

        // بدء مراقبة التنبيهات
        setTimeout(() => {
            this.startAlertMonitoring();
        }, 2000);

        // إضافة مستمعي الأحداث
        this.setupEventListeners();

        console.log('✅ تم تهيئة نظام المبيعات بنجاح');
    },

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners: function() {
        // حفظ تلقائي عند تغيير البيانات
        if (this.data.settings?.autoSave) {
            setInterval(() => {
                this.saveSalesData();
            }, 30000); // حفظ كل 30 ثانية
        }

        // مستمع لتغيير حجم النافذة
        window.addEventListener('resize', () => {
            this.handleWindowResize();
        });

        // مستمع لإغلاق النافذة (تحذير من فقدان البيانات)
        window.addEventListener('beforeunload', (e) => {
            if (this.hasUnsavedChanges()) {
                e.preventDefault();
                e.returnValue = 'لديك تغييرات غير محفوظة. هل تريد المغادرة؟';
            }
        });
    },

    /**
     * معالجة تغيير حجم النافذة
     */
    handleWindowResize: function() {
        // إعادة تحديد حجم الرسوم البيانية إذا كانت موجودة
        if (window.salesChart) {
            window.salesChart.resize();
        }
    },

    /**
     * فحص وجود تغييرات غير محفوظة
     */
    hasUnsavedChanges: function() {
        // فحص بسيط - يمكن تطويره أكثر
        const currentData = JSON.stringify(this.data);
        const savedData = localStorage.getItem('salesData');
        return currentData !== savedData;
    }

    /**
     * عرض المكون الرئيسي
     */
    render: function(params = {}) {
        this.data.currentView = params.view || 'dashboard';
        
        return `
            <div class="sales-container">
                ${this.renderHeader()}
                ${this.renderNavigation()}
                <div class="sales-content">
                    ${this.renderCurrentView()}
                </div>
            </div>
        `;
    },

    /**
     * عرض رأس الصفحة
     */
    renderHeader: function() {
        return `
            <div class="sales-header">
                <div class="header-title">
                    <h2><i class="fas fa-receipt me-2"></i>نظام المبيعات</h2>
                    <p class="text-muted">إدارة شاملة للفواتير والعملاء والمنتجات</p>
                </div>
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="window.SalesComponent.showCreateInvoiceModal()">
                        <i class="fas fa-plus me-1"></i>فاتورة جديدة
                    </button>
                    <button class="btn btn-outline-success" onclick="window.SalesComponent.showCreateCustomerModal()">
                        <i class="fas fa-user-plus me-1"></i>عميل جديد
                    </button>
                    <button class="btn btn-outline-info" onclick="window.SalesComponent.showCreateProductModal()">
                        <i class="fas fa-box me-1"></i>منتج جديد
                    </button>
                </div>
            </div>
        `;
    },

    /**
     * عرض شريط التنقل
     */
    renderNavigation: function() {
        const navItems = [
            { id: 'dashboard', label: 'لوحة التحكم', icon: 'fas fa-tachometer-alt', color: 'primary' },
            { id: 'invoices', label: 'الفواتير', icon: 'fas fa-file-invoice', color: 'success' },
            { id: 'quotes', label: 'عروض الأسعار', icon: 'fas fa-file-contract', color: 'info' },
            { id: 'customers', label: 'العملاء', icon: 'fas fa-users', color: 'warning' },
            { id: 'products', label: 'المنتجات', icon: 'fas fa-boxes', color: 'secondary' },
            { id: 'inventory', label: 'المخزون', icon: 'fas fa-warehouse', color: 'dark' },
            { id: 'payments', label: 'المدفوعات', icon: 'fas fa-credit-card', color: 'success' },
            { id: 'reports', label: 'التقارير', icon: 'fas fa-chart-bar', color: 'info' },
            { id: 'settings', label: 'الإعدادات', icon: 'fas fa-cog', color: 'secondary' }
        ];

        return `
            <div class="sales-navigation">
                <div class="nav-container">
                    <ul class="nav nav-pills nav-fill">
                        ${navItems.map(item => `
                            <li class="nav-item">
                                <a class="nav-link ${this.data.currentView === item.id ? 'active' : ''}"
                                   href="#" onclick="window.SalesComponent.switchView('${item.id}')"
                                   data-bs-toggle="tooltip" title="${item.label}">
                                    <i class="${item.icon} me-1"></i>
                                    <span class="nav-text">${item.label}</span>
                                    ${this.getNavBadge(item.id)}
                                </a>
                            </li>
                        `).join('')}
                    </ul>
                </div>
            </div>
        `;
    },

    /**
     * الحصول على شارة التنقل
     */
    getNavBadge: function(viewId) {
        switch (viewId) {
            case 'invoices':
                const draftCount = Object.values(this.data.invoices || {}).filter(inv => inv.status === 'draft').length;
                return draftCount > 0 ? `<span class="badge bg-warning ms-1">${draftCount}</span>` : '';
            case 'customers':
                const customerCount = Object.keys(this.data.customers || {}).length;
                return customerCount > 0 ? `<span class="badge bg-info ms-1">${customerCount}</span>` : '';
            case 'products':
                const productCount = Object.keys(this.data.products || {}).length;
                return productCount > 0 ? `<span class="badge bg-secondary ms-1">${productCount}</span>` : '';
            case 'payments':
                const overdueCount = Object.values(this.data.invoices || {}).filter(inv => inv.status === 'overdue').length;
                return overdueCount > 0 ? `<span class="badge bg-danger ms-1">${overdueCount}</span>` : '';
            default:
                return '';
        }
    },

    /**
     * عرض المحتوى الحالي
     */
    renderCurrentView: function() {
        switch (this.data.currentView) {
            case 'dashboard':
                return this.renderDashboardView();
            case 'invoices':
                return this.renderInvoicesView();
            case 'customers':
                return this.renderCustomersView();
            case 'products':
                return this.renderProductsView();
            case 'reports':
                return this.renderReportsView();
            case 'settings':
                return this.renderSettingsView();
            case 'quotes':
                return this.renderQuotesView();
            case 'payments':
                return this.renderPaymentsView();
            case 'inventory':
                return this.renderInventoryView();
            default:
                return this.renderDashboardView();
        }
    },

    /**
     * عرض لوحة التحكم الرئيسية
     */
    renderDashboardView: function() {
        const invoices = Object.values(this.data.invoices || {});
        const customers = Object.values(this.data.customers || {});
        const products = Object.values(this.data.products || {});

        // حساب الإحصائيات السريعة
        const stats = this.calculateQuickStats(invoices);
        const recentInvoices = invoices.slice(0, 5);
        const topCustomers = this.getTopCustomers(3);
        const lowStockProducts = this.getLowStockProducts(5);

        return `
            <div class="dashboard-view">
                <!-- الترحيب والإحصائيات السريعة -->
                <div class="welcome-section mb-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h3 class="mb-1">مرحباً بك في نظام المبيعات</h3>
                            <p class="text-muted mb-0">نظرة عامة على أداء مبيعاتك اليوم</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="quick-actions">
                                <button class="btn btn-primary me-2" onclick="window.SalesComponent.showCreateInvoiceModal()">
                                    <i class="fas fa-plus me-1"></i>فاتورة جديدة
                                </button>
                                <button class="btn btn-outline-success" onclick="window.SalesComponent.showCreateCustomerModal()">
                                    <i class="fas fa-user-plus me-1"></i>عميل جديد
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- بطاقات الإحصائيات الرئيسية -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="card stats-card bg-gradient-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title mb-1">إجمالي المبيعات</h6>
                                        <h3 class="mb-0">${this.formatAmount(stats.totalRevenue)}</h3>
                                        <small class="opacity-75">هذا الشهر</small>
                                    </div>
                                    <div class="stats-icon">
                                        <i class="fas fa-chart-line fa-2x opacity-75"></i>
                                    </div>
                                </div>
                                <div class="progress mt-3" style="height: 4px;">
                                    <div class="progress-bar bg-white" style="width: ${Math.min(100, (stats.monthlyGrowth + 50))}%"></div>
                                </div>
                                <small class="opacity-75 mt-1 d-block">
                                    ${stats.monthlyGrowth >= 0 ? '+' : ''}${stats.monthlyGrowth.toFixed(1)}% من الشهر الماضي
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="card stats-card bg-gradient-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title mb-1">الفواتير</h6>
                                        <h3 class="mb-0">${invoices.length}</h3>
                                        <small class="opacity-75">إجمالي الفواتير</small>
                                    </div>
                                    <div class="stats-icon">
                                        <i class="fas fa-file-invoice fa-2x opacity-75"></i>
                                    </div>
                                </div>
                                <div class="row text-center mt-3">
                                    <div class="col-4">
                                        <small class="d-block opacity-75">مدفوعة</small>
                                        <strong>${stats.paidInvoices}</strong>
                                    </div>
                                    <div class="col-4">
                                        <small class="d-block opacity-75">معلقة</small>
                                        <strong>${stats.pendingInvoices}</strong>
                                    </div>
                                    <div class="col-4">
                                        <small class="d-block opacity-75">متأخرة</small>
                                        <strong>${stats.overdueInvoices}</strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="card stats-card bg-gradient-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title mb-1">العملاء</h6>
                                        <h3 class="mb-0">${customers.length}</h3>
                                        <small class="opacity-75">عميل نشط</small>
                                    </div>
                                    <div class="stats-icon">
                                        <i class="fas fa-users fa-2x opacity-75"></i>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <small class="opacity-75">عملاء جدد هذا الشهر</small>
                                    <div class="d-flex justify-content-between">
                                        <strong>${stats.newCustomers}</strong>
                                        <span class="badge bg-white text-info">${stats.customerGrowth >= 0 ? '+' : ''}${stats.customerGrowth}%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="card stats-card bg-gradient-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title mb-1">المنتجات</h6>
                                        <h3 class="mb-0">${products.length}</h3>
                                        <small class="opacity-75">منتج متاح</small>
                                    </div>
                                    <div class="stats-icon">
                                        <i class="fas fa-boxes fa-2x opacity-75"></i>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <small class="opacity-75">مخزون منخفض</small>
                                    <div class="d-flex justify-content-between">
                                        <strong>${lowStockProducts.length}</strong>
                                        <span class="badge bg-white text-warning">تحذير</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسوم البيانية والتحليلات -->
                <div class="row mb-4">
                    <div class="col-xl-8 mb-3">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><i class="fas fa-chart-area me-2"></i>اتجاه المبيعات</h6>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary active">7 أيام</button>
                                    <button class="btn btn-outline-primary">30 يوم</button>
                                    <button class="btn btn-outline-primary">3 أشهر</button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="salesChart" style="height: 300px;">
                                    ${this.renderSalesChart(stats.dailySales)}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-4 mb-3">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-chart-pie me-2"></i>توزيع المبيعات</h6>
                            </div>
                            <div class="card-body">
                                <div id="salesDistribution">
                                    ${this.renderSalesDistribution(stats.categoryStats)}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الأنشطة الحديثة والمهام -->
                <div class="row">
                    <div class="col-xl-6 mb-3">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><i class="fas fa-clock me-2"></i>الفواتير الحديثة</h6>
                                <a href="#" onclick="window.SalesComponent.switchView('invoices')" class="btn btn-sm btn-outline-primary">
                                    عرض الكل
                                </a>
                            </div>
                            <div class="card-body p-0">
                                ${recentInvoices.length === 0 ? `
                                    <div class="text-center py-4">
                                        <i class="fas fa-file-invoice fa-2x text-muted mb-2"></i>
                                        <p class="text-muted mb-0">لا توجد فواتير حديثة</p>
                                    </div>
                                ` : `
                                    <div class="list-group list-group-flush">
                                        ${recentInvoices.map(invoice => {
                                            const customer = this.data.customers[invoice.customerId];
                                            return `
                                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <h6 class="mb-1">${invoice.number}</h6>
                                                        <small class="text-muted">${customer?.name || 'عميل غير محدد'}</small>
                                                    </div>
                                                    <div class="text-end">
                                                        <div class="fw-bold">${this.formatAmount(invoice.total)}</div>
                                                        <span class="badge bg-${this.getInvoiceStatusColor(invoice.status)}">
                                                            ${this.getInvoiceStatusLabel(invoice.status)}
                                                        </span>
                                                    </div>
                                                </div>
                                            `;
                                        }).join('')}
                                    </div>
                                `}
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-6 mb-3">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-star me-2"></i>أفضل العملاء</h6>
                            </div>
                            <div class="card-body p-0">
                                ${topCustomers.length === 0 ? `
                                    <div class="text-center py-4">
                                        <i class="fas fa-users fa-2x text-muted mb-2"></i>
                                        <p class="text-muted mb-0">لا توجد بيانات عملاء</p>
                                    </div>
                                ` : `
                                    <div class="list-group list-group-flush">
                                        ${topCustomers.map((customer, index) => `
                                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-primary me-2">${index + 1}</span>
                                                    <div>
                                                        <h6 class="mb-1">${customer.name}</h6>
                                                        <small class="text-muted">${customer.invoiceCount} فاتورة</small>
                                                    </div>
                                                </div>
                                                <div class="text-end">
                                                    <div class="fw-bold">${this.formatAmount(customer.totalAmount)}</div>
                                                </div>
                                            </div>
                                        `).join('')}
                                    </div>
                                `}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * تبديل العرض
     */
    switchView: function(view) {
        this.data.currentView = view;
        const content = document.getElementById('main-content');
        if (content) {
            content.innerHTML = this.renderCurrentView();
        }

        // تحديث التقارير إذا كان في قسم التقارير
        if (view === 'reports') {
            setTimeout(() => {
                this.updateReports();
            }, 100);
        }

        // تفعيل tooltips
        setTimeout(() => {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }, 100);
    },

    /**
     * حساب الإحصائيات السريعة
     */
    calculateQuickStats: function(invoices) {
        const now = new Date();
        const thisMonth = now.getMonth();
        const thisYear = now.getFullYear();
        const lastMonth = thisMonth === 0 ? 11 : thisMonth - 1;
        const lastMonthYear = thisMonth === 0 ? thisYear - 1 : thisYear;

        // فلترة فواتير هذا الشهر والشهر الماضي
        const thisMonthInvoices = invoices.filter(inv => {
            const invDate = new Date(inv.date);
            return invDate.getMonth() === thisMonth && invDate.getFullYear() === thisYear;
        });

        const lastMonthInvoices = invoices.filter(inv => {
            const invDate = new Date(inv.date);
            return invDate.getMonth() === lastMonth && invDate.getFullYear() === lastMonthYear;
        });

        // حساب الإيرادات
        const thisMonthRevenue = thisMonthInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0);
        const lastMonthRevenue = lastMonthInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0);
        const monthlyGrowth = lastMonthRevenue > 0 ? ((thisMonthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100 : 0;

        // إحصائيات الفواتير
        const paidInvoices = invoices.filter(inv => inv.status === 'paid').length;
        const pendingInvoices = invoices.filter(inv => inv.status === 'sent').length;
        const overdueInvoices = invoices.filter(inv => inv.status === 'overdue').length;

        // إحصائيات العملاء
        const thisMonthCustomers = Object.values(this.data.customers || {}).filter(customer => {
            const customerDate = new Date(customer.createdAt);
            return customerDate.getMonth() === thisMonth && customerDate.getFullYear() === thisYear;
        });
        const newCustomers = thisMonthCustomers.length;
        const customerGrowth = 15; // نمو افتراضي

        // المبيعات اليومية (آخر 7 أيام)
        const dailySales = [];
        for (let i = 6; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            const dayInvoices = invoices.filter(inv => {
                const invDate = new Date(inv.date);
                return invDate.toDateString() === date.toDateString();
            });
            const dayRevenue = dayInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0);
            dailySales.push({
                date: date.toLocaleDateString('ar-SA', { weekday: 'short' }),
                revenue: dayRevenue,
                invoices: dayInvoices.length
            });
        }

        // إحصائيات الفئات
        const categoryStats = {};
        invoices.forEach(invoice => {
            if (invoice.items) {
                invoice.items.forEach(item => {
                    const product = this.data.products[item.productId];
                    const category = product?.category || 'أخرى';
                    if (!categoryStats[category]) {
                        categoryStats[category] = { revenue: 0, count: 0 };
                    }
                    categoryStats[category].revenue += item.total || 0;
                    categoryStats[category].count += item.quantity || 0;
                });
            }
        });

        return {
            totalRevenue: thisMonthRevenue,
            monthlyGrowth,
            paidInvoices,
            pendingInvoices,
            overdueInvoices,
            newCustomers,
            customerGrowth,
            dailySales,
            categoryStats
        };
    },

    /**
     * الحصول على أفضل العملاء
     */
    getTopCustomers: function(limit = 5) {
        const customerStats = {};
        Object.values(this.data.invoices || {}).forEach(invoice => {
            const customer = this.data.customers[invoice.customerId];
            if (customer) {
                if (!customerStats[customer.id]) {
                    customerStats[customer.id] = {
                        name: customer.name,
                        totalAmount: 0,
                        invoiceCount: 0
                    };
                }
                customerStats[customer.id].totalAmount += invoice.total || 0;
                customerStats[customer.id].invoiceCount++;
            }
        });

        return Object.values(customerStats)
            .sort((a, b) => b.totalAmount - a.totalAmount)
            .slice(0, limit);
    },

    /**
     * الحصول على المنتجات منخفضة المخزون
     */
    getLowStockProducts: function(limit = 5) {
        // محاكاة بيانات المخزون
        return Object.values(this.data.products || {})
            .map(product => ({
                ...product,
                stock: Math.floor(Math.random() * 50) + 1
            }))
            .filter(product => product.stock < 10)
            .slice(0, limit);
    },

    /**
     * رسم مخطط المبيعات
     */
    renderSalesChart: function(dailySales) {
        const maxRevenue = Math.max(...dailySales.map(day => day.revenue));

        return `
            <div class="sales-chart">
                <div class="chart-container" style="height: 250px; position: relative;">
                    ${dailySales.map((day, index) => {
                        const height = maxRevenue > 0 ? (day.revenue / maxRevenue) * 200 : 0;
                        return `
                            <div class="chart-bar" style="
                                position: absolute;
                                bottom: 30px;
                                left: ${index * 14.28}%;
                                width: 12%;
                                height: ${height}px;
                                background: linear-gradient(to top, #007bff, #0056b3);
                                border-radius: 4px 4px 0 0;
                                transition: all 0.3s ease;
                            " title="${day.date}: ${this.formatAmount(day.revenue)}">
                            </div>
                            <div style="
                                position: absolute;
                                bottom: 5px;
                                left: ${index * 14.28}%;
                                width: 12%;
                                text-align: center;
                                font-size: 12px;
                                color: #666;
                            ">${day.date}</div>
                        `;
                    }).join('')}
                </div>
                <div class="chart-summary mt-3">
                    <div class="row text-center">
                        <div class="col-4">
                            <small class="text-muted d-block">إجمالي المبيعات</small>
                            <strong>${this.formatAmount(dailySales.reduce((sum, day) => sum + day.revenue, 0))}</strong>
                        </div>
                        <div class="col-4">
                            <small class="text-muted d-block">متوسط يومي</small>
                            <strong>${this.formatAmount(dailySales.reduce((sum, day) => sum + day.revenue, 0) / 7)}</strong>
                        </div>
                        <div class="col-4">
                            <small class="text-muted d-block">إجمالي الفواتير</small>
                            <strong>${dailySales.reduce((sum, day) => sum + day.invoices, 0)}</strong>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * رسم توزيع المبيعات
     */
    renderSalesDistribution: function(categoryStats) {
        const categories = Object.entries(categoryStats);
        const totalRevenue = categories.reduce((sum, [cat, data]) => sum + data.revenue, 0);

        if (totalRevenue === 0) {
            return `
                <div class="text-center py-4">
                    <i class="fas fa-chart-pie fa-2x text-muted mb-2"></i>
                    <p class="text-muted mb-0">لا توجد بيانات مبيعات</p>
                </div>
            `;
        }

        const colors = ['#007bff', '#28a745', '#ffc107', '#dc3545', '#6c757d', '#17a2b8'];

        return `
            <div class="distribution-chart">
                ${categories.map(([category, data], index) => {
                    const percentage = (data.revenue / totalRevenue) * 100;
                    const color = colors[index % colors.length];

                    return `
                        <div class="distribution-item mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span class="fw-medium">${category}</span>
                                <span class="text-muted">${percentage.toFixed(1)}%</span>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar" style="width: ${percentage}%; background-color: ${color}"></div>
                            </div>
                            <small class="text-muted">${this.formatAmount(data.revenue)} - ${data.count} وحدة</small>
                        </div>
                    `;
                }).join('')}
            </div>
        `;
    },

    /**
     * تحميل بيانات المبيعات
     */
    loadSalesData: function() {
        try {
            const savedData = localStorage.getItem('salesData');
            if (savedData) {
                const parsedData = JSON.parse(savedData);
                Object.assign(this.data, parsedData);
            }

            // التأكد من وجود البيانات الأساسية
            if (!this.data.customers) this.data.customers = {};
            if (!this.data.products) this.data.products = {};
            if (!this.data.invoices) this.data.invoices = {};
            if (!this.data.quotes) this.data.quotes = {};
            if (!this.data.payments) this.data.payments = {};
            if (!this.data.inventory) this.data.inventory = {};
            if (!this.data.stockAdjustments) this.data.stockAdjustments = {};
            if (!this.data.filters) this.data.filters = {
                dateFrom: '',
                dateTo: '',
                status: '',
                customer: '',
                quickSearch: ''
            };
            if (!this.data.quoteFilters) this.data.quoteFilters = {
                dateFrom: '',
                dateTo: '',
                status: '',
                customer: ''
            };
            if (!this.data.settings) this.data.settings = {
                taxRate: 0.15,
                currency: 'SAR',
                invoicePrefix: 'INV-',
                nextInvoiceNumber: 1,
                nextQuoteNumber: 1,
                companyName: 'قمة الوعد للسفريات',
                companyAddress: 'المملكة العربية السعودية',
                companyTaxNumber: '*********',
                companyEmail: '<EMAIL>',
                companyPhone: '+966501234567',
                companyWebsite: 'https://qimat-alwaed.com',
                language: 'ar',
                timezone: 'Asia/Riyadh',
                dateFormat: 'ar-SA',
                autoSave: true,
                showNotifications: true,
                enableBackup: true,
                autoCalculateTax: true,
                defaultDueDays: 30
            };
        } catch (error) {
            console.error('خطأ في تحميل بيانات المبيعات:', error);
            // إعادة تهيئة البيانات في حالة الخطأ
            this.data = {
                customers: {},
                products: {},
                invoices: {},
                quotes: {},
                payments: {},
                inventory: {},
                stockAdjustments: {},
                filters: {
                    dateFrom: '',
                    dateTo: '',
                    status: '',
                    customer: '',
                    quickSearch: ''
                },
                quoteFilters: {
                    dateFrom: '',
                    dateTo: '',
                    status: '',
                    customer: ''
                },
                settings: {
                    taxRate: 0.15,
                    currency: 'SAR',
                    invoicePrefix: 'INV-',
                    nextInvoiceNumber: 1,
                    nextQuoteNumber: 1,
                    companyName: 'قمة الوعد للسفريات',
                    companyAddress: 'المملكة العربية السعودية',
                    companyTaxNumber: '*********',
                    companyEmail: '<EMAIL>',
                    companyPhone: '+966501234567',
                    companyWebsite: 'https://qimat-alwaed.com',
                    language: 'ar',
                    timezone: 'Asia/Riyadh',
                    dateFormat: 'ar-SA',
                    autoSave: true,
                    showNotifications: true,
                    enableBackup: true,
                    autoCalculateTax: true,
                    defaultDueDays: 30
                }
            };
        }
    },

    /**
     * حفظ بيانات المبيعات
     */
    saveSalesData: function() {
        try {
            localStorage.setItem('salesData', JSON.stringify(this.data));
        } catch (error) {
            console.error('خطأ في حفظ بيانات المبيعات:', error);
        }
    },

    /**
     * إنشاء بيانات تجريبية
     */
    createSampleData: function() {
        // التحقق من وجود بيانات - إنشاء البيانات المفقودة فقط
        const hasData = Object.keys(this.data.customers || {}).length > 0 &&
                       Object.keys(this.data.products || {}).length > 0 &&
                       Object.keys(this.data.invoices || {}).length > 0;

        if (hasData) {
            return;
        }

        // تهيئة البيانات إذا لم تكن موجودة
        if (!this.data.customers) this.data.customers = {};
        if (!this.data.products) this.data.products = {};
        if (!this.data.invoices) this.data.invoices = {};
        if (!this.data.quotes) this.data.quotes = {};
        if (!this.data.payments) this.data.payments = {};
        if (!this.data.inventory) this.data.inventory = {};
        if (!this.data.stockAdjustments) this.data.stockAdjustments = {};
        if (!this.data.quoteFilters) {
            this.data.quoteFilters = {
                dateFrom: '',
                dateTo: '',
                status: '',
                customer: ''
            };
        }
        if (!this.data.filters) {
            this.data.filters = {
                dateFrom: '',
                dateTo: '',
                status: '',
                customer: '',
                quickSearch: ''
            };
        }

        // إنشاء عملاء تجريبيين
        this.data.customers = {
            'customer1': {
                id: 'customer1',
                name: 'أحمد محمد السعيد',
                email: '<EMAIL>',
                phone: '+966501234567',
                address: 'الرياض، المملكة العربية السعودية',
                taxNumber: '*********',
                createdAt: new Date().toISOString()
            },
            'customer2': {
                id: 'customer2',
                name: 'شركة النور للتجارة',
                email: '<EMAIL>',
                phone: '+966509876543',
                address: 'جدة، المملكة العربية السعودية',
                taxNumber: '*********',
                createdAt: new Date().toISOString()
            }
        };

        // إنشاء منتجات تجريبية
        this.data.products = {
            'product1': {
                id: 'product1',
                name: 'تذكرة طيران - الرياض إلى دبي',
                description: 'تذكرة طيران ذهاب وإياب',
                price: 1200,
                category: 'طيران',
                sku: 'FLT-RUH-DXB',
                createdAt: new Date().toISOString()
            },
            'product2': {
                id: 'product2',
                name: 'حجز فندق - 3 ليالي',
                description: 'إقامة في فندق 4 نجوم',
                price: 800,
                category: 'إقامة',
                sku: 'HTL-3N-4S',
                createdAt: new Date().toISOString()
            }
        };

        // إنشاء فاتورة تجريبية
        this.data.invoices = {
            'invoice1': {
                id: 'invoice1',
                number: 'INV-001',
                date: new Date().toISOString().split('T')[0],
                dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                customerId: 'customer1',
                reference: 'REF-001',
                items: [
                    {
                        productId: 'product1',
                        name: 'تذكرة طيران - الرياض إلى دبي',
                        quantity: 2,
                        price: 1200,
                        total: 2400
                    }
                ],
                subtotal: 2400,
                tax: 360,
                total: 2760,
                status: 'draft',
                notes: 'فاتورة تجريبية',
                createdAt: new Date().toISOString()
            }
        };

        // إنشاء عروض أسعار تجريبية
        this.data.quotes = {
            'quote1': {
                id: 'quote1',
                number: 'QUO-001',
                date: new Date().toISOString().split('T')[0],
                validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                customerId: 'customer1',
                title: 'عرض سعر رحلة عائلية',
                items: [
                    {
                        productId: 'product1',
                        name: 'تذكرة طيران - الرياض إلى دبي',
                        quantity: 4,
                        price: 1200,
                        total: 4800
                    }
                ],
                subtotal: 4800,
                tax: 720,
                total: 5520,
                status: 'pending',
                notes: 'عرض خاص للعائلات - صالح لمدة 30 يوم',
                createdAt: new Date().toISOString()
            }
        };

        // إنشاء مدفوعات تجريبية
        this.data.payments = {};

        // إنشاء مخزون تجريبي
        this.data.inventory = {
            'product1': 25,
            'product2': 15
        };

        // إنشاء سجل تعديلات المخزون
        this.data.stockAdjustments = {};

        // فلاتر عروض الأسعار
        this.data.quoteFilters = {
            dateFrom: '',
            dateTo: '',
            status: '',
            customer: ''
        };

        // تحديث الإعدادات لتشمل عروض الأسعار
        if (!this.data.settings.nextQuoteNumber) {
            this.data.settings.nextQuoteNumber = 2;
        }

        this.saveSalesData();
    },

    /**
     * عرض قسم الفواتير
     */
    renderInvoicesView: function() {
        const invoices = Object.values(this.data.invoices || {});

        return `
            <div class="invoices-management">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4><i class="fas fa-file-invoice me-2"></i>إدارة الفواتير</h4>
                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="window.SalesComponent.showCreateInvoiceModal()">
                            <i class="fas fa-plus me-1"></i>فاتورة جديدة
                        </button>
                        <button class="btn btn-outline-secondary" onclick="window.SalesComponent.exportInvoicesCSV()">
                            <i class="fas fa-download me-1"></i>تصدير CSV
                        </button>
                    </div>
                </div>

                <!-- فلاتر البحث -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label">البحث السريع</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="quickSearch"
                                           placeholder="ابحث في رقم الفاتورة، اسم العميل، أو المرجع..."
                                           value="${this.data.filters.quickSearch || ''}"
                                           onkeyup="window.SalesComponent.updateFilter('quickSearch', this.value)">
                                    <button class="btn btn-outline-secondary" type="button" onclick="window.SalesComponent.clearSearch()">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="filterDateFrom"
                                       value="${this.data.filters.dateFrom}"
                                       onchange="window.SalesComponent.updateFilter('dateFrom', this.value)">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="filterDateTo"
                                       value="${this.data.filters.dateTo}"
                                       onchange="window.SalesComponent.updateFilter('dateTo', this.value)">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الحالة</label>
                                <select class="form-control" id="filterStatus"
                                        onchange="window.SalesComponent.updateFilter('status', this.value)">
                                    <option value="">جميع الحالات</option>
                                    <option value="draft" ${this.data.filters.status === 'draft' ? 'selected' : ''}>مسودة</option>
                                    <option value="sent" ${this.data.filters.status === 'sent' ? 'selected' : ''}>مرسلة</option>
                                    <option value="paid" ${this.data.filters.status === 'paid' ? 'selected' : ''}>مدفوعة</option>
                                    <option value="overdue" ${this.data.filters.status === 'overdue' ? 'selected' : ''}>متأخرة</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">العميل</label>
                                <select class="form-control" id="filterCustomer"
                                        onchange="window.SalesComponent.updateFilter('customer', this.value)">
                                    <option value="">جميع العملاء</option>
                                    ${Object.values(this.data.customers || {}).map(customer => `
                                        <option value="${customer.id}" ${this.data.filters.customer === customer.id ? 'selected' : ''}>
                                            ${customer.name}
                                        </option>
                                    `).join('')}
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول الفواتير -->
                <div class="card">
                    <div class="card-body">
                        ${invoices.length === 0 ? `
                            <div class="text-center py-5">
                                <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد فواتير</h5>
                                <p class="text-muted">ابدأ بإنشاء فاتورة جديدة</p>
                                <button class="btn btn-primary" onclick="window.SalesComponent.showCreateInvoiceModal()">
                                    <i class="fas fa-plus me-1"></i>إنشاء فاتورة
                                </button>
                            </div>
                        ` : `
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>رقم الفاتورة</th>
                                            <th>العميل</th>
                                            <th>التاريخ</th>
                                            <th>الإجمالي</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${this.getFilteredInvoices().map(invoice => `
                                            <tr>
                                                <td>
                                                    <strong>${invoice.number}</strong>
                                                </td>
                                                <td>
                                                    ${this.data.customers[invoice.customerId]?.name || 'عميل غير محدد'}
                                                </td>
                                                <td>
                                                    ${new Date(invoice.date).toLocaleDateString('ar-SA')}
                                                </td>
                                                <td>
                                                    <strong>${this.formatAmount(invoice.total)}</strong>
                                                </td>
                                                <td>
                                                    <span class="badge bg-${this.getInvoiceStatusColor(invoice.status)}">
                                                        ${this.getInvoiceStatusLabel(invoice.status)}
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-primary" onclick="window.SalesComponent.viewInvoice('${invoice.id}')" title="عرض">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="btn btn-outline-success" onclick="window.SalesComponent.editInvoice('${invoice.id}')" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-outline-info" onclick="window.SalesComponent.printInvoice('${invoice.id}')" title="طباعة">
                                                            <i class="fas fa-print"></i>
                                                        </button>
                                                        <button class="btn btn-outline-danger" onclick="window.SalesComponent.deleteInvoice('${invoice.id}')" title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        `}
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض قسم العملاء
     */
    renderCustomersView: function() {
        const customers = Object.values(this.data.customers || {});

        return `
            <div class="customers-management">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4><i class="fas fa-users me-2"></i>إدارة العملاء</h4>
                    <button class="btn btn-primary" onclick="window.SalesComponent.showCreateCustomerModal()">
                        <i class="fas fa-user-plus me-1"></i>عميل جديد
                    </button>
                </div>

                <div class="row">
                    ${customers.length === 0 ? `
                        <div class="col-12">
                            <div class="text-center py-5">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا يوجد عملاء</h5>
                                <p class="text-muted">ابدأ بإضافة عميل جديد</p>
                                <button class="btn btn-primary" onclick="window.SalesComponent.showCreateCustomerModal()">
                                    <i class="fas fa-user-plus me-1"></i>إضافة عميل
                                </button>
                            </div>
                        </div>
                    ` : customers.map(customer => `
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card customer-card">
                                <div class="card-body">
                                    <h6 class="card-title">${customer.name}</h6>
                                    <p class="card-text">
                                        <small class="text-muted">
                                            <i class="fas fa-envelope me-1"></i>${customer.email}<br>
                                            <i class="fas fa-phone me-1"></i>${customer.phone}
                                        </small>
                                    </p>
                                    <div class="btn-group btn-group-sm w-100">
                                        <button class="btn btn-outline-primary" onclick="window.SalesComponent.viewCustomer('${customer.id}')">
                                            <i class="fas fa-eye me-1"></i>عرض
                                        </button>
                                        <button class="btn btn-outline-success" onclick="window.SalesComponent.editCustomer('${customer.id}')">
                                            <i class="fas fa-edit me-1"></i>تعديل
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="window.SalesComponent.deleteCustomer('${customer.id}')">
                                            <i class="fas fa-trash me-1"></i>حذف
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    },

    /**
     * الحصول على الفواتير المفلترة
     */
    getFilteredInvoices: function() {
        let invoices = Object.values(this.data.invoices || {});

        // البحث السريع
        if (this.data.filters.quickSearch) {
            const searchTerm = this.data.filters.quickSearch.toLowerCase();
            invoices = invoices.filter(invoice => {
                const customer = this.data.customers[invoice.customerId];
                return (
                    invoice.number.toLowerCase().includes(searchTerm) ||
                    (customer && customer.name.toLowerCase().includes(searchTerm)) ||
                    (invoice.reference && invoice.reference.toLowerCase().includes(searchTerm))
                );
            });
        }

        // فلترة حسب التاريخ
        if (this.data.filters.dateFrom) {
            invoices = invoices.filter(invoice => invoice.date >= this.data.filters.dateFrom);
        }
        if (this.data.filters.dateTo) {
            invoices = invoices.filter(invoice => invoice.date <= this.data.filters.dateTo);
        }

        // فلترة حسب الحالة
        if (this.data.filters.status) {
            invoices = invoices.filter(invoice => invoice.status === this.data.filters.status);
        }

        // فلترة حسب العميل
        if (this.data.filters.customer) {
            invoices = invoices.filter(invoice => invoice.customerId === this.data.filters.customer);
        }

        return invoices.sort((a, b) => new Date(b.date) - new Date(a.date));
    },

    /**
     * مسح البحث
     */
    clearSearch: function() {
        this.data.filters.quickSearch = '';
        document.getElementById('quickSearch').value = '';
        this.saveSalesData();
        this.refreshData();
    },

    /**
     * مسح جميع الفلاتر
     */
    clearAllFilters: function() {
        this.data.filters = {
            dateFrom: '',
            dateTo: '',
            status: '',
            customer: '',
            quickSearch: ''
        };
        this.saveSalesData();
        this.refreshData();
    },

    /**
     * تحديث الفلتر
     */
    updateFilter: function(filterName, value) {
        this.data.filters[filterName] = value;
        this.saveSalesData();
        this.refreshData();
    },

    /**
     * تنسيق المبلغ
     */
    formatAmount: function(amount) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR'
        }).format(amount || 0);
    },

    /**
     * الحصول على لون حالة الفاتورة
     */
    getInvoiceStatusColor: function(status) {
        const colors = {
            draft: 'secondary',
            sent: 'info',
            paid: 'success',
            overdue: 'danger',
            cancelled: 'dark'
        };
        return colors[status] || 'secondary';
    },

    /**
     * الحصول على تسمية حالة الفاتورة
     */
    getInvoiceStatusLabel: function(status) {
        const labels = {
            draft: 'مسودة',
            sent: 'مرسلة',
            paid: 'مدفوعة',
            overdue: 'متأخرة',
            cancelled: 'ملغية'
        };
        return labels[status] || 'غير محدد';
    },

    /**
     * عرض نافذة إنشاء فاتورة
     */
    showCreateInvoiceModal: function() {
        // التأكد من تحميل البيانات
        if (!this.data || Object.keys(this.data).length === 0) {
            this.loadSalesData();
            this.createSampleData();
        }

        const customers = Object.values(this.data.customers || {});
        const products = Object.values(this.data.products || {});

        if (customers.length === 0) {
            alert('يجب إضافة عميل واحد على الأقل قبل إنشاء فاتورة. سيتم إنشاء بيانات تجريبية.');
            this.createSampleData();
            // إعادة تحميل البيانات بعد إنشاء البيانات التجريبية
            const customersAfter = Object.values(this.data.customers || {});
            const productsAfter = Object.values(this.data.products || {});
            if (customersAfter.length === 0) {
                alert('فشل في تحميل البيانات. يرجى إعادة تحميل الصفحة.');
                return;
            }
        }

        const modalHTML = `
            <div class="modal fade" id="createInvoiceModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-file-invoice me-2"></i>إنشاء فاتورة جديدة
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="createInvoiceForm">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">رقم الفاتورة</label>
                                        <input type="text" class="form-control" name="number"
                                               value="${this.data.settings.invoicePrefix}${this.data.settings.nextInvoiceNumber}" readonly>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">تاريخ الفاتورة</label>
                                        <input type="date" class="form-control" name="date"
                                               value="${new Date().toISOString().split('T')[0]}" required>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">العميل *</label>
                                        <select class="form-control" name="customerId" required>
                                            <option value="">اختر العميل</option>
                                            ${customers.map(customer => `
                                                <option value="${customer.id}">${customer.name}</option>
                                            `).join('')}
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">تاريخ الاستحقاق</label>
                                        <input type="date" class="form-control" name="dueDate"
                                               value="${new Date(Date.now() + this.data.settings.defaultDueDays * 24 * 60 * 60 * 1000).toISOString().split('T')[0]}">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">المرجع (اختياري)</label>
                                    <input type="text" class="form-control" name="reference" placeholder="رقم المرجع أو الطلب">
                                </div>

                                <!-- عناصر الفاتورة -->
                                <div class="card mb-3">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">عناصر الفاتورة</h6>
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="window.SalesComponent.addInvoiceItem()">
                                            <i class="fas fa-plus me-1"></i>إضافة عنصر
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div id="invoiceItems">
                                            <div class="invoice-item row mb-2">
                                                <div class="col-md-4">
                                                    <select class="form-control item-product" name="items[0][productId]" onchange="window.SalesComponent.updateItemFromProduct(0, this.value)">
                                                        <option value="">اختر المنتج</option>
                                                        ${products.map(product => `
                                                            <option value="${product.id}" data-price="${product.price}">${product.name}</option>
                                                        `).join('')}
                                                    </select>
                                                </div>
                                                <div class="col-md-2">
                                                    <input type="number" class="form-control item-quantity" name="items[0][quantity]"
                                                           placeholder="الكمية" value="1" min="1" onchange="window.SalesComponent.calculateItemTotal(0)">
                                                </div>
                                                <div class="col-md-2">
                                                    <input type="number" class="form-control item-price" name="items[0][price]"
                                                           placeholder="السعر" step="0.01" onchange="window.SalesComponent.calculateItemTotal(0)">
                                                </div>
                                                <div class="col-md-2">
                                                    <input type="number" class="form-control item-total" name="items[0][total]"
                                                           placeholder="الإجمالي" readonly>
                                                </div>
                                                <div class="col-md-2">
                                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="window.SalesComponent.removeInvoiceItem(0)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- الإجماليات -->
                                <div class="card">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-8">
                                                <label class="form-label">ملاحظات</label>
                                                <textarea class="form-control" name="notes" rows="3" placeholder="ملاحظات إضافية"></textarea>
                                            </div>
                                            <div class="col-md-4">
                                                <table class="table table-sm">
                                                    <tr>
                                                        <td>المجموع الفرعي:</td>
                                                        <td class="text-end"><span id="invoiceSubtotal">0.00 ر.س</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td>الضريبة (${(this.data.settings.taxRate * 100)}%):</td>
                                                        <td class="text-end"><span id="invoiceTax">0.00 ر.س</span></td>
                                                    </tr>
                                                    <tr class="table-primary">
                                                        <td><strong>الإجمالي:</strong></td>
                                                        <td class="text-end"><strong><span id="invoiceTotal">0.00 ر.س</span></strong></td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveInvoice()">
                                <i class="fas fa-save me-1"></i>حفظ الفاتورة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('createInvoiceModal'));
        modal.show();

        // إزالة النافذة عند الإغلاق
        document.getElementById('createInvoiceModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * إضافة عنصر جديد للفاتورة
     */
    addInvoiceItem: function() {
        const itemsContainer = document.getElementById('invoiceItems');
        if (!itemsContainer) return;

        const itemCount = itemsContainer.children.length;
        const products = Object.values(this.data.products || {});

        const itemHTML = `
            <div class="invoice-item row mb-2">
                <div class="col-md-4">
                    <select class="form-control item-product" name="items[${itemCount}][productId]" onchange="window.SalesComponent.updateItemFromProduct(${itemCount}, this.value)">
                        <option value="">اختر المنتج</option>
                        ${products.map(product => `
                            <option value="${product.id}" data-price="${product.price}">${product.name}</option>
                        `).join('')}
                    </select>
                </div>
                <div class="col-md-2">
                    <input type="number" class="form-control item-quantity" name="items[${itemCount}][quantity]"
                           placeholder="الكمية" value="1" min="1" onchange="window.SalesComponent.calculateItemTotal(${itemCount})">
                </div>
                <div class="col-md-2">
                    <input type="number" class="form-control item-price" name="items[${itemCount}][price]"
                           placeholder="السعر" step="0.01" onchange="window.SalesComponent.calculateItemTotal(${itemCount})">
                </div>
                <div class="col-md-2">
                    <input type="number" class="form-control item-total" name="items[${itemCount}][total]"
                           placeholder="الإجمالي" readonly>
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="window.SalesComponent.removeInvoiceItem(${itemCount})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;

        itemsContainer.insertAdjacentHTML('beforeend', itemHTML);
    },

    /**
     * إزالة عنصر من الفاتورة
     */
    removeInvoiceItem: function(itemIndex) {
        const itemsContainer = document.getElementById('invoiceItems');
        if (!itemsContainer) return;

        const items = itemsContainer.children;

        if (items.length > 1 && items[itemIndex]) {
            items[itemIndex].remove();
            this.calculateInvoiceTotal();
        } else {
            alert('يجب أن تحتوي الفاتورة على عنصر واحد على الأقل');
        }
    },

    /**
     * تحديث عنصر من المنتج المختار
     */
    updateItemFromProduct: function(itemIndex, productId) {
        if (!productId) return;

        const product = this.data.products?.[productId];
        if (!product) return;

        const itemsContainer = document.getElementById('invoiceItems');
        if (!itemsContainer || !itemsContainer.children[itemIndex]) return;

        const item = itemsContainer.children[itemIndex];
        const priceInput = item.querySelector('.item-price');

        if (priceInput) {
            priceInput.value = product.price;
            this.calculateItemTotal(itemIndex);
        }
    },

    /**
     * حساب إجمالي العنصر
     */
    calculateItemTotal: function(itemIndex) {
        const itemsContainer = document.getElementById('invoiceItems');
        if (!itemsContainer || !itemsContainer.children[itemIndex]) return;

        const item = itemsContainer.children[itemIndex];

        const quantityInput = item.querySelector('.item-quantity');
        const priceInput = item.querySelector('.item-price');
        const totalInput = item.querySelector('.item-total');

        if (!quantityInput || !priceInput || !totalInput) return;

        const quantity = parseFloat(quantityInput.value) || 0;
        const price = parseFloat(priceInput.value) || 0;
        const total = quantity * price;

        totalInput.value = total.toFixed(2);

        this.calculateInvoiceTotal();
    },

    /**
     * حساب إجمالي الفاتورة
     */
    calculateInvoiceTotal: function() {
        const itemsContainer = document.getElementById('invoiceItems');
        if (!itemsContainer) return;

        let subtotal = 0;

        Array.from(itemsContainer.children).forEach(item => {
            const totalInput = item.querySelector('.item-total');
            if (totalInput) {
                const total = parseFloat(totalInput.value) || 0;
                subtotal += total;
            }
        });

        const taxRate = this.data.settings?.taxRate || 0.15;
        const tax = subtotal * taxRate;
        const total = subtotal + tax;

        const subtotalElement = document.getElementById('invoiceSubtotal');
        const taxElement = document.getElementById('invoiceTax');
        const totalElement = document.getElementById('invoiceTotal');

        if (subtotalElement) subtotalElement.textContent = this.formatAmount(subtotal);
        if (taxElement) taxElement.textContent = this.formatAmount(tax);
        if (totalElement) totalElement.textContent = this.formatAmount(total);
    },

    /**
     * حفظ الفاتورة
     */
    saveInvoice: function() {
        const form = document.getElementById('createInvoiceForm');
        const formData = new FormData(form);

        // التحقق من البيانات المطلوبة
        if (!formData.get('customerId')) {
            alert('يرجى اختيار العميل');
            return;
        }

        // جمع عناصر الفاتورة
        const items = [];
        const itemsContainer = document.getElementById('invoiceItems');

        if (!itemsContainer) {
            alert('خطأ في تحميل عناصر الفاتورة');
            return;
        }

        Array.from(itemsContainer.children).forEach((item, index) => {
            const productSelect = item.querySelector('.item-product');
            const quantityInput = item.querySelector('.item-quantity');
            const priceInput = item.querySelector('.item-price');
            const totalInput = item.querySelector('.item-total');

            if (productSelect && quantityInput && priceInput && totalInput &&
                productSelect.value && quantityInput.value && priceInput.value) {
                const product = this.data.products?.[productSelect.value];
                items.push({
                    productId: productSelect.value,
                    name: product ? product.name : 'منتج غير محدد',
                    quantity: parseInt(quantityInput.value),
                    price: parseFloat(priceInput.value),
                    total: parseFloat(totalInput.value)
                });
            }
        });

        if (items.length === 0) {
            alert('يرجى إضافة عنصر واحد على الأقل للفاتورة');
            return;
        }

        // حساب الإجماليات
        const subtotal = items.reduce((sum, item) => sum + item.total, 0);
        const taxRate = this.data.settings?.taxRate || 0.15;
        const tax = subtotal * taxRate;
        const total = subtotal + tax;

        // إنشاء الفاتورة
        const invoiceId = 'invoice_' + Date.now();
        const invoice = {
            id: invoiceId,
            number: formData.get('number'),
            date: formData.get('date'),
            dueDate: formData.get('dueDate'),
            customerId: formData.get('customerId'),
            reference: formData.get('reference'),
            items: items,
            subtotal: subtotal,
            tax: tax,
            total: total,
            status: 'draft',
            notes: formData.get('notes'),
            createdAt: new Date().toISOString()
        };

        // حفظ الفاتورة
        if (!this.data.invoices) this.data.invoices = {};
        this.data.invoices[invoiceId] = invoice;
        if (!this.data.settings.nextInvoiceNumber) this.data.settings.nextInvoiceNumber = 1;
        this.data.settings.nextInvoiceNumber++;
        this.saveSalesData();

        // إغلاق النافذة وتحديث العرض
        const modalElement = document.getElementById('createInvoiceModal');
        if (modalElement) {
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.hide();
            }
        }

        this.refreshData();
        alert('تم إنشاء الفاتورة بنجاح');
    },

    /**
     * عرض نافذة إنشاء عميل
     */
    showCreateCustomerModal: function() {
        const modalHTML = `
            <div class="modal fade" id="createCustomerModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-user-plus me-2"></i>إضافة عميل جديد
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="createCustomerForm">
                                <div class="mb-3">
                                    <label class="form-label">اسم العميل *</label>
                                    <input type="text" class="form-control" name="name" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني *</label>
                                    <input type="email" class="form-control" name="email" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف *</label>
                                    <input type="tel" class="form-control" name="phone" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea class="form-control" name="address" rows="3"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الرقم الضريبي</label>
                                    <input type="text" class="form-control" name="taxNumber">
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveCustomer()">
                                <i class="fas fa-save me-1"></i>حفظ العميل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('createCustomerModal'));
        modal.show();

        document.getElementById('createCustomerModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * حفظ العميل
     */
    saveCustomer: function() {
        const form = document.getElementById('createCustomerForm');
        const formData = new FormData(form);

        // التحقق من البيانات المطلوبة
        if (!formData.get('name') || !formData.get('email') || !formData.get('phone')) {
            alert('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        // إنشاء العميل
        const customerId = 'customer_' + Date.now();
        const customer = {
            id: customerId,
            name: formData.get('name'),
            email: formData.get('email'),
            phone: formData.get('phone'),
            address: formData.get('address'),
            taxNumber: formData.get('taxNumber'),
            createdAt: new Date().toISOString()
        };

        // حفظ العميل
        this.data.customers[customerId] = customer;
        this.saveSalesData();

        // إغلاق النافذة وتحديث العرض
        const modal = bootstrap.Modal.getInstance(document.getElementById('createCustomerModal'));
        modal.hide();

        this.refreshData();
        alert('تم إضافة العميل بنجاح');
    },

    /**
     * عرض نافذة إنشاء منتج
     */
    showCreateProductModal: function() {
        const modalHTML = `
            <div class="modal fade" id="createProductModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-box me-2"></i>إضافة منتج جديد
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="createProductForm">
                                <div class="mb-3">
                                    <label class="form-label">اسم المنتج *</label>
                                    <input type="text" class="form-control" name="name" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الوصف</label>
                                    <textarea class="form-control" name="description" rows="3"></textarea>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">السعر *</label>
                                            <input type="number" class="form-control" name="price" step="0.01" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الفئة</label>
                                            <select class="form-control" name="category">
                                                <option value="">اختر الفئة</option>
                                                <option value="طيران">طيران</option>
                                                <option value="إقامة">إقامة</option>
                                                <option value="نقل">نقل</option>
                                                <option value="جولات">جولات</option>
                                                <option value="تأشيرات">تأشيرات</option>
                                                <option value="أخرى">أخرى</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">رمز المنتج (SKU)</label>
                                    <input type="text" class="form-control" name="sku">
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveProduct()">
                                <i class="fas fa-save me-1"></i>حفظ المنتج
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('createProductModal'));
        modal.show();

        document.getElementById('createProductModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * حفظ المنتج
     */
    saveProduct: function() {
        const form = document.getElementById('createProductForm');
        const formData = new FormData(form);

        // التحقق من البيانات المطلوبة
        if (!formData.get('name') || !formData.get('price')) {
            alert('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        // إنشاء المنتج
        const productId = 'product_' + Date.now();
        const product = {
            id: productId,
            name: formData.get('name'),
            description: formData.get('description'),
            price: parseFloat(formData.get('price')),
            category: formData.get('category'),
            sku: formData.get('sku'),
            createdAt: new Date().toISOString()
        };

        // حفظ المنتج
        this.data.products[productId] = product;
        this.saveSalesData();

        // إغلاق النافذة وتحديث العرض
        const modal = bootstrap.Modal.getInstance(document.getElementById('createProductModal'));
        modal.hide();

        this.refreshData();
        alert('تم إضافة المنتج بنجاح');
    },

    /**
     * عرض قسم المنتجات
     */
    renderProductsView: function() {
        const products = Object.values(this.data.products || {});

        return `
            <div class="products-management">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4><i class="fas fa-boxes me-2"></i>إدارة المنتجات</h4>
                    <button class="btn btn-primary" onclick="window.SalesComponent.showCreateProductModal()">
                        <i class="fas fa-box me-1"></i>منتج جديد
                    </button>
                </div>

                <div class="row">
                    ${products.length === 0 ? `
                        <div class="col-12">
                            <div class="text-center py-5">
                                <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد منتجات</h5>
                                <p class="text-muted">ابدأ بإضافة منتج جديد</p>
                                <button class="btn btn-primary" onclick="window.SalesComponent.showCreateProductModal()">
                                    <i class="fas fa-box me-1"></i>إضافة منتج
                                </button>
                            </div>
                        </div>
                    ` : products.map(product => `
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card product-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="card-title">${product.name}</h6>
                                        ${product.category ? `<span class="badge bg-secondary">${product.category}</span>` : ''}
                                    </div>
                                    ${product.description ? `<p class="card-text text-muted small">${product.description}</p>` : ''}
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <strong class="text-primary">${this.formatAmount(product.price)}</strong>
                                        ${product.sku ? `<small class="text-muted">SKU: ${product.sku}</small>` : ''}
                                    </div>
                                    <div class="btn-group btn-group-sm w-100">
                                        <button class="btn btn-outline-primary" onclick="window.SalesComponent.viewProduct('${product.id}')">
                                            <i class="fas fa-eye me-1"></i>عرض
                                        </button>
                                        <button class="btn btn-outline-success" onclick="window.SalesComponent.editProduct('${product.id}')">
                                            <i class="fas fa-edit me-1"></i>تعديل
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="window.SalesComponent.deleteProduct('${product.id}')">
                                            <i class="fas fa-trash me-1"></i>حذف
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    },

    /**
     * عرض قسم التقارير
     */
    renderReportsView: function() {
        const invoices = Object.values(this.data.invoices || {});
        const customers = Object.values(this.data.customers || {});
        const products = Object.values(this.data.products || {});

        // حساب الإحصائيات
        const stats = this.calculateStats(invoices);

        return `
            <div class="reports-management">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4><i class="fas fa-chart-bar me-2"></i>التقارير والإحصائيات</h4>
                    <div class="btn-group">
                        <button class="btn btn-outline-primary" onclick="window.SalesComponent.exportReportPDF()">
                            <i class="fas fa-file-pdf me-1"></i>تصدير PDF
                        </button>
                        <button class="btn btn-outline-success" onclick="window.SalesComponent.exportReportExcel()">
                            <i class="fas fa-file-excel me-1"></i>تصدير Excel
                        </button>
                    </div>
                </div>

                <!-- الإحصائيات العامة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-file-invoice fa-2x mb-2"></i>
                                <h3>${invoices.length}</h3>
                                <p class="mb-0">إجمالي الفواتير</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                                <h3>${this.formatAmount(stats.totalRevenue)}</h3>
                                <p class="mb-0">إجمالي المبيعات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h3>${customers.length}</h3>
                                <p class="mb-0">عدد العملاء</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-boxes fa-2x mb-2"></i>
                                <h3>${products.length}</h3>
                                <p class="mb-0">عدد المنتجات</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- تقرير حالات الفواتير -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-chart-pie me-2"></i>حالات الفواتير</h6>
                            </div>
                            <div class="card-body">
                                ${Object.entries(stats.statusBreakdown).map(([status, data]) => `
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span class="badge bg-${this.getInvoiceStatusColor(status)} me-2">
                                            ${this.getInvoiceStatusLabel(status)}
                                        </span>
                                        <div class="text-end">
                                            <div><strong>${data.count} فاتورة</strong></div>
                                            <small class="text-muted">${this.formatAmount(data.amount)}</small>
                                        </div>
                                    </div>
                                    <div class="progress mb-3" style="height: 8px;">
                                        <div class="progress-bar bg-${this.getInvoiceStatusColor(status)}"
                                             style="width: ${(data.count / invoices.length * 100)}%"></div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>

                    <!-- أفضل العملاء -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-star me-2"></i>أفضل العملاء</h6>
                            </div>
                            <div class="card-body">
                                ${stats.topCustomers.length === 0 ? `
                                    <p class="text-muted text-center">لا توجد بيانات عملاء</p>
                                ` : stats.topCustomers.map((customer, index) => `
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <span class="badge bg-primary me-2">${index + 1}</span>
                                            <strong>${customer.name}</strong>
                                        </div>
                                        <div class="text-end">
                                            <div><strong>${this.formatAmount(customer.totalAmount)}</strong></div>
                                            <small class="text-muted">${customer.invoiceCount} فاتورة</small>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تقرير المنتجات الأكثر مبيعاً -->
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-trophy me-2"></i>المنتجات الأكثر مبيعاً</h6>
                            </div>
                            <div class="card-body">
                                ${stats.topProducts.length === 0 ? `
                                    <p class="text-muted text-center">لا توجد بيانات مبيعات</p>
                                ` : stats.topProducts.map((product, index) => `
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <span class="badge bg-success me-2">${index + 1}</span>
                                            <strong>${product.name}</strong>
                                        </div>
                                        <div class="text-end">
                                            <div><strong>${product.totalQuantity} وحدة</strong></div>
                                            <small class="text-muted">${this.formatAmount(product.totalRevenue)}</small>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>

                    <!-- إحصائيات شهرية -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-calendar me-2"></i>المبيعات الشهرية</h6>
                            </div>
                            <div class="card-body">
                                ${Object.entries(stats.monthlyStats).length === 0 ? `
                                    <p class="text-muted text-center">لا توجد بيانات شهرية</p>
                                ` : Object.entries(stats.monthlyStats).map(([month, data]) => `
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <strong>${month}</strong>
                                        <div class="text-end">
                                            <div><strong>${this.formatAmount(data.revenue)}</strong></div>
                                            <small class="text-muted">${data.invoices} فاتورة</small>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض قسم الإعدادات
     */
    renderSettingsView: function() {
        return `
            <div class="settings-management">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4><i class="fas fa-cog me-2"></i>إعدادات النظام</h4>
                    <button class="btn btn-success" onclick="window.SalesComponent.saveSettings()">
                        <i class="fas fa-save me-1"></i>حفظ الإعدادات
                    </button>
                </div>

                <div class="row">
                    <!-- إعدادات الشركة -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-building me-2"></i>معلومات الشركة</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">اسم الشركة</label>
                                    <input type="text" class="form-control" id="companyName"
                                           value="${this.data.settings.companyName}">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">عنوان الشركة</label>
                                    <textarea class="form-control" id="companyAddress" rows="3">${this.data.settings.companyAddress}</textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" id="companyPhone"
                                           value="${this.data.settings.companyPhone}">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="companyEmail"
                                           value="${this.data.settings.companyEmail}">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الموقع الإلكتروني</label>
                                    <input type="url" class="form-control" id="companyWebsite"
                                           value="${this.data.settings.companyWebsite}">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الرقم الضريبي</label>
                                    <input type="text" class="form-control" id="companyTaxNumber"
                                           value="${this.data.settings.companyTaxNumber}">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات الفواتير -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-file-invoice me-2"></i>إعدادات الفواتير</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">بادئة رقم الفاتورة</label>
                                    <input type="text" class="form-control" id="invoicePrefix"
                                           value="${this.data.settings.invoicePrefix}">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">رقم الفاتورة التالي</label>
                                    <input type="number" class="form-control" id="nextInvoiceNumber"
                                           value="${this.data.settings.nextInvoiceNumber}" min="1">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">معدل الضريبة (%)</label>
                                    <input type="number" class="form-control" id="taxRate"
                                           value="${this.data.settings.taxRate * 100}" step="0.01" min="0" max="100">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">العملة</label>
                                    <select class="form-control" id="currency">
                                        <option value="SAR" ${this.data.settings.currency === 'SAR' ? 'selected' : ''}>ريال سعودي (SAR)</option>
                                        <option value="USD" ${this.data.settings.currency === 'USD' ? 'selected' : ''}>دولار أمريكي (USD)</option>
                                        <option value="EUR" ${this.data.settings.currency === 'EUR' ? 'selected' : ''}>يورو (EUR)</option>
                                        <option value="AED" ${this.data.settings.currency === 'AED' ? 'selected' : ''}>درهم إماراتي (AED)</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">أيام الاستحقاق الافتراضية</label>
                                    <input type="number" class="form-control" id="defaultDueDays"
                                           value="${this.data.settings.defaultDueDays}" min="1">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- إعدادات النظام -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>إعدادات النظام</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">اللغة</label>
                                    <select class="form-control" id="language">
                                        <option value="ar" ${this.data.settings.language === 'ar' ? 'selected' : ''}>العربية</option>
                                        <option value="en" ${this.data.settings.language === 'en' ? 'selected' : ''}>English</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">المنطقة الزمنية</label>
                                    <select class="form-control" id="timezone">
                                        <option value="Asia/Riyadh" ${this.data.settings.timezone === 'Asia/Riyadh' ? 'selected' : ''}>الرياض</option>
                                        <option value="Asia/Dubai" ${this.data.settings.timezone === 'Asia/Dubai' ? 'selected' : ''}>دبي</option>
                                        <option value="Asia/Kuwait" ${this.data.settings.timezone === 'Asia/Kuwait' ? 'selected' : ''}>الكويت</option>
                                    </select>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="autoSave"
                                           ${this.data.settings.autoSave ? 'checked' : ''}>
                                    <label class="form-check-label" for="autoSave">
                                        حفظ تلقائي للبيانات
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="showNotifications"
                                           ${this.data.settings.showNotifications ? 'checked' : ''}>
                                    <label class="form-check-label" for="showNotifications">
                                        عرض الإشعارات
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="enableBackup"
                                           ${this.data.settings.enableBackup ? 'checked' : ''}>
                                    <label class="form-check-label" for="enableBackup">
                                        تفعيل النسخ الاحتياطي
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="autoCalculateTax"
                                           ${this.data.settings.autoCalculateTax ? 'checked' : ''}>
                                    <label class="form-check-label" for="autoCalculateTax">
                                        حساب الضريبة تلقائياً
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إجراءات البيانات -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-database me-2"></i>إدارة البيانات</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-primary" onclick="window.SalesComponent.showBackupModal()">
                                        <i class="fas fa-database me-1"></i>النسخ الاحتياطي والاستيراد
                                    </button>
                                    <button class="btn btn-outline-success" onclick="window.SalesComponent.showAdvancedReports()">
                                        <i class="fas fa-chart-line me-1"></i>التقارير المتقدمة
                                    </button>
                                    <button class="btn btn-outline-info" onclick="window.SalesComponent.displayAlerts()">
                                        <i class="fas fa-bell me-1"></i>فحص التنبيهات
                                    </button>
                                    <button class="btn btn-outline-warning" onclick="window.SalesComponent.diagnose()">
                                        <i class="fas fa-stethoscope me-1"></i>تشخيص النظام
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="window.SalesComponent.resetAllData()">
                                        <i class="fas fa-trash me-1"></i>مسح جميع البيانات
                                    </button>
                                </div>

                                <hr>

                                <h6>معلومات النظام</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td>عدد الفواتير:</td>
                                        <td><strong>${Object.keys(this.data.invoices || {}).length}</strong></td>
                                    </tr>
                                    <tr>
                                        <td>عدد العملاء:</td>
                                        <td><strong>${Object.keys(this.data.customers || {}).length}</strong></td>
                                    </tr>
                                    <tr>
                                        <td>عدد المنتجات:</td>
                                        <td><strong>${Object.keys(this.data.products || {}).length}</strong></td>
                                    </tr>
                                    <tr>
                                        <td>عروض الأسعار:</td>
                                        <td><strong>${Object.keys(this.data.quotes || {}).length}</strong></td>
                                    </tr>
                                    <tr>
                                        <td>المدفوعات:</td>
                                        <td><strong>${Object.keys(this.data.payments || {}).length}</strong></td>
                                    </tr>
                                    <tr>
                                        <td>حجم البيانات:</td>
                                        <td><strong>${this.getDataSize()}</strong></td>
                                    </tr>
                                    <tr>
                                        <td>آخر حفظ:</td>
                                        <td><strong>${new Date().toLocaleString('ar-SA')}</strong></td>
                                    </tr>
                                </table>

                                <div class="alert alert-info mt-3">
                                    <h6><i class="fas fa-info-circle me-2"></i>الميزات الجديدة</h6>
                                    <ul class="mb-0">
                                        <li>نظام التقارير المتقدمة مع رسوم بيانية</li>
                                        <li>النسخ الاحتياطي والاستيراد الذكي</li>
                                        <li>نظام التنبيهات التلقائية</li>
                                        <li>إدارة عروض الأسعار المتكاملة</li>
                                        <li>نظام المدفوعات المتقدم</li>
                                        <li>إدارة المخزون الذكية</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * تصدير الفواتير CSV
     */
    exportInvoicesCSV: function() {
        const invoices = this.getFilteredInvoices();

        if (invoices.length === 0) {
            alert('لا توجد فواتير للتصدير');
            return;
        }

        // إنشاء رؤوس CSV
        const headers = [
            'رقم الفاتورة',
            'العميل',
            'إيميل العميل',
            'هاتف العميل',
            'تاريخ الفاتورة',
            'تاريخ الاستحقاق',
            'المرجع',
            'المجموع الفرعي',
            'الضريبة',
            'الإجمالي',
            'الحالة',
            'ملاحظات',
            'تاريخ الإنشاء'
        ];

        // إنشاء بيانات CSV
        const csvData = [headers];

        invoices.forEach(invoice => {
            const customer = this.data.customers[invoice.customerId];
            csvData.push([
                invoice.number,
                customer?.name || 'عميل غير محدد',
                customer?.email || '',
                customer?.phone || '',
                invoice.date,
                invoice.dueDate || '',
                invoice.reference || '',
                invoice.subtotal || 0,
                invoice.tax || 0,
                invoice.total || 0,
                this.getInvoiceStatusLabel(invoice.status),
                invoice.notes || '',
                new Date(invoice.createdAt).toLocaleDateString('ar-SA')
            ]);
        });

        // تحويل إلى CSV
        const csvContent = csvData.map(row =>
            row.map(cell => `"${cell}"`).join(',')
        ).join('\n');

        // إضافة BOM للدعم العربي
        const blob = new Blob(['\ufeff' + csvContent], {
            type: 'text/csv;charset=utf-8;'
        });

        // تحميل الملف
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `invoices_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();

        alert(`تم تصدير ${invoices.length} فاتورة بنجاح`);
    },

    /**
     * عرض المنتج
     */
    viewProduct: function(productId) {
        const product = this.data.products[productId];
        if (!product) {
            alert('المنتج غير موجود');
            return;
        }

        const modalHTML = `
            <div class="modal fade" id="viewProductModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-box me-2"></i>تفاصيل المنتج
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <table class="table">
                                <tr>
                                    <td><strong>اسم المنتج:</strong></td>
                                    <td>${product.name}</td>
                                </tr>
                                ${product.description ? `
                                <tr>
                                    <td><strong>الوصف:</strong></td>
                                    <td>${product.description}</td>
                                </tr>
                                ` : ''}
                                <tr>
                                    <td><strong>السعر:</strong></td>
                                    <td>${this.formatAmount(product.price)}</td>
                                </tr>
                                ${product.category ? `
                                <tr>
                                    <td><strong>الفئة:</strong></td>
                                    <td>${product.category}</td>
                                </tr>
                                ` : ''}
                                ${product.sku ? `
                                <tr>
                                    <td><strong>رمز المنتج:</strong></td>
                                    <td>${product.sku}</td>
                                </tr>
                                ` : ''}
                                <tr>
                                    <td><strong>تاريخ الإنشاء:</strong></td>
                                    <td>${new Date(product.createdAt).toLocaleDateString('ar-SA')}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.editProduct('${product.id}')">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('viewProductModal'));
        modal.show();

        document.getElementById('viewProductModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * تعديل المنتج
     */
    editProduct: function(productId) {
        const product = this.data.products[productId];
        if (!product) {
            alert('المنتج غير موجود');
            return;
        }

        const modalHTML = `
            <div class="modal fade" id="editProductModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-box-open me-2"></i>تعديل المنتج: ${product.name}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="editProductForm">
                                <input type="hidden" name="productId" value="${productId}">

                                <div class="mb-3">
                                    <label class="form-label">اسم المنتج *</label>
                                    <input type="text" class="form-control" name="name" value="${product.name}" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الوصف</label>
                                    <textarea class="form-control" name="description" rows="3">${product.description || ''}</textarea>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">السعر *</label>
                                            <input type="number" class="form-control" name="price" value="${product.price}" step="0.01" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الفئة</label>
                                            <select class="form-control" name="category">
                                                <option value="">اختر الفئة</option>
                                                <option value="طيران" ${product.category === 'طيران' ? 'selected' : ''}>طيران</option>
                                                <option value="إقامة" ${product.category === 'إقامة' ? 'selected' : ''}>إقامة</option>
                                                <option value="نقل" ${product.category === 'نقل' ? 'selected' : ''}>نقل</option>
                                                <option value="جولات" ${product.category === 'جولات' ? 'selected' : ''}>جولات</option>
                                                <option value="تأشيرات" ${product.category === 'تأشيرات' ? 'selected' : ''}>تأشيرات</option>
                                                <option value="أخرى" ${product.category === 'أخرى' ? 'selected' : ''}>أخرى</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">رمز المنتج (SKU)</label>
                                    <input type="text" class="form-control" name="sku" value="${product.sku || ''}">
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.updateProduct()">
                                <i class="fas fa-save me-1"></i>حفظ التعديلات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('editProductModal'));
        modal.show();

        document.getElementById('editProductModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * تحديث المنتج
     */
    updateProduct: function() {
        const form = document.getElementById('editProductForm');
        const formData = new FormData(form);
        const productId = formData.get('productId');

        // التحقق من البيانات المطلوبة
        if (!formData.get('name') || !formData.get('price')) {
            alert('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        // تحديث المنتج
        const product = this.data.products[productId];
        product.name = formData.get('name');
        product.description = formData.get('description');
        product.price = parseFloat(formData.get('price'));
        product.category = formData.get('category');
        product.sku = formData.get('sku');
        product.updatedAt = new Date().toISOString();

        // حفظ التعديلات
        this.saveSalesData();

        // إغلاق النافذة وتحديث العرض
        const modal = bootstrap.Modal.getInstance(document.getElementById('editProductModal'));
        modal.hide();

        this.refreshData();
        alert('تم تحديث المنتج بنجاح');
    },

    /**
     * حذف المنتج
     */
    deleteProduct: function(productId) {
        if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
            delete this.data.products[productId];
            this.saveSalesData();
            this.refreshData();
            alert('تم حذف المنتج بنجاح');
        }
    },

    /**
     * عرض الفاتورة
     */
    viewInvoice: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            alert('الفاتورة غير موجودة');
            return;
        }

        const customer = this.data.customers[invoice.customerId];

        const modalHTML = `
            <div class="modal fade" id="viewInvoiceModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-file-invoice me-2"></i>فاتورة رقم: ${invoice.number}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="invoice-preview">
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <h6>معلومات الفاتورة</h6>
                                        <table class="table table-sm">
                                            <tr>
                                                <td><strong>رقم الفاتورة:</strong></td>
                                                <td>${invoice.number}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>التاريخ:</strong></td>
                                                <td>${new Date(invoice.date).toLocaleDateString('ar-SA')}</td>
                                            </tr>
                                            ${invoice.dueDate ? `
                                            <tr>
                                                <td><strong>تاريخ الاستحقاق:</strong></td>
                                                <td>${new Date(invoice.dueDate).toLocaleDateString('ar-SA')}</td>
                                            </tr>
                                            ` : ''}
                                            ${invoice.reference ? `
                                            <tr>
                                                <td><strong>المرجع:</strong></td>
                                                <td>${invoice.reference}</td>
                                            </tr>
                                            ` : ''}
                                            <tr>
                                                <td><strong>الحالة:</strong></td>
                                                <td>
                                                    <span class="badge bg-${this.getInvoiceStatusColor(invoice.status)}">
                                                        ${this.getInvoiceStatusLabel(invoice.status)}
                                                    </span>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>معلومات العميل</h6>
                                        ${customer ? `
                                            <table class="table table-sm">
                                                <tr>
                                                    <td><strong>الاسم:</strong></td>
                                                    <td>${customer.name}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>الإيميل:</strong></td>
                                                    <td>${customer.email}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>الهاتف:</strong></td>
                                                    <td>${customer.phone}</td>
                                                </tr>
                                                ${customer.address ? `
                                                <tr>
                                                    <td><strong>العنوان:</strong></td>
                                                    <td>${customer.address}</td>
                                                </tr>
                                                ` : ''}
                                            </table>
                                        ` : '<p class="text-muted">عميل غير محدد</p>'}
                                    </div>
                                </div>

                                <h6>عناصر الفاتورة</h6>
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>المنتج</th>
                                                <th>الكمية</th>
                                                <th>السعر</th>
                                                <th>الإجمالي</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${invoice.items.map(item => `
                                                <tr>
                                                    <td>${item.name}</td>
                                                    <td>${item.quantity}</td>
                                                    <td>${this.formatAmount(item.price)}</td>
                                                    <td>${this.formatAmount(item.total)}</td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                                <td colspan="3"><strong>المجموع الفرعي:</strong></td>
                                                <td><strong>${this.formatAmount(invoice.subtotal)}</strong></td>
                                            </tr>
                                            <tr>
                                                <td colspan="3"><strong>الضريبة:</strong></td>
                                                <td><strong>${this.formatAmount(invoice.tax)}</strong></td>
                                            </tr>
                                            <tr class="table-primary">
                                                <td colspan="3"><strong>الإجمالي:</strong></td>
                                                <td><strong>${this.formatAmount(invoice.total)}</strong></td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>

                                ${invoice.notes ? `
                                    <div class="mt-3">
                                        <h6>ملاحظات</h6>
                                        <p>${invoice.notes}</p>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-info" onclick="window.SalesComponent.printInvoice('${invoice.id}')">
                                <i class="fas fa-print me-1"></i>طباعة
                            </button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.editInvoice('${invoice.id}')">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('viewInvoiceModal'));
        modal.show();

        document.getElementById('viewInvoiceModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * تعديل الفاتورة
     */
    editInvoice: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            alert('الفاتورة غير موجودة');
            return;
        }

        const customers = Object.values(this.data.customers || {});
        const products = Object.values(this.data.products || {});

        const modalHTML = `
            <div class="modal fade" id="editInvoiceModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-edit me-2"></i>تعديل فاتورة رقم: ${invoice.number}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="editInvoiceForm">
                                <input type="hidden" name="invoiceId" value="${invoiceId}">

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">رقم الفاتورة</label>
                                        <input type="text" class="form-control" name="number" value="${invoice.number}" readonly>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">تاريخ الفاتورة</label>
                                        <input type="date" class="form-control" name="date" value="${invoice.date}" required>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">العميل *</label>
                                        <select class="form-control" name="customerId" required>
                                            <option value="">اختر العميل</option>
                                            ${customers.map(customer => `
                                                <option value="${customer.id}" ${customer.id === invoice.customerId ? 'selected' : ''}>
                                                    ${customer.name}
                                                </option>
                                            `).join('')}
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">تاريخ الاستحقاق</label>
                                        <input type="date" class="form-control" name="dueDate" value="${invoice.dueDate || ''}">
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">المرجع</label>
                                        <input type="text" class="form-control" name="reference" value="${invoice.reference || ''}">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">الحالة</label>
                                        <select class="form-control" name="status">
                                            <option value="draft" ${invoice.status === 'draft' ? 'selected' : ''}>مسودة</option>
                                            <option value="sent" ${invoice.status === 'sent' ? 'selected' : ''}>مرسلة</option>
                                            <option value="paid" ${invoice.status === 'paid' ? 'selected' : ''}>مدفوعة</option>
                                            <option value="overdue" ${invoice.status === 'overdue' ? 'selected' : ''}>متأخرة</option>
                                            <option value="cancelled" ${invoice.status === 'cancelled' ? 'selected' : ''}>ملغية</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- عناصر الفاتورة -->
                                <div class="card mb-3">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">عناصر الفاتورة</h6>
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="window.SalesComponent.addEditInvoiceItem()">
                                            <i class="fas fa-plus me-1"></i>إضافة عنصر
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div id="editInvoiceItems">
                                            ${invoice.items.map((item, index) => `
                                                <div class="invoice-item row mb-2">
                                                    <div class="col-md-4">
                                                        <select class="form-control item-product" name="items[${index}][productId]" onchange="window.SalesComponent.updateEditItemFromProduct(${index}, this.value)">
                                                            <option value="">اختر المنتج</option>
                                                            ${products.map(product => `
                                                                <option value="${product.id}" data-price="${product.price}" ${product.id === item.productId ? 'selected' : ''}>
                                                                    ${product.name}
                                                                </option>
                                                            `).join('')}
                                                        </select>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <input type="number" class="form-control item-quantity" name="items[${index}][quantity]"
                                                               value="${item.quantity}" min="1" onchange="window.SalesComponent.calculateEditItemTotal(${index})">
                                                    </div>
                                                    <div class="col-md-2">
                                                        <input type="number" class="form-control item-price" name="items[${index}][price]"
                                                               value="${item.price}" step="0.01" onchange="window.SalesComponent.calculateEditItemTotal(${index})">
                                                    </div>
                                                    <div class="col-md-2">
                                                        <input type="number" class="form-control item-total" name="items[${index}][total]"
                                                               value="${item.total}" readonly>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="window.SalesComponent.removeEditInvoiceItem(${index})">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            `).join('')}
                                        </div>
                                    </div>
                                </div>

                                <!-- الإجماليات -->
                                <div class="card">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-8">
                                                <label class="form-label">ملاحظات</label>
                                                <textarea class="form-control" name="notes" rows="3">${invoice.notes || ''}</textarea>
                                            </div>
                                            <div class="col-md-4">
                                                <table class="table table-sm">
                                                    <tr>
                                                        <td>المجموع الفرعي:</td>
                                                        <td class="text-end"><span id="editInvoiceSubtotal">${this.formatAmount(invoice.subtotal)}</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td>الضريبة (${(this.data.settings.taxRate * 100)}%):</td>
                                                        <td class="text-end"><span id="editInvoiceTax">${this.formatAmount(invoice.tax)}</span></td>
                                                    </tr>
                                                    <tr class="table-primary">
                                                        <td><strong>الإجمالي:</strong></td>
                                                        <td class="text-end"><strong><span id="editInvoiceTotal">${this.formatAmount(invoice.total)}</span></strong></td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.updateInvoice()">
                                <i class="fas fa-save me-1"></i>حفظ التعديلات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('editInvoiceModal'));
        modal.show();

        // حساب الإجماليات الأولية
        setTimeout(() => {
            this.calculateEditInvoiceTotal();
        }, 100);

        document.getElementById('editInvoiceModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * طباعة الفاتورة
     */
    printInvoice: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            alert('الفاتورة غير موجودة');
            return;
        }

        const customer = this.data.customers[invoice.customerId];

        const printHTML = `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>فاتورة رقم ${invoice.number}</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        margin: 20px;
                        direction: rtl;
                        color: #333;
                    }
                    .invoice-header {
                        text-align: center;
                        margin-bottom: 30px;
                        border-bottom: 2px solid #007bff;
                        padding-bottom: 20px;
                    }
                    .company-name {
                        font-size: 24px;
                        font-weight: bold;
                        color: #007bff;
                        margin-bottom: 10px;
                    }
                    .invoice-title {
                        font-size: 20px;
                        margin: 10px 0;
                    }
                    .invoice-info {
                        display: flex;
                        justify-content: space-between;
                        margin: 20px 0;
                    }
                    .info-section {
                        width: 48%;
                    }
                    .info-section h4 {
                        color: #007bff;
                        border-bottom: 1px solid #007bff;
                        padding-bottom: 5px;
                        margin-bottom: 10px;
                    }
                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin: 20px 0;
                    }
                    th, td {
                        border: 1px solid #ddd;
                        padding: 12px;
                        text-align: right;
                    }
                    th {
                        background-color: #007bff;
                        color: white;
                        font-weight: bold;
                    }
                    .total-row {
                        background-color: #f8f9fa;
                        font-weight: bold;
                    }
                    .final-total {
                        background-color: #007bff;
                        color: white;
                        font-weight: bold;
                    }
                    .notes {
                        margin-top: 20px;
                        padding: 15px;
                        background-color: #f8f9fa;
                        border-radius: 5px;
                    }
                    @media print {
                        body { margin: 0; }
                        .no-print { display: none; }
                    }
                </style>
            </head>
            <body>
                <div class="invoice-header">
                    <div class="company-name">${this.data.settings.companyName}</div>
                    <div>${this.data.settings.companyAddress}</div>
                    <div>هاتف: ${this.data.settings.companyPhone} | إيميل: ${this.data.settings.companyEmail}</div>
                    ${this.data.settings.companyTaxNumber ? `<div>الرقم الضريبي: ${this.data.settings.companyTaxNumber}</div>` : ''}
                    <div class="invoice-title">فاتورة رقم: ${invoice.number}</div>
                </div>

                <div class="invoice-info">
                    <div class="info-section">
                        <h4>معلومات الفاتورة</h4>
                        <p><strong>رقم الفاتورة:</strong> ${invoice.number}</p>
                        <p><strong>التاريخ:</strong> ${new Date(invoice.date).toLocaleDateString('ar-SA')}</p>
                        ${invoice.dueDate ? `<p><strong>تاريخ الاستحقاق:</strong> ${new Date(invoice.dueDate).toLocaleDateString('ar-SA')}</p>` : ''}
                        ${invoice.reference ? `<p><strong>المرجع:</strong> ${invoice.reference}</p>` : ''}
                        <p><strong>الحالة:</strong> ${this.getInvoiceStatusLabel(invoice.status)}</p>
                    </div>
                    <div class="info-section">
                        <h4>معلومات العميل</h4>
                        ${customer ? `
                            <p><strong>الاسم:</strong> ${customer.name}</p>
                            <p><strong>الإيميل:</strong> ${customer.email}</p>
                            <p><strong>الهاتف:</strong> ${customer.phone}</p>
                            ${customer.address ? `<p><strong>العنوان:</strong> ${customer.address}</p>` : ''}
                            ${customer.taxNumber ? `<p><strong>الرقم الضريبي:</strong> ${customer.taxNumber}</p>` : ''}
                        ` : '<p>عميل غير محدد</p>'}
                    </div>
                </div>

                <table>
                    <thead>
                        <tr>
                            <th>المنتج/الخدمة</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${invoice.items.map(item => `
                            <tr>
                                <td>${item.name}</td>
                                <td>${item.quantity}</td>
                                <td>${this.formatAmount(item.price)}</td>
                                <td>${this.formatAmount(item.total)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                    <tfoot>
                        <tr class="total-row">
                            <td colspan="3">المجموع الفرعي</td>
                            <td>${this.formatAmount(invoice.subtotal)}</td>
                        </tr>
                        <tr class="total-row">
                            <td colspan="3">الضريبة (${(this.data.settings.taxRate * 100)}%)</td>
                            <td>${this.formatAmount(invoice.tax)}</td>
                        </tr>
                        <tr class="final-total">
                            <td colspan="3">الإجمالي النهائي</td>
                            <td>${this.formatAmount(invoice.total)}</td>
                        </tr>
                    </tfoot>
                </table>

                ${invoice.notes ? `
                    <div class="notes">
                        <h4>ملاحظات:</h4>
                        <p>${invoice.notes}</p>
                    </div>
                ` : ''}

                <div style="margin-top: 40px; text-align: center; color: #666; font-size: 12px;">
                    <p>شكراً لتعاملكم معنا</p>
                    <p>تم إنشاء هذه الفاتورة بواسطة نظام إدارة السفريات</p>
                </div>
            </body>
            </html>
        `;

        // فتح نافذة جديدة للطباعة
        const printWindow = window.open('', '_blank');
        printWindow.document.write(printHTML);
        printWindow.document.close();
        printWindow.focus();

        // طباعة تلقائية
        setTimeout(() => {
            printWindow.print();
        }, 500);
    },

    /**
     * إضافة عنصر جديد للفاتورة المُعدلة
     */
    addEditInvoiceItem: function() {
        const itemsContainer = document.getElementById('editInvoiceItems');
        const itemCount = itemsContainer.children.length;
        const products = Object.values(this.data.products || {});

        const itemHTML = `
            <div class="invoice-item row mb-2">
                <div class="col-md-4">
                    <select class="form-control item-product" name="items[${itemCount}][productId]" onchange="window.SalesComponent.updateEditItemFromProduct(${itemCount}, this.value)">
                        <option value="">اختر المنتج</option>
                        ${products.map(product => `
                            <option value="${product.id}" data-price="${product.price}">${product.name}</option>
                        `).join('')}
                    </select>
                </div>
                <div class="col-md-2">
                    <input type="number" class="form-control item-quantity" name="items[${itemCount}][quantity]"
                           placeholder="الكمية" value="1" min="1" onchange="window.SalesComponent.calculateEditItemTotal(${itemCount})">
                </div>
                <div class="col-md-2">
                    <input type="number" class="form-control item-price" name="items[${itemCount}][price]"
                           placeholder="السعر" step="0.01" onchange="window.SalesComponent.calculateEditItemTotal(${itemCount})">
                </div>
                <div class="col-md-2">
                    <input type="number" class="form-control item-total" name="items[${itemCount}][total]"
                           placeholder="الإجمالي" readonly>
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="window.SalesComponent.removeEditInvoiceItem(${itemCount})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;

        itemsContainer.insertAdjacentHTML('beforeend', itemHTML);
    },

    /**
     * إزالة عنصر من الفاتورة المُعدلة
     */
    removeEditInvoiceItem: function(itemIndex) {
        const itemsContainer = document.getElementById('editInvoiceItems');
        const items = itemsContainer.children;

        if (items.length > 1) {
            items[itemIndex].remove();
            this.calculateEditInvoiceTotal();
        } else {
            alert('يجب أن تحتوي الفاتورة على عنصر واحد على الأقل');
        }
    },

    /**
     * تحديث عنصر من المنتج المختار في التعديل
     */
    updateEditItemFromProduct: function(itemIndex, productId) {
        if (!productId) return;

        const product = this.data.products[productId];
        if (!product) return;

        const itemsContainer = document.getElementById('editInvoiceItems');
        const item = itemsContainer.children[itemIndex];

        const priceInput = item.querySelector('.item-price');
        priceInput.value = product.price;

        this.calculateEditItemTotal(itemIndex);
    },

    /**
     * حساب إجمالي العنصر في التعديل
     */
    calculateEditItemTotal: function(itemIndex) {
        const itemsContainer = document.getElementById('editInvoiceItems');
        const item = itemsContainer.children[itemIndex];

        const quantity = parseFloat(item.querySelector('.item-quantity').value) || 0;
        const price = parseFloat(item.querySelector('.item-price').value) || 0;
        const total = quantity * price;

        item.querySelector('.item-total').value = total.toFixed(2);

        this.calculateEditInvoiceTotal();
    },

    /**
     * حساب إجمالي الفاتورة في التعديل
     */
    calculateEditInvoiceTotal: function() {
        const itemsContainer = document.getElementById('editInvoiceItems');
        let subtotal = 0;

        Array.from(itemsContainer.children).forEach(item => {
            const total = parseFloat(item.querySelector('.item-total').value) || 0;
            subtotal += total;
        });

        const tax = subtotal * this.data.settings.taxRate;
        const total = subtotal + tax;

        document.getElementById('editInvoiceSubtotal').textContent = this.formatAmount(subtotal);
        document.getElementById('editInvoiceTax').textContent = this.formatAmount(tax);
        document.getElementById('editInvoiceTotal').textContent = this.formatAmount(total);
    },

    /**
     * تحديث الفاتورة
     */
    updateInvoice: function() {
        const form = document.getElementById('editInvoiceForm');
        const formData = new FormData(form);
        const invoiceId = formData.get('invoiceId');

        // التحقق من البيانات المطلوبة
        if (!formData.get('customerId')) {
            alert('يرجى اختيار العميل');
            return;
        }

        // جمع عناصر الفاتورة
        const items = [];
        const itemsContainer = document.getElementById('editInvoiceItems');

        Array.from(itemsContainer.children).forEach((item, index) => {
            const productSelect = item.querySelector('.item-product');
            const quantityInput = item.querySelector('.item-quantity');
            const priceInput = item.querySelector('.item-price');
            const totalInput = item.querySelector('.item-total');

            if (productSelect.value && quantityInput.value && priceInput.value) {
                const product = this.data.products[productSelect.value];
                items.push({
                    productId: productSelect.value,
                    name: product ? product.name : 'منتج غير محدد',
                    quantity: parseInt(quantityInput.value),
                    price: parseFloat(priceInput.value),
                    total: parseFloat(totalInput.value)
                });
            }
        });

        if (items.length === 0) {
            alert('يرجى إضافة عنصر واحد على الأقل للفاتورة');
            return;
        }

        // حساب الإجماليات
        const subtotal = items.reduce((sum, item) => sum + item.total, 0);
        const tax = subtotal * this.data.settings.taxRate;
        const total = subtotal + tax;

        // تحديث الفاتورة
        const invoice = this.data.invoices[invoiceId];
        invoice.date = formData.get('date');
        invoice.dueDate = formData.get('dueDate');
        invoice.customerId = formData.get('customerId');
        invoice.reference = formData.get('reference');
        invoice.status = formData.get('status');
        invoice.items = items;
        invoice.subtotal = subtotal;
        invoice.tax = tax;
        invoice.total = total;
        invoice.notes = formData.get('notes');
        invoice.updatedAt = new Date().toISOString();

        // حفظ التعديلات
        this.saveSalesData();

        // إغلاق النافذة وتحديث العرض
        const modal = bootstrap.Modal.getInstance(document.getElementById('editInvoiceModal'));
        modal.hide();

        this.refreshData();
        alert('تم تحديث الفاتورة بنجاح');
    },

    /**
     * حذف الفاتورة
     */
    deleteInvoice: function(invoiceId) {
        if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
            // نقل إلى المحذوفات بدلاً من الحذف النهائي
            this.data.deletedInvoices[invoiceId] = {
                ...this.data.invoices[invoiceId],
                deletedAt: new Date().toISOString()
            };

            delete this.data.invoices[invoiceId];
            this.saveSalesData();
            this.refreshData();
            alert('تم حذف الفاتورة بنجاح');
        }
    },

    /**
     * عرض العميل
     */
    viewCustomer: function(customerId) {
        alert('ميزة عرض العميل قيد التطوير');
    },

    /**
     * تعديل العميل
     */
    editCustomer: function(customerId) {
        const customer = this.data.customers[customerId];
        if (!customer) {
            alert('العميل غير موجود');
            return;
        }

        const modalHTML = `
            <div class="modal fade" id="editCustomerModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-user-edit me-2"></i>تعديل العميل: ${customer.name}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="editCustomerForm">
                                <input type="hidden" name="customerId" value="${customerId}">

                                <div class="mb-3">
                                    <label class="form-label">اسم العميل *</label>
                                    <input type="text" class="form-control" name="name" value="${customer.name}" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني *</label>
                                    <input type="email" class="form-control" name="email" value="${customer.email}" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف *</label>
                                    <input type="tel" class="form-control" name="phone" value="${customer.phone}" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea class="form-control" name="address" rows="3">${customer.address || ''}</textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الرقم الضريبي</label>
                                    <input type="text" class="form-control" name="taxNumber" value="${customer.taxNumber || ''}">
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.updateCustomer()">
                                <i class="fas fa-save me-1"></i>حفظ التعديلات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('editCustomerModal'));
        modal.show();

        document.getElementById('editCustomerModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * تحديث العميل
     */
    updateCustomer: function() {
        const form = document.getElementById('editCustomerForm');
        const formData = new FormData(form);
        const customerId = formData.get('customerId');

        // التحقق من البيانات المطلوبة
        if (!formData.get('name') || !formData.get('email') || !formData.get('phone')) {
            alert('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        // تحديث العميل
        const customer = this.data.customers[customerId];
        customer.name = formData.get('name');
        customer.email = formData.get('email');
        customer.phone = formData.get('phone');
        customer.address = formData.get('address');
        customer.taxNumber = formData.get('taxNumber');
        customer.updatedAt = new Date().toISOString();

        // حفظ التعديلات
        this.saveSalesData();

        // إغلاق النافذة وتحديث العرض
        const modal = bootstrap.Modal.getInstance(document.getElementById('editCustomerModal'));
        modal.hide();

        this.refreshData();
        alert('تم تحديث العميل بنجاح');
    },

    /**
     * حذف العميل
     */
    deleteCustomer: function(customerId) {
        if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
            delete this.data.customers[customerId];
            this.saveSalesData();
            this.refreshData();
            alert('تم حذف العميل بنجاح');
        }
    },

    /**
     * حساب الإحصائيات
     */
    calculateStats: function(invoices) {
        const stats = {
            totalRevenue: 0,
            statusBreakdown: {},
            topCustomers: [],
            topProducts: [],
            monthlyStats: {}
        };

        // حساب الإيرادات الإجمالية وتوزيع الحالات
        invoices.forEach(invoice => {
            stats.totalRevenue += invoice.total || 0;

            if (!stats.statusBreakdown[invoice.status]) {
                stats.statusBreakdown[invoice.status] = { count: 0, amount: 0 };
            }
            stats.statusBreakdown[invoice.status].count++;
            stats.statusBreakdown[invoice.status].amount += invoice.total || 0;
        });

        // حساب أفضل العملاء
        const customerStats = {};
        invoices.forEach(invoice => {
            const customer = this.data.customers[invoice.customerId];
            if (customer) {
                if (!customerStats[customer.id]) {
                    customerStats[customer.id] = {
                        name: customer.name,
                        totalAmount: 0,
                        invoiceCount: 0
                    };
                }
                customerStats[customer.id].totalAmount += invoice.total || 0;
                customerStats[customer.id].invoiceCount++;
            }
        });

        stats.topCustomers = Object.values(customerStats)
            .sort((a, b) => b.totalAmount - a.totalAmount)
            .slice(0, 5);

        // حساب أفضل المنتجات
        const productStats = {};
        invoices.forEach(invoice => {
            if (invoice.items) {
                invoice.items.forEach(item => {
                    if (!productStats[item.productId]) {
                        productStats[item.productId] = {
                            name: item.name,
                            totalQuantity: 0,
                            totalRevenue: 0
                        };
                    }
                    productStats[item.productId].totalQuantity += item.quantity || 0;
                    productStats[item.productId].totalRevenue += item.total || 0;
                });
            }
        });

        stats.topProducts = Object.values(productStats)
            .sort((a, b) => b.totalRevenue - a.totalRevenue)
            .slice(0, 5);

        // حساب الإحصائيات الشهرية
        invoices.forEach(invoice => {
            const date = new Date(invoice.date);
            const monthKey = date.toLocaleDateString('ar-SA', { year: 'numeric', month: 'long' });

            if (!stats.monthlyStats[monthKey]) {
                stats.monthlyStats[monthKey] = {
                    revenue: 0,
                    invoices: 0
                };
            }
            stats.monthlyStats[monthKey].revenue += invoice.total || 0;
            stats.monthlyStats[monthKey].invoices++;
        });

        return stats;
    },

    /**
     * تصدير تقرير PDF
     */
    exportReportPDF: function() {
        alert('ميزة تصدير PDF قيد التطوير');
    },

    /**
     * تصدير تقرير Excel
     */
    exportReportExcel: function() {
        alert('ميزة تصدير Excel قيد التطوير');
    },

    /**
     * تحديث التقارير
     */
    updateReports: function() {
        // إعادة حساب وعرض التقارير
        if (this.data.currentView === 'reports') {
            this.refreshData();
        }
    },

    /**
     * حفظ الإعدادات
     */
    saveSettings: function() {
        try {
            // جمع البيانات من النموذج
            this.data.settings.companyName = document.getElementById('companyName')?.value || this.data.settings.companyName;
            this.data.settings.companyAddress = document.getElementById('companyAddress')?.value || this.data.settings.companyAddress;
            this.data.settings.companyPhone = document.getElementById('companyPhone')?.value || this.data.settings.companyPhone;
            this.data.settings.companyEmail = document.getElementById('companyEmail')?.value || this.data.settings.companyEmail;
            this.data.settings.companyWebsite = document.getElementById('companyWebsite')?.value || this.data.settings.companyWebsite;
            this.data.settings.companyTaxNumber = document.getElementById('companyTaxNumber')?.value || this.data.settings.companyTaxNumber;

            this.data.settings.invoicePrefix = document.getElementById('invoicePrefix')?.value || this.data.settings.invoicePrefix;
            this.data.settings.nextInvoiceNumber = parseInt(document.getElementById('nextInvoiceNumber')?.value) || this.data.settings.nextInvoiceNumber;
            this.data.settings.taxRate = parseFloat(document.getElementById('taxRate')?.value) / 100 || this.data.settings.taxRate;
            this.data.settings.currency = document.getElementById('currency')?.value || this.data.settings.currency;
            this.data.settings.defaultDueDays = parseInt(document.getElementById('defaultDueDays')?.value) || this.data.settings.defaultDueDays;

            this.data.settings.language = document.getElementById('language')?.value || this.data.settings.language;
            this.data.settings.timezone = document.getElementById('timezone')?.value || this.data.settings.timezone;
            this.data.settings.autoSave = document.getElementById('autoSave')?.checked || false;
            this.data.settings.showNotifications = document.getElementById('showNotifications')?.checked || false;
            this.data.settings.enableBackup = document.getElementById('enableBackup')?.checked || false;
            this.data.settings.autoCalculateTax = document.getElementById('autoCalculateTax')?.checked || false;

            // حفظ البيانات
            this.saveSalesData();
            alert('تم حفظ الإعدادات بنجاح');

        } catch (error) {
            console.error('خطأ في حفظ الإعدادات:', error);
            alert('حدث خطأ أثناء حفظ الإعدادات');
        }
    },

    /**
     * تصدير جميع البيانات
     */
    exportAllData: function() {
        try {
            const dataToExport = {
                invoices: this.data.invoices,
                customers: this.data.customers,
                products: this.data.products,
                settings: this.data.settings,
                exportDate: new Date().toISOString(),
                version: '1.0'
            };

            const dataStr = JSON.stringify(dataToExport, null, 2);
            const blob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `sales_data_backup_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            alert('تم تصدير البيانات بنجاح');
        } catch (error) {
            console.error('خطأ في تصدير البيانات:', error);
            alert('حدث خطأ أثناء تصدير البيانات');
        }
    },

    /**
     * استيراد البيانات
     */
    importData: function() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';

        input.onchange = (event) => {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const importedData = JSON.parse(e.target.result);

                    if (confirm('هل أنت متأكد من استيراد البيانات؟ سيتم استبدال البيانات الحالية.')) {
                        if (importedData.invoices) this.data.invoices = importedData.invoices;
                        if (importedData.customers) this.data.customers = importedData.customers;
                        if (importedData.products) this.data.products = importedData.products;
                        if (importedData.settings) this.data.settings = { ...this.data.settings, ...importedData.settings };

                        this.saveSalesData();
                        this.refreshData();
                        alert('تم استيراد البيانات بنجاح');
                    }
                } catch (error) {
                    console.error('خطأ في استيراد البيانات:', error);
                    alert('ملف البيانات غير صالح');
                }
            };
            reader.readAsText(file);
        };

        input.click();
    },

    /**
     * إعادة تعيين الإعدادات
     */
    resetSettings: function() {
        if (confirm('هل أنت متأكد من إعادة تعيين الإعدادات إلى القيم الافتراضية؟')) {
            this.data.settings = {
                taxRate: 0.15,
                currency: 'SAR',
                invoicePrefix: 'INV-',
                nextInvoiceNumber: this.data.settings.nextInvoiceNumber, // الاحتفاظ برقم الفاتورة التالي
                companyName: 'قمة الوعد للسفريات',
                companyAddress: 'المملكة العربية السعودية',
                companyTaxNumber: '*********',
                companyEmail: '<EMAIL>',
                companyPhone: '+966501234567',
                companyWebsite: 'https://qimat-alwaed.com',
                language: 'ar',
                timezone: 'Asia/Riyadh',
                dateFormat: 'ar-SA',
                autoSave: true,
                showNotifications: true,
                enableBackup: true,
                autoCalculateTax: true,
                defaultDueDays: 30
            };

            this.saveSalesData();
            this.refreshData();
            alert('تم إعادة تعيين الإعدادات بنجاح');
        }
    },

    /**
     * مسح جميع البيانات
     */
    clearAllData: function() {
        if (confirm('تحذير: هذا الإجراء سيحذف جميع البيانات نهائياً. هل أنت متأكد؟')) {
            if (confirm('تأكيد أخير: سيتم حذف جميع الفواتير والعملاء والمنتجات. هل تريد المتابعة؟')) {
                this.data.invoices = {};
                this.data.customers = {};
                this.data.products = {};
                this.data.deletedInvoices = {};

                this.saveSalesData();
                this.refreshData();
                alert('تم مسح جميع البيانات');
            }
        }
    },

    /**
     * حساب حجم البيانات
     */
    getDataSize: function() {
        try {
            const dataStr = JSON.stringify(this.data);
            const sizeInBytes = new Blob([dataStr]).size;

            if (sizeInBytes < 1024) {
                return sizeInBytes + ' بايت';
            } else if (sizeInBytes < 1024 * 1024) {
                return (sizeInBytes / 1024).toFixed(2) + ' كيلوبايت';
            } else {
                return (sizeInBytes / (1024 * 1024)).toFixed(2) + ' ميجابايت';
            }
        } catch (error) {
            return 'غير محدد';
        }
    },

    /**
     * عرض قسم عروض الأسعار
     */
    renderQuotesView: function() {
        const quotes = Object.values(this.data.quotes || {});

        return `
            <div class="quotes-management">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4><i class="fas fa-file-contract me-2"></i>إدارة عروض الأسعار</h4>
                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="window.SalesComponent.showCreateQuoteModal()">
                            <i class="fas fa-plus me-1"></i>عرض سعر جديد
                        </button>
                        <button class="btn btn-outline-secondary" onclick="window.SalesComponent.exportQuotesCSV()">
                            <i class="fas fa-download me-1"></i>تصدير CSV
                        </button>
                    </div>
                </div>

                <!-- إحصائيات عروض الأسعار -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-file-contract fa-2x mb-2"></i>
                                <h3>${quotes.length}</h3>
                                <p class="mb-0">إجمالي العروض</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <h3>${quotes.filter(q => q.status === 'pending').length}</h3>
                                <p class="mb-0">في الانتظار</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <h3>${quotes.filter(q => q.status === 'accepted').length}</h3>
                                <p class="mb-0">مقبولة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-times-circle fa-2x mb-2"></i>
                                <h3>${quotes.filter(q => q.status === 'rejected').length}</h3>
                                <p class="mb-0">مرفوضة</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- فلاتر البحث -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="quoteFilterDateFrom"
                                       onchange="window.SalesComponent.updateQuoteFilter('dateFrom', this.value)">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="quoteFilterDateTo"
                                       onchange="window.SalesComponent.updateQuoteFilter('dateTo', this.value)">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الحالة</label>
                                <select class="form-control" id="quoteFilterStatus"
                                        onchange="window.SalesComponent.updateQuoteFilter('status', this.value)">
                                    <option value="">جميع الحالات</option>
                                    <option value="pending">في الانتظار</option>
                                    <option value="accepted">مقبولة</option>
                                    <option value="rejected">مرفوضة</option>
                                    <option value="expired">منتهية الصلاحية</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">العميل</label>
                                <select class="form-control" id="quoteFilterCustomer"
                                        onchange="window.SalesComponent.updateQuoteFilter('customer', this.value)">
                                    <option value="">جميع العملاء</option>
                                    ${Object.values(this.data.customers || {}).map(customer => `
                                        <option value="${customer.id}">${customer.name}</option>
                                    `).join('')}
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول عروض الأسعار -->
                <div class="card">
                    <div class="card-body">
                        ${quotes.length === 0 ? `
                            <div class="text-center py-5">
                                <i class="fas fa-file-contract fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد عروض أسعار</h5>
                                <p class="text-muted">ابدأ بإنشاء عرض سعر جديد</p>
                                <button class="btn btn-primary" onclick="window.SalesComponent.showCreateQuoteModal()">
                                    <i class="fas fa-plus me-1"></i>إنشاء عرض سعر
                                </button>
                            </div>
                        ` : `
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>رقم العرض</th>
                                            <th>العميل</th>
                                            <th>التاريخ</th>
                                            <th>صالح حتى</th>
                                            <th>الإجمالي</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${this.getFilteredQuotes().map(quote => `
                                            <tr>
                                                <td><strong>${quote.number}</strong></td>
                                                <td>${this.data.customers[quote.customerId]?.name || 'عميل غير محدد'}</td>
                                                <td>${new Date(quote.date).toLocaleDateString('ar-SA')}</td>
                                                <td>${new Date(quote.validUntil).toLocaleDateString('ar-SA')}</td>
                                                <td><strong>${this.formatAmount(quote.total)}</strong></td>
                                                <td>
                                                    <span class="badge bg-${this.getQuoteStatusColor(quote.status)}">
                                                        ${this.getQuoteStatusLabel(quote.status)}
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-primary" onclick="window.SalesComponent.viewQuote('${quote.id}')" title="عرض">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="btn btn-outline-success" onclick="window.SalesComponent.editQuote('${quote.id}')" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-outline-info" onclick="window.SalesComponent.printQuote('${quote.id}')" title="طباعة">
                                                            <i class="fas fa-print"></i>
                                                        </button>
                                                        ${quote.status === 'accepted' ? `
                                                            <button class="btn btn-outline-warning" onclick="window.SalesComponent.convertToInvoice('${quote.id}')" title="تحويل لفاتورة">
                                                                <i class="fas fa-exchange-alt"></i>
                                                            </button>
                                                        ` : ''}
                                                        <button class="btn btn-outline-danger" onclick="window.SalesComponent.deleteQuote('${quote.id}')" title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        `}
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض قسم المدفوعات
     */
    renderPaymentsView: function() {
        const overdueInvoices = Object.values(this.data.invoices || {}).filter(inv => inv.status === 'overdue');
        const pendingInvoices = Object.values(this.data.invoices || {}).filter(inv => inv.status === 'sent');

        return `
            <div class="payments-management">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4><i class="fas fa-credit-card me-2"></i>إدارة المدفوعات</h4>
                    <button class="btn btn-primary" onclick="window.SalesComponent.showRecordPaymentModal()">
                        <i class="fas fa-plus me-1"></i>تسجيل دفعة
                    </button>
                </div>

                <!-- إحصائيات المدفوعات -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                                <h3>${overdueInvoices.length}</h3>
                                <p class="mb-0">فواتير متأخرة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <h3>${pendingInvoices.length}</h3>
                                <p class="mb-0">فواتير معلقة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <h3>${this.formatAmount(this.getTotalPaidAmount())}</h3>
                                <p class="mb-0">إجمالي المحصل</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الفواتير المتأخرة -->
                ${overdueInvoices.length > 0 ? `
                    <div class="card mb-4">
                        <div class="card-header bg-danger text-white">
                            <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>فواتير متأخرة تحتاج متابعة</h6>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>رقم الفاتورة</th>
                                            <th>العميل</th>
                                            <th>المبلغ</th>
                                            <th>تاريخ الاستحقاق</th>
                                            <th>الأيام المتأخرة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${overdueInvoices.map(invoice => {
                                            const customer = this.data.customers[invoice.customerId];
                                            const daysOverdue = invoice.dueDate ? Math.floor((new Date() - new Date(invoice.dueDate)) / (1000 * 60 * 60 * 24)) : 0;
                                            return `
                                                <tr>
                                                    <td><strong>${invoice.number}</strong></td>
                                                    <td>${customer?.name || 'عميل غير محدد'}</td>
                                                    <td><strong class="text-danger">${this.formatAmount(invoice.total)}</strong></td>
                                                    <td>${invoice.dueDate ? new Date(invoice.dueDate).toLocaleDateString('ar-SA') : '-'}</td>
                                                    <td><span class="badge bg-danger">${daysOverdue} يوم</span></td>
                                                    <td>
                                                        <button class="btn btn-sm btn-success" onclick="window.SalesComponent.markAsPaid('${invoice.id}')">
                                                            <i class="fas fa-check me-1"></i>تم الدفع
                                                        </button>
                                                        <button class="btn btn-sm btn-info" onclick="window.SalesComponent.sendReminder('${invoice.id}')">
                                                            <i class="fas fa-bell me-1"></i>تذكير
                                                        </button>
                                                    </td>
                                                </tr>
                                            `;
                                        }).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                ` : ''}

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>قريباً:</strong> نظام إدارة المدفوعات المتقدم
                    <ul class="mt-2 mb-0">
                        <li>تسجيل المدفوعات الجزئية</li>
                        <li>ربط مع بوابات الدفع الإلكتروني</li>
                        <li>تقارير التدفق النقدي</li>
                        <li>تذكيرات تلقائية للعملاء</li>
                    </ul>
                </div>
            </div>
        `;
    },

    /**
     * عرض قسم المخزون
     */
    renderInventoryView: function() {
        const products = Object.values(this.data.products || {});
        const lowStockProducts = this.getLowStockProducts(10);

        return `
            <div class="inventory-management">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4><i class="fas fa-warehouse me-2"></i>إدارة المخزون</h4>
                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="window.SalesComponent.showStockAdjustmentModal()">
                            <i class="fas fa-edit me-1"></i>تعديل المخزون
                        </button>
                        <button class="btn btn-outline-info" onclick="window.SalesComponent.exportInventoryReport()">
                            <i class="fas fa-download me-1"></i>تقرير المخزون
                        </button>
                    </div>
                </div>

                <!-- إحصائيات المخزون -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-boxes fa-2x mb-2"></i>
                                <h3>${products.length}</h3>
                                <p class="mb-0">إجمالي المنتجات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                                <h3>${lowStockProducts.length}</h3>
                                <p class="mb-0">مخزون منخفض</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-line fa-2x mb-2"></i>
                                <h3>${this.formatAmount(this.getTotalInventoryValue())}</h3>
                                <p class="mb-0">قيمة المخزون</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-sync fa-2x mb-2"></i>
                                <h3>85%</h3>
                                <p class="mb-0">معدل الدوران</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- المنتجات منخفضة المخزون -->
                ${lowStockProducts.length > 0 ? `
                    <div class="card mb-4">
                        <div class="card-header bg-warning text-white">
                            <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>منتجات تحتاج إعادة تموين</h6>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>المنتج</th>
                                            <th>الفئة</th>
                                            <th>المخزون الحالي</th>
                                            <th>الحد الأدنى</th>
                                            <th>السعر</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${lowStockProducts.map(product => `
                                            <tr>
                                                <td><strong>${product.name}</strong></td>
                                                <td><span class="badge bg-secondary">${product.category || 'غير محدد'}</span></td>
                                                <td><span class="badge bg-danger">${product.stock}</span></td>
                                                <td>10</td>
                                                <td>${this.formatAmount(product.price)}</td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary" onclick="window.SalesComponent.reorderProduct('${product.id}')">
                                                        <i class="fas fa-shopping-cart me-1"></i>إعادة طلب
                                                    </button>
                                                </td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                ` : ''}

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>قريباً:</strong> نظام إدارة المخزون المتقدم
                    <ul class="mt-2 mb-0">
                        <li>تتبع حركة المخزون في الوقت الفعلي</li>
                        <li>تنبيهات تلقائية للمخزون المنخفض</li>
                        <li>إدارة الموردين وأوامر الشراء</li>
                        <li>تقارير تحليل المخزون والربحية</li>
                    </ul>
                </div>
            </div>
        `;
    },

    /**
     * الحصول على إجمالي المبلغ المدفوع
     */
    getTotalPaidAmount: function() {
        return Object.values(this.data.invoices || {})
            .filter(inv => inv.status === 'paid')
            .reduce((sum, inv) => sum + (inv.total || 0), 0);
    },

    /**
     * الحصول على قيمة المخزون الإجمالية
     */
    getTotalInventoryValue: function() {
        return Object.values(this.data.products || {})
            .reduce((sum, product) => {
                const stock = Math.floor(Math.random() * 50) + 1; // محاكاة المخزون
                return sum + (product.price * stock);
            }, 0);
    },

    /**
     * الحصول على عروض الأسعار المفلترة
     */
    getFilteredQuotes: function() {
        let quotes = Object.values(this.data.quotes || {});

        // فلترة حسب التاريخ
        if (this.data.quoteFilters?.dateFrom) {
            quotes = quotes.filter(quote => quote.date >= this.data.quoteFilters.dateFrom);
        }
        if (this.data.quoteFilters?.dateTo) {
            quotes = quotes.filter(quote => quote.date <= this.data.quoteFilters.dateTo);
        }

        // فلترة حسب الحالة
        if (this.data.quoteFilters?.status) {
            quotes = quotes.filter(quote => quote.status === this.data.quoteFilters.status);
        }

        // فلترة حسب العميل
        if (this.data.quoteFilters?.customer) {
            quotes = quotes.filter(quote => quote.customerId === this.data.quoteFilters.customer);
        }

        return quotes.sort((a, b) => new Date(b.date) - new Date(a.date));
    },

    /**
     * تحديث فلتر عروض الأسعار
     */
    updateQuoteFilter: function(filterName, value) {
        if (!this.data.quoteFilters) this.data.quoteFilters = {};
        this.data.quoteFilters[filterName] = value;
        this.saveSalesData();
        this.refreshData();
    },

    /**
     * الحصول على لون حالة عرض السعر
     */
    getQuoteStatusColor: function(status) {
        const colors = {
            pending: 'warning',
            accepted: 'success',
            rejected: 'danger',
            expired: 'secondary'
        };
        return colors[status] || 'secondary';
    },

    /**
     * الحصول على تسمية حالة عرض السعر
     */
    getQuoteStatusLabel: function(status) {
        const labels = {
            pending: 'في الانتظار',
            accepted: 'مقبول',
            rejected: 'مرفوض',
            expired: 'منتهي الصلاحية'
        };
        return labels[status] || 'غير محدد';
    },

    /**
     * عرض نافذة إنشاء عرض سعر
     */
    showCreateQuoteModal: function() {
        const customers = Object.values(this.data.customers || {});
        const products = Object.values(this.data.products || {});

        if (customers.length === 0) {
            alert('يجب إضافة عميل واحد على الأقل قبل إنشاء عرض سعر');
            return;
        }

        const modalHTML = `
            <div class="modal fade" id="createQuoteModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-file-contract me-2"></i>إنشاء عرض سعر جديد
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="createQuoteForm">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">رقم عرض السعر</label>
                                        <input type="text" class="form-control" name="number"
                                               value="QUO-${this.getNextQuoteNumber()}" readonly>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">تاريخ العرض</label>
                                        <input type="date" class="form-control" name="date"
                                               value="${new Date().toISOString().split('T')[0]}" required>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">العميل *</label>
                                        <select class="form-control" name="customerId" required>
                                            <option value="">اختر العميل</option>
                                            ${customers.map(customer => `
                                                <option value="${customer.id}">${customer.name}</option>
                                            `).join('')}
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">صالح حتى تاريخ</label>
                                        <input type="date" class="form-control" name="validUntil"
                                               value="${new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]}" required>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">عنوان العرض</label>
                                    <input type="text" class="form-control" name="title" placeholder="عنوان عرض السعر">
                                </div>

                                <!-- عناصر عرض السعر -->
                                <div class="card mb-3">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">عناصر عرض السعر</h6>
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="window.SalesComponent.addQuoteItem()">
                                            <i class="fas fa-plus me-1"></i>إضافة عنصر
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div id="quoteItems">
                                            <div class="quote-item row mb-2">
                                                <div class="col-md-4">
                                                    <select class="form-control item-product" name="items[0][productId]" onchange="window.SalesComponent.updateQuoteItemFromProduct(0, this.value)">
                                                        <option value="">اختر المنتج</option>
                                                        ${products.map(product => `
                                                            <option value="${product.id}" data-price="${product.price}">${product.name}</option>
                                                        `).join('')}
                                                    </select>
                                                </div>
                                                <div class="col-md-2">
                                                    <input type="number" class="form-control item-quantity" name="items[0][quantity]"
                                                           placeholder="الكمية" value="1" min="1" onchange="window.SalesComponent.calculateQuoteItemTotal(0)">
                                                </div>
                                                <div class="col-md-2">
                                                    <input type="number" class="form-control item-price" name="items[0][price]"
                                                           placeholder="السعر" step="0.01" onchange="window.SalesComponent.calculateQuoteItemTotal(0)">
                                                </div>
                                                <div class="col-md-2">
                                                    <input type="number" class="form-control item-total" name="items[0][total]"
                                                           placeholder="الإجمالي" readonly>
                                                </div>
                                                <div class="col-md-2">
                                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="window.SalesComponent.removeQuoteItem(0)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- الإجماليات -->
                                <div class="card">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-8">
                                                <label class="form-label">ملاحظات وشروط العرض</label>
                                                <textarea class="form-control" name="notes" rows="4" placeholder="شروط وأحكام عرض السعر..."></textarea>
                                            </div>
                                            <div class="col-md-4">
                                                <table class="table table-sm">
                                                    <tr>
                                                        <td>المجموع الفرعي:</td>
                                                        <td class="text-end"><span id="quoteSubtotal">0.00 ر.س</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td>الضريبة (${(this.data.settings.taxRate * 100)}%):</td>
                                                        <td class="text-end"><span id="quoteTax">0.00 ر.س</span></td>
                                                    </tr>
                                                    <tr class="table-primary">
                                                        <td><strong>الإجمالي:</strong></td>
                                                        <td class="text-end"><strong><span id="quoteTotal">0.00 ر.س</span></strong></td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveQuote()">
                                <i class="fas fa-save me-1"></i>حفظ عرض السعر
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('createQuoteModal'));
        modal.show();

        document.getElementById('createQuoteModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * الحصول على رقم عرض السعر التالي
     */
    getNextQuoteNumber: function() {
        if (!this.data.settings.nextQuoteNumber) {
            this.data.settings.nextQuoteNumber = 1;
        }
        return this.data.settings.nextQuoteNumber;
    },
    /**
     * إضافة عنصر جديد لعرض السعر
     */
    addQuoteItem: function() {
        const itemsContainer = document.getElementById('quoteItems');
        const itemCount = itemsContainer.children.length;
        const products = Object.values(this.data.products || {});

        const itemHTML = `
            <div class="quote-item row mb-2">
                <div class="col-md-4">
                    <select class="form-control item-product" name="items[${itemCount}][productId]" onchange="window.SalesComponent.updateQuoteItemFromProduct(${itemCount}, this.value)">
                        <option value="">اختر المنتج</option>
                        ${products.map(product => `
                            <option value="${product.id}" data-price="${product.price}">${product.name}</option>
                        `).join('')}
                    </select>
                </div>
                <div class="col-md-2">
                    <input type="number" class="form-control item-quantity" name="items[${itemCount}][quantity]"
                           placeholder="الكمية" value="1" min="1" onchange="window.SalesComponent.calculateQuoteItemTotal(${itemCount})">
                </div>
                <div class="col-md-2">
                    <input type="number" class="form-control item-price" name="items[${itemCount}][price]"
                           placeholder="السعر" step="0.01" onchange="window.SalesComponent.calculateQuoteItemTotal(${itemCount})">
                </div>
                <div class="col-md-2">
                    <input type="number" class="form-control item-total" name="items[${itemCount}][total]"
                           placeholder="الإجمالي" readonly>
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="window.SalesComponent.removeQuoteItem(${itemCount})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;

        itemsContainer.insertAdjacentHTML('beforeend', itemHTML);
    },

    /**
     * إزالة عنصر من عرض السعر
     */
    removeQuoteItem: function(itemIndex) {
        const itemsContainer = document.getElementById('quoteItems');
        const items = itemsContainer.children;

        if (items.length > 1) {
            items[itemIndex].remove();
            this.calculateQuoteTotal();
        } else {
            alert('يجب أن يحتوي عرض السعر على عنصر واحد على الأقل');
        }
    },

    /**
     * تحديث عنصر من المنتج المختار
     */
    updateQuoteItemFromProduct: function(itemIndex, productId) {
        if (!productId) return;

        const product = this.data.products[productId];
        if (!product) return;

        const itemsContainer = document.getElementById('quoteItems');
        const item = itemsContainer.children[itemIndex];

        const priceInput = item.querySelector('.item-price');
        priceInput.value = product.price;

        this.calculateQuoteItemTotal(itemIndex);
    },

    /**
     * حساب إجمالي العنصر
     */
    calculateQuoteItemTotal: function(itemIndex) {
        const itemsContainer = document.getElementById('quoteItems');
        const item = itemsContainer.children[itemIndex];

        const quantity = parseFloat(item.querySelector('.item-quantity').value) || 0;
        const price = parseFloat(item.querySelector('.item-price').value) || 0;
        const total = quantity * price;

        item.querySelector('.item-total').value = total.toFixed(2);

        this.calculateQuoteTotal();
    },

    /**
     * حساب إجمالي عرض السعر
     */
    calculateQuoteTotal: function() {
        const itemsContainer = document.getElementById('quoteItems');
        let subtotal = 0;

        Array.from(itemsContainer.children).forEach(item => {
            const total = parseFloat(item.querySelector('.item-total').value) || 0;
            subtotal += total;
        });

        const tax = subtotal * this.data.settings.taxRate;
        const total = subtotal + tax;

        document.getElementById('quoteSubtotal').textContent = this.formatAmount(subtotal);
        document.getElementById('quoteTax').textContent = this.formatAmount(tax);
        document.getElementById('quoteTotal').textContent = this.formatAmount(total);
    },

    /**
     * حفظ عرض السعر
     */
    saveQuote: function() {
        const form = document.getElementById('createQuoteForm');
        const formData = new FormData(form);

        // التحقق من البيانات المطلوبة
        if (!formData.get('customerId')) {
            alert('يرجى اختيار العميل');
            return;
        }

        // جمع عناصر عرض السعر
        const items = [];
        const itemsContainer = document.getElementById('quoteItems');

        Array.from(itemsContainer.children).forEach((item, index) => {
            const productSelect = item.querySelector('.item-product');
            const quantityInput = item.querySelector('.item-quantity');
            const priceInput = item.querySelector('.item-price');
            const totalInput = item.querySelector('.item-total');

            if (productSelect.value && quantityInput.value && priceInput.value) {
                const product = this.data.products[productSelect.value];
                items.push({
                    productId: productSelect.value,
                    name: product ? product.name : 'منتج غير محدد',
                    quantity: parseInt(quantityInput.value),
                    price: parseFloat(priceInput.value),
                    total: parseFloat(totalInput.value)
                });
            }
        });

        if (items.length === 0) {
            alert('يرجى إضافة عنصر واحد على الأقل لعرض السعر');
            return;
        }

        // حساب الإجماليات
        const subtotal = items.reduce((sum, item) => sum + item.total, 0);
        const tax = subtotal * this.data.settings.taxRate;
        const total = subtotal + tax;

        // إنشاء عرض السعر
        const quoteId = 'quote_' + Date.now();
        const quote = {
            id: quoteId,
            number: formData.get('number'),
            date: formData.get('date'),
            validUntil: formData.get('validUntil'),
            customerId: formData.get('customerId'),
            title: formData.get('title'),
            items: items,
            subtotal: subtotal,
            tax: tax,
            total: total,
            status: 'pending',
            notes: formData.get('notes'),
            createdAt: new Date().toISOString()
        };

        // حفظ عرض السعر
        if (!this.data.quotes) this.data.quotes = {};
        this.data.quotes[quoteId] = quote;
        this.data.settings.nextQuoteNumber++;
        this.saveSalesData();

        // إغلاق النافذة وتحديث العرض
        const modal = bootstrap.Modal.getInstance(document.getElementById('createQuoteModal'));
        modal.hide();

        this.refreshData();
        alert('تم إنشاء عرض السعر بنجاح');
    },

    /**
     * تحويل عرض السعر إلى فاتورة
     */
    convertToInvoice: function(quoteId) {
        const quote = this.data.quotes[quoteId];
        if (!quote) {
            alert('عرض السعر غير موجود');
            return;
        }

        if (quote.status !== 'accepted') {
            alert('يمكن تحويل العروض المقبولة فقط إلى فواتير');
            return;
        }

        if (confirm('هل تريد تحويل عرض السعر إلى فاتورة؟')) {
            // إنشاء فاتورة من عرض السعر
            const invoiceId = 'invoice_' + Date.now();
            const invoice = {
                id: invoiceId,
                number: this.data.settings.invoicePrefix + this.data.settings.nextInvoiceNumber,
                date: new Date().toISOString().split('T')[0],
                dueDate: new Date(Date.now() + this.data.settings.defaultDueDays * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                customerId: quote.customerId,
                reference: `تحويل من عرض السعر ${quote.number}`,
                items: quote.items,
                subtotal: quote.subtotal,
                tax: quote.tax,
                total: quote.total,
                status: 'draft',
                notes: quote.notes,
                createdAt: new Date().toISOString(),
                convertedFromQuote: quoteId
            };

            // حفظ الفاتورة
            this.data.invoices[invoiceId] = invoice;
            this.data.settings.nextInvoiceNumber++;

            // تحديث حالة عرض السعر
            quote.convertedToInvoice = invoiceId;
            quote.convertedAt = new Date().toISOString();

            this.saveSalesData();
            this.refreshData();

            alert(`تم تحويل عرض السعر إلى فاتورة رقم ${invoice.number}`);
        }
    },

    /**
     * وظائف أخرى لعروض الأسعار
     */
    viewQuote: function(quoteId) { alert('ميزة عرض عرض السعر قيد التطوير'); },
    editQuote: function(quoteId) { alert('ميزة تعديل عرض السعر قيد التطوير'); },
    printQuote: function(quoteId) { alert('ميزة طباعة عرض السعر قيد التطوير'); },
    deleteQuote: function(quoteId) {
        if (confirm('هل أنت متأكد من حذف عرض السعر؟')) {
            delete this.data.quotes[quoteId];
            this.saveSalesData();
            this.refreshData();
            alert('تم حذف عرض السعر بنجاح');
        }
    },
    exportQuotesCSV: function() { alert('ميزة تصدير عروض الأسعار قيد التطوير'); },

    /**
     * عرض نافذة تسجيل دفعة
     */
    showRecordPaymentModal: function() {
        const unpaidInvoices = Object.values(this.data.invoices || {}).filter(inv =>
            inv.status === 'sent' || inv.status === 'overdue'
        );

        if (unpaidInvoices.length === 0) {
            alert('لا توجد فواتير غير مدفوعة');
            return;
        }

        const modalHTML = `
            <div class="modal fade" id="recordPaymentModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-credit-card me-2"></i>تسجيل دفعة جديدة
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="recordPaymentForm">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">الفاتورة *</label>
                                        <select class="form-control" name="invoiceId" required onchange="window.SalesComponent.updatePaymentInvoiceDetails(this.value)">
                                            <option value="">اختر الفاتورة</option>
                                            ${unpaidInvoices.map(invoice => {
                                                const customer = this.data.customers[invoice.customerId];
                                                return `
                                                    <option value="${invoice.id}" data-total="${invoice.total}" data-customer="${customer?.name || 'عميل غير محدد'}">
                                                        ${invoice.number} - ${customer?.name || 'عميل غير محدد'} - ${this.formatAmount(invoice.total)}
                                                    </option>
                                                `;
                                            }).join('')}
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">تاريخ الدفع</label>
                                        <input type="date" class="form-control" name="paymentDate"
                                               value="${new Date().toISOString().split('T')[0]}" required>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">مبلغ الدفع *</label>
                                        <input type="number" class="form-control" name="amount" step="0.01" required
                                               onchange="window.SalesComponent.calculatePaymentBalance()">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">طريقة الدفع</label>
                                        <select class="form-control" name="paymentMethod">
                                            <option value="cash">نقداً</option>
                                            <option value="bank_transfer">تحويل بنكي</option>
                                            <option value="credit_card">بطاقة ائتمان</option>
                                            <option value="check">شيك</option>
                                            <option value="other">أخرى</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">رقم المرجع</label>
                                        <input type="text" class="form-control" name="reference" placeholder="رقم التحويل أو الشيك">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">البنك</label>
                                        <input type="text" class="form-control" name="bank" placeholder="اسم البنك (اختياري)">
                                    </div>
                                </div>

                                <!-- تفاصيل الفاتورة -->
                                <div class="card mb-3" id="invoiceDetailsCard" style="display: none;">
                                    <div class="card-header">
                                        <h6 class="mb-0">تفاصيل الفاتورة</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <table class="table table-sm">
                                                    <tr>
                                                        <td><strong>رقم الفاتورة:</strong></td>
                                                        <td id="invoiceNumber">-</td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>العميل:</strong></td>
                                                        <td id="invoiceCustomer">-</td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>إجمالي الفاتورة:</strong></td>
                                                        <td id="invoiceTotal">-</td>
                                                    </tr>
                                                </table>
                                            </div>
                                            <div class="col-md-6">
                                                <table class="table table-sm">
                                                    <tr>
                                                        <td><strong>المدفوع سابقاً:</strong></td>
                                                        <td id="previousPayments">0.00 ر.س</td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>المبلغ المستحق:</strong></td>
                                                        <td id="remainingAmount">-</td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>الرصيد بعد الدفع:</strong></td>
                                                        <td id="balanceAfterPayment">-</td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="3" placeholder="ملاحظات إضافية حول الدفعة"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.savePayment()">
                                <i class="fas fa-save me-1"></i>تسجيل الدفعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('recordPaymentModal'));
        modal.show();

        document.getElementById('recordPaymentModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * تحديث تفاصيل الفاتورة في نافذة الدفع
     */
    updatePaymentInvoiceDetails: function(invoiceId) {
        if (!invoiceId) {
            document.getElementById('invoiceDetailsCard').style.display = 'none';
            return;
        }

        const invoice = this.data.invoices[invoiceId];
        const customer = this.data.customers[invoice.customerId];

        // عرض تفاصيل الفاتورة
        document.getElementById('invoiceNumber').textContent = invoice.number;
        document.getElementById('invoiceCustomer').textContent = customer?.name || 'عميل غير محدد';
        document.getElementById('invoiceTotal').textContent = this.formatAmount(invoice.total);

        // حساب المدفوعات السابقة
        const previousPayments = this.getInvoicePayments(invoiceId);
        const totalPaid = previousPayments.reduce((sum, payment) => sum + payment.amount, 0);
        const remainingAmount = invoice.total - totalPaid;

        document.getElementById('previousPayments').textContent = this.formatAmount(totalPaid);
        document.getElementById('remainingAmount').textContent = this.formatAmount(remainingAmount);

        // تعيين المبلغ المستحق كقيمة افتراضية
        document.querySelector('input[name="amount"]').value = remainingAmount.toFixed(2);

        document.getElementById('invoiceDetailsCard').style.display = 'block';
        this.calculatePaymentBalance();
    },

    /**
     * حساب الرصيد بعد الدفع
     */
    calculatePaymentBalance: function() {
        const invoiceId = document.querySelector('select[name="invoiceId"]').value;
        const paymentAmount = parseFloat(document.querySelector('input[name="amount"]').value) || 0;

        if (!invoiceId) return;

        const invoice = this.data.invoices[invoiceId];
        const previousPayments = this.getInvoicePayments(invoiceId);
        const totalPaid = previousPayments.reduce((sum, payment) => sum + payment.amount, 0);
        const balanceAfterPayment = invoice.total - totalPaid - paymentAmount;

        document.getElementById('balanceAfterPayment').textContent = this.formatAmount(balanceAfterPayment);

        // تغيير لون الرصيد حسب الحالة
        const balanceElement = document.getElementById('balanceAfterPayment');
        if (balanceAfterPayment > 0) {
            balanceElement.className = 'text-warning';
        } else if (balanceAfterPayment === 0) {
            balanceElement.className = 'text-success';
        } else {
            balanceElement.className = 'text-info';
        }
    },

    /**
     * الحصول على مدفوعات فاتورة معينة
     */
    getInvoicePayments: function(invoiceId) {
        if (!this.data.payments) this.data.payments = {};
        return Object.values(this.data.payments).filter(payment => payment.invoiceId === invoiceId);
    },

    /**
     * حفظ الدفعة
     */
    savePayment: function() {
        const form = document.getElementById('recordPaymentForm');
        const formData = new FormData(form);

        // التحقق من البيانات المطلوبة
        if (!formData.get('invoiceId') || !formData.get('amount')) {
            alert('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        const invoiceId = formData.get('invoiceId');
        const amount = parseFloat(formData.get('amount'));

        if (amount <= 0) {
            alert('مبلغ الدفع يجب أن يكون أكبر من صفر');
            return;
        }

        // التحقق من عدم تجاوز المبلغ المستحق
        const invoice = this.data.invoices[invoiceId];
        const previousPayments = this.getInvoicePayments(invoiceId);
        const totalPaid = previousPayments.reduce((sum, payment) => sum + payment.amount, 0);
        const remainingAmount = invoice.total - totalPaid;

        if (amount > remainingAmount) {
            if (!confirm(`المبلغ المدخل (${this.formatAmount(amount)}) أكبر من المبلغ المستحق (${this.formatAmount(remainingAmount)}). هل تريد المتابعة؟`)) {
                return;
            }
        }

        // إنشاء الدفعة
        const paymentId = 'payment_' + Date.now();
        const payment = {
            id: paymentId,
            invoiceId: invoiceId,
            amount: amount,
            paymentDate: formData.get('paymentDate'),
            paymentMethod: formData.get('paymentMethod'),
            reference: formData.get('reference'),
            bank: formData.get('bank'),
            notes: formData.get('notes'),
            createdAt: new Date().toISOString()
        };

        // حفظ الدفعة
        if (!this.data.payments) this.data.payments = {};
        this.data.payments[paymentId] = payment;

        // تحديث حالة الفاتورة
        const newTotalPaid = totalPaid + amount;
        if (newTotalPaid >= invoice.total) {
            invoice.status = 'paid';
            invoice.paidAt = new Date().toISOString();
        } else {
            invoice.status = 'partial';
        }

        this.saveSalesData();

        // إغلاق النافذة وتحديث العرض
        const modal = bootstrap.Modal.getInstance(document.getElementById('recordPaymentModal'));
        modal.hide();

        this.refreshData();
        alert('تم تسجيل الدفعة بنجاح');
    },
    /**
     * عرض نافذة تعديل المخزون
     */
    showStockAdjustmentModal: function() {
        const products = Object.values(this.data.products || {});

        if (products.length === 0) {
            alert('لا توجد منتجات لتعديل مخزونها');
            return;
        }

        const modalHTML = `
            <div class="modal fade" id="stockAdjustmentModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-edit me-2"></i>تعديل المخزون
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="stockAdjustmentForm">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">المنتج *</label>
                                        <select class="form-control" name="productId" required onchange="window.SalesComponent.updateCurrentStock(this.value)">
                                            <option value="">اختر المنتج</option>
                                            ${products.map(product => `
                                                <option value="${product.id}">${product.name}</option>
                                            `).join('')}
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">تاريخ التعديل</label>
                                        <input type="date" class="form-control" name="adjustmentDate"
                                               value="${new Date().toISOString().split('T')[0]}" required>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label class="form-label">المخزون الحالي</label>
                                        <input type="number" class="form-control" id="currentStock" readonly>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">نوع التعديل</label>
                                        <select class="form-control" name="adjustmentType" onchange="window.SalesComponent.updateAdjustmentType(this.value)">
                                            <option value="add">إضافة مخزون</option>
                                            <option value="subtract">خصم مخزون</option>
                                            <option value="set">تعيين مخزون جديد</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">الكمية *</label>
                                        <input type="number" class="form-control" name="quantity" required min="0"
                                               onchange="window.SalesComponent.calculateNewStock()">
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">المخزون الجديد</label>
                                        <input type="number" class="form-control" id="newStock" readonly>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">سبب التعديل</label>
                                        <select class="form-control" name="reason">
                                            <option value="purchase">شراء جديد</option>
                                            <option value="return">إرجاع من عميل</option>
                                            <option value="damage">تلف</option>
                                            <option value="theft">فقدان/سرقة</option>
                                            <option value="sale">بيع</option>
                                            <option value="transfer">نقل</option>
                                            <option value="count">جرد</option>
                                            <option value="other">أخرى</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="3" placeholder="تفاصيل إضافية حول تعديل المخزون"></textarea>
                                </div>

                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>تنبيه:</strong> سيتم تسجيل هذا التعديل في سجل حركات المخزون ولا يمكن التراجع عنه.
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveStockAdjustment()">
                                <i class="fas fa-save me-1"></i>حفظ التعديل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('stockAdjustmentModal'));
        modal.show();

        document.getElementById('stockAdjustmentModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * تحديث المخزون الحالي
     */
    updateCurrentStock: function(productId) {
        if (!productId) {
            document.getElementById('currentStock').value = '';
            document.getElementById('newStock').value = '';
            return;
        }

        const currentStock = this.getProductStock(productId);
        document.getElementById('currentStock').value = currentStock;
        this.calculateNewStock();
    },

    /**
     * تحديث نوع التعديل
     */
    updateAdjustmentType: function(type) {
        const quantityInput = document.querySelector('input[name="quantity"]');
        const currentStock = parseInt(document.getElementById('currentStock').value) || 0;

        if (type === 'set') {
            quantityInput.placeholder = 'المخزون الجديد';
            quantityInput.value = currentStock;
        } else {
            quantityInput.placeholder = 'الكمية';
            quantityInput.value = '';
        }

        this.calculateNewStock();
    },

    /**
     * حساب المخزون الجديد
     */
    calculateNewStock: function() {
        const currentStock = parseInt(document.getElementById('currentStock').value) || 0;
        const quantity = parseInt(document.querySelector('input[name="quantity"]').value) || 0;
        const adjustmentType = document.querySelector('select[name="adjustmentType"]').value;

        let newStock = currentStock;

        switch (adjustmentType) {
            case 'add':
                newStock = currentStock + quantity;
                break;
            case 'subtract':
                newStock = Math.max(0, currentStock - quantity);
                break;
            case 'set':
                newStock = quantity;
                break;
        }

        document.getElementById('newStock').value = newStock;

        // تحذير إذا كان المخزون سيصبح سالباً
        if (newStock < 0) {
            document.getElementById('newStock').style.color = 'red';
        } else {
            document.getElementById('newStock').style.color = '';
        }
    },

    /**
     * الحصول على مخزون المنتج
     */
    getProductStock: function(productId) {
        if (!this.data.inventory) this.data.inventory = {};
        return this.data.inventory[productId] || Math.floor(Math.random() * 50) + 1; // محاكاة المخزون
    },

    /**
     * حفظ تعديل المخزون
     */
    saveStockAdjustment: function() {
        const form = document.getElementById('stockAdjustmentForm');
        const formData = new FormData(form);

        // التحقق من البيانات المطلوبة
        if (!formData.get('productId') || !formData.get('quantity')) {
            alert('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        const productId = formData.get('productId');
        const quantity = parseInt(formData.get('quantity'));
        const adjustmentType = formData.get('adjustmentType');
        const newStock = parseInt(document.getElementById('newStock').value);

        if (quantity < 0) {
            alert('الكمية يجب أن تكون أكبر من أو تساوي صفر');
            return;
        }

        if (newStock < 0 && !confirm('المخزون الجديد سيكون سالباً. هل تريد المتابعة؟')) {
            return;
        }

        // إنشاء سجل تعديل المخزون
        const adjustmentId = 'adjustment_' + Date.now();
        const adjustment = {
            id: adjustmentId,
            productId: productId,
            adjustmentDate: formData.get('adjustmentDate'),
            adjustmentType: adjustmentType,
            quantity: quantity,
            oldStock: this.getProductStock(productId),
            newStock: newStock,
            reason: formData.get('reason'),
            notes: formData.get('notes'),
            createdAt: new Date().toISOString()
        };

        // حفظ التعديل
        if (!this.data.stockAdjustments) this.data.stockAdjustments = {};
        this.data.stockAdjustments[adjustmentId] = adjustment;

        // تحديث المخزون
        if (!this.data.inventory) this.data.inventory = {};
        this.data.inventory[productId] = newStock;

        this.saveSalesData();

        // إغلاق النافذة وتحديث العرض
        const modal = bootstrap.Modal.getInstance(document.getElementById('stockAdjustmentModal'));
        modal.hide();

        this.refreshData();
        alert('تم تعديل المخزون بنجاح');
    },
    exportInventoryReport: function() { alert('ميزة تقرير المخزون قيد التطوير'); },
    markAsPaid: function(invoiceId) {
        if (confirm('هل تم دفع هذه الفاتورة؟')) {
            this.data.invoices[invoiceId].status = 'paid';
            this.saveSalesData();
            this.refreshData();
            alert('تم تحديث حالة الفاتورة إلى مدفوعة');
        }
    },
    sendReminder: function(invoiceId) { alert('ميزة إرسال التذكيرات قيد التطوير'); },
    reorderProduct: function(productId) { alert('ميزة إعادة طلب المنتجات قيد التطوير'); },

    /**
     * وظيفة بديلة لإنشاء فاتورة جديدة (للتوافق مع الاستدعاءات القديمة)
     */
    showNewInvoiceModal: function() {
        this.showCreateInvoiceModal();
    },

    /**
     * تشخيص حالة المكون
     */
    diagnose: function() {
        console.log('🔍 تشخيص مكون المبيعات:');
        console.log('- البيانات محملة:', !!this.data);
        console.log('- عدد العملاء:', Object.keys(this.data.customers || {}).length);
        console.log('- عدد المنتجات:', Object.keys(this.data.products || {}).length);
        console.log('- عدد الفواتير:', Object.keys(this.data.invoices || {}).length);
        console.log('- الإعدادات:', !!this.data.settings);
        console.log('- العرض الحالي:', this.data.currentView);

        // اختبار وظيفة إنشاء الفاتورة
        try {
            console.log('- اختبار وظيفة إنشاء الفاتورة...');
            if (typeof this.showCreateInvoiceModal === 'function') {
                console.log('✅ وظيفة showCreateInvoiceModal متاحة');
            } else {
                console.log('❌ وظيفة showCreateInvoiceModal غير متاحة');
            }
        } catch (error) {
            console.log('❌ خطأ في اختبار وظيفة إنشاء الفاتورة:', error);
        }

        return {
            dataLoaded: !!this.data,
            customersCount: Object.keys(this.data.customers || {}).length,
            productsCount: Object.keys(this.data.products || {}).length,
            invoicesCount: Object.keys(this.data.invoices || {}).length,
            hasSettings: !!this.data.settings,
            currentView: this.data.currentView
        };
    },

    /**
     * تحديث البيانات
     */
    refreshData: function() {
        this.loadSalesData();
        const content = document.getElementById('main-content');
        if (content) {
            content.innerHTML = this.renderCurrentView();
        }

        // تحديث التقارير إذا كان في قسم التقارير
        if (this.data.currentView === 'reports') {
            setTimeout(() => {
                this.updateReports();
            }, 100);
        }
    }
};

    /**
     * عرض نافذة التقارير المتقدمة
     */
    showAdvancedReports: function() {
        const modalHTML = `
            <div class="modal fade" id="advancedReportsModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-chart-line me-2"></i>التقارير المتقدمة
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <label class="form-label">نوع التقرير</label>
                                    <select class="form-control" id="reportType" onchange="window.SalesComponent.updateReportOptions()">
                                        <option value="sales">تقرير المبيعات</option>
                                        <option value="customers">تقرير العملاء</option>
                                        <option value="products">تقرير المنتجات</option>
                                        <option value="payments">تقرير المدفوعات</option>
                                        <option value="inventory">تقرير المخزون</option>
                                        <option value="quotes">تقرير عروض الأسعار</option>
                                        <option value="financial">التقرير المالي</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">الفترة الزمنية</label>
                                    <select class="form-control" id="reportPeriod" onchange="window.SalesComponent.updateDateRange()">
                                        <option value="today">اليوم</option>
                                        <option value="week">هذا الأسبوع</option>
                                        <option value="month">هذا الشهر</option>
                                        <option value="quarter">هذا الربع</option>
                                        <option value="year">هذا العام</option>
                                        <option value="custom">فترة مخصصة</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" id="reportDateFrom"
                                           value="${new Date().toISOString().split('T')[0]}">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="reportDateTo"
                                           value="${new Date().toISOString().split('T')[0]}">
                                </div>
                            </div>

                            <!-- معاينة التقرير -->
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">معاينة التقرير</h6>
                                </div>
                                <div class="card-body" id="reportPreview">
                                    <div class="text-center py-4">
                                        <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">اختر نوع التقرير والفترة الزمنية لعرض المعاينة</p>
                                        <button class="btn btn-primary" onclick="window.SalesComponent.generateReportPreview()">
                                            <i class="fas fa-eye me-1"></i>معاينة التقرير
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-info" onclick="window.SalesComponent.generateReportPreview()">
                                <i class="fas fa-eye me-1"></i>معاينة
                            </button>
                            <button type="button" class="btn btn-success" onclick="window.SalesComponent.exportAdvancedReport()">
                                <i class="fas fa-download me-1"></i>تصدير التقرير
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('advancedReportsModal'));
        modal.show();

        document.getElementById('advancedReportsModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * تحديث نطاق التاريخ حسب الفترة المختارة
     */
    updateDateRange: function() {
        const period = document.getElementById('reportPeriod')?.value;
        const dateFrom = document.getElementById('reportDateFrom');
        const dateTo = document.getElementById('reportDateTo');

        if (!period || !dateFrom || !dateTo) return;

        const today = new Date();
        let fromDate = new Date();

        switch (period) {
            case 'today':
                fromDate = today;
                break;
            case 'week':
                fromDate.setDate(today.getDate() - 7);
                break;
            case 'month':
                fromDate.setMonth(today.getMonth() - 1);
                break;
            case 'quarter':
                fromDate.setMonth(today.getMonth() - 3);
                break;
            case 'year':
                fromDate.setFullYear(today.getFullYear() - 1);
                break;
            case 'custom':
                return; // لا تغيير للفترة المخصصة
        }

        dateFrom.value = fromDate.toISOString().split('T')[0];
        dateTo.value = today.toISOString().split('T')[0];
    },

    /**
     * إنشاء معاينة التقرير
     */
    generateReportPreview: function() {
        const reportType = document.getElementById('reportType')?.value;
        const dateFrom = document.getElementById('reportDateFrom')?.value;
        const dateTo = document.getElementById('reportDateTo')?.value;
        const previewContainer = document.getElementById('reportPreview');

        if (!reportType || !dateFrom || !dateTo || !previewContainer) return;

        // تصفية البيانات حسب التاريخ
        const fromDate = new Date(dateFrom);
        const toDate = new Date(dateTo);

        let reportData = {};
        let reportHTML = '';

        switch (reportType) {
            case 'sales':
                reportData = this.generateSalesReport(fromDate, toDate);
                reportHTML = this.renderSalesReportHTML(reportData);
                break;
            case 'customers':
                reportData = this.generateCustomersReport(fromDate, toDate);
                reportHTML = this.renderCustomersReportHTML(reportData);
                break;
            case 'products':
                reportData = this.generateProductsReport(fromDate, toDate);
                reportHTML = this.renderProductsReportHTML(reportData);
                break;
            case 'payments':
                reportData = this.generatePaymentsReport(fromDate, toDate);
                reportHTML = this.renderPaymentsReportHTML(reportData);
                break;
            case 'financial':
                reportData = this.generateFinancialReport(fromDate, toDate);
                reportHTML = this.renderFinancialReportHTML(reportData);
                break;
            default:
                reportHTML = '<div class="alert alert-warning">نوع التقرير غير مدعوم حالياً</div>';
        }

        previewContainer.innerHTML = reportHTML;
    },

    /**
     * إنشاء تقرير المبيعات
     */
    generateSalesReport: function(fromDate, toDate) {
        const invoices = Object.values(this.data.invoices || {}).filter(invoice => {
            const invoiceDate = new Date(invoice.date);
            return invoiceDate >= fromDate && invoiceDate <= toDate;
        });

        const totalSales = invoices.reduce((sum, inv) => sum + inv.total, 0);
        const totalInvoices = invoices.length;
        const averageInvoice = totalInvoices > 0 ? totalSales / totalInvoices : 0;

        const statusBreakdown = invoices.reduce((acc, inv) => {
            acc[inv.status] = (acc[inv.status] || 0) + 1;
            return acc;
        }, {});

        return {
            totalSales,
            totalInvoices,
            averageInvoice,
            statusBreakdown,
            invoices
        };
    },

    /**
     * عرض تقرير المبيعات
     */
    renderSalesReportHTML: function(data) {
        return `
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h4>${this.formatAmount(data.totalSales)}</h4>
                            <p class="mb-0">إجمالي المبيعات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h4>${data.totalInvoices}</h4>
                            <p class="mb-0">عدد الفواتير</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h4>${this.formatAmount(data.averageInvoice)}</h4>
                            <p class="mb-0">متوسط الفاتورة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-dark">
                        <div class="card-body text-center">
                            <h4>${Object.keys(data.statusBreakdown).length}</h4>
                            <p class="mb-0">حالات مختلفة</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>التاريخ</th>
                            <th>العميل</th>
                            <th>المبلغ</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.invoices.map(invoice => {
                            const customer = this.data.customers[invoice.customerId];
                            return `
                                <tr>
                                    <td>${invoice.number}</td>
                                    <td>${new Date(invoice.date).toLocaleDateString('ar-SA')}</td>
                                    <td>${customer?.name || 'غير محدد'}</td>
                                    <td>${this.formatAmount(invoice.total)}</td>
                                    <td><span class="badge bg-${this.getStatusColor(invoice.status)}">${this.getStatusLabel(invoice.status)}</span></td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
        `;
    },

    /**
     * تصدير التقرير المتقدم
     */
    exportAdvancedReport: function() {
        alert('ميزة تصدير التقارير المتقدمة قيد التطوير');
    },

    /**
     * عرض نافذة النسخ الاحتياطي والاستيراد
     */
    showBackupModal: function() {
        const modalHTML = `
            <div class="modal fade" id="backupModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-database me-2"></i>النسخ الاحتياطي والاستيراد
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <!-- النسخ الاحتياطي -->
                                <div class="col-md-6">
                                    <div class="card h-100">
                                        <div class="card-header">
                                            <h6 class="mb-0">
                                                <i class="fas fa-download me-2"></i>تصدير البيانات
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <p class="text-muted">قم بتصدير جميع بيانات النظام كنسخة احتياطية</p>

                                            <div class="mb-3">
                                                <label class="form-label">نوع التصدير</label>
                                                <select class="form-control" id="exportType">
                                                    <option value="full">نسخة كاملة</option>
                                                    <option value="invoices">الفواتير فقط</option>
                                                    <option value="customers">العملاء فقط</option>
                                                    <option value="products">المنتجات فقط</option>
                                                    <option value="settings">الإعدادات فقط</option>
                                                </select>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">تنسيق الملف</label>
                                                <select class="form-control" id="exportFormat">
                                                    <option value="json">JSON</option>
                                                    <option value="excel">Excel</option>
                                                    <option value="csv">CSV</option>
                                                </select>
                                            </div>

                                            <button class="btn btn-primary w-100" onclick="window.SalesComponent.exportData()">
                                                <i class="fas fa-download me-1"></i>تصدير البيانات
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- الاستيراد -->
                                <div class="col-md-6">
                                    <div class="card h-100">
                                        <div class="card-header">
                                            <h6 class="mb-0">
                                                <i class="fas fa-upload me-2"></i>استيراد البيانات
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <p class="text-muted">قم باستيراد البيانات من ملف نسخة احتياطية</p>

                                            <div class="mb-3">
                                                <label class="form-label">اختر الملف</label>
                                                <input type="file" class="form-control" id="importFile"
                                                       accept=".json,.xlsx,.csv" onchange="window.SalesComponent.previewImportFile()">
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">طريقة الاستيراد</label>
                                                <select class="form-control" id="importMode">
                                                    <option value="merge">دمج مع البيانات الحالية</option>
                                                    <option value="replace">استبدال البيانات الحالية</option>
                                                    <option value="append">إضافة فقط</option>
                                                </select>
                                            </div>

                                            <div id="importPreview" class="mb-3" style="display: none;">
                                                <div class="alert alert-info">
                                                    <strong>معاينة الملف:</strong>
                                                    <div id="importStats"></div>
                                                </div>
                                            </div>

                                            <button class="btn btn-success w-100" onclick="window.SalesComponent.importData()" disabled id="importBtn">
                                                <i class="fas fa-upload me-1"></i>استيراد البيانات
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- معلومات النظام -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">
                                                <i class="fas fa-info-circle me-2"></i>معلومات النظام
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <strong>عدد الفواتير:</strong>
                                                    <span class="text-primary">${Object.keys(this.data.invoices || {}).length}</span>
                                                </div>
                                                <div class="col-md-3">
                                                    <strong>عدد العملاء:</strong>
                                                    <span class="text-success">${Object.keys(this.data.customers || {}).length}</span>
                                                </div>
                                                <div class="col-md-3">
                                                    <strong>عدد المنتجات:</strong>
                                                    <span class="text-info">${Object.keys(this.data.products || {}).length}</span>
                                                </div>
                                                <div class="col-md-3">
                                                    <strong>حجم البيانات:</strong>
                                                    <span class="text-warning">${this.getDataSize()}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-warning" onclick="window.SalesComponent.resetAllData()">
                                <i class="fas fa-trash me-1"></i>مسح جميع البيانات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('backupModal'));
        modal.show();

        document.getElementById('backupModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * تصدير البيانات
     */
    exportData: function() {
        const exportType = document.getElementById('exportType').value;
        const exportFormat = document.getElementById('exportFormat').value;

        let dataToExport = {};

        switch (exportType) {
            case 'full':
                dataToExport = this.data;
                break;
            case 'invoices':
                dataToExport = { invoices: this.data.invoices };
                break;
            case 'customers':
                dataToExport = { customers: this.data.customers };
                break;
            case 'products':
                dataToExport = { products: this.data.products };
                break;
            case 'settings':
                dataToExport = { settings: this.data.settings };
                break;
        }

        const timestamp = new Date().toISOString().split('T')[0];
        const filename = `sales_backup_${exportType}_${timestamp}`;

        switch (exportFormat) {
            case 'json':
                this.downloadJSON(dataToExport, filename);
                break;
            case 'excel':
                this.downloadExcel(dataToExport, filename);
                break;
            case 'csv':
                this.downloadCSV(dataToExport, filename);
                break;
        }
    },

    /**
     * تحميل ملف JSON
     */
    downloadJSON: function(data, filename) {
        const jsonString = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = filename + '.json';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        alert('تم تصدير البيانات بنجاح!');
    },

    /**
     * معاينة ملف الاستيراد
     */
    previewImportFile: function() {
        const fileInput = document.getElementById('importFile');
        const file = fileInput.files[0];
        const importBtn = document.getElementById('importBtn');
        const previewDiv = document.getElementById('importPreview');
        const statsDiv = document.getElementById('importStats');

        if (!file) {
            importBtn.disabled = true;
            previewDiv.style.display = 'none';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                let data = {};

                if (file.name.endsWith('.json')) {
                    data = JSON.parse(e.target.result);
                } else {
                    alert('نوع الملف غير مدعوم حالياً. يرجى استخدام ملف JSON.');
                    return;
                }

                // عرض إحصائيات الملف
                const stats = [];
                if (data.invoices) stats.push(`${Object.keys(data.invoices).length} فاتورة`);
                if (data.customers) stats.push(`${Object.keys(data.customers).length} عميل`);
                if (data.products) stats.push(`${Object.keys(data.products).length} منتج`);
                if (data.settings) stats.push('إعدادات النظام');

                statsDiv.innerHTML = stats.join(' • ');
                previewDiv.style.display = 'block';
                importBtn.disabled = false;

                // حفظ البيانات للاستيراد
                window.importData = data;

            } catch (error) {
                alert('خطأ في قراءة الملف: ' + error.message);
                importBtn.disabled = true;
                previewDiv.style.display = 'none';
            }
        };

        reader.readAsText(file);
    },

    /**
     * استيراد البيانات
     */
    importData: function() {
        if (!window.importData) {
            alert('لم يتم تحديد ملف للاستيراد');
            return;
        }

        const importMode = document.getElementById('importMode').value;
        const data = window.importData;

        if (!confirm('هل أنت متأكد من استيراد البيانات؟ قد يؤثر هذا على البيانات الحالية.')) {
            return;
        }

        try {
            switch (importMode) {
                case 'replace':
                    // استبدال البيانات الحالية
                    Object.assign(this.data, data);
                    break;
                case 'merge':
                    // دمج البيانات
                    Object.keys(data).forEach(key => {
                        if (typeof data[key] === 'object' && !Array.isArray(data[key])) {
                            this.data[key] = { ...this.data[key], ...data[key] };
                        } else {
                            this.data[key] = data[key];
                        }
                    });
                    break;
                case 'append':
                    // إضافة البيانات الجديدة فقط
                    Object.keys(data).forEach(key => {
                        if (typeof data[key] === 'object' && !Array.isArray(data[key])) {
                            Object.keys(data[key]).forEach(itemKey => {
                                if (!this.data[key] || !this.data[key][itemKey]) {
                                    if (!this.data[key]) this.data[key] = {};
                                    this.data[key][itemKey] = data[key][itemKey];
                                }
                            });
                        }
                    });
                    break;
            }

            this.saveSalesData();
            this.refreshData();

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('backupModal'));
            modal.hide();

            alert('تم استيراد البيانات بنجاح!');

        } catch (error) {
            alert('خطأ في استيراد البيانات: ' + error.message);
        }
    },

    /**
     * حساب حجم البيانات
     */
    getDataSize: function() {
        const dataString = JSON.stringify(this.data);
        const sizeInBytes = new Blob([dataString]).size;

        if (sizeInBytes < 1024) {
            return sizeInBytes + ' بايت';
        } else if (sizeInBytes < 1024 * 1024) {
            return Math.round(sizeInBytes / 1024) + ' كيلوبايت';
        } else {
            return Math.round(sizeInBytes / (1024 * 1024)) + ' ميجابايت';
        }
    },

    /**
     * مسح جميع البيانات
     */
    resetAllData: function() {
        if (!confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!')) {
            return;
        }

        if (!confirm('تأكيد أخير: سيتم مسح جميع الفواتير والعملاء والمنتجات والإعدادات. هل تريد المتابعة؟')) {
            return;
        }

        // مسح البيانات
        localStorage.removeItem('salesData');

        // إعادة تهيئة البيانات
        this.data = {
            customers: {},
            products: {},
            invoices: {},
            quotes: {},
            payments: {},
            inventory: {},
            stockAdjustments: {},
            filters: {
                dateFrom: '',
                dateTo: '',
                status: '',
                customer: '',
                quickSearch: ''
            },
            quoteFilters: {
                dateFrom: '',
                dateTo: '',
                status: '',
                customer: ''
            },
            settings: {
                taxRate: 0.15,
                currency: 'SAR',
                invoicePrefix: 'INV-',
                nextInvoiceNumber: 1,
                nextQuoteNumber: 1,
                companyName: 'قمة الوعد للسفريات',
                companyAddress: 'المملكة العربية السعودية',
                companyTaxNumber: '*********',
                companyEmail: '<EMAIL>',
                companyPhone: '+966501234567',
                companyWebsite: 'https://qimat-alwaed.com',
                language: 'ar',
                timezone: 'Asia/Riyadh',
                dateFormat: 'ar-SA',
                autoSave: true,
                showNotifications: true,
                enableBackup: true,
                autoCalculateTax: true,
                defaultDueDays: 30
            }
        };

        this.saveSalesData();
        this.refreshData();

        // إغلاق النافذة
        const modal = bootstrap.Modal.getInstance(document.getElementById('backupModal'));
        modal.hide();

        alert('تم مسح جميع البيانات وإعادة تهيئة النظام!');
    },

    /**
     * نظام الإشعارات والتنبيهات
     */
    showNotification: function(message, type = 'info', duration = 5000) {
        // إنشاء عنصر الإشعار
        const notificationId = 'notification_' + Date.now();
        const notificationHTML = `
            <div class="toast align-items-center text-white bg-${type} border-0" role="alert" id="${notificationId}">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-${this.getNotificationIcon(type)} me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;

        // إضافة الإشعار إلى الحاوية
        let toastContainer = document.getElementById('toastContainer');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toastContainer';
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            toastContainer.style.zIndex = '9999';
            document.body.appendChild(toastContainer);
        }

        toastContainer.insertAdjacentHTML('beforeend', notificationHTML);

        // تفعيل الإشعار
        const toastElement = document.getElementById(notificationId);
        const toast = new bootstrap.Toast(toastElement, { delay: duration });
        toast.show();

        // إزالة الإشعار بعد الإخفاء
        toastElement.addEventListener('hidden.bs.toast', function() {
            this.remove();
        });
    },

    /**
     * الحصول على أيقونة الإشعار
     */
    getNotificationIcon: function(type) {
        const icons = {
            success: 'check-circle',
            danger: 'exclamation-triangle',
            warning: 'exclamation-circle',
            info: 'info-circle',
            primary: 'bell'
        };
        return icons[type] || 'bell';
    },

    /**
     * فحص التنبيهات التلقائية
     */
    checkAlerts: function() {
        const alerts = [];

        // تنبيهات الفواتير المتأخرة
        const overdueInvoices = this.getOverdueInvoices();
        if (overdueInvoices.length > 0) {
            alerts.push({
                type: 'warning',
                title: 'فواتير متأخرة',
                message: `لديك ${overdueInvoices.length} فاتورة متأخرة السداد`,
                action: () => this.showOverdueInvoices()
            });
        }

        // تنبيهات المخزون المنخفض
        const lowStockProducts = this.getLowStockProducts();
        if (lowStockProducts.length > 0) {
            alerts.push({
                type: 'danger',
                title: 'مخزون منخفض',
                message: `${lowStockProducts.length} منتج بحاجة لإعادة تموين`,
                action: () => this.showLowStockProducts()
            });
        }

        // تنبيهات عروض الأسعار المنتهية الصلاحية
        const expiredQuotes = this.getExpiredQuotes();
        if (expiredQuotes.length > 0) {
            alerts.push({
                type: 'info',
                title: 'عروض أسعار منتهية',
                message: `${expiredQuotes.length} عرض سعر انتهت صلاحيته`,
                action: () => this.showExpiredQuotes()
            });
        }

        return alerts;
    },

    /**
     * عرض التنبيهات في الواجهة
     */
    displayAlerts: function() {
        const alerts = this.checkAlerts();

        if (alerts.length === 0) return;

        // عرض التنبيهات كإشعارات
        alerts.forEach((alert, index) => {
            setTimeout(() => {
                this.showNotification(
                    `<strong>${alert.title}:</strong> ${alert.message}`,
                    alert.type,
                    8000
                );
            }, index * 1000);
        });

        // تحديث شارات التنبيه في شريط التنقل
        this.updateAlertBadges(alerts);
    },

    /**
     * تحديث شارات التنبيه
     */
    updateAlertBadges: function(alerts) {
        // تحديث شارة الفواتير
        const invoicesBadge = document.querySelector('[data-view="invoices"] .badge');
        const overdueCount = alerts.filter(a => a.title === 'فواتير متأخرة').length;
        if (invoicesBadge && overdueCount > 0) {
            invoicesBadge.textContent = overdueCount;
            invoicesBadge.style.display = 'inline';
        }

        // تحديث شارة المخزون
        const inventoryBadge = document.querySelector('[data-view="inventory"] .badge');
        const lowStockCount = alerts.filter(a => a.title === 'مخزون منخفض').length;
        if (inventoryBadge && lowStockCount > 0) {
            inventoryBadge.textContent = lowStockCount;
            inventoryBadge.style.display = 'inline';
        }
    },

    /**
     * الحصول على الفواتير المتأخرة
     */
    getOverdueInvoices: function() {
        const today = new Date();
        return Object.values(this.data.invoices || {}).filter(invoice => {
            if (invoice.status === 'paid') return false;
            const dueDate = new Date(invoice.dueDate);
            return dueDate < today;
        });
    },

    /**
     * الحصول على المنتجات ذات المخزون المنخفض
     */
    getLowStockProducts: function() {
        const lowStockThreshold = 5; // الحد الأدنى للمخزون
        return Object.values(this.data.products || {}).filter(product => {
            const stock = this.getProductStock(product.id);
            return stock <= lowStockThreshold;
        });
    },

    /**
     * الحصول على عروض الأسعار المنتهية الصلاحية
     */
    getExpiredQuotes: function() {
        const today = new Date();
        return Object.values(this.data.quotes || {}).filter(quote => {
            if (quote.status !== 'pending') return false;
            const validUntil = new Date(quote.validUntil);
            return validUntil < today;
        });
    },

    /**
     * عرض الفواتير المتأخرة
     */
    showOverdueInvoices: function() {
        this.switchView('invoices');
        // تطبيق فلتر للفواتير المتأخرة
        setTimeout(() => {
            const statusFilter = document.getElementById('statusFilter');
            if (statusFilter) {
                statusFilter.value = 'overdue';
                this.applyFilters();
            }
        }, 500);
    },

    /**
     * عرض المنتجات ذات المخزون المنخفض
     */
    showLowStockProducts: function() {
        this.switchView('inventory');
        this.showNotification('تم التبديل إلى قسم المخزون لعرض المنتجات ذات المخزون المنخفض', 'info');
    },

    /**
     * عرض عروض الأسعار المنتهية الصلاحية
     */
    showExpiredQuotes: function() {
        this.switchView('quotes');
        // تطبيق فلتر لعروض الأسعار المنتهية
        setTimeout(() => {
            const statusFilter = document.getElementById('quoteStatusFilter');
            if (statusFilter) {
                statusFilter.value = 'expired';
                this.applyQuoteFilters();
            }
        }, 500);
    },

    /**
     * تشغيل فحص التنبيهات التلقائي
     */
    startAlertMonitoring: function() {
        // فحص فوري
        this.displayAlerts();

        // فحص دوري كل 5 دقائق
        setInterval(() => {
            this.displayAlerts();
        }, 5 * 60 * 1000);
    }
};

// تصدير المكون للاستخدام العام
window.SalesComponent = SalesComponent;
