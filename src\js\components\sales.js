/**
 * مكون نظام المبيعات
 * Sales Management Component
 */

const SalesComponent = {
    // بيانات النظام
    data: {
        currentView: 'dashboard',
        customers: {},
        products: {},
        invoices: {},
        deletedInvoices: {},
        salesTransactions: {},
        settings: {
            taxRate: 0.15, // ضريبة القيمة المضافة 15%
            currency: 'SAR',
            invoicePrefix: 'INV-',
            nextInvoiceNumber: 1,
            companyName: 'قمة الوعد للسفريات',
            companyAddress: 'المملكة العربية السعودية',
            companyTaxNumber: '*********',
            companyEmail: '<EMAIL>',
            companyPhone: '+966501234567'
        }
    },

    // إعدادات العرض
    views: [
        { key: 'dashboard', label: 'لوحة التحكم', icon: 'fas fa-tachometer-alt' },
        { key: 'invoices', label: 'الفواتير', icon: 'fas fa-file-invoice' },
        { key: 'customers', label: 'العملاء', icon: 'fas fa-users' },
        { key: 'products', label: 'المنتجات والخدمات', icon: 'fas fa-box' },
        { key: 'reports', label: 'التقارير', icon: 'fas fa-chart-bar' },
        { key: 'settings', label: 'الإعدادات', icon: 'fas fa-cog' }
    ],

    /**
     * تهيئة مكون المبيعات
     */
    init: function() {
        this.loadSalesData();
        this.setupEventListeners();
        console.log('✅ تم تهيئة نظام المبيعات');
    },

    /**
     * تحميل بيانات المبيعات
     */
    loadSalesData: function() {
        try {
            // تحميل البيانات من localStorage
            const savedData = localStorage.getItem('salesData');
            if (savedData) {
                const data = JSON.parse(savedData);
                this.data = { ...this.data, ...data };
            } else {
                // إنشاء بيانات تجريبية إذا لم توجد بيانات محفوظة
                this.createSampleData();
            }
        } catch (error) {
            console.error('خطأ في تحميل بيانات المبيعات:', error);
            this.createSampleData();
        }
    },

    /**
     * حفظ بيانات المبيعات
     */
    saveSalesData: function() {
        try {
            localStorage.setItem('salesData', JSON.stringify(this.data));
        } catch (error) {
            console.error('خطأ في حفظ بيانات المبيعات:', error);
        }
    },

    /**
     * إنشاء بيانات تجريبية
     */
    createSampleData: function() {
        // عملاء تجريبيون
        this.data.customers = {
            'CUST_001': {
                id: 'CUST_001',
                name: 'أحمد محمد السعيد',
                email: '<EMAIL>',
                phone: '+966501234567',
                address: 'الرياض، المملكة العربية السعودية',
                type: 'individual',
                createdAt: new Date().toISOString()
            },
            'CUST_002': {
                id: 'CUST_002',
                name: 'شركة النور للسفريات',
                email: '<EMAIL>',
                phone: '+966507654321',
                address: 'جدة، المملكة العربية السعودية',
                type: 'corporate',
                taxNumber: '*********',
                createdAt: new Date().toISOString()
            }
        };

        // منتجات وخدمات تجريبية
        this.data.products = {
            'PROD_001': {
                id: 'PROD_001',
                name: 'تذكرة طيران - الرياض إلى دبي',
                description: 'تذكرة طيران ذهاب وعودة',
                price: 1200,
                cost: 900,
                category: 'flights',
                createdAt: new Date().toISOString()
            },
            'PROD_002': {
                id: 'PROD_002',
                name: 'حجز فندق - 3 نجوم',
                description: 'حجز فندق لليلة واحدة',
                price: 300,
                cost: 200,
                category: 'hotels',
                createdAt: new Date().toISOString()
            },
            'PROD_003': {
                id: 'PROD_003',
                name: 'تأشيرة سياحية',
                description: 'استخراج تأشيرة سياحية',
                price: 500,
                cost: 350,
                category: 'visas',
                createdAt: new Date().toISOString()
            }
        };

        // فواتير تجريبية
        this.data.invoices = {
            'INV_001': {
                id: 'INV_001',
                number: 'INV-0001',
                customerId: 'CUST_001',
                date: new Date().toISOString().split('T')[0],
                dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                status: 'sent',
                items: [
                    {
                        productId: 'PROD_001',
                        quantity: 1,
                        price: 1200,
                        discount: 0,
                        total: 1200
                    }
                ],
                subtotal: 1200,
                totalDiscount: 0,
                taxAmount: 180,
                total: 1380,
                notes: 'شكراً لتعاملكم معنا',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            }
        };

        this.saveSalesData();
    },

    /**
     * تبديل العرض
     */
    switchView: function(view) {
        this.data.currentView = view;
        const content = document.getElementById('main-content');
        if (content) {
            content.innerHTML = this.renderCurrentView();
        }
    },

    /**
     * عرض المحتوى الحالي
     */
    renderCurrentView: function() {
        switch (this.data.currentView) {
            case 'dashboard':
                return this.renderDashboard();
            case 'invoices':
                return this.renderInvoices();
            case 'customers':
                return this.renderCustomers();
            case 'products':
                return this.renderProducts();
            case 'reports':
                return this.renderReports();
            case 'settings':
                return this.renderSettings();
            default:
                return this.renderDashboard();
        }
    },

    /**
     * عرض نافذة المبيعات الرئيسية
     */
    render: function() {
        return `
            <div class="sales-management">
                <!-- شريط التنقل -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <nav class="nav nav-pills">
                                    ${this.views.map(view => `
                                        <button class="nav-link ${this.data.currentView === view.key ? 'active' : ''}" 
                                                onclick="window.SalesComponent.switchView('${view.key}')">
                                            <i class="${view.icon} me-1"></i>${view.label}
                                        </button>
                                    `).join('')}
                                </nav>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="btn-group">
                                    <button class="btn btn-outline-primary" onclick="window.SalesComponent.showNewInvoiceModal()">
                                        <i class="fas fa-plus me-1"></i>فاتورة جديدة
                                    </button>
                                    <button class="btn btn-outline-success" onclick="window.SalesComponent.showNewCustomerModal()">
                                        <i class="fas fa-user-plus me-1"></i>عميل جديد
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- المحتوى الرئيسي -->
                <div id="sales-content">
                    ${this.renderCurrentView()}
                </div>
            </div>
        `;
    },

    /**
     * عرض لوحة التحكم
     */
    renderDashboard: function() {
        const stats = this.calculateDashboardStats();
        
        return `
            <div class="dashboard">
                <!-- الإحصائيات السريعة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0">${stats.totalInvoices}</h4>
                                        <p class="mb-0">إجمالي الفواتير</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-file-invoice fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0">${this.formatAmount(stats.totalRevenue)}</h4>
                                        <p class="mb-0">إجمالي الإيرادات</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-money-bill-wave fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0">${stats.totalCustomers}</h4>
                                        <p class="mb-0">إجمالي العملاء</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0">${stats.pendingInvoices}</h4>
                                        <p class="mb-0">فواتير معلقة</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-clock fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- آخر الفواتير -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2"></i>آخر الفواتير
                        </h5>
                    </div>
                    <div class="card-body">
                        ${this.renderRecentInvoices()}
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * حساب إحصائيات لوحة التحكم
     */
    calculateDashboardStats: function() {
        const invoices = Object.values(this.data.invoices || {});
        const customers = Object.values(this.data.customers || {});
        
        return {
            totalInvoices: invoices.length,
            totalRevenue: invoices.reduce((sum, inv) => sum + (inv.total || 0), 0),
            totalCustomers: customers.length,
            pendingInvoices: invoices.filter(inv => inv.status === 'sent' || inv.status === 'overdue').length
        };
    },

    /**
     * تنسيق المبالغ
     */
    formatAmount: function(amount) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: this.data.settings.currency
        }).format(amount);
    },

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners: function() {
        // سيتم إضافة مستمعي الأحداث هنا
    },

    /**
     * عرض آخر الفواتير
     */
    renderRecentInvoices: function() {
        const invoices = Object.values(this.data.invoices || {})
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, 5);

        if (invoices.length === 0) {
            return `
                <div class="text-center py-4">
                    <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد فواتير</h5>
                    <p class="text-muted">ابدأ بإنشاء فاتورة جديدة</p>
                    <button class="btn btn-primary" onclick="window.SalesComponent.showNewInvoiceModal()">
                        <i class="fas fa-plus me-1"></i>فاتورة جديدة
                    </button>
                </div>
            `;
        }

        return `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>التاريخ</th>
                            <th>المبلغ</th>
                            <th>الحالة</th>
                            <th>إجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${invoices.map(invoice => `
                            <tr>
                                <td><strong>${invoice.number}</strong></td>
                                <td>${this.data.customers[invoice.customerId]?.name || 'عميل غير محدد'}</td>
                                <td>${new Date(invoice.date).toLocaleDateString('ar-SA')}</td>
                                <td>${this.formatAmount(invoice.total)}</td>
                                <td>
                                    <span class="badge bg-${this.getInvoiceStatusColor(invoice.status)}">
                                        ${this.getInvoiceStatusLabel(invoice.status)}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="window.SalesComponent.viewInvoice('${invoice.id}')" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="window.SalesComponent.editInvoice('${invoice.id}')" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-info" onclick="window.SalesComponent.printInvoice('${invoice.id}')" title="طباعة">
                                            <i class="fas fa-print"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    },

    /**
     * الحصول على لون حالة الفاتورة
     */
    getInvoiceStatusColor: function(status) {
        const colors = {
            'draft': 'secondary',
            'sent': 'info',
            'paid': 'success',
            'overdue': 'danger',
            'cancelled': 'dark'
        };
        return colors[status] || 'secondary';
    },

    /**
     * الحصول على تسمية حالة الفاتورة
     */
    getInvoiceStatusLabel: function(status) {
        const labels = {
            'draft': 'مسودة',
            'sent': 'مرسلة',
            'paid': 'مدفوعة',
            'overdue': 'متأخرة',
            'cancelled': 'ملغية'
        };
        return labels[status] || 'غير محدد';
    },

    /**
     * عرض رسالة للمستخدم
     */
    showMessage: function(message, type = 'info') {
        const alertClass = type === 'success' ? 'alert-success' :
                          type === 'error' ? 'alert-danger' :
                          type === 'warning' ? 'alert-warning' : 'alert-info';

        const alertHTML = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <i class="fas fa-${type === 'success' ? 'check-circle' :
                                  type === 'error' ? 'exclamation-circle' :
                                  type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // إضافة الرسالة إلى أعلى الصفحة
        const container = document.querySelector('.sales-management') || document.body;
        container.insertAdjacentHTML('afterbegin', alertHTML);

        // إزالة الرسالة تلقائياً بعد 5 ثوان
        setTimeout(() => {
            const alert = container.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    },

    /**
     * توليد معرف فريد
     */
    generateId: function() {
        return 'ID_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    },

    /**
     * عرض قائمة الفواتير المتقدمة
     */
    renderInvoices: function() {
        const invoices = Object.values(this.data.invoices || {})
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        return `
            <div class="invoices-management">
                <!-- الإحصائيات السريعة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-light rounded p-3 text-center">
                            <h4 class="text-primary mb-1">${invoices.length}</h4>
                            <small class="text-muted">إجمالي الفواتير</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light rounded p-3 text-center">
                            <h4 class="text-success mb-1">${invoices.filter(inv => inv.status === 'paid').length}</h4>
                            <small class="text-muted">الفواتير المدفوعة</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light rounded p-3 text-center">
                            <h4 class="text-warning mb-1">${invoices.filter(inv => inv.status === 'sent' || inv.status === 'overdue').length}</h4>
                            <small class="text-muted">الفواتير المعلقة</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light rounded p-3 text-center">
                            <h4 class="text-info mb-1">${this.formatAmount(invoices.reduce((sum, inv) => sum + (inv.total || 0), 0))}</h4>
                            <small class="text-muted">إجمالي القيمة</small>
                        </div>
                    </div>
                </div>

                <!-- شريط الأدوات -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-0">
                                    <i class="fas fa-file-invoice me-2"></i>إدارة الفواتير
                                </h5>
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="btn-group">
                                    <button class="btn btn-primary" onclick="window.SalesComponent.showNewInvoiceModal()">
                                        <i class="fas fa-plus me-1"></i>فاتورة جديدة
                                    </button>
                                    <div class="btn-group">
                                        <button class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                            <i class="fas fa-download me-1"></i>تصدير
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.exportInvoicesCSV()">
                                                <i class="fas fa-file-csv me-2"></i>تصدير CSV
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.exportInvoicesExcel()">
                                                <i class="fas fa-file-excel me-2"></i>تصدير Excel
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.exportInvoicesPDF()">
                                                <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.printAllInvoices()">
                                                <i class="fas fa-print me-2"></i>طباعة الكل
                                            </a></li>
                                        </ul>
                                    </div>
                                    <button class="btn btn-outline-warning" onclick="window.SalesComponent.showDeletedInvoices()" title="سلة المحذوفات">
                                        <i class="fas fa-trash-restore me-1"></i>المحذوفات
                                        ${Object.keys(this.data.deletedInvoices || {}).length > 0 ?
                                            `<span class="badge bg-danger ms-1">${Object.keys(this.data.deletedInvoices || {}).length}</span>` :
                                            ''
                                        }
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- فلاتر البحث المتقدمة -->
                <div class="card bg-light">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label small">البحث</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="رقم الفاتورة أو المرجع..."
                                           id="invoiceSearch" onkeyup="window.SalesComponent.filterInvoices()">
                                    <button class="btn btn-outline-secondary" type="button" onclick="window.SalesComponent.clearSearch()">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small">الحالة</label>
                                <select class="form-control" id="statusFilter" onchange="window.SalesComponent.filterInvoices()">
                                    <option value="">جميع الحالات</option>
                                    <option value="draft">مسودة</option>
                                    <option value="sent">مرسلة</option>
                                    <option value="paid">مدفوعة</option>
                                    <option value="overdue">متأخرة</option>
                                    <option value="cancelled">ملغية</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small">من تاريخ</label>
                                <input type="date" class="form-control" id="dateFromFilter" onchange="window.SalesComponent.filterInvoices()">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small">إلى تاريخ</label>
                                <input type="date" class="form-control" id="dateToFilter" onchange="window.SalesComponent.filterInvoices()">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small">العميل</label>
                                <select class="form-control" id="customerFilter" onchange="window.SalesComponent.filterInvoices()">
                                    <option value="">جميع العملاء</option>
                                    ${Object.values(this.data.customers || {}).map(customer => `
                                        <option value="${customer.id}">${customer.name}</option>
                                    `).join('')}
                                </select>
                            </div>
                            <div class="col-md-1">
                                <label class="form-label small">&nbsp;</label>
                                <button class="btn btn-outline-primary w-100" onclick="window.SalesComponent.clearAllFilters()" title="مسح الفلاتر">
                                    <i class="fas fa-eraser"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول الفواتير المتطور -->
                <div class="card mt-3">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAllInvoices" onchange="window.SalesComponent.toggleSelectAll()">
                                    <label class="form-check-label" for="selectAllInvoices">
                                        تحديد الكل
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 text-end">
                                <div id="bulkActionsContainer" style="display: none;">
                                    <span class="text-muted me-2">المحدد: <span id="selectedCount">0</span></span>
                                    <div class="btn-group">
                                        <button class="btn btn-outline-success btn-sm" onclick="window.SalesComponent.bulkMarkAsPaid()">
                                            <i class="fas fa-check me-1"></i>تحديد كمدفوع
                                        </button>
                                        <button class="btn btn-outline-warning btn-sm" onclick="window.SalesComponent.bulkMarkAsSent()">
                                            <i class="fas fa-paper-plane me-1"></i>تحديد كمرسل
                                        </button>
                                        <button class="btn btn-outline-info btn-sm" onclick="window.SalesComponent.bulkPrint()">
                                            <i class="fas fa-print me-1"></i>طباعة المحدد
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm" onclick="window.SalesComponent.bulkDelete()">
                                            <i class="fas fa-trash me-1"></i>حذف المحدد
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div id="invoicesTableContainer">
                            ${invoices.length === 0 ? this.renderEmptyInvoices() : this.renderInvoicesTableAdvanced(invoices)}
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض رسالة عدم وجود فواتير
     */
    renderEmptyInvoices: function() {
        return `
            <div class="text-center py-5">
                <i class="fas fa-file-invoice fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد فواتير</h4>
                <p class="text-muted">ابدأ بإنشاء فاتورة جديدة لعملائك</p>
                <button class="btn btn-primary" onclick="window.SalesComponent.showNewInvoiceModal()">
                    <i class="fas fa-plus me-1"></i>إنشاء فاتورة جديدة
                </button>
            </div>
        `;
    },

    /**
     * عرض جدول الفواتير المتطور
     */
    renderInvoicesTableAdvanced: function(invoices) {
        return `
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="3%">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAllInvoicesHeader">
                                </div>
                            </th>
                            <th width="15%">رقم الفاتورة</th>
                            <th width="20%">العميل</th>
                            <th width="15%">التاريخ</th>
                            <th width="15%">الإجمالي</th>
                            <th width="12%">الحالة</th>
                            <th width="20%">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${invoices.map(invoice => `
                            <tr class="invoice-row" data-invoice-id="${invoice.id}">
                                <td>
                                    <div class="form-check">
                                        <input class="form-check-input invoice-checkbox" type="checkbox"
                                               value="${invoice.id}" onchange="window.SalesComponent.updateBulkActions()">
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong class="text-primary">${invoice.number}</strong>
                                        ${invoice.reference ? `<br><small class="text-muted"><i class="fas fa-tag me-1"></i>${invoice.reference}</small>` : ''}
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong>${this.data.customers[invoice.customerId]?.name || 'عميل غير محدد'}</strong>
                                        ${this.data.customers[invoice.customerId]?.email ? `<br><small class="text-muted">${this.data.customers[invoice.customerId].email}</small>` : ''}
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong>${new Date(invoice.date).toLocaleDateString('ar-SA')}</strong>
                                        ${invoice.dueDate ? `<br><small class="text-warning">
                                            <i class="fas fa-clock me-1"></i>الاستحقاق: ${new Date(invoice.dueDate).toLocaleDateString('ar-SA')}
                                        </small>` : ''}
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong class="fs-6">${this.formatAmount(invoice.total)}</strong>
                                        <br><small class="text-muted">${invoice.items?.length || 0} عنصر</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-${this.getInvoiceStatusColor(invoice.status)}">
                                        ${this.getInvoiceStatusLabel(invoice.status)}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="window.SalesComponent.viewInvoice('${invoice.id}')" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="window.SalesComponent.editInvoice('${invoice.id}')" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <div class="btn-group">
                                            <button class="btn btn-outline-info dropdown-toggle" data-bs-toggle="dropdown" title="المزيد">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.printInvoice('${invoice.id}')">
                                                    <i class="fas fa-print me-2"></i>طباعة
                                                </a></li>
                                                <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.duplicateInvoice('${invoice.id}')">
                                                    <i class="fas fa-copy me-2"></i>نسخ
                                                </a></li>
                                                <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.sendInvoiceEmail('${invoice.id}')">
                                                    <i class="fas fa-envelope me-2"></i>إرسال بالبريد
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#" onclick="window.SalesComponent.deleteInvoice('${invoice.id}')">
                                                    <i class="fas fa-trash me-2"></i>حذف
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    },

    /**
     * فلترة الفواتير المتقدمة
     */
    filterInvoices: function() {
        const searchTerm = document.getElementById('invoiceSearch')?.value.toLowerCase() || '';
        const statusFilter = document.getElementById('statusFilter')?.value || '';
        const dateFromFilter = document.getElementById('dateFromFilter')?.value || '';
        const dateToFilter = document.getElementById('dateToFilter')?.value || '';
        const customerFilter = document.getElementById('customerFilter')?.value || '';

        let filteredInvoices = Object.values(this.data.invoices || {});

        // فلترة بالبحث
        if (searchTerm) {
            filteredInvoices = filteredInvoices.filter(invoice =>
                invoice.number.toLowerCase().includes(searchTerm) ||
                invoice.reference?.toLowerCase().includes(searchTerm) ||
                this.data.customers[invoice.customerId]?.name.toLowerCase().includes(searchTerm)
            );
        }

        // فلترة بالحالة
        if (statusFilter) {
            filteredInvoices = filteredInvoices.filter(invoice => invoice.status === statusFilter);
        }

        // فلترة بالتاريخ من
        if (dateFromFilter) {
            filteredInvoices = filteredInvoices.filter(invoice => invoice.date >= dateFromFilter);
        }

        // فلترة بالتاريخ إلى
        if (dateToFilter) {
            filteredInvoices = filteredInvoices.filter(invoice => invoice.date <= dateToFilter);
        }

        // فلترة بالعميل
        if (customerFilter) {
            filteredInvoices = filteredInvoices.filter(invoice => invoice.customerId === customerFilter);
        }

        // ترتيب النتائج
        filteredInvoices.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        // تحديث الجدول
        this.updateInvoicesDisplay(filteredInvoices);
    },

    /**
     * تحديث عرض الفواتير
     */
    updateInvoicesDisplay: function(filteredInvoices) {
        const container = document.getElementById('invoicesTableContainer');
        if (container) {
            container.innerHTML = filteredInvoices.length === 0 ?
                '<div class="text-center py-4"><p class="text-muted">لا توجد فواتير تطابق معايير البحث</p></div>' :
                this.renderInvoicesTableAdvanced(filteredInvoices);
        }
    },

    /**
     * مسح جميع الفلاتر
     */
    clearAllFilters: function() {
        document.getElementById('invoiceSearch').value = '';
        document.getElementById('statusFilter').value = '';
        document.getElementById('dateFromFilter').value = '';
        document.getElementById('dateToFilter').value = '';
        document.getElementById('customerFilter').value = '';
        this.filterInvoices();
    },

    /**
     * مسح البحث
     */
    clearSearch: function() {
        document.getElementById('invoiceSearch').value = '';
        this.filterInvoices();
    },

    /**
     * تبديل تحديد الكل
     */
    toggleSelectAll: function() {
        const selectAllCheckbox = document.getElementById('selectAllInvoices');
        const invoiceCheckboxes = document.querySelectorAll('.invoice-checkbox');

        invoiceCheckboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
        });

        this.updateBulkActions();
    },

    /**
     * تحديث إجراءات التحديد المجمع
     */
    updateBulkActions: function() {
        const selectedCheckboxes = document.querySelectorAll('.invoice-checkbox:checked');
        const bulkActionsContainer = document.getElementById('bulkActionsContainer');
        const selectedCountSpan = document.getElementById('selectedCount');

        if (selectedCheckboxes.length > 0) {
            bulkActionsContainer.style.display = 'block';
            selectedCountSpan.textContent = selectedCheckboxes.length;
        } else {
            bulkActionsContainer.style.display = 'none';
        }

        // تحديث حالة تحديد الكل
        const selectAllCheckbox = document.getElementById('selectAllInvoices');
        const allCheckboxes = document.querySelectorAll('.invoice-checkbox');
        selectAllCheckbox.checked = selectedCheckboxes.length === allCheckboxes.length;
    },

    /**
     * عرض تفاصيل الفاتورة
     */
    viewInvoice: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            this.showMessage('الفاتورة غير موجودة', 'error');
            return;
        }

        const customer = this.data.customers[invoice.customerId];

        const modalHTML = `
            <div class="modal fade" id="viewInvoiceModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-file-invoice me-2"></i>فاتورة رقم ${invoice.number}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${this.renderInvoiceDetails(invoice, customer)}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-outline-primary" onclick="window.SalesComponent.editInvoice('${invoiceId}')">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="window.SalesComponent.printInvoice('${invoiceId}')">
                                <i class="fas fa-print me-1"></i>طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('viewInvoiceModal'));
        modal.show();

        document.getElementById('viewInvoiceModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * عرض تفاصيل الفاتورة
     */
    renderInvoiceDetails: function(invoice, customer) {
        return `
            <div class="invoice-details">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6>معلومات الفاتورة:</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>رقم الفاتورة:</strong></td>
                                <td>${invoice.number}</td>
                            </tr>
                            <tr>
                                <td><strong>التاريخ:</strong></td>
                                <td>${new Date(invoice.date).toLocaleDateString('ar-SA')}</td>
                            </tr>
                            ${invoice.dueDate ? `
                                <tr>
                                    <td><strong>تاريخ الاستحقاق:</strong></td>
                                    <td>${new Date(invoice.dueDate).toLocaleDateString('ar-SA')}</td>
                                </tr>
                            ` : ''}
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    <span class="badge bg-${this.getInvoiceStatusColor(invoice.status)}">
                                        ${this.getInvoiceStatusLabel(invoice.status)}
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>معلومات العميل:</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>الاسم:</strong></td>
                                <td>${customer?.name || 'عميل غير محدد'}</td>
                            </tr>
                            ${customer?.email ? `
                                <tr>
                                    <td><strong>البريد الإلكتروني:</strong></td>
                                    <td>${customer.email}</td>
                                </tr>
                            ` : ''}
                            ${customer?.phone ? `
                                <tr>
                                    <td><strong>الهاتف:</strong></td>
                                    <td>${customer.phone}</td>
                                </tr>
                            ` : ''}
                        </table>
                    </div>
                </div>

                <h6>عناصر الفاتورة:</h6>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th>المنتج/الخدمة</th>
                                <th>الكمية</th>
                                <th>السعر</th>
                                <th>الخصم</th>
                                <th>المجموع</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${invoice.items?.map(item => {
                                const product = this.data.products[item.productId];
                                return `
                                    <tr>
                                        <td>${product?.name || 'منتج غير محدد'}</td>
                                        <td>${item.quantity}</td>
                                        <td>${this.formatAmount(item.price)}</td>
                                        <td>${this.formatAmount(item.discount)}</td>
                                        <td>${this.formatAmount(item.total)}</td>
                                    </tr>
                                `;
                            }).join('') || '<tr><td colspan="5">لا توجد عناصر</td></tr>'}
                        </tbody>
                    </table>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        ${invoice.notes ? `
                            <h6>ملاحظات:</h6>
                            <p class="text-muted">${invoice.notes}</p>
                        ` : ''}
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>المجموع الفرعي:</strong></td>
                                <td class="text-end">${this.formatAmount(invoice.subtotal)}</td>
                            </tr>
                            <tr>
                                <td><strong>الخصم:</strong></td>
                                <td class="text-end">${this.formatAmount(invoice.totalDiscount)}</td>
                            </tr>
                            <tr>
                                <td><strong>الضريبة:</strong></td>
                                <td class="text-end">${this.formatAmount(invoice.taxAmount)}</td>
                            </tr>
                            <tr class="table-primary">
                                <td><strong>الإجمالي:</strong></td>
                                <td class="text-end"><strong>${this.formatAmount(invoice.total)}</strong></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * تعديل الفاتورة
     */
    editInvoice: function(invoiceId) {
        this.showMessage('وظيفة التعديل قيد التطوير', 'info');
    },

    /**
     * حذف الفاتورة
     */
    deleteInvoice: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            this.showMessage('الفاتورة غير موجودة', 'error');
            return;
        }

        if (confirm(`هل أنت متأكد من حذف الفاتورة ${invoice.number}؟`)) {
            // نقل إلى سلة المحذوفات
            if (!this.data.deletedInvoices) {
                this.data.deletedInvoices = {};
            }

            this.data.deletedInvoices[invoiceId] = {
                ...invoice,
                deletedAt: new Date().toISOString(),
                deleteReason: 'حذف يدوي'
            };

            delete this.data.invoices[invoiceId];
            this.saveSalesData();
            this.showMessage(`تم نقل الفاتورة ${invoice.number} إلى سلة المحذوفات`, 'success');
            this.refreshData();
        }
    },

    /**
     * طباعة الفاتورة
     */
    printInvoice: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            this.showMessage('الفاتورة غير موجودة', 'error');
            return;
        }

        const customer = this.data.customers[invoice.customerId];
        const printWindow = window.open('', '_blank');

        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>فاتورة ${invoice.number}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
                    .company-name { font-size: 24px; font-weight: bold; color: #2c3e50; margin-bottom: 10px; }
                    table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                    th, td { border: 1px solid #ddd; padding: 12px; text-align: right; }
                    th { background-color: #f8f9fa; font-weight: bold; }
                    .totals { margin-top: 20px; text-align: left; }
                    .total-row { font-size: 18px; font-weight: bold; color: #2c3e50; }
                    @media print { .no-print { display: none; } }
                </style>
            </head>
            <body>
                <div class="header">
                    <div class="company-name">${this.data.settings.companyName}</div>
                    <p>${this.data.settings.companyAddress}</p>
                    <p>الرقم الضريبي: ${this.data.settings.companyTaxNumber}</p>
                </div>

                <h2>فاتورة رقم: ${invoice.number}</h2>
                <p>تاريخ الإصدار: ${new Date(invoice.date).toLocaleDateString('ar-SA')}</p>
                ${invoice.dueDate ? `<p>تاريخ الاستحقاق: ${new Date(invoice.dueDate).toLocaleDateString('ar-SA')}</p>` : ''}

                <h3>معلومات العميل:</h3>
                <p><strong>${customer?.name || 'عميل غير محدد'}</strong></p>
                ${customer?.email ? `<p>البريد الإلكتروني: ${customer.email}</p>` : ''}
                ${customer?.phone ? `<p>الهاتف: ${customer.phone}</p>` : ''}

                <table>
                    <thead>
                        <tr>
                            <th>المنتج/الخدمة</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>الخصم</th>
                            <th>المجموع</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${invoice.items?.map(item => {
                            const product = this.data.products[item.productId];
                            return `
                                <tr>
                                    <td>${product?.name || 'منتج غير محدد'}</td>
                                    <td>${item.quantity}</td>
                                    <td>${this.formatAmount(item.price)}</td>
                                    <td>${this.formatAmount(item.discount)}</td>
                                    <td>${this.formatAmount(item.total)}</td>
                                </tr>
                            `;
                        }).join('') || '<tr><td colspan="5">لا توجد عناصر</td></tr>'}
                    </tbody>
                </table>

                <div class="totals">
                    <p>المجموع الفرعي: ${this.formatAmount(invoice.subtotal)}</p>
                    <p>الخصم: ${this.formatAmount(invoice.totalDiscount)}</p>
                    <p>الضريبة: ${this.formatAmount(invoice.taxAmount)}</p>
                    <p class="total-row">الإجمالي: ${this.formatAmount(invoice.total)}</p>
                </div>

                ${invoice.notes ? `<div><h3>ملاحظات:</h3><p>${invoice.notes}</p></div>` : ''}

                <button class="no-print" onclick="window.print()">طباعة</button>
            </body>
            </html>
        `);

        printWindow.document.close();
        printWindow.focus();
    },

    /**
     * عرض نافذة فاتورة جديدة
     */
    showNewInvoiceModal: function() {
        this.showMessage('وظيفة إنشاء فاتورة جديدة قيد التطوير', 'info');
    },

    /**
     * عرض نافذة عميل جديد
     */
    showNewCustomerModal: function() {
        this.showMessage('وظيفة إنشاء عميل جديد قيد التطوير', 'info');
    },

    /**
     * نسخ الفاتورة
     */
    duplicateInvoice: function(invoiceId) {
        this.showMessage('وظيفة نسخ الفاتورة قيد التطوير', 'info');
    },

    /**
     * إرسال الفاتورة بالبريد الإلكتروني
     */
    sendInvoiceEmail: function(invoiceId) {
        this.showMessage('وظيفة إرسال البريد الإلكتروني قيد التطوير', 'info');
    },

    /**
     * تصدير الفواتير CSV
     */
    exportInvoicesCSV: function() {
        this.showMessage('وظيفة تصدير CSV قيد التطوير', 'info');
    },

    /**
     * تصدير الفواتير Excel
     */
    exportInvoicesExcel: function() {
        this.showMessage('وظيفة تصدير Excel قيد التطوير', 'info');
    },

    /**
     * تصدير الفواتير PDF
     */
    exportInvoicesPDF: function() {
        this.showMessage('وظيفة تصدير PDF قيد التطوير', 'info');
    },

    /**
     * طباعة جميع الفواتير
     */
    printAllInvoices: function() {
        this.showMessage('وظيفة طباعة جميع الفواتير قيد التطوير', 'info');
    },

    /**
     * عرض سلة المحذوفات
     */
    showDeletedInvoices: function() {
        const deletedInvoices = Object.values(this.data.deletedInvoices || {});

        if (deletedInvoices.length === 0) {
            this.showMessage('سلة المحذوفات فارغة', 'info');
            return;
        }

        this.showMessage(`يوجد ${deletedInvoices.length} فاتورة في سلة المحذوفات`, 'info');
    },

    /**
     * تحديد المحدد كمدفوع
     */
    bulkMarkAsPaid: function() {
        this.showMessage('وظيفة التحديد المجمع كمدفوع قيد التطوير', 'info');
    },

    /**
     * تحديد المحدد كمرسل
     */
    bulkMarkAsSent: function() {
        this.showMessage('وظيفة التحديد المجمع كمرسل قيد التطوير', 'info');
    },

    /**
     * طباعة المحدد
     */
    bulkPrint: function() {
        this.showMessage('وظيفة الطباعة المجمعة قيد التطوير', 'info');
    },

    /**
     * حذف المحدد
     */
    bulkDelete: function() {
        this.showMessage('وظيفة الحذف المجمع قيد التطوير', 'info');
    },

    /**
     * عرض إدارة العملاء
     */
    renderCustomers: function() {
        return `
            <div class="customers-management">
                <div class="text-center py-5">
                    <i class="fas fa-users fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">إدارة العملاء</h4>
                    <p class="text-muted">هذا القسم قيد التطوير</p>
                </div>
            </div>
        `;
    },

    /**
     * عرض إدارة المنتجات
     */
    renderProducts: function() {
        return `
            <div class="products-management">
                <div class="text-center py-5">
                    <i class="fas fa-box fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">إدارة المنتجات والخدمات</h4>
                    <p class="text-muted">هذا القسم قيد التطوير</p>
                </div>
            </div>
        `;
    },

    /**
     * عرض التقارير
     */
    renderReports: function() {
        return `
            <div class="reports-management">
                <div class="text-center py-5">
                    <i class="fas fa-chart-bar fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">التقارير والإحصائيات</h4>
                    <p class="text-muted">هذا القسم قيد التطوير</p>
                </div>
            </div>
        `;
    },

    /**
     * عرض الإعدادات
     */
    renderSettings: function() {
        return `
            <div class="settings-management">
                <div class="text-center py-5">
                    <i class="fas fa-cog fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">الإعدادات</h4>
                    <p class="text-muted">هذا القسم قيد التطوير</p>
                </div>
            </div>
        `;
    },

    /**
     * تحديث البيانات
     */
    refreshData: function() {
        this.loadSalesData();
        const content = document.getElementById('main-content');
        if (content) {
            content.innerHTML = this.renderCurrentView();
        }
    }
};

// تصدير المكون للاستخدام العام
window.SalesComponent = SalesComponent;
