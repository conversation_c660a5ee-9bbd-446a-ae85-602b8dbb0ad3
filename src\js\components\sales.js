/**
 * مكون نظام المبيعات
 * Sales Management Component
 */

const SalesComponent = {
    // بيانات النظام
    data: {
        currentView: 'dashboard',
        customers: {},
        products: {},
        invoices: {},
        deletedInvoices: {},
        salesTransactions: {},
        settings: {
            taxRate: 0.15, // ضريبة القيمة المضافة 15%
            currency: 'SAR',
            invoicePrefix: 'INV-',
            nextInvoiceNumber: 1,
            companyName: 'قمة الوعد للسفريات',
            companyAddress: 'المملكة العربية السعودية',
            companyTaxNumber: '*********',
            companyEmail: '<EMAIL>',
            companyPhone: '+966501234567'
        }
    },

    // إعدادات العرض
    views: [
        { key: 'dashboard', label: 'لوحة التحكم', icon: 'fas fa-tachometer-alt' },
        { key: 'invoices', label: 'الفواتير', icon: 'fas fa-file-invoice' },
        { key: 'customers', label: 'العملاء', icon: 'fas fa-users' },
        { key: 'products', label: 'المنتجات والخدمات', icon: 'fas fa-box' },
        { key: 'reports', label: 'التقارير', icon: 'fas fa-chart-bar' },
        { key: 'settings', label: 'الإعدادات', icon: 'fas fa-cog' }
    ],

    /**
     * تهيئة مكون المبيعات
     */
    init: function() {
        this.loadSalesData();
        this.setupEventListeners();
        console.log('✅ تم تهيئة نظام المبيعات');
    },

    /**
     * تحميل بيانات المبيعات
     */
    loadSalesData: function() {
        try {
            // تحميل البيانات من localStorage
            const savedData = localStorage.getItem('salesData');
            if (savedData) {
                const data = JSON.parse(savedData);
                this.data = { ...this.data, ...data };
            } else {
                // إنشاء بيانات تجريبية إذا لم توجد بيانات محفوظة
                this.createSampleData();
            }
        } catch (error) {
            console.error('خطأ في تحميل بيانات المبيعات:', error);
            this.createSampleData();
        }
    },

    /**
     * حفظ بيانات المبيعات
     */
    saveSalesData: function() {
        try {
            localStorage.setItem('salesData', JSON.stringify(this.data));
        } catch (error) {
            console.error('خطأ في حفظ بيانات المبيعات:', error);
        }
    },

    /**
     * إنشاء بيانات تجريبية
     */
    createSampleData: function() {
        // عملاء تجريبيون
        this.data.customers = {
            'CUST_001': {
                id: 'CUST_001',
                name: 'أحمد محمد السعيد',
                email: '<EMAIL>',
                phone: '+966501234567',
                address: 'الرياض، المملكة العربية السعودية',
                type: 'individual',
                createdAt: new Date().toISOString()
            },
            'CUST_002': {
                id: 'CUST_002',
                name: 'شركة النور للسفريات',
                email: '<EMAIL>',
                phone: '+966507654321',
                address: 'جدة، المملكة العربية السعودية',
                type: 'corporate',
                taxNumber: '*********',
                createdAt: new Date().toISOString()
            }
        };

        // منتجات وخدمات تجريبية
        this.data.products = {
            'PROD_001': {
                id: 'PROD_001',
                name: 'تذكرة طيران - الرياض إلى دبي',
                description: 'تذكرة طيران ذهاب وعودة',
                price: 1200,
                cost: 900,
                category: 'flights',
                createdAt: new Date().toISOString()
            },
            'PROD_002': {
                id: 'PROD_002',
                name: 'حجز فندق - 3 نجوم',
                description: 'حجز فندق لليلة واحدة',
                price: 300,
                cost: 200,
                category: 'hotels',
                createdAt: new Date().toISOString()
            },
            'PROD_003': {
                id: 'PROD_003',
                name: 'تأشيرة سياحية',
                description: 'استخراج تأشيرة سياحية',
                price: 500,
                cost: 350,
                category: 'visas',
                createdAt: new Date().toISOString()
            }
        };

        // فواتير تجريبية
        this.data.invoices = {
            'INV_001': {
                id: 'INV_001',
                number: 'INV-0001',
                customerId: 'CUST_001',
                date: new Date().toISOString().split('T')[0],
                dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                status: 'sent',
                items: [
                    {
                        productId: 'PROD_001',
                        quantity: 1,
                        price: 1200,
                        discount: 0,
                        total: 1200
                    }
                ],
                subtotal: 1200,
                totalDiscount: 0,
                taxAmount: 180,
                total: 1380,
                notes: 'شكراً لتعاملكم معنا',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            }
        };

        // الإعدادات الافتراضية
        this.data.settings = {
            taxRate: 0.15,
            currency: 'SAR',
            invoicePrefix: 'INV-',
            nextInvoiceNumber: 2,
            companyName: 'قمة الوعد للسفريات',
            companyAddress: 'المملكة العربية السعودية',
            companyTaxNumber: '*********',
            companyEmail: '<EMAIL>',
            companyPhone: '+966501234567',
            companyWebsite: 'https://qimat-alwaed.com',
            language: 'ar',
            timezone: 'Asia/Riyadh',
            dateFormat: 'ar-SA',
            autoSave: true,
            showNotifications: true,
            enableBackup: true,
            autoCalculateTax: true,
            defaultDueDays: 30,
            // إعدادات تصميم الفواتير
            selectedTemplate: 'classic',
            primaryColor: '#007bff',
            secondaryColor: '#6c757d',
            fontSize: 'medium',
            showLogo: true,
            showWatermark: false,
            showBorder: true
        };

        this.saveSalesData();
    },

    /**
     * تبديل العرض
     */
    switchView: function(view) {
        this.data.currentView = view;
        const content = document.getElementById('main-content');
        if (content) {
            content.innerHTML = this.renderCurrentView();
        }
    },

    /**
     * عرض المحتوى الحالي
     */
    renderCurrentView: function() {
        switch (this.data.currentView) {
            case 'dashboard':
                return this.renderDashboard();
            case 'invoices':
                return this.renderInvoices();
            case 'customers':
                return this.renderCustomers();
            case 'products':
                return this.renderProducts();
            case 'reports':
                return this.renderReports();
            case 'settings':
                return this.renderSettings();
            default:
                return this.renderDashboard();
        }
    },

    /**
     * عرض نافذة المبيعات الرئيسية
     */
    render: function() {
        return `
            <div class="sales-management">
                <!-- شريط التنقل -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <nav class="nav nav-pills">
                                    ${this.views.map(view => `
                                        <button class="nav-link ${this.data.currentView === view.key ? 'active' : ''}" 
                                                onclick="window.SalesComponent.switchView('${view.key}')">
                                            <i class="${view.icon} me-1"></i>${view.label}
                                        </button>
                                    `).join('')}
                                </nav>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="btn-group">
                                    <button class="btn btn-outline-primary" onclick="window.SalesComponent.showNewInvoiceModal()">
                                        <i class="fas fa-plus me-1"></i>فاتورة جديدة
                                    </button>
                                    <button class="btn btn-outline-success" onclick="window.SalesComponent.showNewCustomerModal()">
                                        <i class="fas fa-user-plus me-1"></i>عميل جديد
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- المحتوى الرئيسي -->
                <div id="sales-content">
                    ${this.renderCurrentView()}
                </div>
            </div>
        `;
    },

    /**
     * عرض لوحة التحكم
     */
    renderDashboard: function() {
        const stats = this.calculateDashboardStats();
        
        return `
            <div class="dashboard">
                <!-- الإحصائيات السريعة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0">${stats.totalInvoices}</h4>
                                        <p class="mb-0">إجمالي الفواتير</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-file-invoice fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0">${this.formatAmount(stats.totalRevenue)}</h4>
                                        <p class="mb-0">إجمالي الإيرادات</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-money-bill-wave fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0">${stats.totalCustomers}</h4>
                                        <p class="mb-0">إجمالي العملاء</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0">${stats.pendingInvoices}</h4>
                                        <p class="mb-0">فواتير معلقة</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-clock fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- آخر الفواتير -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2"></i>آخر الفواتير
                        </h5>
                    </div>
                    <div class="card-body">
                        ${this.renderRecentInvoices()}
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * حساب إحصائيات لوحة التحكم
     */
    calculateDashboardStats: function() {
        const invoices = Object.values(this.data.invoices || {});
        const customers = Object.values(this.data.customers || {});
        
        return {
            totalInvoices: invoices.length,
            totalRevenue: invoices.reduce((sum, inv) => sum + (inv.total || 0), 0),
            totalCustomers: customers.length,
            pendingInvoices: invoices.filter(inv => inv.status === 'sent' || inv.status === 'overdue').length
        };
    },

    /**
     * تنسيق المبالغ
     */
    formatAmount: function(amount) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: this.data.settings.currency
        }).format(amount);
    },

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners: function() {
        // سيتم إضافة مستمعي الأحداث هنا
    },

    /**
     * عرض آخر الفواتير
     */
    renderRecentInvoices: function() {
        const invoices = Object.values(this.data.invoices || {})
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, 5);

        if (invoices.length === 0) {
            return `
                <div class="text-center py-4">
                    <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد فواتير</h5>
                    <p class="text-muted">ابدأ بإنشاء فاتورة جديدة</p>
                    <button class="btn btn-primary" onclick="window.SalesComponent.showNewInvoiceModal()">
                        <i class="fas fa-plus me-1"></i>فاتورة جديدة
                    </button>
                </div>
            `;
        }

        return `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>التاريخ</th>
                            <th>المبلغ</th>
                            <th>الحالة</th>
                            <th>إجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${invoices.map(invoice => `
                            <tr>
                                <td><strong>${invoice.number}</strong></td>
                                <td>${this.data.customers[invoice.customerId]?.name || 'عميل غير محدد'}</td>
                                <td>${new Date(invoice.date).toLocaleDateString('ar-SA')}</td>
                                <td>${this.formatAmount(invoice.total)}</td>
                                <td>
                                    <span class="badge bg-${this.getInvoiceStatusColor(invoice.status)}">
                                        ${this.getInvoiceStatusLabel(invoice.status)}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="window.SalesComponent.viewInvoice('${invoice.id}')" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="window.SalesComponent.editInvoice('${invoice.id}')" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-info" onclick="window.SalesComponent.printInvoice('${invoice.id}')" title="طباعة">
                                            <i class="fas fa-print"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    },

    /**
     * الحصول على لون حالة الفاتورة
     */
    getInvoiceStatusColor: function(status) {
        const colors = {
            'draft': 'secondary',
            'sent': 'info',
            'paid': 'success',
            'overdue': 'danger',
            'cancelled': 'dark'
        };
        return colors[status] || 'secondary';
    },

    /**
     * الحصول على تسمية حالة الفاتورة
     */
    getInvoiceStatusLabel: function(status) {
        const labels = {
            'draft': 'مسودة',
            'sent': 'مرسلة',
            'paid': 'مدفوعة',
            'overdue': 'متأخرة',
            'cancelled': 'ملغية'
        };
        return labels[status] || 'غير محدد';
    },

    /**
     * عرض رسالة للمستخدم
     */
    showMessage: function(message, type = 'info') {
        const alertClass = type === 'success' ? 'alert-success' :
                          type === 'error' ? 'alert-danger' :
                          type === 'warning' ? 'alert-warning' : 'alert-info';

        const alertHTML = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <i class="fas fa-${type === 'success' ? 'check-circle' :
                                  type === 'error' ? 'exclamation-circle' :
                                  type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // إضافة الرسالة إلى أعلى الصفحة
        const container = document.querySelector('.sales-management') || document.body;
        container.insertAdjacentHTML('afterbegin', alertHTML);

        // إزالة الرسالة تلقائياً بعد 5 ثوان
        setTimeout(() => {
            const alert = container.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    },

    /**
     * توليد معرف فريد
     */
    generateId: function() {
        return 'ID_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    },

    /**
     * عرض قائمة الفواتير المتقدمة
     */
    renderInvoices: function() {
        const invoices = Object.values(this.data.invoices || {})
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        return `
            <div class="invoices-management">
                <!-- الإحصائيات السريعة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-light rounded p-3 text-center">
                            <h4 class="text-primary mb-1">${invoices.length}</h4>
                            <small class="text-muted">إجمالي الفواتير</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light rounded p-3 text-center">
                            <h4 class="text-success mb-1">${invoices.filter(inv => inv.status === 'paid').length}</h4>
                            <small class="text-muted">الفواتير المدفوعة</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light rounded p-3 text-center">
                            <h4 class="text-warning mb-1">${invoices.filter(inv => inv.status === 'sent' || inv.status === 'overdue').length}</h4>
                            <small class="text-muted">الفواتير المعلقة</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light rounded p-3 text-center">
                            <h4 class="text-info mb-1">${this.formatAmount(invoices.reduce((sum, inv) => sum + (inv.total || 0), 0))}</h4>
                            <small class="text-muted">إجمالي القيمة</small>
                        </div>
                    </div>
                </div>

                <!-- شريط الأدوات -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-0">
                                    <i class="fas fa-file-invoice me-2"></i>إدارة الفواتير
                                </h5>
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="btn-group">
                                    <button class="btn btn-primary" onclick="window.SalesComponent.showNewInvoiceModal()">
                                        <i class="fas fa-plus me-1"></i>فاتورة جديدة
                                    </button>
                                    <div class="btn-group">
                                        <button class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                            <i class="fas fa-download me-1"></i>تصدير
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.exportInvoicesCSV()">
                                                <i class="fas fa-file-csv me-2"></i>تصدير CSV
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.exportInvoicesExcel()">
                                                <i class="fas fa-file-excel me-2"></i>تصدير Excel
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.exportInvoicesPDF()">
                                                <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.printAllInvoices()">
                                                <i class="fas fa-print me-2"></i>طباعة الكل
                                            </a></li>
                                        </ul>
                                    </div>
                                    <button class="btn btn-outline-warning" onclick="window.SalesComponent.showDeletedInvoices()" title="سلة المحذوفات">
                                        <i class="fas fa-trash-restore me-1"></i>المحذوفات
                                        ${Object.keys(this.data.deletedInvoices || {}).length > 0 ?
                                            `<span class="badge bg-danger ms-1">${Object.keys(this.data.deletedInvoices || {}).length}</span>` :
                                            ''
                                        }
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- فلاتر البحث المتقدمة -->
                <div class="card bg-light">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label small">البحث</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="رقم الفاتورة أو المرجع..."
                                           id="invoiceSearch" onkeyup="window.SalesComponent.filterInvoices()">
                                    <button class="btn btn-outline-secondary" type="button" onclick="window.SalesComponent.clearSearch()">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small">الحالة</label>
                                <select class="form-control" id="statusFilter" onchange="window.SalesComponent.filterInvoices()">
                                    <option value="">جميع الحالات</option>
                                    <option value="draft">مسودة</option>
                                    <option value="sent">مرسلة</option>
                                    <option value="paid">مدفوعة</option>
                                    <option value="overdue">متأخرة</option>
                                    <option value="cancelled">ملغية</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small">من تاريخ</label>
                                <input type="date" class="form-control" id="dateFromFilter" onchange="window.SalesComponent.filterInvoices()">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small">إلى تاريخ</label>
                                <input type="date" class="form-control" id="dateToFilter" onchange="window.SalesComponent.filterInvoices()">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small">العميل</label>
                                <select class="form-control" id="customerFilter" onchange="window.SalesComponent.filterInvoices()">
                                    <option value="">جميع العملاء</option>
                                    ${Object.values(this.data.customers || {}).map(customer => `
                                        <option value="${customer.id}">${customer.name}</option>
                                    `).join('')}
                                </select>
                            </div>
                            <div class="col-md-1">
                                <label class="form-label small">&nbsp;</label>
                                <button class="btn btn-outline-primary w-100" onclick="window.SalesComponent.clearAllFilters()" title="مسح الفلاتر">
                                    <i class="fas fa-eraser"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول الفواتير المتطور -->
                <div class="card mt-3">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAllInvoices" onchange="window.SalesComponent.toggleSelectAll()">
                                    <label class="form-check-label" for="selectAllInvoices">
                                        تحديد الكل
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 text-end">
                                <div id="bulkActionsContainer" style="display: none;">
                                    <span class="text-muted me-2">المحدد: <span id="selectedCount">0</span></span>
                                    <div class="btn-group">
                                        <button class="btn btn-outline-success btn-sm" onclick="window.SalesComponent.bulkMarkAsPaid()">
                                            <i class="fas fa-check me-1"></i>تحديد كمدفوع
                                        </button>
                                        <button class="btn btn-outline-warning btn-sm" onclick="window.SalesComponent.bulkMarkAsSent()">
                                            <i class="fas fa-paper-plane me-1"></i>تحديد كمرسل
                                        </button>
                                        <button class="btn btn-outline-info btn-sm" onclick="window.SalesComponent.bulkPrint()">
                                            <i class="fas fa-print me-1"></i>طباعة المحدد
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm" onclick="window.SalesComponent.bulkDelete()">
                                            <i class="fas fa-trash me-1"></i>حذف المحدد
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div id="invoicesTableContainer">
                            ${invoices.length === 0 ? this.renderEmptyInvoices() : this.renderInvoicesTableAdvanced(invoices)}
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض رسالة عدم وجود فواتير
     */
    renderEmptyInvoices: function() {
        return `
            <div class="text-center py-5">
                <i class="fas fa-file-invoice fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد فواتير</h4>
                <p class="text-muted">ابدأ بإنشاء فاتورة جديدة لعملائك</p>
                <button class="btn btn-primary" onclick="window.SalesComponent.showNewInvoiceModal()">
                    <i class="fas fa-plus me-1"></i>إنشاء فاتورة جديدة
                </button>
            </div>
        `;
    },

    /**
     * عرض جدول الفواتير المتطور
     */
    renderInvoicesTableAdvanced: function(invoices) {
        return `
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="3%">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAllInvoicesHeader">
                                </div>
                            </th>
                            <th width="15%">رقم الفاتورة</th>
                            <th width="20%">العميل</th>
                            <th width="15%">التاريخ</th>
                            <th width="15%">الإجمالي</th>
                            <th width="12%">الحالة</th>
                            <th width="20%">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${invoices.map(invoice => `
                            <tr class="invoice-row" data-invoice-id="${invoice.id}">
                                <td>
                                    <div class="form-check">
                                        <input class="form-check-input invoice-checkbox" type="checkbox"
                                               value="${invoice.id}" onchange="window.SalesComponent.updateBulkActions()">
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong class="text-primary">${invoice.number}</strong>
                                        ${invoice.reference ? `<br><small class="text-muted"><i class="fas fa-tag me-1"></i>${invoice.reference}</small>` : ''}
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong>${this.data.customers[invoice.customerId]?.name || 'عميل غير محدد'}</strong>
                                        ${this.data.customers[invoice.customerId]?.email ? `<br><small class="text-muted">${this.data.customers[invoice.customerId].email}</small>` : ''}
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong>${new Date(invoice.date).toLocaleDateString('ar-SA')}</strong>
                                        ${invoice.dueDate ? `<br><small class="text-warning">
                                            <i class="fas fa-clock me-1"></i>الاستحقاق: ${new Date(invoice.dueDate).toLocaleDateString('ar-SA')}
                                        </small>` : ''}
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong class="fs-6">${this.formatAmount(invoice.total)}</strong>
                                        <br><small class="text-muted">${invoice.items?.length || 0} عنصر</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-${this.getInvoiceStatusColor(invoice.status)}">
                                        ${this.getInvoiceStatusLabel(invoice.status)}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="window.SalesComponent.viewInvoice('${invoice.id}')" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="window.SalesComponent.editInvoice('${invoice.id}')" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <div class="btn-group">
                                            <button class="btn btn-outline-info dropdown-toggle" data-bs-toggle="dropdown" title="المزيد">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.printInvoice('${invoice.id}')">
                                                    <i class="fas fa-print me-2"></i>طباعة
                                                </a></li>
                                                <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.duplicateInvoice('${invoice.id}')">
                                                    <i class="fas fa-copy me-2"></i>نسخ
                                                </a></li>
                                                <li><a class="dropdown-item" href="#" onclick="window.SalesComponent.sendInvoiceEmail('${invoice.id}')">
                                                    <i class="fas fa-envelope me-2"></i>إرسال بالبريد
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#" onclick="window.SalesComponent.deleteInvoice('${invoice.id}')">
                                                    <i class="fas fa-trash me-2"></i>حذف
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    },

    /**
     * فلترة الفواتير المتقدمة
     */
    filterInvoices: function() {
        const searchTerm = document.getElementById('invoiceSearch')?.value.toLowerCase() || '';
        const statusFilter = document.getElementById('statusFilter')?.value || '';
        const dateFromFilter = document.getElementById('dateFromFilter')?.value || '';
        const dateToFilter = document.getElementById('dateToFilter')?.value || '';
        const customerFilter = document.getElementById('customerFilter')?.value || '';

        let filteredInvoices = Object.values(this.data.invoices || {});

        // فلترة بالبحث
        if (searchTerm) {
            filteredInvoices = filteredInvoices.filter(invoice =>
                invoice.number.toLowerCase().includes(searchTerm) ||
                invoice.reference?.toLowerCase().includes(searchTerm) ||
                this.data.customers[invoice.customerId]?.name.toLowerCase().includes(searchTerm)
            );
        }

        // فلترة بالحالة
        if (statusFilter) {
            filteredInvoices = filteredInvoices.filter(invoice => invoice.status === statusFilter);
        }

        // فلترة بالتاريخ من
        if (dateFromFilter) {
            filteredInvoices = filteredInvoices.filter(invoice => invoice.date >= dateFromFilter);
        }

        // فلترة بالتاريخ إلى
        if (dateToFilter) {
            filteredInvoices = filteredInvoices.filter(invoice => invoice.date <= dateToFilter);
        }

        // فلترة بالعميل
        if (customerFilter) {
            filteredInvoices = filteredInvoices.filter(invoice => invoice.customerId === customerFilter);
        }

        // ترتيب النتائج
        filteredInvoices.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        // تحديث الجدول
        this.updateInvoicesDisplay(filteredInvoices);
    },

    /**
     * تحديث عرض الفواتير
     */
    updateInvoicesDisplay: function(filteredInvoices) {
        const container = document.getElementById('invoicesTableContainer');
        if (container) {
            container.innerHTML = filteredInvoices.length === 0 ?
                '<div class="text-center py-4"><p class="text-muted">لا توجد فواتير تطابق معايير البحث</p></div>' :
                this.renderInvoicesTableAdvanced(filteredInvoices);
        }
    },

    /**
     * مسح جميع الفلاتر
     */
    clearAllFilters: function() {
        document.getElementById('invoiceSearch').value = '';
        document.getElementById('statusFilter').value = '';
        document.getElementById('dateFromFilter').value = '';
        document.getElementById('dateToFilter').value = '';
        document.getElementById('customerFilter').value = '';
        this.filterInvoices();
    },

    /**
     * مسح البحث
     */
    clearSearch: function() {
        document.getElementById('invoiceSearch').value = '';
        this.filterInvoices();
    },

    /**
     * تبديل تحديد الكل
     */
    toggleSelectAll: function() {
        const selectAllCheckbox = document.getElementById('selectAllInvoices');
        const invoiceCheckboxes = document.querySelectorAll('.invoice-checkbox');

        invoiceCheckboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
        });

        this.updateBulkActions();
    },

    /**
     * تحديث إجراءات التحديد المجمع
     */
    updateBulkActions: function() {
        const selectedCheckboxes = document.querySelectorAll('.invoice-checkbox:checked');
        const bulkActionsContainer = document.getElementById('bulkActionsContainer');
        const selectedCountSpan = document.getElementById('selectedCount');

        if (selectedCheckboxes.length > 0) {
            bulkActionsContainer.style.display = 'block';
            selectedCountSpan.textContent = selectedCheckboxes.length;
        } else {
            bulkActionsContainer.style.display = 'none';
        }

        // تحديث حالة تحديد الكل
        const selectAllCheckbox = document.getElementById('selectAllInvoices');
        const allCheckboxes = document.querySelectorAll('.invoice-checkbox');
        selectAllCheckbox.checked = selectedCheckboxes.length === allCheckboxes.length;
    },

    /**
     * عرض تفاصيل الفاتورة
     */
    viewInvoice: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            this.showMessage('الفاتورة غير موجودة', 'error');
            return;
        }

        const customer = this.data.customers[invoice.customerId];

        const modalHTML = `
            <div class="modal fade" id="viewInvoiceModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-file-invoice me-2"></i>فاتورة رقم ${invoice.number}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${this.renderInvoiceDetails(invoice, customer)}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-outline-primary" onclick="window.SalesComponent.editInvoice('${invoiceId}')">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="window.SalesComponent.printInvoice('${invoiceId}')">
                                <i class="fas fa-print me-1"></i>طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('viewInvoiceModal'));
        modal.show();

        document.getElementById('viewInvoiceModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * عرض تفاصيل الفاتورة
     */
    renderInvoiceDetails: function(invoice, customer) {
        return `
            <div class="invoice-details">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6>معلومات الفاتورة:</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>رقم الفاتورة:</strong></td>
                                <td>${invoice.number}</td>
                            </tr>
                            <tr>
                                <td><strong>التاريخ:</strong></td>
                                <td>${new Date(invoice.date).toLocaleDateString('ar-SA')}</td>
                            </tr>
                            ${invoice.dueDate ? `
                                <tr>
                                    <td><strong>تاريخ الاستحقاق:</strong></td>
                                    <td>${new Date(invoice.dueDate).toLocaleDateString('ar-SA')}</td>
                                </tr>
                            ` : ''}
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    <span class="badge bg-${this.getInvoiceStatusColor(invoice.status)}">
                                        ${this.getInvoiceStatusLabel(invoice.status)}
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>معلومات العميل:</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>الاسم:</strong></td>
                                <td>${customer?.name || 'عميل غير محدد'}</td>
                            </tr>
                            ${customer?.email ? `
                                <tr>
                                    <td><strong>البريد الإلكتروني:</strong></td>
                                    <td>${customer.email}</td>
                                </tr>
                            ` : ''}
                            ${customer?.phone ? `
                                <tr>
                                    <td><strong>الهاتف:</strong></td>
                                    <td>${customer.phone}</td>
                                </tr>
                            ` : ''}
                        </table>
                    </div>
                </div>

                <h6>عناصر الفاتورة:</h6>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th>المنتج/الخدمة</th>
                                <th>الكمية</th>
                                <th>السعر</th>
                                <th>الخصم</th>
                                <th>المجموع</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${invoice.items?.map(item => {
                                const product = this.data.products[item.productId];
                                return `
                                    <tr>
                                        <td>${product?.name || 'منتج غير محدد'}</td>
                                        <td>${item.quantity}</td>
                                        <td>${this.formatAmount(item.price)}</td>
                                        <td>${this.formatAmount(item.discount)}</td>
                                        <td>${this.formatAmount(item.total)}</td>
                                    </tr>
                                `;
                            }).join('') || '<tr><td colspan="5">لا توجد عناصر</td></tr>'}
                        </tbody>
                    </table>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        ${invoice.notes ? `
                            <h6>ملاحظات:</h6>
                            <p class="text-muted">${invoice.notes}</p>
                        ` : ''}
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>المجموع الفرعي:</strong></td>
                                <td class="text-end">${this.formatAmount(invoice.subtotal)}</td>
                            </tr>
                            <tr>
                                <td><strong>الخصم:</strong></td>
                                <td class="text-end">${this.formatAmount(invoice.totalDiscount)}</td>
                            </tr>
                            <tr>
                                <td><strong>الضريبة:</strong></td>
                                <td class="text-end">${this.formatAmount(invoice.taxAmount)}</td>
                            </tr>
                            <tr class="table-primary">
                                <td><strong>الإجمالي:</strong></td>
                                <td class="text-end"><strong>${this.formatAmount(invoice.total)}</strong></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * تعديل الفاتورة
     */
    editInvoice: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            this.showMessage('الفاتورة غير موجودة', 'error');
            return;
        }

        const modalHTML = `
            <div class="modal fade" id="editInvoiceModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-edit me-2"></i>تعديل الفاتورة ${invoice.number}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${this.renderEditInvoiceForm(invoice)}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveEditedInvoice()">
                                <i class="fas fa-save me-1"></i>حفظ التعديلات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('editInvoiceModal'));
        modal.show();

        // تهيئة النموذج
        setTimeout(() => {
            this.initializeEditInvoiceForm(invoice);
        }, 100);

        document.getElementById('editInvoiceModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * عرض نموذج تعديل الفاتورة
     */
    renderEditInvoiceForm: function(invoice) {
        const customers = Object.values(this.data.customers || {});
        const products = Object.values(this.data.products || {});

        return `
            <form id="editInvoiceForm">
                <input type="hidden" name="invoiceId" value="${invoice.id}">

                <div class="row">
                    <!-- معلومات الفاتورة -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">معلومات الفاتورة</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">رقم الفاتورة</label>
                                    <input type="text" class="form-control" name="invoiceNumber"
                                           value="${invoice.number}" readonly>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">تاريخ الفاتورة</label>
                                    <input type="date" class="form-control" name="invoiceDate"
                                           value="${invoice.date}" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">تاريخ الاستحقاق</label>
                                    <input type="date" class="form-control" name="dueDate"
                                           value="${invoice.dueDate || ''}">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">المرجع (اختياري)</label>
                                    <input type="text" class="form-control" name="reference"
                                           value="${invoice.reference || ''}" placeholder="رقم المرجع">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الحالة</label>
                                    <select class="form-control" name="status" required>
                                        <option value="draft" ${invoice.status === 'draft' ? 'selected' : ''}>مسودة</option>
                                        <option value="sent" ${invoice.status === 'sent' ? 'selected' : ''}>مرسلة</option>
                                        <option value="paid" ${invoice.status === 'paid' ? 'selected' : ''}>مدفوعة</option>
                                        <option value="overdue" ${invoice.status === 'overdue' ? 'selected' : ''}>متأخرة</option>
                                        <option value="cancelled" ${invoice.status === 'cancelled' ? 'selected' : ''}>ملغية</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات العميل -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">معلومات العميل</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">اختر العميل</label>
                                    <select class="form-control" name="customerId" onchange="window.SalesComponent.updateEditCustomerInfo()" required>
                                        <option value="">اختر العميل</option>
                                        ${customers.map(customer => `
                                            <option value="${customer.id}" ${customer.id === invoice.customerId ? 'selected' : ''}>
                                                ${customer.name}
                                            </option>
                                        `).join('')}
                                    </select>
                                </div>
                                <div id="editCustomerInfo" class="customer-info">
                                    <!-- سيتم عرض معلومات العميل هنا -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- عناصر الفاتورة -->
                <div class="card mt-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">عناصر الفاتورة</h6>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="window.SalesComponent.addEditInvoiceItem()">
                            <i class="fas fa-plus me-1"></i>إضافة عنصر
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>المنتج/الخدمة</th>
                                        <th width="120">الكمية</th>
                                        <th width="120">السعر</th>
                                        <th width="120">الخصم</th>
                                        <th width="120">المجموع</th>
                                        <th width="80">إجراء</th>
                                    </tr>
                                </thead>
                                <tbody id="editInvoiceItems">
                                    <!-- سيتم إضافة العناصر هنا -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- الإجماليات -->
                <div class="row mt-3">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">ملاحظات</h6>
                            </div>
                            <div class="card-body">
                                <textarea class="form-control" name="notes" rows="4" placeholder="ملاحظات إضافية...">${invoice.notes || ''}</textarea>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">الإجماليات</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>المجموع الفرعي:</span>
                                    <span id="editSubtotal">0.00 ر.س</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>إجمالي الخصم:</span>
                                    <span id="editTotalDiscount">0.00 ر.س</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>الضريبة (${(this.data.settings.taxRate * 100)}%):</span>
                                    <span id="editTaxAmount">0.00 ر.س</span>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <strong>الإجمالي النهائي:</strong>
                                    <strong id="editGrandTotal">0.00 ر.س</strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        `;
    },

    /**
     * حذف الفاتورة
     */
    deleteInvoice: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            this.showMessage('الفاتورة غير موجودة', 'error');
            return;
        }

        if (confirm(`هل أنت متأكد من حذف الفاتورة ${invoice.number}؟`)) {
            // نقل إلى سلة المحذوفات
            if (!this.data.deletedInvoices) {
                this.data.deletedInvoices = {};
            }

            this.data.deletedInvoices[invoiceId] = {
                ...invoice,
                deletedAt: new Date().toISOString(),
                deleteReason: 'حذف يدوي'
            };

            delete this.data.invoices[invoiceId];
            this.saveSalesData();
            this.showMessage(`تم نقل الفاتورة ${invoice.number} إلى سلة المحذوفات`, 'success');
            this.refreshData();
        }
    },

    /**
     * طباعة الفاتورة
     */
    printInvoice: function(invoiceId) {
        const invoice = this.data.invoices[invoiceId];
        if (!invoice) {
            this.showMessage('الفاتورة غير موجودة', 'error');
            return;
        }

        const customer = this.data.customers[invoice.customerId];
        if (!customer) {
            this.showMessage('بيانات العميل غير موجودة', 'error');
            return;
        }

        // إنشاء نافذة طباعة جديدة
        const printWindow = window.open('', '_blank');

        // استخدام التصميم المحدد في الإعدادات
        const printContent = this.generatePrintableInvoice(invoice, customer);

        printWindow.document.write(printContent);
        printWindow.document.close();

        // انتظار تحميل المحتوى ثم طباعة
        printWindow.onload = function() {
            printWindow.print();
            printWindow.close();
        };
    },

    /**
     * إنشاء فاتورة قابلة للطباعة مع التصميم المحدد
     */
    generatePrintableInvoice: function(invoice, customer) {
        const settings = this.data.settings;
        const template = settings.selectedTemplate || 'classic';
        const primaryColor = settings.primaryColor || '#007bff';
        const secondaryColor = settings.secondaryColor || '#6c757d';
        const fontSize = settings.fontSize || 'medium';
        const showLogo = settings.showLogo !== false;
        const showWatermark = settings.showWatermark === true;
        const showBorder = settings.showBorder !== false;

        // تحديد أحجام الخط للطباعة
        const fontSizes = {
            small: { base: '11px', title: '20px', header: '13px' },
            medium: { base: '12px', title: '24px', header: '14px' },
            large: { base: '14px', title: '28px', header: '16px' }
        };

        const currentFontSize = fontSizes[fontSize];

        return `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>فاتورة ${invoice.number}</title>
                <style>
                    body {
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        margin: 0;
                        padding: 20px;
                        background: white;
                        color: #333;
                        direction: rtl;
                        font-size: ${currentFontSize.base};
                        ${showWatermark ? `background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><text x="100" y="100" font-size="30" fill="${secondaryColor}" opacity="0.1" text-anchor="middle">${settings.companyName || 'قمة الوعد'}</text></svg>'); background-repeat: repeat; background-size: 200px 200px;` : ''}
                    }
                    .invoice-container {
                        max-width: 800px;
                        margin: 0 auto;
                        background: white;
                        ${showBorder ? `border: 2px solid ${primaryColor};` : 'border: 1px solid #ddd;'}
                        border-radius: 8px;
                        overflow: hidden;
                        position: relative;
                    }
                    .invoice-header {
                        ${template === 'colorful' ? `background: linear-gradient(135deg, ${primaryColor}, ${secondaryColor});` :
                          template === 'minimal' ? `background: transparent; border-bottom: 3px solid ${primaryColor};` :
                          `background: ${primaryColor};`}
                        color: ${template === 'minimal' ? primaryColor : 'white'};
                        padding: 30px;
                        text-align: center;
                        ${template === 'modern' ? 'border-radius: 0 0 20px 20px;' : ''}
                    }
                    .invoice-title {
                        font-size: ${currentFontSize.title};
                        font-weight: bold;
                        margin: 0 0 10px 0;
                    }
                    .invoice-number {
                        font-size: ${currentFontSize.header};
                        opacity: 0.9;
                    }
                    .invoice-body {
                        padding: 30px;
                    }
                    .company-info, .customer-info {
                        ${template === 'modern' ? 'background: #f8f9fa; border-radius: 12px;' :
                          template === 'professional' ? 'background: #f8f9fa; border-left: 4px solid ' + primaryColor + ';' :
                          template === 'minimal' ? 'background: transparent; border: 1px solid ' + primaryColor + ';' :
                          'background: #f8f9fa;'}
                        padding: 20px;
                        margin-bottom: 20px;
                    }
                    .info-title {
                        font-size: ${currentFontSize.header};
                        font-weight: bold;
                        color: ${primaryColor};
                        margin-bottom: 10px;
                        ${template === 'minimal' ? 'border-bottom: 1px solid ' + primaryColor + ';' : 'border-bottom: 2px solid ' + primaryColor + ';'}
                        padding-bottom: 5px;
                    }
                    .logo-placeholder {
                        position: absolute;
                        top: 20px;
                        left: 20px;
                        width: 80px;
                        height: 80px;
                        background: ${template === 'minimal' ? 'transparent' : secondaryColor + '30'};
                        border: ${template === 'minimal' ? '2px solid ' + primaryColor : 'none'};
                        border-radius: 8px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: ${primaryColor};
                        font-size: 10px;
                        text-align: center;
                        font-weight: bold;
                    }
                    .invoice-table {
                        width: 100%;
                        border-collapse: collapse;
                        margin: 20px 0;
                        ${template === 'modern' ? 'border-radius: 8px; overflow: hidden;' : ''}
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    }
                    .invoice-table th {
                        ${template === 'colorful' ? `background: linear-gradient(135deg, ${primaryColor}, ${secondaryColor});` :
                          template === 'minimal' ? `background: transparent; border-bottom: 2px solid ${primaryColor}; color: ${primaryColor};` :
                          `background: ${primaryColor};`}
                        color: ${template === 'minimal' ? primaryColor : 'white'};
                        padding: 15px;
                        text-align: right;
                        font-weight: bold;
                        font-size: ${currentFontSize.header};
                    }
                    .invoice-table td {
                        padding: 12px 15px;
                        border-bottom: 1px solid #dee2e6;
                        text-align: right;
                    }
                    .invoice-table tr:nth-child(even) {
                        background: ${template === 'colorful' ? primaryColor + '10' : '#f8f9fa'};
                    }
                    .invoice-total {
                        ${template === 'colorful' ? `background: linear-gradient(135deg, ${primaryColor}20, ${secondaryColor}20);` :
                          template === 'minimal' ? `background: transparent; border: 2px solid ${primaryColor};` :
                          template === 'professional' ? `background: #f8f9fa; border-left: 4px solid ${primaryColor};` :
                          'background: linear-gradient(135deg, #f8f9fa, #e9ecef);'}
                        padding: 20px;
                        border-radius: 8px;
                        ${template !== 'minimal' && template !== 'professional' ? `border: 2px solid ${primaryColor};` : ''}
                    }
                    .total-row {
                        display: flex;
                        justify-content: space-between;
                        margin: 8px 0;
                        font-size: ${currentFontSize.header};
                    }
                    .total-final {
                        font-weight: bold;
                        font-size: ${currentFontSize.title};
                        color: ${primaryColor};
                        border-top: 2px solid ${primaryColor};
                        padding-top: 10px;
                        margin-top: 10px;
                    }
                    .invoice-footer {
                        text-align: center;
                        margin-top: 30px;
                        padding: 20px;
                        ${template === 'colorful' ? `background: linear-gradient(135deg, ${primaryColor}10, ${secondaryColor}10);` : 'background: #f8f9fa;'}
                        border-radius: 8px;
                        color: #6c757d;
                    }
                    @media print {
                        body { margin: 0; padding: 10px; }
                        .invoice-container { border: none; box-shadow: none; }
                        .logo-placeholder { display: ${showLogo ? 'flex' : 'none'}; }
                        .no-print { display: none; }
                    }
                </style>
            </head>
            <body>
                <div class="invoice-container">
                    ${showLogo ? '<div class="logo-placeholder">شعار<br>الشركة</div>' : ''}

                    <div class="invoice-header">
                        <h1 class="invoice-title">فاتورة</h1>
                        <p class="invoice-number">رقم الفاتورة: ${invoice.number}</p>
                    </div>

                    <div class="invoice-body">
                        <div style="display: flex; gap: 20px; margin-bottom: 20px;">
                            <div style="flex: 1;">
                                <div class="company-info">
                                    <div class="info-title">معلومات الشركة</div>
                                    <p><strong>${settings.companyName || 'قمة الوعد للسفريات'}</strong></p>
                                    <p>${settings.companyAddress || 'المملكة العربية السعودية'}</p>
                                    <p>هاتف: ${settings.companyPhone || '+966501234567'}</p>
                                    <p>بريد إلكتروني: ${settings.companyEmail || '<EMAIL>'}</p>
                                    ${settings.companyTaxNumber ? `<p>الرقم الضريبي: ${settings.companyTaxNumber}</p>` : ''}
                                </div>
                            </div>
                            <div style="flex: 1;">
                                <div class="customer-info">
                                    <div class="info-title">بيانات العميل</div>
                                    <p><strong>${customer.name}</strong></p>
                                    ${customer.email ? `<p>${customer.email}</p>` : ''}
                                    ${customer.phone ? `<p>${customer.phone}</p>` : ''}
                                    ${customer.address ? `<p>${customer.address}</p>` : ''}
                                    ${customer.taxNumber ? `<p>الرقم الضريبي: ${customer.taxNumber}</p>` : ''}
                                </div>
                            </div>
                        </div>

                        <div style="display: flex; gap: 20px; margin-bottom: 20px;">
                            <div style="flex: 1;">
                                <p><strong>تاريخ الفاتورة:</strong> ${new Date(invoice.date).toLocaleDateString('ar-SA')}</p>
                            </div>
                            <div style="flex: 1;">
                                <p><strong>تاريخ الاستحقاق:</strong> ${new Date(invoice.dueDate).toLocaleDateString('ar-SA')}</p>
                            </div>
                        </div>

                        <table class="invoice-table">
                            <thead>
                                <tr>
                                    <th>البند</th>
                                    <th>الكمية</th>
                                    <th>السعر</th>
                                    <th>الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${invoice.items.map(item => `
                                    <tr>
                                        <td>${item.name}</td>
                                        <td>${item.quantity}</td>
                                        <td>${this.formatAmount(item.price)}</td>
                                        <td>${this.formatAmount(item.total)}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>

                        <div style="display: flex; gap: 20px;">
                            <div style="flex: 1;">
                                ${invoice.notes ? `
                                    <div>
                                        <div class="info-title">ملاحظات</div>
                                        <p>${invoice.notes}</p>
                                    </div>
                                ` : ''}
                            </div>
                            <div style="flex: 1;">
                                <div class="invoice-total">
                                    <div class="total-row">
                                        <span>المجموع الفرعي:</span>
                                        <span>${this.formatAmount(invoice.subtotal)}</span>
                                    </div>
                                    <div class="total-row">
                                        <span>ضريبة القيمة المضافة (${(settings.taxRate * 100) || 15}%):</span>
                                        <span>${this.formatAmount(invoice.tax)}</span>
                                    </div>
                                    <div class="total-row total-final">
                                        <span>الإجمالي النهائي:</span>
                                        <span>${this.formatAmount(invoice.total)}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="invoice-footer">
                            <p>شكراً لتعاملكم معنا</p>
                            <p>تم إنشاء هذه الفاتورة بواسطة نظام إدارة المبيعات - ${this.getTemplateLabel(template)}</p>
                        </div>
                    </div>
                </div>

                <div class="no-print" style="text-align: center; margin-top: 20px;">
                    <button onclick="window.print()" style="background: ${primaryColor}; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                        طباعة الفاتورة
                    </button>
                </div>
            </body>
            </html>
        `;
    },

    /**
     * عرض نافذة فاتورة جديدة
     */
    showNewInvoiceModal: function() {
        const modalHTML = `
            <div class="modal fade" id="newInvoiceModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-plus me-2"></i>إنشاء فاتورة جديدة
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${this.renderInvoiceForm()}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-outline-primary" onclick="window.SalesComponent.saveInvoiceAsDraft()">
                                <i class="fas fa-save me-1"></i>حفظ كمسودة
                            </button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveAndSendInvoice()">
                                <i class="fas fa-paper-plane me-1"></i>حفظ وإرسال
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('newInvoiceModal'));
        modal.show();

        // تهيئة النموذج
        setTimeout(() => {
            this.initializeInvoiceForm();
        }, 100);

        document.getElementById('newInvoiceModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * عرض نموذج الفاتورة
     */
    renderInvoiceForm: function() {
        const customers = Object.values(this.data.customers || {});
        const products = Object.values(this.data.products || {});

        return `
            <form id="invoiceForm">
                <div class="row">
                    <!-- معلومات الفاتورة -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">معلومات الفاتورة</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">رقم الفاتورة</label>
                                    <input type="text" class="form-control" name="invoiceNumber"
                                           value="${this.data.settings.invoicePrefix}${this.data.settings.nextInvoiceNumber.toString().padStart(4, '0')}" readonly>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">تاريخ الفاتورة</label>
                                    <input type="date" class="form-control" name="invoiceDate"
                                           value="${new Date().toISOString().split('T')[0]}" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">تاريخ الاستحقاق</label>
                                    <input type="date" class="form-control" name="dueDate">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">المرجع (اختياري)</label>
                                    <input type="text" class="form-control" name="reference" placeholder="رقم المرجع">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات العميل -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">معلومات العميل</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">اختر العميل</label>
                                    <select class="form-control" name="customerId" onchange="window.SalesComponent.updateCustomerInfo()" required>
                                        <option value="">اختر العميل</option>
                                        ${customers.map(customer => `
                                            <option value="${customer.id}">${customer.name}</option>
                                        `).join('')}
                                    </select>
                                </div>
                                <div id="customerInfo" class="customer-info" style="display: none;">
                                    <!-- سيتم عرض معلومات العميل هنا -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- عناصر الفاتورة -->
                <div class="card mt-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">عناصر الفاتورة</h6>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="window.SalesComponent.addInvoiceItem()">
                            <i class="fas fa-plus me-1"></i>إضافة عنصر
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>المنتج/الخدمة</th>
                                        <th width="120">الكمية</th>
                                        <th width="120">السعر</th>
                                        <th width="120">الخصم</th>
                                        <th width="120">المجموع</th>
                                        <th width="80">إجراء</th>
                                    </tr>
                                </thead>
                                <tbody id="invoiceItems">
                                    <!-- سيتم إضافة العناصر هنا -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- الإجماليات -->
                <div class="row mt-3">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">ملاحظات</h6>
                            </div>
                            <div class="card-body">
                                <textarea class="form-control" name="notes" rows="4" placeholder="ملاحظات إضافية..."></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">الإجماليات</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>المجموع الفرعي:</span>
                                    <span id="subtotal">0.00 ر.س</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>إجمالي الخصم:</span>
                                    <span id="totalDiscount">0.00 ر.س</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>الضريبة (${(this.data.settings.taxRate * 100)}%):</span>
                                    <span id="taxAmount">0.00 ر.س</span>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <strong>الإجمالي النهائي:</strong>
                                    <strong id="grandTotal">0.00 ر.س</strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        `;
    },

    /**
     * تهيئة نموذج الفاتورة
     */
    initializeInvoiceForm: function() {
        // إضافة عنصر افتراضي
        this.addInvoiceItem();

        // تحديث معلومات العميل
        setTimeout(() => {
            this.updateCustomerInfo();
            this.calculateInvoiceTotal();
        }, 100);
    },

    /**
     * إضافة عنصر للفاتورة
     */
    addInvoiceItem: function() {
        const tbody = document.getElementById('invoiceItems');
        const products = Object.values(this.data.products || {});
        const itemIndex = tbody.children.length;

        const itemHTML = `
            <tr data-item-index="${itemIndex}">
                <td>
                    <select class="form-control" name="items[${itemIndex}][productId]" onchange="window.SalesComponent.updateItemPrice(${itemIndex})" required>
                        <option value="">اختر المنتج/الخدمة</option>
                        ${products.map(product => `
                            <option value="${product.id}" data-price="${product.price}">
                                ${product.name}
                            </option>
                        `).join('')}
                    </select>
                </td>
                <td>
                    <input type="number" class="form-control" name="items[${itemIndex}][quantity]"
                           value="1" min="1" step="0.01"
                           onchange="window.SalesComponent.calculateItemTotal(${itemIndex})" required>
                </td>
                <td>
                    <input type="number" class="form-control" name="items[${itemIndex}][price]"
                           value="0" step="0.01" min="0"
                           onchange="window.SalesComponent.calculateItemTotal(${itemIndex})" required>
                </td>
                <td>
                    <input type="number" class="form-control" name="items[${itemIndex}][discount]"
                           value="0" step="0.01" min="0"
                           onchange="window.SalesComponent.calculateItemTotal(${itemIndex})">
                </td>
                <td>
                    <input type="number" class="form-control" name="items[${itemIndex}][total]"
                           value="0" readonly>
                </td>
                <td>
                    <button type="button" class="btn btn-danger btn-sm" onclick="window.SalesComponent.removeInvoiceItem(${itemIndex})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;

        tbody.insertAdjacentHTML('beforeend', itemHTML);
    },

    /**
     * تحديث سعر العنصر عند اختيار المنتج
     */
    updateItemPrice: function(itemIndex) {
        const select = document.querySelector(`select[name="items[${itemIndex}][productId]"]`);
        const priceInput = document.querySelector(`input[name="items[${itemIndex}][price]"]`);

        if (select.selectedOptions.length > 0) {
            const price = select.selectedOptions[0].getAttribute('data-price');
            priceInput.value = price || 0;
            this.calculateItemTotal(itemIndex);
        }
    },

    /**
     * حساب إجمالي العنصر
     */
    calculateItemTotal: function(itemIndex) {
        const quantity = parseFloat(document.querySelector(`input[name="items[${itemIndex}][quantity]"]`).value) || 0;
        const price = parseFloat(document.querySelector(`input[name="items[${itemIndex}][price]"]`).value) || 0;
        const discount = parseFloat(document.querySelector(`input[name="items[${itemIndex}][discount]"]`).value) || 0;

        const total = (quantity * price) - discount;
        document.querySelector(`input[name="items[${itemIndex}][total]"]`).value = total.toFixed(2);

        this.calculateInvoiceTotal();
    },

    /**
     * حساب إجمالي الفاتورة
     */
    calculateInvoiceTotal: function() {
        const itemTotals = document.querySelectorAll('input[name*="[total]"]');
        let subtotal = 0;
        let totalDiscount = 0;

        itemTotals.forEach(input => {
            subtotal += parseFloat(input.value) || 0;
        });

        // حساب إجمالي الخصومات
        const discountInputs = document.querySelectorAll('input[name*="[discount]"]');
        discountInputs.forEach(input => {
            totalDiscount += parseFloat(input.value) || 0;
        });

        const taxAmount = subtotal * this.data.settings.taxRate;
        const grandTotal = subtotal + taxAmount;

        // تحديث العرض
        document.getElementById('subtotal').textContent = this.formatAmount(subtotal);
        document.getElementById('totalDiscount').textContent = this.formatAmount(totalDiscount);
        document.getElementById('taxAmount').textContent = this.formatAmount(taxAmount);
        document.getElementById('grandTotal').textContent = this.formatAmount(grandTotal);
    },

    /**
     * حذف عنصر من الفاتورة
     */
    removeInvoiceItem: function(itemIndex) {
        const row = document.querySelector(`tr[data-item-index="${itemIndex}"]`);
        if (row) {
            row.remove();
            this.calculateInvoiceTotal();
        }
    },

    /**
     * تحديث معلومات العميل
     */
    updateCustomerInfo: function() {
        const select = document.querySelector('select[name="customerId"]');
        const customerInfo = document.getElementById('customerInfo');

        if (select.value) {
            const customer = this.data.customers[select.value];
            if (customer) {
                customerInfo.style.display = 'block';
                customerInfo.innerHTML = `
                    <div class="alert alert-info">
                        <h6>${customer.name}</h6>
                        ${customer.email ? `<p class="mb-1"><i class="fas fa-envelope me-1"></i>${customer.email}</p>` : ''}
                        ${customer.phone ? `<p class="mb-1"><i class="fas fa-phone me-1"></i>${customer.phone}</p>` : ''}
                        ${customer.address ? `<p class="mb-0"><i class="fas fa-map-marker-alt me-1"></i>${customer.address}</p>` : ''}
                    </div>
                `;
            }
        } else {
            customerInfo.style.display = 'none';
        }
    },

    /**
     * حفظ الفاتورة كمسودة
     */
    saveInvoiceAsDraft: function() {
        this.saveInvoice('draft');
    },

    /**
     * حفظ وإرسال الفاتورة
     */
    saveAndSendInvoice: function() {
        this.saveInvoice('sent');
    },

    /**
     * حفظ الفاتورة
     */
    saveInvoice: function(status = 'draft') {
        const form = document.getElementById('invoiceForm');
        const formData = new FormData(form);

        try {
            // جمع بيانات الفاتورة
            const invoice = {
                id: this.generateId(),
                number: formData.get('invoiceNumber'),
                date: formData.get('invoiceDate'),
                dueDate: formData.get('dueDate'),
                customerId: formData.get('customerId'),
                reference: formData.get('reference'),
                notes: formData.get('notes'),
                status: status,
                items: [],
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // التحقق من صحة البيانات
            if (!invoice.customerId) {
                throw new Error('يرجى اختيار العميل');
            }

            // جمع عناصر الفاتورة
            const itemRows = document.querySelectorAll('#invoiceItems tr');
            itemRows.forEach((row, index) => {
                const productId = row.querySelector(`select[name="items[${index}][productId]"]`)?.value;
                const quantity = parseFloat(row.querySelector(`input[name="items[${index}][quantity]"]`)?.value) || 0;
                const price = parseFloat(row.querySelector(`input[name="items[${index}][price]"]`)?.value) || 0;
                const discount = parseFloat(row.querySelector(`input[name="items[${index}][discount]"]`)?.value) || 0;
                const total = parseFloat(row.querySelector(`input[name="items[${index}][total]"]`)?.value) || 0;

                if (productId && quantity > 0 && price > 0) {
                    invoice.items.push({
                        productId,
                        quantity,
                        price,
                        discount,
                        total
                    });
                }
            });

            if (invoice.items.length === 0) {
                throw new Error('يرجى إضافة عنصر واحد على الأقل للفاتورة');
            }

            // حساب الإجماليات
            invoice.subtotal = invoice.items.reduce((sum, item) => sum + (item.quantity * item.price), 0);
            invoice.totalDiscount = invoice.items.reduce((sum, item) => sum + item.discount, 0);
            invoice.taxAmount = (invoice.subtotal - invoice.totalDiscount) * this.data.settings.taxRate;
            invoice.total = invoice.subtotal - invoice.totalDiscount + invoice.taxAmount;

            // حفظ الفاتورة
            this.data.invoices[invoice.id] = invoice;

            // تحديث رقم الفاتورة التالي
            this.data.settings.nextInvoiceNumber++;

            this.saveSalesData();

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('newInvoiceModal'));
            modal.hide();

            // عرض رسالة نجاح
            const statusLabel = status === 'draft' ? 'كمسودة' : 'وتم إرسالها';
            this.showMessage(`تم حفظ الفاتورة ${invoice.number} ${statusLabel} بنجاح`, 'success');

            // تحديث العرض
            this.refreshData();

        } catch (error) {
            this.showMessage('خطأ في حفظ الفاتورة: ' + error.message, 'error');
        }
    },

    /**
     * تهيئة نموذج تعديل الفاتورة
     */
    initializeEditInvoiceForm: function(invoice) {
        // إضافة عناصر الفاتورة الموجودة
        const tbody = document.getElementById('editInvoiceItems');
        tbody.innerHTML = '';

        if (invoice.items && invoice.items.length > 0) {
            invoice.items.forEach((item, index) => {
                this.addEditInvoiceItem(item, index);
            });
        } else {
            this.addEditInvoiceItem();
        }

        // تحديث معلومات العميل
        setTimeout(() => {
            this.updateEditCustomerInfo();
            this.calculateEditInvoiceTotal();
        }, 100);
    },

    /**
     * إضافة عنصر لتعديل الفاتورة
     */
    addEditInvoiceItem: function(existingItem = null, itemIndex = null) {
        const tbody = document.getElementById('editInvoiceItems');
        const products = Object.values(this.data.products || {});
        const index = itemIndex !== null ? itemIndex : tbody.children.length;

        const itemHTML = `
            <tr data-item-index="${index}">
                <td>
                    <select class="form-control" name="items[${index}][productId]" onchange="window.SalesComponent.updateEditItemPrice(${index})" required>
                        <option value="">اختر المنتج/الخدمة</option>
                        ${products.map(product => `
                            <option value="${product.id}" data-price="${product.price}"
                                    ${existingItem && existingItem.productId === product.id ? 'selected' : ''}>
                                ${product.name}
                            </option>
                        `).join('')}
                    </select>
                </td>
                <td>
                    <input type="number" class="form-control" name="items[${index}][quantity]"
                           value="${existingItem ? existingItem.quantity : 1}" min="1" step="0.01"
                           onchange="window.SalesComponent.calculateEditItemTotal(${index})" required>
                </td>
                <td>
                    <input type="number" class="form-control" name="items[${index}][price]"
                           value="${existingItem ? existingItem.price : 0}" step="0.01" min="0"
                           onchange="window.SalesComponent.calculateEditItemTotal(${index})" required>
                </td>
                <td>
                    <input type="number" class="form-control" name="items[${index}][discount]"
                           value="${existingItem ? existingItem.discount : 0}" step="0.01" min="0"
                           onchange="window.SalesComponent.calculateEditItemTotal(${index})">
                </td>
                <td>
                    <input type="number" class="form-control" name="items[${index}][total]"
                           value="${existingItem ? existingItem.total : 0}" readonly>
                </td>
                <td>
                    <button type="button" class="btn btn-danger btn-sm" onclick="window.SalesComponent.removeEditInvoiceItem(${index})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;

        tbody.insertAdjacentHTML('beforeend', itemHTML);

        // حساب الإجمالي إذا كان عنصر موجود
        if (existingItem) {
            this.calculateEditItemTotal(index);
        }
    },

    /**
     * تحديث سعر العنصر في التعديل
     */
    updateEditItemPrice: function(itemIndex) {
        const select = document.querySelector(`select[name="items[${itemIndex}][productId]"]`);
        const priceInput = document.querySelector(`input[name="items[${itemIndex}][price]"]`);

        if (select.selectedOptions.length > 0) {
            const price = select.selectedOptions[0].getAttribute('data-price');
            priceInput.value = price || 0;
            this.calculateEditItemTotal(itemIndex);
        }
    },

    /**
     * حساب إجمالي العنصر في التعديل
     */
    calculateEditItemTotal: function(itemIndex) {
        const quantity = parseFloat(document.querySelector(`input[name="items[${itemIndex}][quantity]"]`).value) || 0;
        const price = parseFloat(document.querySelector(`input[name="items[${itemIndex}][price]"]`).value) || 0;
        const discount = parseFloat(document.querySelector(`input[name="items[${itemIndex}][discount]"]`).value) || 0;

        const total = (quantity * price) - discount;
        document.querySelector(`input[name="items[${itemIndex}][total]"]`).value = total.toFixed(2);

        this.calculateEditInvoiceTotal();
    },

    /**
     * حساب إجمالي الفاتورة في التعديل
     */
    calculateEditInvoiceTotal: function() {
        const itemTotals = document.querySelectorAll('#editInvoiceItems input[name*="[total]"]');
        let subtotal = 0;
        let totalDiscount = 0;

        itemTotals.forEach(input => {
            subtotal += parseFloat(input.value) || 0;
        });

        // حساب إجمالي الخصومات
        const discountInputs = document.querySelectorAll('#editInvoiceItems input[name*="[discount]"]');
        discountInputs.forEach(input => {
            totalDiscount += parseFloat(input.value) || 0;
        });

        const taxAmount = subtotal * this.data.settings.taxRate;
        const grandTotal = subtotal + taxAmount;

        // تحديث العرض
        document.getElementById('editSubtotal').textContent = this.formatAmount(subtotal);
        document.getElementById('editTotalDiscount').textContent = this.formatAmount(totalDiscount);
        document.getElementById('editTaxAmount').textContent = this.formatAmount(taxAmount);
        document.getElementById('editGrandTotal').textContent = this.formatAmount(grandTotal);
    },

    /**
     * حذف عنصر من تعديل الفاتورة
     */
    removeEditInvoiceItem: function(itemIndex) {
        const row = document.querySelector(`#editInvoiceItems tr[data-item-index="${itemIndex}"]`);
        if (row) {
            row.remove();
            this.calculateEditInvoiceTotal();
        }
    },

    /**
     * تحديث معلومات العميل في التعديل
     */
    updateEditCustomerInfo: function() {
        const select = document.querySelector('select[name="customerId"]');
        const customerInfo = document.getElementById('editCustomerInfo');

        if (select.value) {
            const customer = this.data.customers[select.value];
            if (customer) {
                customerInfo.innerHTML = `
                    <div class="alert alert-info">
                        <h6>${customer.name}</h6>
                        ${customer.email ? `<p class="mb-1"><i class="fas fa-envelope me-1"></i>${customer.email}</p>` : ''}
                        ${customer.phone ? `<p class="mb-1"><i class="fas fa-phone me-1"></i>${customer.phone}</p>` : ''}
                        ${customer.address ? `<p class="mb-0"><i class="fas fa-map-marker-alt me-1"></i>${customer.address}</p>` : ''}
                    </div>
                `;
            }
        } else {
            customerInfo.innerHTML = '';
        }
    },

    /**
     * حفظ تعديلات الفاتورة
     */
    saveEditedInvoice: function() {
        const form = document.getElementById('editInvoiceForm');
        const formData = new FormData(form);

        try {
            const invoiceId = formData.get('invoiceId');
            const originalInvoice = this.data.invoices[invoiceId];

            if (!originalInvoice) {
                throw new Error('الفاتورة غير موجودة');
            }

            // جمع بيانات الفاتورة المحدثة
            const updatedInvoice = {
                ...originalInvoice,
                date: formData.get('invoiceDate'),
                dueDate: formData.get('dueDate'),
                customerId: formData.get('customerId'),
                reference: formData.get('reference'),
                notes: formData.get('notes'),
                status: formData.get('status'),
                items: [],
                updatedAt: new Date().toISOString()
            };

            // التحقق من صحة البيانات
            if (!updatedInvoice.customerId) {
                throw new Error('يرجى اختيار العميل');
            }

            // جمع عناصر الفاتورة
            const itemRows = document.querySelectorAll('#editInvoiceItems tr');
            itemRows.forEach((row, index) => {
                const productId = row.querySelector(`select[name="items[${index}][productId]"]`)?.value;
                const quantity = parseFloat(row.querySelector(`input[name="items[${index}][quantity]"]`)?.value) || 0;
                const price = parseFloat(row.querySelector(`input[name="items[${index}][price]"]`)?.value) || 0;
                const discount = parseFloat(row.querySelector(`input[name="items[${index}][discount]"]`)?.value) || 0;
                const total = parseFloat(row.querySelector(`input[name="items[${index}][total]"]`)?.value) || 0;

                if (productId && quantity > 0 && price > 0) {
                    updatedInvoice.items.push({
                        productId,
                        quantity,
                        price,
                        discount,
                        total
                    });
                }
            });

            if (updatedInvoice.items.length === 0) {
                throw new Error('يرجى إضافة عنصر واحد على الأقل للفاتورة');
            }

            // حساب الإجماليات
            updatedInvoice.subtotal = updatedInvoice.items.reduce((sum, item) => sum + (item.quantity * item.price), 0);
            updatedInvoice.totalDiscount = updatedInvoice.items.reduce((sum, item) => sum + item.discount, 0);
            updatedInvoice.taxAmount = (updatedInvoice.subtotal - updatedInvoice.totalDiscount) * this.data.settings.taxRate;
            updatedInvoice.total = updatedInvoice.subtotal - updatedInvoice.totalDiscount + updatedInvoice.taxAmount;

            // حفظ الفاتورة المحدثة
            this.data.invoices[invoiceId] = updatedInvoice;
            this.saveSalesData();

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('editInvoiceModal'));
            modal.hide();

            // عرض رسالة نجاح
            this.showMessage('تم تحديث الفاتورة بنجاح', 'success');

            // تحديث العرض
            this.refreshData();

        } catch (error) {
            this.showMessage('خطأ في تحديث الفاتورة: ' + error.message, 'error');
        }
    },

    /**
     * الحصول على لون حالة العميل
     */
    getCustomerStatusColor: function(customer) {
        const invoices = Object.values(this.data.invoices || {});
        const customerInvoices = invoices.filter(inv => inv.customerId === customer.id);

        if (customerInvoices.length === 0) return 'secondary';

        const recentInvoices = customerInvoices.filter(inv => {
            const invoiceDate = new Date(inv.createdAt);
            const threeMonthsAgo = new Date();
            threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
            return invoiceDate > threeMonthsAgo;
        });

        return recentInvoices.length > 0 ? 'success' : 'warning';
    },

    /**
     * الحصول على تسمية حالة العميل
     */
    getCustomerStatusLabel: function(customer) {
        const invoices = Object.values(this.data.invoices || {});
        const customerInvoices = invoices.filter(inv => inv.customerId === customer.id);

        if (customerInvoices.length === 0) return 'جديد';

        const recentInvoices = customerInvoices.filter(inv => {
            const invoiceDate = new Date(inv.createdAt);
            const threeMonthsAgo = new Date();
            threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
            return invoiceDate > threeMonthsAgo;
        });

        return recentInvoices.length > 0 ? 'نشط' : 'غير نشط';
    },

    /**
     * الحصول على عدد فواتير العميل
     */
    getCustomerInvoicesCount: function(customerId) {
        const invoices = Object.values(this.data.invoices || {});
        return invoices.filter(inv => inv.customerId === customerId).length;
    },

    /**
     * الحصول على إجمالي مبيعات العميل
     */
    getCustomerTotalSales: function(customerId) {
        const invoices = Object.values(this.data.invoices || {});
        return invoices
            .filter(inv => inv.customerId === customerId)
            .reduce((total, inv) => total + (inv.total || 0), 0);
    },

    /**
     * فلترة العملاء
     */
    filterCustomers: function() {
        const searchTerm = document.getElementById('customerSearch')?.value.toLowerCase() || '';
        const typeFilter = document.getElementById('customerTypeFilter')?.value || '';
        const statusFilter = document.getElementById('customerStatusFilter')?.value || '';

        let filteredCustomers = Object.values(this.data.customers || {});

        // فلترة بالبحث
        if (searchTerm) {
            filteredCustomers = filteredCustomers.filter(customer =>
                customer.name.toLowerCase().includes(searchTerm) ||
                customer.email?.toLowerCase().includes(searchTerm) ||
                customer.phone?.includes(searchTerm)
            );
        }

        // فلترة بالنوع
        if (typeFilter) {
            filteredCustomers = filteredCustomers.filter(customer => customer.type === typeFilter);
        }

        // فلترة بالحالة
        if (statusFilter) {
            filteredCustomers = filteredCustomers.filter(customer => {
                const status = this.getCustomerStatusLabel(customer);
                return (statusFilter === 'active' && status === 'نشط') ||
                       (statusFilter === 'inactive' && status !== 'نشط');
            });
        }

        // ترتيب النتائج
        filteredCustomers.sort((a, b) => a.name.localeCompare(b.name));

        // تحديث العرض
        this.updateCustomersDisplay(filteredCustomers);
    },

    /**
     * تحديث عرض العملاء
     */
    updateCustomersDisplay: function(filteredCustomers) {
        const container = document.getElementById('customersContainer');
        if (container) {
            container.innerHTML = filteredCustomers.length === 0 ?
                '<div class="text-center py-4"><p class="text-muted">لا يوجد عملاء تطابق معايير البحث</p></div>' :
                this.renderCustomersGrid(filteredCustomers);
        }
    },

    /**
     * مسح بحث العملاء
     */
    clearCustomerSearch: function() {
        document.getElementById('customerSearch').value = '';
        this.filterCustomers();
    },

    /**
     * مسح فلاتر العملاء
     */
    clearCustomerFilters: function() {
        document.getElementById('customerSearch').value = '';
        document.getElementById('customerTypeFilter').value = '';
        document.getElementById('customerStatusFilter').value = '';
        this.filterCustomers();
    },

    /**
     * عرض نافذة عميل جديد
     */
    showNewCustomerModal: function() {
        const modalHTML = `
            <div class="modal fade" id="newCustomerModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-user-plus me-2"></i>إضافة عميل جديد
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${this.renderCustomerForm()}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveNewCustomer()">
                                <i class="fas fa-save me-1"></i>حفظ العميل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('newCustomerModal'));
        modal.show();

        document.getElementById('newCustomerModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * عرض نموذج العميل
     */
    renderCustomerForm: function(customer = null) {
        const isEdit = customer !== null;

        return `
            <form id="customerForm">
                ${isEdit ? `<input type="hidden" name="customerId" value="${customer.id}">` : ''}

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">نوع العميل</label>
                            <select class="form-control" name="type" onchange="window.SalesComponent.toggleCustomerFields()" required>
                                <option value="individual" ${!isEdit || customer.type === 'individual' ? 'selected' : ''}>فرد</option>
                                <option value="corporate" ${isEdit && customer.type === 'corporate' ? 'selected' : ''}>شركة</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">الاسم الكامل / اسم الشركة</label>
                            <input type="text" class="form-control" name="name"
                                   value="${isEdit ? customer.name : ''}" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" name="email"
                                   value="${isEdit ? customer.email || '' : ''}">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" name="phone"
                                   value="${isEdit ? customer.phone || '' : ''}" placeholder="+966501234567">
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">العنوان</label>
                    <textarea class="form-control" name="address" rows="3"
                              placeholder="العنوان الكامل">${isEdit ? customer.address || '' : ''}</textarea>
                </div>

                <div id="taxNumberField" style="display: ${!isEdit || customer.type === 'corporate' ? 'block' : 'none'};">
                    <div class="mb-3">
                        <label class="form-label">الرقم الضريبي</label>
                        <input type="text" class="form-control" name="taxNumber"
                               value="${isEdit ? customer.taxNumber || '' : ''}" placeholder="*********">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">الموقع الإلكتروني (اختياري)</label>
                            <input type="url" class="form-control" name="website"
                                   value="${isEdit ? customer.website || '' : ''}" placeholder="https://example.com">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">اسم جهة الاتصال (للشركات)</label>
                            <input type="text" class="form-control" name="contactPerson"
                                   value="${isEdit ? customer.contactPerson || '' : ''}" placeholder="اسم المسؤول">
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">ملاحظات</label>
                    <textarea class="form-control" name="notes" rows="3"
                              placeholder="ملاحظات إضافية عن العميل">${isEdit ? customer.notes || '' : ''}</textarea>
                </div>
            </form>
        `;
    },

    /**
     * تبديل حقول العميل حسب النوع
     */
    toggleCustomerFields: function() {
        const typeSelect = document.querySelector('select[name="type"]');
        const taxNumberField = document.getElementById('taxNumberField');

        if (typeSelect.value === 'corporate') {
            taxNumberField.style.display = 'block';
        } else {
            taxNumberField.style.display = 'none';
        }
    },

    /**
     * نسخ الفاتورة
     */
    duplicateInvoice: function(invoiceId) {
        this.showMessage('وظيفة نسخ الفاتورة قيد التطوير', 'info');
    },

    /**
     * إرسال الفاتورة بالبريد الإلكتروني
     */
    sendInvoiceEmail: function(invoiceId) {
        this.showMessage('وظيفة إرسال البريد الإلكتروني قيد التطوير', 'info');
    },

    /**
     * تصدير الفواتير CSV
     */
    exportInvoicesCSV: function() {
        const invoices = Object.values(this.data.invoices || {});
        if (invoices.length === 0) {
            this.showMessage('لا توجد فواتير للتصدير', 'error');
            return;
        }

        const csvData = [
            ['رقم الفاتورة', 'العميل', 'التاريخ', 'تاريخ الاستحقاق', 'الإجمالي', 'الحالة', 'المرجع', 'الملاحظات']
        ];

        invoices.forEach(invoice => {
            const customer = this.data.customers[invoice.customerId];
            csvData.push([
                invoice.number,
                customer?.name || 'عميل غير محدد',
                invoice.date,
                invoice.dueDate || '',
                invoice.total,
                this.getInvoiceStatusLabel(invoice.status),
                invoice.reference || '',
                invoice.notes || ''
            ]);
        });

        const csvContent = csvData.map(row => row.join(',')).join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `invoices_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();

        this.showMessage('تم تصدير الفواتير بنجاح', 'success');
    },

    /**
     * تصدير الفواتير Excel
     */
    exportInvoicesExcel: function() {
        this.showMessage('وظيفة تصدير Excel ستكون متاحة قريباً', 'info');
    },

    /**
     * تصدير الفواتير PDF
     */
    exportInvoicesPDF: function() {
        this.showMessage('وظيفة تصدير PDF ستكون متاحة قريباً', 'info');
    },

    /**
     * طباعة جميع الفواتير
     */
    printAllInvoices: function() {
        const invoices = Object.values(this.data.invoices || {});
        if (invoices.length === 0) {
            this.showMessage('لا توجد فواتير للطباعة', 'error');
            return;
        }

        if (confirm(`هل تريد طباعة جميع الفواتير (${invoices.length} فاتورة)؟`)) {
            invoices.forEach((invoice, index) => {
                setTimeout(() => {
                    this.printInvoice(invoice.id);
                }, index * 1500); // تأخير 1.5 ثانية بين كل طباعة
            });
            this.showMessage(`جاري طباعة ${invoices.length} فاتورة...`, 'info');
        }
    },

    /**
     * عرض سلة المحذوفات
     */
    showDeletedInvoices: function() {
        const deletedInvoices = Object.values(this.data.deletedInvoices || {});

        const modalHTML = `
            <div class="modal fade" id="deletedInvoicesModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-trash-restore me-2"></i>سلة المحذوفات (${deletedInvoices.length} فاتورة)
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${deletedInvoices.length === 0 ? `
                                <div class="text-center py-4">
                                    <i class="fas fa-trash fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">سلة المحذوفات فارغة</h5>
                                    <p class="text-muted">لا توجد فواتير محذوفة</p>
                                </div>
                            ` : `
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>رقم الفاتورة</th>
                                                <th>العميل</th>
                                                <th>المبلغ</th>
                                                <th>تاريخ الحذف</th>
                                                <th>سبب الحذف</th>
                                                <th>إجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${deletedInvoices.map(invoice => `
                                                <tr>
                                                    <td><strong>${invoice.number}</strong></td>
                                                    <td>${this.data.customers[invoice.customerId]?.name || 'عميل غير محدد'}</td>
                                                    <td>${this.formatAmount(invoice.total)}</td>
                                                    <td>${new Date(invoice.deletedAt).toLocaleDateString('ar-SA')}</td>
                                                    <td>${invoice.deleteReason || '-'}</td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <button class="btn btn-outline-success" onclick="window.SalesComponent.restoreInvoice('${invoice.id}')" title="استعادة">
                                                                <i class="fas fa-undo"></i>
                                                            </button>
                                                            <button class="btn btn-outline-info" onclick="window.SalesComponent.viewDeletedInvoice('${invoice.id}')" title="عرض">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <button class="btn btn-outline-danger" onclick="window.SalesComponent.permanentDelete('${invoice.id}')" title="حذف نهائي">
                                                                <i class="fas fa-times"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            `}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            ${deletedInvoices.length > 0 ? `
                                <button type="button" class="btn btn-warning" onclick="window.SalesComponent.emptyTrash()">
                                    <i class="fas fa-trash me-1"></i>إفراغ سلة المحذوفات
                                </button>
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('deletedInvoicesModal'));
        modal.show();

        document.getElementById('deletedInvoicesModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * استعادة فاتورة محذوفة
     */
    restoreInvoice: function(invoiceId) {
        const deletedInvoice = this.data.deletedInvoices[invoiceId];
        if (!deletedInvoice) {
            this.showMessage('الفاتورة غير موجودة في سلة المحذوفات', 'error');
            return;
        }

        if (confirm(`هل تريد استعادة الفاتورة ${deletedInvoice.number}؟`)) {
            // إزالة معلومات الحذف
            const restoredInvoice = { ...deletedInvoice };
            delete restoredInvoice.deletedAt;
            delete restoredInvoice.deleteReason;

            // إعادة إلى الفواتير الأساسية
            this.data.invoices[invoiceId] = restoredInvoice;

            // حذف من سلة المحذوفات
            delete this.data.deletedInvoices[invoiceId];

            this.saveSalesData();
            this.showMessage(`تم استعادة الفاتورة ${deletedInvoice.number} بنجاح`, 'success');

            // تحديث نافذة سلة المحذوفات
            const modal = bootstrap.Modal.getInstance(document.getElementById('deletedInvoicesModal'));
            modal.hide();
            this.showDeletedInvoices();
            this.refreshData();
        }
    },

    /**
     * عرض فاتورة محذوفة
     */
    viewDeletedInvoice: function(invoiceId) {
        const invoice = this.data.deletedInvoices[invoiceId];
        if (!invoice) {
            this.showMessage('الفاتورة غير موجودة', 'error');
            return;
        }

        const customer = this.data.customers[invoice.customerId];

        const modalHTML = `
            <div class="modal fade" id="viewDeletedInvoiceModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-warning text-dark">
                            <h5 class="modal-title">
                                <i class="fas fa-trash me-2"></i>فاتورة محذوفة - ${invoice.number}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-warning">
                                <strong>تحذير:</strong> هذه فاتورة محذوفة. تم حذفها في ${new Date(invoice.deletedAt).toLocaleDateString('ar-SA')}
                                ${invoice.deleteReason ? `<br><strong>السبب:</strong> ${invoice.deleteReason}` : ''}
                            </div>
                            ${this.renderInvoiceDetails(invoice, customer)}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-success" onclick="window.SalesComponent.restoreInvoice('${invoiceId}')">
                                <i class="fas fa-undo me-1"></i>استعادة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('viewDeletedInvoiceModal'));
        modal.show();

        document.getElementById('viewDeletedInvoiceModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * حذف نهائي من سلة المحذوفات
     */
    permanentDelete: function(invoiceId) {
        const deletedInvoice = this.data.deletedInvoices[invoiceId];
        if (!deletedInvoice) {
            this.showMessage('الفاتورة غير موجودة في سلة المحذوفات', 'error');
            return;
        }

        if (confirm(`هل أنت متأكد من الحذف النهائي للفاتورة ${deletedInvoice.number}؟ لا يمكن التراجع عن هذا الإجراء!`)) {
            // حذف نهائي
            delete this.data.deletedInvoices[invoiceId];

            this.saveSalesData();
            this.showMessage(`تم الحذف النهائي للفاتورة ${deletedInvoice.number}`, 'success');

            // تحديث نافذة سلة المحذوفات
            const modal = bootstrap.Modal.getInstance(document.getElementById('deletedInvoicesModal'));
            modal.hide();
            this.showDeletedInvoices();
        }
    },

    /**
     * إفراغ سلة المحذوفات
     */
    emptyTrash: function() {
        const deletedCount = Object.keys(this.data.deletedInvoices || {}).length;

        if (deletedCount === 0) {
            this.showMessage('سلة المحذوفات فارغة بالفعل', 'info');
            return;
        }

        if (confirm(`هل أنت متأكد من حذف جميع الفواتير (${deletedCount} فاتورة) نهائياً؟ لا يمكن التراجع عن هذا الإجراء!`)) {
            // إفراغ سلة المحذوفات
            this.data.deletedInvoices = {};

            this.saveSalesData();
            this.showMessage(`تم حذف ${deletedCount} فاتورة نهائياً`, 'success');

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('deletedInvoicesModal'));
            modal.hide();
            this.refreshData();
        }
    },

    /**
     * الحصول على الفواتير المحددة
     */
    getSelectedInvoices: function() {
        const selectedCheckboxes = document.querySelectorAll('.invoice-checkbox:checked');
        return Array.from(selectedCheckboxes).map(checkbox => checkbox.value);
    },

    /**
     * تحديد المحدد كمدفوع
     */
    bulkMarkAsPaid: function() {
        const selectedInvoices = this.getSelectedInvoices();
        if (selectedInvoices.length === 0) {
            this.showMessage('يرجى تحديد فاتورة واحدة على الأقل', 'warning');
            return;
        }

        if (confirm(`هل تريد تحديد ${selectedInvoices.length} فاتورة كمدفوعة؟`)) {
            selectedInvoices.forEach(invoiceId => {
                if (this.data.invoices[invoiceId]) {
                    this.data.invoices[invoiceId].status = 'paid';
                    this.data.invoices[invoiceId].updatedAt = new Date().toISOString();
                }
            });

            this.saveSalesData();
            this.showMessage(`تم تحديد ${selectedInvoices.length} فاتورة كمدفوعة`, 'success');
            this.refreshData();
        }
    },

    /**
     * تحديد المحدد كمرسل
     */
    bulkMarkAsSent: function() {
        const selectedInvoices = this.getSelectedInvoices();
        if (selectedInvoices.length === 0) {
            this.showMessage('يرجى تحديد فاتورة واحدة على الأقل', 'warning');
            return;
        }

        if (confirm(`هل تريد تحديد ${selectedInvoices.length} فاتورة كمرسلة؟`)) {
            selectedInvoices.forEach(invoiceId => {
                if (this.data.invoices[invoiceId]) {
                    this.data.invoices[invoiceId].status = 'sent';
                    this.data.invoices[invoiceId].updatedAt = new Date().toISOString();
                }
            });

            this.saveSalesData();
            this.showMessage(`تم تحديد ${selectedInvoices.length} فاتورة كمرسلة`, 'success');
            this.refreshData();
        }
    },

    /**
     * طباعة المحدد
     */
    bulkPrint: function() {
        const selectedInvoices = this.getSelectedInvoices();
        if (selectedInvoices.length === 0) {
            this.showMessage('يرجى تحديد فاتورة واحدة على الأقل', 'warning');
            return;
        }

        if (confirm(`هل تريد طباعة ${selectedInvoices.length} فاتورة؟`)) {
            selectedInvoices.forEach((invoiceId, index) => {
                setTimeout(() => {
                    this.printInvoice(invoiceId);
                }, index * 1000); // تأخير ثانية واحدة بين كل طباعة
            });
            this.showMessage(`جاري طباعة ${selectedInvoices.length} فاتورة...`, 'info');
        }
    },

    /**
     * حذف المحدد
     */
    bulkDelete: function() {
        const selectedInvoices = this.getSelectedInvoices();
        if (selectedInvoices.length === 0) {
            this.showMessage('يرجى تحديد فاتورة واحدة على الأقل', 'warning');
            return;
        }

        if (confirm(`هل أنت متأكد من حذف ${selectedInvoices.length} فاتورة؟ سيتم نقلها إلى سلة المحذوفات.`)) {
            // إنشاء سلة المحذوفات إذا لم تكن موجودة
            if (!this.data.deletedInvoices) {
                this.data.deletedInvoices = {};
            }

            selectedInvoices.forEach(invoiceId => {
                const invoice = this.data.invoices[invoiceId];
                if (invoice) {
                    // نقل إلى سلة المحذوفات
                    this.data.deletedInvoices[invoiceId] = {
                        ...invoice,
                        deletedAt: new Date().toISOString(),
                        deleteReason: 'حذف مجمع'
                    };

                    // حذف من الفواتير الأساسية
                    delete this.data.invoices[invoiceId];
                }
            });

            this.saveSalesData();
            this.showMessage(`تم نقل ${selectedInvoices.length} فاتورة إلى سلة المحذوفات`, 'success');
            this.refreshData();
        }
    },

    /**
     * عرض إدارة العملاء
     */
    renderCustomers: function() {
        const customers = Object.values(this.data.customers || {})
            .sort((a, b) => a.name.localeCompare(b.name));

        return `
            <div class="customers-management">
                <!-- الإحصائيات السريعة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-light rounded p-3 text-center">
                            <h4 class="text-primary mb-1">${customers.length}</h4>
                            <small class="text-muted">إجمالي العملاء</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light rounded p-3 text-center">
                            <h4 class="text-success mb-1">${customers.filter(c => c.type === 'individual').length}</h4>
                            <small class="text-muted">عملاء أفراد</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light rounded p-3 text-center">
                            <h4 class="text-info mb-1">${customers.filter(c => c.type === 'corporate').length}</h4>
                            <small class="text-muted">عملاء شركات</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light rounded p-3 text-center">
                            <h4 class="text-warning mb-1">${this.getActiveCustomersCount()}</h4>
                            <small class="text-muted">عملاء نشطين</small>
                        </div>
                    </div>
                </div>

                <!-- شريط الأدوات -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-0">
                                    <i class="fas fa-users me-2"></i>إدارة العملاء
                                </h5>
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="btn-group">
                                    <button class="btn btn-primary" onclick="window.SalesComponent.showNewCustomerModal()">
                                        <i class="fas fa-plus me-1"></i>عميل جديد
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="window.SalesComponent.exportCustomersCSV()">
                                        <i class="fas fa-download me-1"></i>تصدير
                                    </button>
                                    <button class="btn btn-outline-info" onclick="window.SalesComponent.importCustomers()">
                                        <i class="fas fa-upload me-1"></i>استيراد
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- فلاتر البحث -->
                        <div class="row mt-3">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="البحث في العملاء..."
                                           id="customerSearch" onkeyup="window.SalesComponent.filterCustomers()">
                                    <button class="btn btn-outline-secondary" type="button" onclick="window.SalesComponent.clearCustomerSearch()">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-control" id="customerTypeFilter" onchange="window.SalesComponent.filterCustomers()">
                                    <option value="">جميع الأنواع</option>
                                    <option value="individual">أفراد</option>
                                    <option value="corporate">شركات</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-control" id="customerStatusFilter" onchange="window.SalesComponent.filterCustomers()">
                                    <option value="">جميع الحالات</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-outline-primary w-100" onclick="window.SalesComponent.clearCustomerFilters()" title="مسح الفلاتر">
                                    <i class="fas fa-eraser"></i> مسح
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قائمة العملاء -->
                <div class="card">
                    <div class="card-body p-0">
                        <div id="customersContainer">
                            ${customers.length === 0 ? this.renderEmptyCustomers() : this.renderCustomersGrid(customers)}
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * حساب عدد العملاء النشطين
     */
    getActiveCustomersCount: function() {
        const customers = Object.values(this.data.customers || {});
        const invoices = Object.values(this.data.invoices || {});

        // عميل نشط = له فاتورة واحدة على الأقل في آخر 6 شهور
        const sixMonthsAgo = new Date();
        sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

        return customers.filter(customer => {
            return invoices.some(invoice =>
                invoice.customerId === customer.id &&
                new Date(invoice.createdAt) > sixMonthsAgo
            );
        }).length;
    },

    /**
     * عرض رسالة عدم وجود عملاء
     */
    renderEmptyCustomers: function() {
        return `
            <div class="text-center py-5">
                <i class="fas fa-users fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا يوجد عملاء</h4>
                <p class="text-muted">ابدأ بإضافة عملائك لإدارة فواتيرهم</p>
                <button class="btn btn-primary" onclick="window.SalesComponent.showNewCustomerModal()">
                    <i class="fas fa-plus me-1"></i>إضافة عميل جديد
                </button>
            </div>
        `;
    },

    /**
     * عرض شبكة العملاء
     */
    renderCustomersGrid: function(customers) {
        return `
            <div class="row p-3">
                ${customers.map(customer => `
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card customer-card h-100">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-${customer.type === 'corporate' ? 'building' : 'user'} me-2 text-primary"></i>
                                    <strong>${customer.name}</strong>
                                </div>
                                <span class="badge bg-${this.getCustomerStatusColor(customer)}">
                                    ${this.getCustomerStatusLabel(customer)}
                                </span>
                            </div>
                            <div class="card-body">
                                <div class="customer-info">
                                    ${customer.email ? `
                                        <p class="mb-2">
                                            <i class="fas fa-envelope text-muted me-2"></i>
                                            <small>${customer.email}</small>
                                        </p>
                                    ` : ''}
                                    ${customer.phone ? `
                                        <p class="mb-2">
                                            <i class="fas fa-phone text-muted me-2"></i>
                                            <small>${customer.phone}</small>
                                        </p>
                                    ` : ''}
                                    ${customer.address ? `
                                        <p class="mb-2">
                                            <i class="fas fa-map-marker-alt text-muted me-2"></i>
                                            <small>${customer.address}</small>
                                        </p>
                                    ` : ''}
                                    ${customer.type === 'corporate' && customer.taxNumber ? `
                                        <p class="mb-2">
                                            <i class="fas fa-receipt text-muted me-2"></i>
                                            <small>ض.ق: ${customer.taxNumber}</small>
                                        </p>
                                    ` : ''}
                                </div>

                                <div class="customer-stats mt-3">
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <small class="text-muted">الفواتير</small>
                                            <div class="fw-bold text-primary">${this.getCustomerInvoicesCount(customer.id)}</div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">إجمالي المبيعات</small>
                                            <div class="fw-bold text-success">${this.formatAmount(this.getCustomerTotalSales(customer.id))}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <div class="btn-group w-100">
                                    <button class="btn btn-outline-primary btn-sm" onclick="window.SalesComponent.viewCustomer('${customer.id}')" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="window.SalesComponent.editCustomer('${customer.id}')" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" onclick="window.SalesComponent.createInvoiceForCustomer('${customer.id}')" title="فاتورة جديدة">
                                        <i class="fas fa-file-invoice"></i>
                                    </button>
                                    <button class="btn btn-outline-info btn-sm" onclick="window.SalesComponent.customerInvoices('${customer.id}')" title="فواتير العميل">
                                        <i class="fas fa-list"></i>
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" onclick="window.SalesComponent.deleteCustomer('${customer.id}')" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    },

    /**
     * عرض إدارة المنتجات
     */
    renderProducts: function() {
        const products = Object.values(this.data.products || {})
            .sort((a, b) => a.name.localeCompare(b.name));

        return `
            <div class="products-management">
                <!-- الإحصائيات السريعة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-light rounded p-3 text-center">
                            <h4 class="text-primary mb-1">${products.length}</h4>
                            <small class="text-muted">إجمالي المنتجات</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light rounded p-3 text-center">
                            <h4 class="text-success mb-1">${products.filter(p => p.category === 'flights').length}</h4>
                            <small class="text-muted">تذاكر طيران</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light rounded p-3 text-center">
                            <h4 class="text-info mb-1">${products.filter(p => p.category === 'hotels').length}</h4>
                            <small class="text-muted">حجوزات فنادق</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light rounded p-3 text-center">
                            <h4 class="text-warning mb-1">${products.filter(p => p.category === 'visas').length}</h4>
                            <small class="text-muted">تأشيرات</small>
                        </div>
                    </div>
                </div>

                <!-- شريط الأدوات -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-0">
                                    <i class="fas fa-box me-2"></i>إدارة المنتجات والخدمات
                                </h5>
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="btn-group">
                                    <button class="btn btn-primary" onclick="window.SalesComponent.showNewProductModal()">
                                        <i class="fas fa-plus me-1"></i>منتج/خدمة جديدة
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="window.SalesComponent.exportProductsCSV()">
                                        <i class="fas fa-download me-1"></i>تصدير
                                    </button>
                                    <button class="btn btn-outline-info" onclick="window.SalesComponent.importProducts()">
                                        <i class="fas fa-upload me-1"></i>استيراد
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- فلاتر البحث -->
                        <div class="row mt-3">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="البحث في المنتجات..."
                                           id="productSearch" onkeyup="window.SalesComponent.filterProducts()">
                                    <button class="btn btn-outline-secondary" type="button" onclick="window.SalesComponent.clearProductSearch()">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-control" id="categoryFilter" onchange="window.SalesComponent.filterProducts()">
                                    <option value="">جميع الفئات</option>
                                    <option value="flights">تذاكر طيران</option>
                                    <option value="hotels">حجوزات فنادق</option>
                                    <option value="visas">تأشيرات</option>
                                    <option value="tours">جولات سياحية</option>
                                    <option value="transport">نقل</option>
                                    <option value="insurance">تأمين سفر</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-control" id="priceRangeFilter" onchange="window.SalesComponent.filterProducts()">
                                    <option value="">جميع الأسعار</option>
                                    <option value="0-100">أقل من 100 ر.س</option>
                                    <option value="100-500">100 - 500 ر.س</option>
                                    <option value="500-1000">500 - 1000 ر.س</option>
                                    <option value="1000-5000">1000 - 5000 ر.س</option>
                                    <option value="5000+">أكثر من 5000 ر.س</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-outline-primary w-100" onclick="window.SalesComponent.clearProductFilters()" title="مسح الفلاتر">
                                    <i class="fas fa-eraser"></i> مسح
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قائمة المنتجات -->
                <div class="card">
                    <div class="card-body p-0">
                        <div id="productsContainer">
                            ${products.length === 0 ? this.renderEmptyProducts() : this.renderProductsGrid(products)}
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض رسالة عدم وجود منتجات
     */
    renderEmptyProducts: function() {
        return `
            <div class="text-center py-5">
                <i class="fas fa-box fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد منتجات أو خدمات</h4>
                <p class="text-muted">ابدأ بإضافة منتجاتك وخدماتك لإنشاء الفواتير</p>
                <button class="btn btn-primary" onclick="window.SalesComponent.showNewProductModal()">
                    <i class="fas fa-plus me-1"></i>إضافة منتج/خدمة جديدة
                </button>
            </div>
        `;
    },

    /**
     * عرض شبكة المنتجات
     */
    renderProductsGrid: function(products) {
        return `
            <div class="row p-3">
                ${products.map(product => `
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card product-card h-100">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-${this.getProductIcon(product.category)} me-2 text-primary"></i>
                                    <strong>${product.name}</strong>
                                </div>
                                <span class="badge bg-${this.getCategoryColor(product.category)}">
                                    ${this.getCategoryLabel(product.category)}
                                </span>
                            </div>
                            <div class="card-body">
                                ${product.description ? `
                                    <p class="text-muted small mb-3">${product.description}</p>
                                ` : ''}

                                <div class="product-pricing mb-3">
                                    <div class="row">
                                        <div class="col-6">
                                            <small class="text-muted">سعر البيع</small>
                                            <div class="fw-bold text-success fs-5">${this.formatAmount(product.price)}</div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">سعر التكلفة</small>
                                            <div class="fw-bold text-info">${this.formatAmount(product.cost || 0)}</div>
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <small class="text-muted">هامش الربح</small>
                                        <div class="fw-bold text-warning">
                                            ${this.formatAmount((product.price || 0) - (product.cost || 0))}
                                            (${product.price > 0 ? Math.round(((product.price - (product.cost || 0)) / product.price) * 100) : 0}%)
                                        </div>
                                    </div>
                                </div>

                                <div class="product-stats">
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <small class="text-muted">مرات البيع</small>
                                            <div class="fw-bold text-primary">${this.getProductSalesCount(product.id)}</div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">إجمالي الإيرادات</small>
                                            <div class="fw-bold text-success">${this.formatAmount(this.getProductTotalRevenue(product.id))}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <div class="btn-group w-100">
                                    <button class="btn btn-outline-primary btn-sm" onclick="window.SalesComponent.viewProduct('${product.id}')" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="window.SalesComponent.editProduct('${product.id}')" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" onclick="window.SalesComponent.duplicateProduct('${product.id}')" title="نسخ">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                    <button class="btn btn-outline-info btn-sm" onclick="window.SalesComponent.productSalesReport('${product.id}')" title="تقرير المبيعات">
                                        <i class="fas fa-chart-bar"></i>
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" onclick="window.SalesComponent.deleteProduct('${product.id}')" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    },

    /**
     * عرض التقارير
     */
    renderReports: function() {
        return `
            <div class="reports-management">
                <!-- شريط الأدوات -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-bar me-2"></i>التقارير والإحصائيات
                                </h5>
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="btn-group">
                                    <button class="btn btn-outline-primary" onclick="window.SalesComponent.refreshReports()">
                                        <i class="fas fa-sync me-1"></i>تحديث
                                    </button>
                                    <button class="btn btn-outline-success" onclick="window.SalesComponent.exportAllReports()">
                                        <i class="fas fa-download me-1"></i>تصدير جميع التقارير
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- فلاتر التقارير -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">فلاتر التقارير</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label small">من تاريخ</label>
                                <input type="date" class="form-control" id="reportsFromDate" onchange="window.SalesComponent.updateReports()">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label small">إلى تاريخ</label>
                                <input type="date" class="form-control" id="reportsToDate" onchange="window.SalesComponent.updateReports()">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label small">فترة سريعة</label>
                                <select class="form-control" id="quickPeriod" onchange="window.SalesComponent.setQuickPeriod()">
                                    <option value="">اختر الفترة</option>
                                    <option value="today">اليوم</option>
                                    <option value="week">هذا الأسبوع</option>
                                    <option value="month">هذا الشهر</option>
                                    <option value="quarter">هذا الربع</option>
                                    <option value="year">هذا العام</option>
                                    <option value="last30">آخر 30 يوم</option>
                                    <option value="last90">آخر 90 يوم</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label small">&nbsp;</label>
                                <button class="btn btn-outline-secondary w-100" onclick="window.SalesComponent.clearReportsFilters()">
                                    <i class="fas fa-eraser me-1"></i>مسح الفلاتر
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الإحصائيات الرئيسية -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-file-invoice fa-2x mb-2"></i>
                                <h3 id="totalInvoicesReport">0</h3>
                                <p class="mb-0">إجمالي الفواتير</p>
                                <small id="invoicesChange" class="opacity-75"></small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                                <h3 id="totalRevenueReport">0.00 ر.س</h3>
                                <p class="mb-0">إجمالي الإيرادات</p>
                                <small id="revenueChange" class="opacity-75"></small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h3 id="activeCustomersReport">0</h3>
                                <p class="mb-0">العملاء النشطين</p>
                                <small id="customersChange" class="opacity-75"></small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-line fa-2x mb-2"></i>
                                <h3 id="averageInvoiceReport">0.00 ر.س</h3>
                                <p class="mb-0">متوسط قيمة الفاتورة</p>
                                <small id="averageChange" class="opacity-75"></small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- التقارير التفصيلية -->
                <div class="row">
                    <!-- تقرير المبيعات -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-line me-2"></i>تقرير المبيعات
                                </h6>
                                <button class="btn btn-outline-primary btn-sm" onclick="window.SalesComponent.exportSalesReport()">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                            <div class="card-body">
                                <div id="salesReportContent">
                                    <!-- سيتم تحميل المحتوى هنا -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تقرير العملاء -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="fas fa-users me-2"></i>تقرير العملاء
                                </h6>
                                <button class="btn btn-outline-primary btn-sm" onclick="window.SalesComponent.exportCustomersReport()">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                            <div class="card-body">
                                <div id="customersReportContent">
                                    <!-- سيتم تحميل المحتوى هنا -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تقرير المنتجات -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="fas fa-box me-2"></i>تقرير المنتجات
                                </h6>
                                <button class="btn btn-outline-primary btn-sm" onclick="window.SalesComponent.exportProductsReport()">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                            <div class="card-body">
                                <div id="productsReportContent">
                                    <!-- سيتم تحميل المحتوى هنا -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تقرير الأرباح -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="fas fa-dollar-sign me-2"></i>تقرير الأرباح
                                </h6>
                                <button class="btn btn-outline-primary btn-sm" onclick="window.SalesComponent.exportProfitReport()">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                            <div class="card-body">
                                <div id="profitReportContent">
                                    <!-- سيتم تحميل المحتوى هنا -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تقارير إضافية -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-pie me-2"></i>تقارير إضافية
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <button class="btn btn-outline-info w-100" onclick="window.SalesComponent.showPaymentStatusReport()">
                                    <i class="fas fa-credit-card me-2"></i>تقرير حالات الدفع
                                </button>
                            </div>
                            <div class="col-md-4 mb-3">
                                <button class="btn btn-outline-success w-100" onclick="window.SalesComponent.showMonthlyTrendsReport()">
                                    <i class="fas fa-calendar-alt me-2"></i>الاتجاهات الشهرية
                                </button>
                            </div>
                            <div class="col-md-4 mb-3">
                                <button class="btn btn-outline-warning w-100" onclick="window.SalesComponent.showTopCustomersReport()">
                                    <i class="fas fa-star me-2"></i>أفضل العملاء
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض الإعدادات
     */
    renderSettings: function() {
        return `
            <div class="settings-management">
                <!-- شريط الأدوات -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-0">
                                    <i class="fas fa-cog me-2"></i>إعدادات النظام
                                </h5>
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="btn-group">
                                    <button class="btn btn-outline-success" onclick="window.SalesComponent.saveAllSettings()">
                                        <i class="fas fa-save me-1"></i>حفظ جميع الإعدادات
                                    </button>
                                    <button class="btn btn-outline-warning" onclick="window.SalesComponent.resetSettings()">
                                        <i class="fas fa-undo me-1"></i>إعادة تعيين
                                    </button>
                                    <button class="btn btn-outline-info" onclick="window.SalesComponent.exportSettings()">
                                        <i class="fas fa-download me-1"></i>تصدير الإعدادات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- إعدادات الشركة -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-building me-2"></i>معلومات الشركة
                                </h6>
                            </div>
                            <div class="card-body">
                                <form id="companySettingsForm">
                                    <div class="mb-3">
                                        <label class="form-label">اسم الشركة</label>
                                        <input type="text" class="form-control" name="companyName"
                                               value="${this.data.settings.companyName || ''}" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">عنوان الشركة</label>
                                        <textarea class="form-control" name="companyAddress" rows="3">${this.data.settings.companyAddress || ''}</textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">الرقم الضريبي</label>
                                        <input type="text" class="form-control" name="companyTaxNumber"
                                               value="${this.data.settings.companyTaxNumber || ''}">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" name="companyEmail"
                                               value="${this.data.settings.companyEmail || ''}">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" name="companyPhone"
                                               value="${this.data.settings.companyPhone || ''}">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">الموقع الإلكتروني</label>
                                        <input type="url" class="form-control" name="companyWebsite"
                                               value="${this.data.settings.companyWebsite || ''}" placeholder="https://example.com">
                                    </div>
                                </form>
                                <button class="btn btn-primary" onclick="window.SalesComponent.saveCompanySettings()">
                                    <i class="fas fa-save me-1"></i>حفظ معلومات الشركة
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات الفواتير -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-file-invoice me-2"></i>إعدادات الفواتير
                                </h6>
                            </div>
                            <div class="card-body">
                                <form id="invoiceSettingsForm">
                                    <div class="mb-3">
                                        <label class="form-label">بادئة رقم الفاتورة</label>
                                        <input type="text" class="form-control" name="invoicePrefix"
                                               value="${this.data.settings.invoicePrefix || 'INV-'}" required>
                                        <small class="form-text text-muted">مثال: INV-, BILL-, FAT-</small>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">رقم الفاتورة التالي</label>
                                        <input type="number" class="form-control" name="nextInvoiceNumber"
                                               value="${this.data.settings.nextInvoiceNumber || 1}" min="1" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">معدل الضريبة (%)</label>
                                        <input type="number" class="form-control" name="taxRate"
                                               value="${(this.data.settings.taxRate * 100) || 15}" step="0.01" min="0" max="100" required>
                                        <small class="form-text text-muted">ضريبة القيمة المضافة</small>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">العملة</label>
                                        <select class="form-control" name="currency" required>
                                            <option value="SAR" ${this.data.settings.currency === 'SAR' ? 'selected' : ''}>ريال سعودي (SAR)</option>
                                            <option value="USD" ${this.data.settings.currency === 'USD' ? 'selected' : ''}>دولار أمريكي (USD)</option>
                                            <option value="EUR" ${this.data.settings.currency === 'EUR' ? 'selected' : ''}>يورو (EUR)</option>
                                            <option value="AED" ${this.data.settings.currency === 'AED' ? 'selected' : ''}>درهم إماراتي (AED)</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">مدة الاستحقاق الافتراضية (أيام)</label>
                                        <input type="number" class="form-control" name="defaultDueDays"
                                               value="${this.data.settings.defaultDueDays || 30}" min="0">
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="autoCalculateTax"
                                               ${this.data.settings.autoCalculateTax !== false ? 'checked' : ''}>
                                        <label class="form-check-label">حساب الضريبة تلقائياً</label>
                                    </div>
                                </form>
                                <button class="btn btn-primary mt-3" onclick="window.SalesComponent.saveInvoiceSettings()">
                                    <i class="fas fa-save me-1"></i>حفظ إعدادات الفواتير
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات النظام -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-cogs me-2"></i>إعدادات النظام
                                </h6>
                            </div>
                            <div class="card-body">
                                <form id="systemSettingsForm">
                                    <div class="mb-3">
                                        <label class="form-label">اللغة</label>
                                        <select class="form-control" name="language">
                                            <option value="ar" ${this.data.settings.language === 'ar' ? 'selected' : ''}>العربية</option>
                                            <option value="en" ${this.data.settings.language === 'en' ? 'selected' : ''}>English</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">المنطقة الزمنية</label>
                                        <select class="form-control" name="timezone">
                                            <option value="Asia/Riyadh" ${this.data.settings.timezone === 'Asia/Riyadh' ? 'selected' : ''}>الرياض (GMT+3)</option>
                                            <option value="Asia/Dubai" ${this.data.settings.timezone === 'Asia/Dubai' ? 'selected' : ''}>دبي (GMT+4)</option>
                                            <option value="UTC" ${this.data.settings.timezone === 'UTC' ? 'selected' : ''}>UTC (GMT+0)</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">تنسيق التاريخ</label>
                                        <select class="form-control" name="dateFormat">
                                            <option value="ar-SA" ${this.data.settings.dateFormat === 'ar-SA' ? 'selected' : ''}>التاريخ الهجري</option>
                                            <option value="en-US" ${this.data.settings.dateFormat === 'en-US' ? 'selected' : ''}>التاريخ الميلادي</option>
                                        </select>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" name="autoSave"
                                               ${this.data.settings.autoSave !== false ? 'checked' : ''}>
                                        <label class="form-check-label">الحفظ التلقائي</label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" name="showNotifications"
                                               ${this.data.settings.showNotifications !== false ? 'checked' : ''}>
                                        <label class="form-check-label">عرض الإشعارات</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="enableBackup"
                                               ${this.data.settings.enableBackup !== false ? 'checked' : ''}>
                                        <label class="form-check-label">تفعيل النسخ الاحتياطي</label>
                                    </div>
                                </form>
                                <button class="btn btn-primary mt-3" onclick="window.SalesComponent.saveSystemSettings()">
                                    <i class="fas fa-save me-1"></i>حفظ إعدادات النظام
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- تصميمات الفواتير -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-palette me-2"></i>تصميمات الفواتير
                                </h6>
                            </div>
                            <div class="card-body">
                                <form id="invoiceDesignForm">
                                    <div class="mb-3">
                                        <label class="form-label">التصميم المحدد</label>
                                        <select class="form-control" name="selectedTemplate" onchange="window.SalesComponent.previewInvoiceTemplate()">
                                            <option value="classic" ${this.data.settings.selectedTemplate === 'classic' ? 'selected' : ''}>كلاسيكي</option>
                                            <option value="modern" ${this.data.settings.selectedTemplate === 'modern' ? 'selected' : ''}>عصري</option>
                                            <option value="minimal" ${this.data.settings.selectedTemplate === 'minimal' ? 'selected' : ''}>بسيط</option>
                                            <option value="professional" ${this.data.settings.selectedTemplate === 'professional' ? 'selected' : ''}>احترافي</option>
                                            <option value="colorful" ${this.data.settings.selectedTemplate === 'colorful' ? 'selected' : ''}>ملون</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">اللون الأساسي</label>
                                        <div class="row">
                                            <div class="col-8">
                                                <input type="color" class="form-control form-control-color" name="primaryColor"
                                                       value="${this.data.settings.primaryColor || '#007bff'}" onchange="window.SalesComponent.updateColorPreview()">
                                            </div>
                                            <div class="col-4">
                                                <input type="text" class="form-control" name="primaryColorHex"
                                                       value="${this.data.settings.primaryColor || '#007bff'}" onchange="window.SalesComponent.updateColorFromHex()">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">اللون الثانوي</label>
                                        <div class="row">
                                            <div class="col-8">
                                                <input type="color" class="form-control form-control-color" name="secondaryColor"
                                                       value="${this.data.settings.secondaryColor || '#6c757d'}" onchange="window.SalesComponent.updateSecondaryColorPreview()">
                                            </div>
                                            <div class="col-4">
                                                <input type="text" class="form-control" name="secondaryColorHex"
                                                       value="${this.data.settings.secondaryColor || '#6c757d'}" onchange="window.SalesComponent.updateSecondaryColorFromHex()">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">حجم الخط</label>
                                        <select class="form-control" name="fontSize">
                                            <option value="small" ${this.data.settings.fontSize === 'small' ? 'selected' : ''}>صغير</option>
                                            <option value="medium" ${this.data.settings.fontSize === 'medium' ? 'selected' : ''}>متوسط</option>
                                            <option value="large" ${this.data.settings.fontSize === 'large' ? 'selected' : ''}>كبير</option>
                                        </select>
                                    </div>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" name="showLogo"
                                               ${this.data.settings.showLogo !== false ? 'checked' : ''}>
                                        <label class="form-check-label">عرض شعار الشركة</label>
                                    </div>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" name="showWatermark"
                                               ${this.data.settings.showWatermark === true ? 'checked' : ''}>
                                        <label class="form-check-label">عرض العلامة المائية</label>
                                    </div>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" name="showBorder"
                                               ${this.data.settings.showBorder !== false ? 'checked' : ''}>
                                        <label class="form-check-label">عرض الحدود</label>
                                    </div>
                                </form>

                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary" onclick="window.SalesComponent.saveInvoiceDesign()">
                                        <i class="fas fa-save me-1"></i>حفظ التصميم
                                    </button>
                                    <button class="btn btn-outline-info" onclick="window.SalesComponent.previewInvoiceDesign()">
                                        <i class="fas fa-eye me-1"></i>معاينة التصميم
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="window.SalesComponent.resetInvoiceDesign()">
                                        <i class="fas fa-undo me-1"></i>إعادة تعيين
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إدارة البيانات -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-database me-2"></i>إدارة البيانات
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <h6>النسخ الاحتياطي</h6>
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-outline-success" onclick="window.SalesComponent.createBackup()">
                                            <i class="fas fa-download me-1"></i>إنشاء نسخة احتياطية
                                        </button>
                                        <button class="btn btn-outline-info" onclick="window.SalesComponent.showRestoreBackup()">
                                            <i class="fas fa-upload me-1"></i>استعادة نسخة احتياطية
                                        </button>
                                    </div>
                                </div>

                                <hr>

                                <div class="mb-3">
                                    <h6>إعادة تعيين البيانات</h6>
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-outline-warning" onclick="window.SalesComponent.clearAllData()">
                                            <i class="fas fa-trash me-1"></i>مسح جميع البيانات
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="window.SalesComponent.resetToSampleData()">
                                            <i class="fas fa-refresh me-1"></i>إعادة تحميل البيانات التجريبية
                                        </button>
                                    </div>
                                </div>

                                <hr>

                                <div class="mb-3">
                                    <h6>إحصائيات البيانات</h6>
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <div class="border rounded p-2">
                                                <div class="fw-bold">${Object.keys(this.data.invoices || {}).length}</div>
                                                <small class="text-muted">فاتورة</small>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="border rounded p-2">
                                                <div class="fw-bold">${Object.keys(this.data.customers || {}).length}</div>
                                                <small class="text-muted">عميل</small>
                                            </div>
                                        </div>
                                        <div class="col-6 mt-2">
                                            <div class="border rounded p-2">
                                                <div class="fw-bold">${Object.keys(this.data.products || {}).length}</div>
                                                <small class="text-muted">منتج</small>
                                            </div>
                                        </div>
                                        <div class="col-6 mt-2">
                                            <div class="border rounded p-2">
                                                <div class="fw-bold">${Object.keys(this.data.deletedInvoices || {}).length}</div>
                                                <small class="text-muted">محذوف</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * حفظ عميل جديد
     */
    saveNewCustomer: function() {
        const form = document.getElementById('customerForm');
        const formData = new FormData(form);

        try {
            // جمع بيانات العميل
            const customer = {
                id: this.generateId(),
                name: formData.get('name'),
                type: formData.get('type'),
                email: formData.get('email'),
                phone: formData.get('phone'),
                address: formData.get('address'),
                taxNumber: formData.get('taxNumber'),
                website: formData.get('website'),
                contactPerson: formData.get('contactPerson'),
                notes: formData.get('notes'),
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // التحقق من صحة البيانات
            if (!customer.name.trim()) {
                throw new Error('يرجى إدخال اسم العميل');
            }

            // التحقق من عدم تكرار الاسم
            const existingCustomer = Object.values(this.data.customers || {})
                .find(c => c.name.toLowerCase() === customer.name.toLowerCase());

            if (existingCustomer) {
                throw new Error('يوجد عميل بنفس الاسم مسبقاً');
            }

            // حفظ العميل
            this.data.customers[customer.id] = customer;
            this.saveSalesData();

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('newCustomerModal'));
            modal.hide();

            // عرض رسالة نجاح
            this.showMessage(`تم إضافة العميل ${customer.name} بنجاح`, 'success');

            // تحديث العرض
            this.refreshData();

        } catch (error) {
            this.showMessage('خطأ في حفظ العميل: ' + error.message, 'error');
        }
    },

    /**
     * عرض تفاصيل العميل
     */
    viewCustomer: function(customerId) {
        const customer = this.data.customers[customerId];
        if (!customer) {
            this.showMessage('العميل غير موجود', 'error');
            return;
        }

        const customerInvoices = Object.values(this.data.invoices || {})
            .filter(inv => inv.customerId === customerId)
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        const modalHTML = `
            <div class="modal fade" id="viewCustomerModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-${customer.type === 'corporate' ? 'building' : 'user'} me-2"></i>
                                ${customer.name}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">معلومات العميل</h6>
                                        </div>
                                        <div class="card-body">
                                            <table class="table table-sm">
                                                <tr>
                                                    <td><strong>النوع:</strong></td>
                                                    <td>${customer.type === 'corporate' ? 'شركة' : 'فرد'}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>الاسم:</strong></td>
                                                    <td>${customer.name}</td>
                                                </tr>
                                                ${customer.email ? `
                                                    <tr>
                                                        <td><strong>البريد الإلكتروني:</strong></td>
                                                        <td>${customer.email}</td>
                                                    </tr>
                                                ` : ''}
                                                ${customer.phone ? `
                                                    <tr>
                                                        <td><strong>الهاتف:</strong></td>
                                                        <td>${customer.phone}</td>
                                                    </tr>
                                                ` : ''}
                                                ${customer.address ? `
                                                    <tr>
                                                        <td><strong>العنوان:</strong></td>
                                                        <td>${customer.address}</td>
                                                    </tr>
                                                ` : ''}
                                                ${customer.taxNumber ? `
                                                    <tr>
                                                        <td><strong>الرقم الضريبي:</strong></td>
                                                        <td>${customer.taxNumber}</td>
                                                    </tr>
                                                ` : ''}
                                                ${customer.website ? `
                                                    <tr>
                                                        <td><strong>الموقع الإلكتروني:</strong></td>
                                                        <td><a href="${customer.website}" target="_blank">${customer.website}</a></td>
                                                    </tr>
                                                ` : ''}
                                                ${customer.contactPerson ? `
                                                    <tr>
                                                        <td><strong>جهة الاتصال:</strong></td>
                                                        <td>${customer.contactPerson}</td>
                                                    </tr>
                                                ` : ''}
                                                <tr>
                                                    <td><strong>تاريخ الإضافة:</strong></td>
                                                    <td>${new Date(customer.createdAt).toLocaleDateString('ar-SA')}</td>
                                                </tr>
                                            </table>
                                            ${customer.notes ? `
                                                <div class="mt-3">
                                                    <strong>ملاحظات:</strong>
                                                    <p class="text-muted">${customer.notes}</p>
                                                </div>
                                            ` : ''}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">إحصائيات العميل</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row text-center">
                                                <div class="col-6">
                                                    <div class="border rounded p-3">
                                                        <h4 class="text-primary mb-1">${customerInvoices.length}</h4>
                                                        <small class="text-muted">إجمالي الفواتير</small>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="border rounded p-3">
                                                        <h4 class="text-success mb-1">${this.formatAmount(this.getCustomerTotalSales(customerId))}</h4>
                                                        <small class="text-muted">إجمالي المبيعات</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row text-center mt-3">
                                                <div class="col-6">
                                                    <div class="border rounded p-3">
                                                        <h4 class="text-warning mb-1">${customerInvoices.filter(inv => inv.status === 'paid').length}</h4>
                                                        <small class="text-muted">فواتير مدفوعة</small>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="border rounded p-3">
                                                        <h4 class="text-danger mb-1">${customerInvoices.filter(inv => inv.status === 'sent' || inv.status === 'overdue').length}</h4>
                                                        <small class="text-muted">فواتير معلقة</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- فواتير العميل -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h6 class="mb-0">فواتير العميل (${customerInvoices.length})</h6>
                                </div>
                                <div class="card-body">
                                    ${customerInvoices.length === 0 ? `
                                        <div class="text-center py-3">
                                            <p class="text-muted">لا توجد فواتير لهذا العميل</p>
                                            <button class="btn btn-primary btn-sm" onclick="window.SalesComponent.createInvoiceForCustomer('${customerId}')">
                                                <i class="fas fa-plus me-1"></i>إنشاء فاتورة جديدة
                                            </button>
                                        </div>
                                    ` : `
                                        <div class="table-responsive">
                                            <table class="table table-sm table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>رقم الفاتورة</th>
                                                        <th>التاريخ</th>
                                                        <th>المبلغ</th>
                                                        <th>الحالة</th>
                                                        <th>إجراءات</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    ${customerInvoices.slice(0, 10).map(invoice => `
                                                        <tr>
                                                            <td><strong>${invoice.number}</strong></td>
                                                            <td>${new Date(invoice.date).toLocaleDateString('ar-SA')}</td>
                                                            <td>${this.formatAmount(invoice.total)}</td>
                                                            <td>
                                                                <span class="badge bg-${this.getInvoiceStatusColor(invoice.status)}">
                                                                    ${this.getInvoiceStatusLabel(invoice.status)}
                                                                </span>
                                                            </td>
                                                            <td>
                                                                <div class="btn-group btn-group-sm">
                                                                    <button class="btn btn-outline-primary" onclick="window.SalesComponent.viewInvoice('${invoice.id}')" title="عرض">
                                                                        <i class="fas fa-eye"></i>
                                                                    </button>
                                                                    <button class="btn btn-outline-info" onclick="window.SalesComponent.printInvoice('${invoice.id}')" title="طباعة">
                                                                        <i class="fas fa-print"></i>
                                                                    </button>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    `).join('')}
                                                </tbody>
                                            </table>
                                            ${customerInvoices.length > 10 ? `
                                                <div class="text-center">
                                                    <button class="btn btn-outline-primary btn-sm" onclick="window.SalesComponent.customerInvoices('${customerId}')">
                                                        عرض جميع الفواتير (${customerInvoices.length})
                                                    </button>
                                                </div>
                                            ` : ''}
                                        </div>
                                    `}
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-outline-primary" onclick="window.SalesComponent.editCustomer('${customerId}')">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.createInvoiceForCustomer('${customerId}')">
                                <i class="fas fa-file-invoice me-1"></i>فاتورة جديدة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('viewCustomerModal'));
        modal.show();

        document.getElementById('viewCustomerModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * تعديل العميل
     */
    editCustomer: function(customerId) {
        const customer = this.data.customers[customerId];
        if (!customer) {
            this.showMessage('العميل غير موجود', 'error');
            return;
        }

        const modalHTML = `
            <div class="modal fade" id="editCustomerModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-edit me-2"></i>تعديل العميل: ${customer.name}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${this.renderCustomerForm(customer)}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveEditedCustomer()">
                                <i class="fas fa-save me-1"></i>حفظ التعديلات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('editCustomerModal'));
        modal.show();

        // تهيئة الحقول
        setTimeout(() => {
            this.toggleCustomerFields();
        }, 100);

        document.getElementById('editCustomerModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * حفظ تعديلات العميل
     */
    saveEditedCustomer: function() {
        const form = document.getElementById('customerForm');
        const formData = new FormData(form);

        try {
            const customerId = formData.get('customerId');
            const originalCustomer = this.data.customers[customerId];

            if (!originalCustomer) {
                throw new Error('العميل غير موجود');
            }

            // جمع بيانات العميل المحدثة
            const updatedCustomer = {
                ...originalCustomer,
                name: formData.get('name'),
                type: formData.get('type'),
                email: formData.get('email'),
                phone: formData.get('phone'),
                address: formData.get('address'),
                taxNumber: formData.get('taxNumber'),
                website: formData.get('website'),
                contactPerson: formData.get('contactPerson'),
                notes: formData.get('notes'),
                updatedAt: new Date().toISOString()
            };

            // التحقق من صحة البيانات
            if (!updatedCustomer.name.trim()) {
                throw new Error('يرجى إدخال اسم العميل');
            }

            // التحقق من عدم تكرار الاسم (باستثناء العميل الحالي)
            const existingCustomer = Object.values(this.data.customers || {})
                .find(c => c.id !== customerId && c.name.toLowerCase() === updatedCustomer.name.toLowerCase());

            if (existingCustomer) {
                throw new Error('يوجد عميل آخر بنفس الاسم');
            }

            // حفظ العميل المحدث
            this.data.customers[customerId] = updatedCustomer;
            this.saveSalesData();

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('editCustomerModal'));
            modal.hide();

            // عرض رسالة نجاح
            this.showMessage(`تم تحديث بيانات العميل ${updatedCustomer.name} بنجاح`, 'success');

            // تحديث العرض
            this.refreshData();

        } catch (error) {
            this.showMessage('خطأ في تحديث العميل: ' + error.message, 'error');
        }
    },

    /**
     * حذف العميل
     */
    deleteCustomer: function(customerId) {
        const customer = this.data.customers[customerId];
        if (!customer) {
            this.showMessage('العميل غير موجود', 'error');
            return;
        }

        // التحقق من وجود فواتير للعميل
        const customerInvoices = Object.values(this.data.invoices || {})
            .filter(inv => inv.customerId === customerId);

        if (customerInvoices.length > 0) {
            if (!confirm(`تحذير: يوجد ${customerInvoices.length} فاتورة لهذا العميل. هل تريد حذف العميل مع الاحتفاظ بالفواتير؟`)) {
                return;
            }
        }

        if (confirm(`هل أنت متأكد من حذف العميل "${customer.name}"؟`)) {
            delete this.data.customers[customerId];
            this.saveSalesData();
            this.showMessage(`تم حذف العميل ${customer.name} بنجاح`, 'success');
            this.refreshData();
        }
    },

    /**
     * إنشاء فاتورة للعميل
     */
    createInvoiceForCustomer: function(customerId) {
        // إغلاق أي نوافذ مفتوحة
        const openModals = document.querySelectorAll('.modal.show');
        openModals.forEach(modal => {
            const modalInstance = bootstrap.Modal.getInstance(modal);
            if (modalInstance) modalInstance.hide();
        });

        // تبديل إلى قسم الفواتير
        this.switchView('invoices');

        // فتح نافذة فاتورة جديدة مع تحديد العميل
        setTimeout(() => {
            this.showNewInvoiceModal();
            setTimeout(() => {
                const customerSelect = document.querySelector('select[name="customerId"]');
                if (customerSelect) {
                    customerSelect.value = customerId;
                    this.updateCustomerInfo();
                }
            }, 200);
        }, 100);
    },

    /**
     * عرض فواتير العميل
     */
    customerInvoices: function(customerId) {
        const customer = this.data.customers[customerId];
        if (!customer) {
            this.showMessage('العميل غير موجود', 'error');
            return;
        }

        // إغلاق النافذة الحالية
        const openModals = document.querySelectorAll('.modal.show');
        openModals.forEach(modal => {
            const modalInstance = bootstrap.Modal.getInstance(modal);
            if (modalInstance) modalInstance.hide();
        });

        // تبديل إلى قسم الفواتير مع فلترة العميل
        this.switchView('invoices');

        setTimeout(() => {
            const customerFilter = document.getElementById('customerFilter');
            if (customerFilter) {
                customerFilter.value = customerId;
                this.filterInvoices();
            }
        }, 100);
    },

    /**
     * تصدير العملاء CSV
     */
    exportCustomersCSV: function() {
        const customers = Object.values(this.data.customers || {});
        if (customers.length === 0) {
            this.showMessage('لا يوجد عملاء للتصدير', 'error');
            return;
        }

        const csvData = [
            ['الاسم', 'النوع', 'البريد الإلكتروني', 'الهاتف', 'العنوان', 'الرقم الضريبي', 'الموقع الإلكتروني', 'جهة الاتصال', 'تاريخ الإضافة', 'ملاحظات']
        ];

        customers.forEach(customer => {
            csvData.push([
                customer.name,
                customer.type === 'corporate' ? 'شركة' : 'فرد',
                customer.email || '',
                customer.phone || '',
                customer.address || '',
                customer.taxNumber || '',
                customer.website || '',
                customer.contactPerson || '',
                new Date(customer.createdAt).toLocaleDateString('ar-SA'),
                customer.notes || ''
            ]);
        });

        const csvContent = csvData.map(row => row.join(',')).join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `customers_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();

        this.showMessage('تم تصدير العملاء بنجاح', 'success');
    },

    /**
     * استيراد العملاء
     */
    importCustomers: function() {
        this.showMessage('وظيفة استيراد العملاء ستكون متاحة قريباً', 'info');
    },

    /**
     * الحصول على أيقونة المنتج حسب الفئة
     */
    getProductIcon: function(category) {
        const icons = {
            'flights': 'plane',
            'hotels': 'bed',
            'visas': 'passport',
            'tours': 'map-marked-alt',
            'transport': 'bus',
            'insurance': 'shield-alt',
            'other': 'box'
        };
        return icons[category] || 'box';
    },

    /**
     * الحصول على لون الفئة
     */
    getCategoryColor: function(category) {
        const colors = {
            'flights': 'primary',
            'hotels': 'success',
            'visas': 'warning',
            'tours': 'info',
            'transport': 'secondary',
            'insurance': 'danger',
            'other': 'dark'
        };
        return colors[category] || 'secondary';
    },

    /**
     * الحصول على تسمية الفئة
     */
    getCategoryLabel: function(category) {
        const labels = {
            'flights': 'تذاكر طيران',
            'hotels': 'حجوزات فنادق',
            'visas': 'تأشيرات',
            'tours': 'جولات سياحية',
            'transport': 'نقل',
            'insurance': 'تأمين سفر',
            'other': 'أخرى'
        };
        return labels[category] || 'غير محدد';
    },

    /**
     * الحصول على عدد مبيعات المنتج
     */
    getProductSalesCount: function(productId) {
        const invoices = Object.values(this.data.invoices || {});
        let count = 0;

        invoices.forEach(invoice => {
            if (invoice.items) {
                invoice.items.forEach(item => {
                    if (item.productId === productId) {
                        count += item.quantity;
                    }
                });
            }
        });

        return count;
    },

    /**
     * الحصول على إجمالي إيرادات المنتج
     */
    getProductTotalRevenue: function(productId) {
        const invoices = Object.values(this.data.invoices || {});
        let revenue = 0;

        invoices.forEach(invoice => {
            if (invoice.items) {
                invoice.items.forEach(item => {
                    if (item.productId === productId) {
                        revenue += item.total || 0;
                    }
                });
            }
        });

        return revenue;
    },

    /**
     * فلترة المنتجات
     */
    filterProducts: function() {
        const searchTerm = document.getElementById('productSearch')?.value.toLowerCase() || '';
        const categoryFilter = document.getElementById('categoryFilter')?.value || '';
        const priceRangeFilter = document.getElementById('priceRangeFilter')?.value || '';

        let filteredProducts = Object.values(this.data.products || {});

        // فلترة بالبحث
        if (searchTerm) {
            filteredProducts = filteredProducts.filter(product =>
                product.name.toLowerCase().includes(searchTerm) ||
                product.description?.toLowerCase().includes(searchTerm)
            );
        }

        // فلترة بالفئة
        if (categoryFilter) {
            filteredProducts = filteredProducts.filter(product => product.category === categoryFilter);
        }

        // فلترة بنطاق السعر
        if (priceRangeFilter) {
            filteredProducts = filteredProducts.filter(product => {
                const price = product.price || 0;
                switch (priceRangeFilter) {
                    case '0-100': return price < 100;
                    case '100-500': return price >= 100 && price < 500;
                    case '500-1000': return price >= 500 && price < 1000;
                    case '1000-5000': return price >= 1000 && price < 5000;
                    case '5000+': return price >= 5000;
                    default: return true;
                }
            });
        }

        // ترتيب النتائج
        filteredProducts.sort((a, b) => a.name.localeCompare(b.name));

        // تحديث العرض
        this.updateProductsDisplay(filteredProducts);
    },

    /**
     * تحديث عرض المنتجات
     */
    updateProductsDisplay: function(filteredProducts) {
        const container = document.getElementById('productsContainer');
        if (container) {
            container.innerHTML = filteredProducts.length === 0 ?
                '<div class="text-center py-4"><p class="text-muted">لا توجد منتجات تطابق معايير البحث</p></div>' :
                this.renderProductsGrid(filteredProducts);
        }
    },

    /**
     * مسح بحث المنتجات
     */
    clearProductSearch: function() {
        document.getElementById('productSearch').value = '';
        this.filterProducts();
    },

    /**
     * مسح فلاتر المنتجات
     */
    clearProductFilters: function() {
        document.getElementById('productSearch').value = '';
        document.getElementById('categoryFilter').value = '';
        document.getElementById('priceRangeFilter').value = '';
        this.filterProducts();
    },

    /**
     * عرض نافذة منتج جديد
     */
    showNewProductModal: function() {
        const modalHTML = `
            <div class="modal fade" id="newProductModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-plus me-2"></i>إضافة منتج/خدمة جديدة
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${this.renderProductForm()}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveNewProduct()">
                                <i class="fas fa-save me-1"></i>حفظ المنتج
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('newProductModal'));
        modal.show();

        document.getElementById('newProductModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * عرض نموذج المنتج
     */
    renderProductForm: function(product = null) {
        const isEdit = product !== null;

        return `
            <form id="productForm">
                ${isEdit ? `<input type="hidden" name="productId" value="${product.id}">` : ''}

                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label class="form-label">اسم المنتج/الخدمة</label>
                            <input type="text" class="form-control" name="name"
                                   value="${isEdit ? product.name : ''}" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">الفئة</label>
                            <select class="form-control" name="category" required>
                                <option value="">اختر الفئة</option>
                                <option value="flights" ${isEdit && product.category === 'flights' ? 'selected' : ''}>تذاكر طيران</option>
                                <option value="hotels" ${isEdit && product.category === 'hotels' ? 'selected' : ''}>حجوزات فنادق</option>
                                <option value="visas" ${isEdit && product.category === 'visas' ? 'selected' : ''}>تأشيرات</option>
                                <option value="tours" ${isEdit && product.category === 'tours' ? 'selected' : ''}>جولات سياحية</option>
                                <option value="transport" ${isEdit && product.category === 'transport' ? 'selected' : ''}>نقل</option>
                                <option value="insurance" ${isEdit && product.category === 'insurance' ? 'selected' : ''}>تأمين سفر</option>
                                <option value="other" ${isEdit && product.category === 'other' ? 'selected' : ''}>أخرى</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">الوصف</label>
                    <textarea class="form-control" name="description" rows="3"
                              placeholder="وصف تفصيلي للمنتج أو الخدمة">${isEdit ? product.description || '' : ''}</textarea>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">سعر البيع (ر.س)</label>
                            <input type="number" class="form-control" name="price"
                                   value="${isEdit ? product.price || '' : ''}" step="0.01" min="0" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">سعر التكلفة (ر.س) - اختياري</label>
                            <input type="number" class="form-control" name="cost"
                                   value="${isEdit ? product.cost || '' : ''}" step="0.01" min="0">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">الوحدة</label>
                            <input type="text" class="form-control" name="unit"
                                   value="${isEdit ? product.unit || '' : ''}" placeholder="قطعة، ليلة، رحلة، إلخ">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">رمز المنتج (SKU) - اختياري</label>
                            <input type="text" class="form-control" name="sku"
                                   value="${isEdit ? product.sku || '' : ''}" placeholder="PROD-001">
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">ملاحظات</label>
                    <textarea class="form-control" name="notes" rows="3"
                              placeholder="ملاحظات إضافية">${isEdit ? product.notes || '' : ''}</textarea>
                </div>

                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="active"
                           ${!isEdit || product.active !== false ? 'checked' : ''}>
                    <label class="form-check-label">
                        منتج نشط (متاح للبيع)
                    </label>
                </div>
            </form>
        `;
    },

    /**
     * حفظ منتج جديد
     */
    saveNewProduct: function() {
        const form = document.getElementById('productForm');
        const formData = new FormData(form);

        try {
            // جمع بيانات المنتج
            const product = {
                id: this.generateId(),
                name: formData.get('name'),
                category: formData.get('category'),
                description: formData.get('description'),
                price: parseFloat(formData.get('price')) || 0,
                cost: parseFloat(formData.get('cost')) || 0,
                unit: formData.get('unit'),
                sku: formData.get('sku'),
                notes: formData.get('notes'),
                active: formData.has('active'),
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // التحقق من صحة البيانات
            if (!product.name.trim()) {
                throw new Error('يرجى إدخال اسم المنتج');
            }

            if (!product.category) {
                throw new Error('يرجى اختيار فئة المنتج');
            }

            if (product.price <= 0) {
                throw new Error('يرجى إدخال سعر صحيح');
            }

            // التحقق من عدم تكرار الاسم
            const existingProduct = Object.values(this.data.products || {})
                .find(p => p.name.toLowerCase() === product.name.toLowerCase());

            if (existingProduct) {
                throw new Error('يوجد منتج بنفس الاسم مسبقاً');
            }

            // التحقق من عدم تكرار SKU إذا تم إدخاله
            if (product.sku) {
                const existingSKU = Object.values(this.data.products || {})
                    .find(p => p.sku && p.sku.toLowerCase() === product.sku.toLowerCase());

                if (existingSKU) {
                    throw new Error('يوجد منتج بنفس رمز المنتج (SKU) مسبقاً');
                }
            }

            // حفظ المنتج
            this.data.products[product.id] = product;
            this.saveSalesData();

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('newProductModal'));
            modal.hide();

            // عرض رسالة نجاح
            this.showMessage(`تم إضافة المنتج ${product.name} بنجاح`, 'success');

            // تحديث العرض
            this.refreshData();

        } catch (error) {
            this.showMessage('خطأ في حفظ المنتج: ' + error.message, 'error');
        }
    },

    /**
     * عرض تفاصيل المنتج
     */
    viewProduct: function(productId) {
        const product = this.data.products[productId];
        if (!product) {
            this.showMessage('المنتج غير موجود', 'error');
            return;
        }

        const salesCount = this.getProductSalesCount(productId);
        const totalRevenue = this.getProductTotalRevenue(productId);

        const modalHTML = `
            <div class="modal fade" id="viewProductModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-${this.getProductIcon(product.category)} me-2"></i>
                                ${product.name}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">معلومات المنتج</h6>
                                        </div>
                                        <div class="card-body">
                                            <table class="table table-sm">
                                                <tr>
                                                    <td><strong>الاسم:</strong></td>
                                                    <td>${product.name}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>الفئة:</strong></td>
                                                    <td>
                                                        <span class="badge bg-${this.getCategoryColor(product.category)}">
                                                            ${this.getCategoryLabel(product.category)}
                                                        </span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><strong>سعر البيع:</strong></td>
                                                    <td class="text-success fw-bold">${this.formatAmount(product.price)}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>سعر التكلفة:</strong></td>
                                                    <td class="text-info fw-bold">${this.formatAmount(product.cost || 0)}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>هامش الربح:</strong></td>
                                                    <td class="text-warning fw-bold">
                                                        ${this.formatAmount((product.price || 0) - (product.cost || 0))}
                                                        (${product.price > 0 ? Math.round(((product.price - (product.cost || 0)) / product.price) * 100) : 0}%)
                                                    </td>
                                                </tr>
                                                ${product.unit ? `
                                                    <tr>
                                                        <td><strong>الوحدة:</strong></td>
                                                        <td>${product.unit}</td>
                                                    </tr>
                                                ` : ''}
                                                ${product.sku ? `
                                                    <tr>
                                                        <td><strong>رمز المنتج:</strong></td>
                                                        <td>${product.sku}</td>
                                                    </tr>
                                                ` : ''}
                                                <tr>
                                                    <td><strong>الحالة:</strong></td>
                                                    <td>
                                                        <span class="badge bg-${product.active !== false ? 'success' : 'secondary'}">
                                                            ${product.active !== false ? 'نشط' : 'غير نشط'}
                                                        </span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><strong>تاريخ الإضافة:</strong></td>
                                                    <td>${new Date(product.createdAt).toLocaleDateString('ar-SA')}</td>
                                                </tr>
                                            </table>
                                            ${product.description ? `
                                                <div class="mt-3">
                                                    <strong>الوصف:</strong>
                                                    <p class="text-muted">${product.description}</p>
                                                </div>
                                            ` : ''}
                                            ${product.notes ? `
                                                <div class="mt-3">
                                                    <strong>ملاحظات:</strong>
                                                    <p class="text-muted">${product.notes}</p>
                                                </div>
                                            ` : ''}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">إحصائيات المبيعات</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row text-center">
                                                <div class="col-6">
                                                    <div class="border rounded p-3">
                                                        <h4 class="text-primary mb-1">${salesCount}</h4>
                                                        <small class="text-muted">مرات البيع</small>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="border rounded p-3">
                                                        <h4 class="text-success mb-1">${this.formatAmount(totalRevenue)}</h4>
                                                        <small class="text-muted">إجمالي الإيرادات</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row text-center mt-3">
                                                <div class="col-6">
                                                    <div class="border rounded p-3">
                                                        <h4 class="text-info mb-1">${salesCount > 0 ? this.formatAmount(totalRevenue / salesCount) : '0.00 ر.س'}</h4>
                                                        <small class="text-muted">متوسط سعر البيع</small>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="border rounded p-3">
                                                        <h4 class="text-warning mb-1">${this.formatAmount(totalRevenue - (salesCount * (product.cost || 0)))}</h4>
                                                        <small class="text-muted">إجمالي الربح</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-outline-primary" onclick="window.SalesComponent.editProduct('${productId}')">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="window.SalesComponent.productSalesReport('${productId}')">
                                <i class="fas fa-chart-bar me-1"></i>تقرير المبيعات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('viewProductModal'));
        modal.show();

        document.getElementById('viewProductModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * تعديل المنتج
     */
    editProduct: function(productId) {
        const product = this.data.products[productId];
        if (!product) {
            this.showMessage('المنتج غير موجود', 'error');
            return;
        }

        const modalHTML = `
            <div class="modal fade" id="editProductModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-edit me-2"></i>تعديل المنتج: ${product.name}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${this.renderProductForm(product)}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveEditedProduct()">
                                <i class="fas fa-save me-1"></i>حفظ التعديلات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('editProductModal'));
        modal.show();

        document.getElementById('editProductModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * حفظ تعديلات المنتج
     */
    saveEditedProduct: function() {
        const form = document.getElementById('productForm');
        const formData = new FormData(form);

        try {
            const productId = formData.get('productId');
            const originalProduct = this.data.products[productId];

            if (!originalProduct) {
                throw new Error('المنتج غير موجود');
            }

            // جمع بيانات المنتج المحدثة
            const updatedProduct = {
                ...originalProduct,
                name: formData.get('name'),
                category: formData.get('category'),
                description: formData.get('description'),
                price: parseFloat(formData.get('price')) || 0,
                cost: parseFloat(formData.get('cost')) || 0,
                unit: formData.get('unit'),
                sku: formData.get('sku'),
                notes: formData.get('notes'),
                active: formData.has('active'),
                updatedAt: new Date().toISOString()
            };

            // التحقق من صحة البيانات
            if (!updatedProduct.name.trim()) {
                throw new Error('يرجى إدخال اسم المنتج');
            }

            if (!updatedProduct.category) {
                throw new Error('يرجى اختيار فئة المنتج');
            }

            if (updatedProduct.price <= 0) {
                throw new Error('يرجى إدخال سعر صحيح');
            }

            // التحقق من عدم تكرار الاسم (باستثناء المنتج الحالي)
            const existingProduct = Object.values(this.data.products || {})
                .find(p => p.id !== productId && p.name.toLowerCase() === updatedProduct.name.toLowerCase());

            if (existingProduct) {
                throw new Error('يوجد منتج آخر بنفس الاسم');
            }

            // التحقق من عدم تكرار SKU إذا تم إدخاله (باستثناء المنتج الحالي)
            if (updatedProduct.sku) {
                const existingSKU = Object.values(this.data.products || {})
                    .find(p => p.id !== productId && p.sku && p.sku.toLowerCase() === updatedProduct.sku.toLowerCase());

                if (existingSKU) {
                    throw new Error('يوجد منتج آخر بنفس رمز المنتج (SKU)');
                }
            }

            // حفظ المنتج المحدث
            this.data.products[productId] = updatedProduct;
            this.saveSalesData();

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('editProductModal'));
            modal.hide();

            // عرض رسالة نجاح
            this.showMessage(`تم تحديث المنتج ${updatedProduct.name} بنجاح`, 'success');

            // تحديث العرض
            this.refreshData();

        } catch (error) {
            this.showMessage('خطأ في تحديث المنتج: ' + error.message, 'error');
        }
    },

    /**
     * نسخ المنتج
     */
    duplicateProduct: function(productId) {
        const originalProduct = this.data.products[productId];
        if (!originalProduct) {
            this.showMessage('المنتج غير موجود', 'error');
            return;
        }

        const newProduct = {
            ...originalProduct,
            id: this.generateId(),
            name: originalProduct.name + ' (نسخة)',
            sku: originalProduct.sku ? originalProduct.sku + '_COPY' : '',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        this.data.products[newProduct.id] = newProduct;
        this.saveSalesData();

        this.showMessage(`تم نسخ المنتج بنجاح: ${newProduct.name}`, 'success');
        this.refreshData();
    },

    /**
     * حذف المنتج
     */
    deleteProduct: function(productId) {
        const product = this.data.products[productId];
        if (!product) {
            this.showMessage('المنتج غير موجود', 'error');
            return;
        }

        // التحقق من وجود فواتير تحتوي على هذا المنتج
        const invoicesWithProduct = Object.values(this.data.invoices || {})
            .filter(invoice => invoice.items && invoice.items.some(item => item.productId === productId));

        if (invoicesWithProduct.length > 0) {
            if (!confirm(`تحذير: يوجد ${invoicesWithProduct.length} فاتورة تحتوي على هذا المنتج. هل تريد حذف المنتج مع الاحتفاظ بالفواتير؟`)) {
                return;
            }
        }

        if (confirm(`هل أنت متأكد من حذف المنتج "${product.name}"؟`)) {
            delete this.data.products[productId];
            this.saveSalesData();
            this.showMessage(`تم حذف المنتج ${product.name} بنجاح`, 'success');
            this.refreshData();
        }
    },

    /**
     * تقرير مبيعات المنتج
     */
    productSalesReport: function(productId) {
        const product = this.data.products[productId];
        if (!product) {
            this.showMessage('المنتج غير موجود', 'error');
            return;
        }

        // جمع بيانات المبيعات
        const invoices = Object.values(this.data.invoices || {});
        const salesData = [];

        invoices.forEach(invoice => {
            if (invoice.items) {
                invoice.items.forEach(item => {
                    if (item.productId === productId) {
                        salesData.push({
                            invoiceNumber: invoice.number,
                            customerName: this.data.customers[invoice.customerId]?.name || 'عميل غير محدد',
                            date: invoice.date,
                            quantity: item.quantity,
                            price: item.price,
                            total: item.total,
                            status: invoice.status
                        });
                    }
                });
            }
        });

        const modalHTML = `
            <div class="modal fade" id="productSalesReportModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-chart-bar me-2"></i>تقرير مبيعات: ${product.name}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body text-center">
                                            <h4>${salesData.length}</h4>
                                            <small>إجمالي المبيعات</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-success text-white">
                                        <div class="card-body text-center">
                                            <h4>${salesData.reduce((sum, sale) => sum + sale.quantity, 0)}</h4>
                                            <small>إجمالي الكمية</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-info text-white">
                                        <div class="card-body text-center">
                                            <h4>${this.formatAmount(salesData.reduce((sum, sale) => sum + sale.total, 0))}</h4>
                                            <small>إجمالي الإيرادات</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-warning text-white">
                                        <div class="card-body text-center">
                                            <h4>${salesData.length > 0 ? this.formatAmount(salesData.reduce((sum, sale) => sum + sale.total, 0) / salesData.length) : '0.00 ر.س'}</h4>
                                            <small>متوسط قيمة البيع</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            ${salesData.length === 0 ? `
                                <div class="text-center py-4">
                                    <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا توجد مبيعات لهذا المنتج</h5>
                                    <p class="text-muted">لم يتم بيع هذا المنتج في أي فاتورة حتى الآن</p>
                                </div>
                            ` : `
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>رقم الفاتورة</th>
                                                <th>العميل</th>
                                                <th>التاريخ</th>
                                                <th>الكمية</th>
                                                <th>السعر</th>
                                                <th>الإجمالي</th>
                                                <th>حالة الفاتورة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${salesData.map(sale => `
                                                <tr>
                                                    <td><strong>${sale.invoiceNumber}</strong></td>
                                                    <td>${sale.customerName}</td>
                                                    <td>${new Date(sale.date).toLocaleDateString('ar-SA')}</td>
                                                    <td>${sale.quantity}</td>
                                                    <td>${this.formatAmount(sale.price)}</td>
                                                    <td>${this.formatAmount(sale.total)}</td>
                                                    <td>
                                                        <span class="badge bg-${this.getInvoiceStatusColor(sale.status)}">
                                                            ${this.getInvoiceStatusLabel(sale.status)}
                                                        </span>
                                                    </td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            `}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-outline-primary" onclick="window.SalesComponent.exportProductSalesReport('${productId}')">
                                <i class="fas fa-download me-1"></i>تصدير التقرير
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('productSalesReportModal'));
        modal.show();

        document.getElementById('productSalesReportModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * تصدير تقرير مبيعات المنتج
     */
    exportProductSalesReport: function(productId) {
        const product = this.data.products[productId];
        const invoices = Object.values(this.data.invoices || {});
        const salesData = [];

        invoices.forEach(invoice => {
            if (invoice.items) {
                invoice.items.forEach(item => {
                    if (item.productId === productId) {
                        salesData.push([
                            invoice.number,
                            this.data.customers[invoice.customerId]?.name || 'عميل غير محدد',
                            invoice.date,
                            item.quantity,
                            item.price,
                            item.total,
                            this.getInvoiceStatusLabel(invoice.status)
                        ]);
                    }
                });
            }
        });

        if (salesData.length === 0) {
            this.showMessage('لا توجد مبيعات لتصديرها', 'error');
            return;
        }

        const csvData = [
            ['رقم الفاتورة', 'العميل', 'التاريخ', 'الكمية', 'السعر', 'الإجمالي', 'حالة الفاتورة'],
            ...salesData
        ];

        const csvContent = csvData.map(row => row.join(',')).join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `product_sales_${product.name}_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();

        this.showMessage('تم تصدير تقرير المبيعات بنجاح', 'success');
    },

    /**
     * تصدير المنتجات CSV
     */
    exportProductsCSV: function() {
        const products = Object.values(this.data.products || {});
        if (products.length === 0) {
            this.showMessage('لا توجد منتجات للتصدير', 'error');
            return;
        }

        const csvData = [
            ['الاسم', 'الفئة', 'الوصف', 'سعر البيع', 'سعر التكلفة', 'هامش الربح', 'الوحدة', 'رمز المنتج', 'الحالة', 'تاريخ الإضافة', 'ملاحظات']
        ];

        products.forEach(product => {
            const profitMargin = (product.price || 0) - (product.cost || 0);
            csvData.push([
                product.name,
                this.getCategoryLabel(product.category),
                product.description || '',
                product.price || 0,
                product.cost || 0,
                profitMargin,
                product.unit || '',
                product.sku || '',
                product.active !== false ? 'نشط' : 'غير نشط',
                new Date(product.createdAt).toLocaleDateString('ar-SA'),
                product.notes || ''
            ]);
        });

        const csvContent = csvData.map(row => row.join(',')).join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `products_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();

        this.showMessage('تم تصدير المنتجات بنجاح', 'success');
    },

    /**
     * استيراد المنتجات
     */
    importProducts: function() {
        this.showMessage('وظيفة استيراد المنتجات ستكون متاحة قريباً', 'info');
    },

    /**
     * تحديث التقارير
     */
    updateReports: function() {
        if (this.data.currentView === 'reports') {
            this.calculateReportsData();
            this.updateReportsDisplay();
        }
    },

    /**
     * حساب بيانات التقارير
     */
    calculateReportsData: function() {
        const fromDate = document.getElementById('reportsFromDate')?.value;
        const toDate = document.getElementById('reportsToDate')?.value;

        let filteredInvoices = Object.values(this.data.invoices || {});

        // فلترة بالتاريخ
        if (fromDate) {
            filteredInvoices = filteredInvoices.filter(inv => inv.date >= fromDate);
        }
        if (toDate) {
            filteredInvoices = filteredInvoices.filter(inv => inv.date <= toDate);
        }

        // حساب الإحصائيات الرئيسية
        const totalInvoices = filteredInvoices.length;
        const totalRevenue = filteredInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0);
        const paidInvoices = filteredInvoices.filter(inv => inv.status === 'paid');
        const activeCustomers = new Set(filteredInvoices.map(inv => inv.customerId)).size;
        const averageInvoice = totalInvoices > 0 ? totalRevenue / totalInvoices : 0;

        // تحديث العرض
        document.getElementById('totalInvoicesReport').textContent = totalInvoices;
        document.getElementById('totalRevenueReport').textContent = this.formatAmount(totalRevenue);
        document.getElementById('activeCustomersReport').textContent = activeCustomers;
        document.getElementById('averageInvoiceReport').textContent = this.formatAmount(averageInvoice);

        // تحديث التقارير التفصيلية
        this.updateSalesReport(filteredInvoices);
        this.updateCustomersReport(filteredInvoices);
        this.updateProductsReport(filteredInvoices);
        this.updateProfitReport(filteredInvoices);
    },

    /**
     * تحديث تقرير المبيعات
     */
    updateSalesReport: function(invoices) {
        const salesByStatus = {};
        const salesByMonth = {};

        invoices.forEach(invoice => {
            // حسب الحالة
            salesByStatus[invoice.status] = (salesByStatus[invoice.status] || 0) + (invoice.total || 0);

            // حسب الشهر
            const month = new Date(invoice.date).toLocaleDateString('ar-SA', { year: 'numeric', month: 'long' });
            salesByMonth[month] = (salesByMonth[month] || 0) + (invoice.total || 0);
        });

        const content = `
            <div class="mb-3">
                <h6>المبيعات حسب الحالة:</h6>
                ${Object.entries(salesByStatus).map(([status, amount]) => `
                    <div class="d-flex justify-content-between">
                        <span class="badge bg-${this.getInvoiceStatusColor(status)}">${this.getInvoiceStatusLabel(status)}</span>
                        <strong>${this.formatAmount(amount)}</strong>
                    </div>
                `).join('')}
            </div>
            <div>
                <h6>المبيعات الشهرية:</h6>
                ${Object.entries(salesByMonth).slice(0, 6).map(([month, amount]) => `
                    <div class="d-flex justify-content-between">
                        <span>${month}</span>
                        <strong>${this.formatAmount(amount)}</strong>
                    </div>
                `).join('')}
            </div>
        `;

        document.getElementById('salesReportContent').innerHTML = content;
    },

    /**
     * تحديث تقرير العملاء
     */
    updateCustomersReport: function(invoices) {
        const customerSales = {};

        invoices.forEach(invoice => {
            const customerId = invoice.customerId;
            if (!customerSales[customerId]) {
                customerSales[customerId] = {
                    name: this.data.customers[customerId]?.name || 'عميل غير محدد',
                    total: 0,
                    count: 0
                };
            }
            customerSales[customerId].total += invoice.total || 0;
            customerSales[customerId].count += 1;
        });

        const topCustomers = Object.values(customerSales)
            .sort((a, b) => b.total - a.total)
            .slice(0, 5);

        const content = `
            <div class="mb-3">
                <h6>أفضل العملاء:</h6>
                ${topCustomers.map((customer, index) => `
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <span class="badge bg-primary">${index + 1}</span>
                            <span class="ms-2">${customer.name}</span>
                        </div>
                        <div class="text-end">
                            <div class="fw-bold">${this.formatAmount(customer.total)}</div>
                            <small class="text-muted">${customer.count} فاتورة</small>
                        </div>
                    </div>
                `).join('')}
            </div>
            <div class="text-center">
                <small class="text-muted">إجمالي العملاء النشطين: ${Object.keys(customerSales).length}</small>
            </div>
        `;

        document.getElementById('customersReportContent').innerHTML = content;
    },

    /**
     * تحديث تقرير المنتجات
     */
    updateProductsReport: function(invoices) {
        const productSales = {};

        invoices.forEach(invoice => {
            if (invoice.items) {
                invoice.items.forEach(item => {
                    const productId = item.productId;
                    if (!productSales[productId]) {
                        productSales[productId] = {
                            name: this.data.products[productId]?.name || 'منتج غير محدد',
                            quantity: 0,
                            revenue: 0
                        };
                    }
                    productSales[productId].quantity += item.quantity || 0;
                    productSales[productId].revenue += item.total || 0;
                });
            }
        });

        const topProducts = Object.values(productSales)
            .sort((a, b) => b.revenue - a.revenue)
            .slice(0, 5);

        const content = `
            <div class="mb-3">
                <h6>أكثر المنتجات مبيعاً:</h6>
                ${topProducts.map((product, index) => `
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <span class="badge bg-success">${index + 1}</span>
                            <span class="ms-2">${product.name}</span>
                        </div>
                        <div class="text-end">
                            <div class="fw-bold">${this.formatAmount(product.revenue)}</div>
                            <small class="text-muted">${product.quantity} وحدة</small>
                        </div>
                    </div>
                `).join('')}
            </div>
            <div class="text-center">
                <small class="text-muted">إجمالي المنتجات المباعة: ${Object.keys(productSales).length}</small>
            </div>
        `;

        document.getElementById('productsReportContent').innerHTML = content;
    },

    /**
     * تحديث تقرير الأرباح
     */
    updateProfitReport: function(invoices) {
        let totalRevenue = 0;
        let totalCost = 0;
        let totalProfit = 0;

        invoices.forEach(invoice => {
            if (invoice.items) {
                invoice.items.forEach(item => {
                    const product = this.data.products[item.productId];
                    const revenue = item.total || 0;
                    const cost = (product?.cost || 0) * (item.quantity || 0);

                    totalRevenue += revenue;
                    totalCost += cost;
                    totalProfit += (revenue - cost);
                });
            }
        });

        const profitMargin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0;

        const content = `
            <div class="row text-center">
                <div class="col-6">
                    <div class="border rounded p-2 mb-2">
                        <div class="fw-bold text-success">${this.formatAmount(totalRevenue)}</div>
                        <small class="text-muted">إجمالي الإيرادات</small>
                    </div>
                </div>
                <div class="col-6">
                    <div class="border rounded p-2 mb-2">
                        <div class="fw-bold text-danger">${this.formatAmount(totalCost)}</div>
                        <small class="text-muted">إجمالي التكاليف</small>
                    </div>
                </div>
                <div class="col-6">
                    <div class="border rounded p-2 mb-2">
                        <div class="fw-bold text-warning">${this.formatAmount(totalProfit)}</div>
                        <small class="text-muted">صافي الربح</small>
                    </div>
                </div>
                <div class="col-6">
                    <div class="border rounded p-2 mb-2">
                        <div class="fw-bold text-info">${profitMargin.toFixed(1)}%</div>
                        <small class="text-muted">هامش الربح</small>
                    </div>
                </div>
            </div>
        `;

        document.getElementById('profitReportContent').innerHTML = content;
    },

    /**
     * تعيين فترة سريعة
     */
    setQuickPeriod: function() {
        const period = document.getElementById('quickPeriod').value;
        const today = new Date();
        let fromDate, toDate;

        switch (period) {
            case 'today':
                fromDate = toDate = today.toISOString().split('T')[0];
                break;
            case 'week':
                const weekStart = new Date(today.setDate(today.getDate() - today.getDay()));
                fromDate = weekStart.toISOString().split('T')[0];
                toDate = new Date().toISOString().split('T')[0];
                break;
            case 'month':
                fromDate = new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0];
                toDate = new Date().toISOString().split('T')[0];
                break;
            case 'quarter':
                const quarter = Math.floor(today.getMonth() / 3);
                fromDate = new Date(today.getFullYear(), quarter * 3, 1).toISOString().split('T')[0];
                toDate = new Date().toISOString().split('T')[0];
                break;
            case 'year':
                fromDate = new Date(today.getFullYear(), 0, 1).toISOString().split('T')[0];
                toDate = new Date().toISOString().split('T')[0];
                break;
            case 'last30':
                fromDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
                toDate = new Date().toISOString().split('T')[0];
                break;
            case 'last90':
                fromDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
                toDate = new Date().toISOString().split('T')[0];
                break;
            default:
                return;
        }

        document.getElementById('reportsFromDate').value = fromDate;
        document.getElementById('reportsToDate').value = toDate;
        this.updateReports();
    },

    /**
     * مسح فلاتر التقارير
     */
    clearReportsFilters: function() {
        document.getElementById('reportsFromDate').value = '';
        document.getElementById('reportsToDate').value = '';
        document.getElementById('quickPeriod').value = '';
        this.updateReports();
    },

    /**
     * تحديث عرض التقارير
     */
    updateReportsDisplay: function() {
        // تحديث التقارير عند تغيير الفلاتر
        setTimeout(() => {
            this.calculateReportsData();
        }, 100);
    },

    /**
     * تحديث التقارير
     */
    refreshReports: function() {
        this.updateReports();
        this.showMessage('تم تحديث التقارير بنجاح', 'success');
    },

    /**
     * تصدير تقرير المبيعات
     */
    exportSalesReport: function() {
        const fromDate = document.getElementById('reportsFromDate')?.value;
        const toDate = document.getElementById('reportsToDate')?.value;

        let filteredInvoices = Object.values(this.data.invoices || {});

        if (fromDate) filteredInvoices = filteredInvoices.filter(inv => inv.date >= fromDate);
        if (toDate) filteredInvoices = filteredInvoices.filter(inv => inv.date <= toDate);

        const csvData = [
            ['رقم الفاتورة', 'العميل', 'التاريخ', 'المبلغ', 'الحالة', 'المرجع']
        ];

        filteredInvoices.forEach(invoice => {
            csvData.push([
                invoice.number,
                this.data.customers[invoice.customerId]?.name || 'عميل غير محدد',
                invoice.date,
                invoice.total,
                this.getInvoiceStatusLabel(invoice.status),
                invoice.reference || ''
            ]);
        });

        this.downloadCSV(csvData, 'sales_report');
    },

    /**
     * تصدير تقرير العملاء
     */
    exportCustomersReport: function() {
        const customers = Object.values(this.data.customers || {});
        const csvData = [
            ['اسم العميل', 'النوع', 'عدد الفواتير', 'إجمالي المبيعات', 'متوسط الفاتورة', 'آخر فاتورة']
        ];

        customers.forEach(customer => {
            const customerInvoices = Object.values(this.data.invoices || {})
                .filter(inv => inv.customerId === customer.id);

            const totalSales = customerInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0);
            const avgInvoice = customerInvoices.length > 0 ? totalSales / customerInvoices.length : 0;
            const lastInvoice = customerInvoices.length > 0 ?
                Math.max(...customerInvoices.map(inv => new Date(inv.date).getTime())) : null;

            csvData.push([
                customer.name,
                customer.type === 'corporate' ? 'شركة' : 'فرد',
                customerInvoices.length,
                totalSales,
                avgInvoice,
                lastInvoice ? new Date(lastInvoice).toLocaleDateString('ar-SA') : 'لا يوجد'
            ]);
        });

        this.downloadCSV(csvData, 'customers_report');
    },

    /**
     * تصدير تقرير المنتجات
     */
    exportProductsReport: function() {
        const products = Object.values(this.data.products || {});
        const csvData = [
            ['اسم المنتج', 'الفئة', 'السعر', 'التكلفة', 'هامش الربح', 'مرات البيع', 'إجمالي الإيرادات']
        ];

        products.forEach(product => {
            const salesCount = this.getProductSalesCount(product.id);
            const totalRevenue = this.getProductTotalRevenue(product.id);
            const profitMargin = (product.price || 0) - (product.cost || 0);

            csvData.push([
                product.name,
                this.getCategoryLabel(product.category),
                product.price || 0,
                product.cost || 0,
                profitMargin,
                salesCount,
                totalRevenue
            ]);
        });

        this.downloadCSV(csvData, 'products_report');
    },

    /**
     * تصدير تقرير الأرباح
     */
    exportProfitReport: function() {
        const fromDate = document.getElementById('reportsFromDate')?.value;
        const toDate = document.getElementById('reportsToDate')?.value;

        let filteredInvoices = Object.values(this.data.invoices || {});

        if (fromDate) filteredInvoices = filteredInvoices.filter(inv => inv.date >= fromDate);
        if (toDate) filteredInvoices = filteredInvoices.filter(inv => inv.date <= toDate);

        const csvData = [
            ['رقم الفاتورة', 'التاريخ', 'الإيرادات', 'التكاليف', 'الربح', 'هامش الربح %']
        ];

        filteredInvoices.forEach(invoice => {
            let revenue = 0;
            let cost = 0;

            if (invoice.items) {
                invoice.items.forEach(item => {
                    const product = this.data.products[item.productId];
                    revenue += item.total || 0;
                    cost += (product?.cost || 0) * (item.quantity || 0);
                });
            }

            const profit = revenue - cost;
            const profitMargin = revenue > 0 ? (profit / revenue) * 100 : 0;

            csvData.push([
                invoice.number,
                invoice.date,
                revenue,
                cost,
                profit,
                profitMargin.toFixed(2)
            ]);
        });

        this.downloadCSV(csvData, 'profit_report');
    },

    /**
     * تصدير جميع التقارير
     */
    exportAllReports: function() {
        this.exportSalesReport();
        setTimeout(() => this.exportCustomersReport(), 500);
        setTimeout(() => this.exportProductsReport(), 1000);
        setTimeout(() => this.exportProfitReport(), 1500);

        this.showMessage('جاري تصدير جميع التقارير...', 'info');
    },

    /**
     * تحميل ملف CSV
     */
    downloadCSV: function(data, filename) {
        const csvContent = data.map(row => row.join(',')).join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `${filename}_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();
    },

    /**
     * عرض تقرير حالات الدفع
     */
    showPaymentStatusReport: function() {
        const invoices = Object.values(this.data.invoices || {});
        const statusCounts = {};
        const statusAmounts = {};

        invoices.forEach(invoice => {
            const status = invoice.status;
            statusCounts[status] = (statusCounts[status] || 0) + 1;
            statusAmounts[status] = (statusAmounts[status] || 0) + (invoice.total || 0);
        });

        const modalHTML = `
            <div class="modal fade" id="paymentStatusModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-credit-card me-2"></i>تقرير حالات الدفع
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                ${Object.entries(statusCounts).map(([status, count]) => `
                                    <div class="col-md-6 mb-3">
                                        <div class="card">
                                            <div class="card-body text-center">
                                                <span class="badge bg-${this.getInvoiceStatusColor(status)} fs-6 mb-2">
                                                    ${this.getInvoiceStatusLabel(status)}
                                                </span>
                                                <h4>${count}</h4>
                                                <p class="mb-0">فاتورة</p>
                                                <small class="text-muted">${this.formatAmount(statusAmounts[status] || 0)}</small>
                                            </div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('paymentStatusModal'));
        modal.show();

        document.getElementById('paymentStatusModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * عرض تقرير الاتجاهات الشهرية
     */
    showMonthlyTrendsReport: function() {
        const invoices = Object.values(this.data.invoices || {});
        const monthlyData = {};

        invoices.forEach(invoice => {
            const month = new Date(invoice.date).toLocaleDateString('ar-SA', { year: 'numeric', month: 'long' });
            if (!monthlyData[month]) {
                monthlyData[month] = { count: 0, amount: 0 };
            }
            monthlyData[month].count += 1;
            monthlyData[month].amount += invoice.total || 0;
        });

        const sortedMonths = Object.entries(monthlyData)
            .sort(([a], [b]) => new Date(a) - new Date(b))
            .slice(-12); // آخر 12 شهر

        const modalHTML = `
            <div class="modal fade" id="monthlyTrendsModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-calendar-alt me-2"></i>الاتجاهات الشهرية
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الشهر</th>
                                            <th>عدد الفواتير</th>
                                            <th>إجمالي المبيعات</th>
                                            <th>متوسط الفاتورة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${sortedMonths.map(([month, data]) => `
                                            <tr>
                                                <td><strong>${month}</strong></td>
                                                <td>${data.count}</td>
                                                <td>${this.formatAmount(data.amount)}</td>
                                                <td>${this.formatAmount(data.count > 0 ? data.amount / data.count : 0)}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('monthlyTrendsModal'));
        modal.show();

        document.getElementById('monthlyTrendsModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * عرض تقرير أفضل العملاء
     */
    showTopCustomersReport: function() {
        const customers = Object.values(this.data.customers || {});
        const customerData = customers.map(customer => {
            const customerInvoices = Object.values(this.data.invoices || {})
                .filter(inv => inv.customerId === customer.id);

            const totalSales = customerInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0);
            const invoiceCount = customerInvoices.length;
            const avgInvoice = invoiceCount > 0 ? totalSales / invoiceCount : 0;

            return {
                ...customer,
                totalSales,
                invoiceCount,
                avgInvoice
            };
        }).sort((a, b) => b.totalSales - a.totalSales).slice(0, 20);

        const modalHTML = `
            <div class="modal fade" id="topCustomersModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-star me-2"></i>أفضل العملاء
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الترتيب</th>
                                            <th>اسم العميل</th>
                                            <th>النوع</th>
                                            <th>عدد الفواتير</th>
                                            <th>إجمالي المبيعات</th>
                                            <th>متوسط الفاتورة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${customerData.map((customer, index) => `
                                            <tr>
                                                <td>
                                                    <span class="badge bg-${index < 3 ? 'warning' : 'secondary'}">
                                                        ${index + 1}
                                                    </span>
                                                </td>
                                                <td><strong>${customer.name}</strong></td>
                                                <td>
                                                    <span class="badge bg-${customer.type === 'corporate' ? 'info' : 'primary'}">
                                                        ${customer.type === 'corporate' ? 'شركة' : 'فرد'}
                                                    </span>
                                                </td>
                                                <td>${customer.invoiceCount}</td>
                                                <td class="fw-bold text-success">${this.formatAmount(customer.totalSales)}</td>
                                                <td>${this.formatAmount(customer.avgInvoice)}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('topCustomersModal'));
        modal.show();

        document.getElementById('topCustomersModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * حفظ إعدادات الشركة
     */
    saveCompanySettings: function() {
        const form = document.getElementById('companySettingsForm');
        const formData = new FormData(form);

        try {
            this.data.settings.companyName = formData.get('companyName');
            this.data.settings.companyAddress = formData.get('companyAddress');
            this.data.settings.companyTaxNumber = formData.get('companyTaxNumber');
            this.data.settings.companyEmail = formData.get('companyEmail');
            this.data.settings.companyPhone = formData.get('companyPhone');
            this.data.settings.companyWebsite = formData.get('companyWebsite');

            this.saveSalesData();
            this.showMessage('تم حفظ معلومات الشركة بنجاح', 'success');
        } catch (error) {
            this.showMessage('خطأ في حفظ معلومات الشركة: ' + error.message, 'error');
        }
    },

    /**
     * حفظ إعدادات الفواتير
     */
    saveInvoiceSettings: function() {
        const form = document.getElementById('invoiceSettingsForm');
        const formData = new FormData(form);

        try {
            this.data.settings.invoicePrefix = formData.get('invoicePrefix');
            this.data.settings.nextInvoiceNumber = parseInt(formData.get('nextInvoiceNumber'));
            this.data.settings.taxRate = parseFloat(formData.get('taxRate')) / 100;
            this.data.settings.currency = formData.get('currency');
            this.data.settings.defaultDueDays = parseInt(formData.get('defaultDueDays'));
            this.data.settings.autoCalculateTax = formData.has('autoCalculateTax');

            this.saveSalesData();
            this.showMessage('تم حفظ إعدادات الفواتير بنجاح', 'success');
        } catch (error) {
            this.showMessage('خطأ في حفظ إعدادات الفواتير: ' + error.message, 'error');
        }
    },

    /**
     * حفظ إعدادات النظام
     */
    saveSystemSettings: function() {
        const form = document.getElementById('systemSettingsForm');
        const formData = new FormData(form);

        try {
            this.data.settings.language = formData.get('language');
            this.data.settings.timezone = formData.get('timezone');
            this.data.settings.dateFormat = formData.get('dateFormat');
            this.data.settings.autoSave = formData.has('autoSave');
            this.data.settings.showNotifications = formData.has('showNotifications');
            this.data.settings.enableBackup = formData.has('enableBackup');

            this.saveSalesData();
            this.showMessage('تم حفظ إعدادات النظام بنجاح', 'success');
        } catch (error) {
            this.showMessage('خطأ في حفظ إعدادات النظام: ' + error.message, 'error');
        }
    },

    /**
     * حفظ جميع الإعدادات
     */
    saveAllSettings: function() {
        this.saveCompanySettings();
        this.saveInvoiceSettings();
        this.saveSystemSettings();
        this.showMessage('تم حفظ جميع الإعدادات بنجاح', 'success');
    },

    /**
     * إعادة تعيين الإعدادات
     */
    resetSettings: function() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟')) {
            this.data.settings = {
                taxRate: 0.15,
                currency: 'SAR',
                invoicePrefix: 'INV-',
                nextInvoiceNumber: 1,
                companyName: 'قمة الوعد للسفريات',
                companyAddress: 'المملكة العربية السعودية',
                companyTaxNumber: '*********',
                companyEmail: '<EMAIL>',
                companyPhone: '+966501234567',
                language: 'ar',
                timezone: 'Asia/Riyadh',
                dateFormat: 'ar-SA',
                autoSave: true,
                showNotifications: true,
                enableBackup: true,
                autoCalculateTax: true,
                defaultDueDays: 30
            };

            this.saveSalesData();
            this.refreshData();
            this.showMessage('تم إعادة تعيين الإعدادات بنجاح', 'success');
        }
    },

    /**
     * تصدير الإعدادات
     */
    exportSettings: function() {
        const settingsData = JSON.stringify(this.data.settings, null, 2);
        const blob = new Blob([settingsData], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `settings_${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        this.showMessage('تم تصدير الإعدادات بنجاح', 'success');
    },

    /**
     * إنشاء نسخة احتياطية
     */
    createBackup: function() {
        const backupData = {
            version: '1.0',
            timestamp: new Date().toISOString(),
            data: this.data
        };

        const backupJson = JSON.stringify(backupData, null, 2);
        const blob = new Blob([backupJson], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `backup_${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        this.showMessage('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
    },

    /**
     * عرض نافذة استعادة النسخة الاحتياطية
     */
    showRestoreBackup: function() {
        const modalHTML = `
            <div class="modal fade" id="restoreBackupModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-upload me-2"></i>استعادة نسخة احتياطية
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>تحذير:</strong> سيتم استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية.
                            </div>
                            <div class="mb-3">
                                <label class="form-label">اختر ملف النسخة الاحتياطية</label>
                                <input type="file" class="form-control" id="backupFile" accept=".json">
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-warning" onclick="window.SalesComponent.restoreBackup()">
                                <i class="fas fa-upload me-1"></i>استعادة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('restoreBackupModal'));
        modal.show();

        document.getElementById('restoreBackupModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * استعادة النسخة الاحتياطية
     */
    restoreBackup: function() {
        const fileInput = document.getElementById('backupFile');
        const file = fileInput.files[0];

        if (!file) {
            this.showMessage('يرجى اختيار ملف النسخة الاحتياطية', 'error');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const backupData = JSON.parse(e.target.result);

                if (!backupData.data) {
                    throw new Error('ملف النسخة الاحتياطية غير صحيح');
                }

                this.data = backupData.data;
                this.saveSalesData();

                const modal = bootstrap.Modal.getInstance(document.getElementById('restoreBackupModal'));
                modal.hide();

                this.showMessage('تم استعادة النسخة الاحتياطية بنجاح', 'success');
                this.refreshData();

            } catch (error) {
                this.showMessage('خطأ في استعادة النسخة الاحتياطية: ' + error.message, 'error');
            }
        };

        reader.readAsText(file);
    },

    /**
     * مسح جميع البيانات
     */
    clearAllData: function() {
        if (confirm('هل أنت متأكد من مسح جميع البيانات؟ لا يمكن التراجع عن هذا الإجراء!')) {
            if (confirm('تأكيد أخير: سيتم حذف جميع الفواتير والعملاء والمنتجات نهائياً!')) {
                localStorage.removeItem('salesData');
                this.data = {
                    currentView: 'dashboard',
                    customers: {},
                    products: {},
                    invoices: {},
                    deletedInvoices: {},
                    salesTransactions: {},
                    settings: {
                        taxRate: 0.15,
                        currency: 'SAR',
                        invoicePrefix: 'INV-',
                        nextInvoiceNumber: 1,
                        companyName: 'قمة الوعد للسفريات',
                        companyAddress: 'المملكة العربية السعودية',
                        companyTaxNumber: '*********',
                        companyEmail: '<EMAIL>',
                        companyPhone: '+966501234567'
                    }
                };

                this.saveSalesData();
                this.refreshData();
                this.showMessage('تم مسح جميع البيانات بنجاح', 'success');
            }
        }
    },

    /**
     * إعادة تحميل البيانات التجريبية
     */
    resetToSampleData: function() {
        if (confirm('هل تريد إعادة تحميل البيانات التجريبية؟ سيتم استبدال البيانات الحالية.')) {
            this.createSampleData();
            this.refreshData();
            this.showMessage('تم إعادة تحميل البيانات التجريبية بنجاح', 'success');
        }
    },

    /**
     * حفظ تصميم الفاتورة
     */
    saveInvoiceDesign: function() {
        const form = document.getElementById('invoiceDesignForm');
        const formData = new FormData(form);

        try {
            this.data.settings.selectedTemplate = formData.get('selectedTemplate');
            this.data.settings.primaryColor = formData.get('primaryColor');
            this.data.settings.secondaryColor = formData.get('secondaryColor');
            this.data.settings.fontSize = formData.get('fontSize');
            this.data.settings.showLogo = formData.has('showLogo');
            this.data.settings.showWatermark = formData.has('showWatermark');
            this.data.settings.showBorder = formData.has('showBorder');

            this.saveSalesData();
            this.showMessage('تم حفظ تصميم الفاتورة بنجاح', 'success');
        } catch (error) {
            this.showMessage('خطأ في حفظ تصميم الفاتورة: ' + error.message, 'error');
        }
    },

    /**
     * تحديث معاينة اللون الأساسي
     */
    updateColorPreview: function() {
        const colorInput = document.querySelector('input[name="primaryColor"]');
        const hexInput = document.querySelector('input[name="primaryColorHex"]');
        if (colorInput && hexInput) {
            hexInput.value = colorInput.value;
        }
    },

    /**
     * تحديث اللون من الكود السادس عشر
     */
    updateColorFromHex: function() {
        const colorInput = document.querySelector('input[name="primaryColor"]');
        const hexInput = document.querySelector('input[name="primaryColorHex"]');
        if (colorInput && hexInput) {
            colorInput.value = hexInput.value;
        }
    },

    /**
     * تحديث معاينة اللون الثانوي
     */
    updateSecondaryColorPreview: function() {
        const colorInput = document.querySelector('input[name="secondaryColor"]');
        const hexInput = document.querySelector('input[name="secondaryColorHex"]');
        if (colorInput && hexInput) {
            hexInput.value = colorInput.value;
        }
    },

    /**
     * تحديث اللون الثانوي من الكود السادس عشر
     */
    updateSecondaryColorFromHex: function() {
        const colorInput = document.querySelector('input[name="secondaryColor"]');
        const hexInput = document.querySelector('input[name="secondaryColorHex"]');
        if (colorInput && hexInput) {
            colorInput.value = hexInput.value;
        }
    },

    /**
     * معاينة قالب الفاتورة
     */
    previewInvoiceTemplate: function() {
        const selectedTemplate = document.querySelector('select[name="selectedTemplate"]').value;
        this.showMessage(`تم اختيار القالب: ${this.getTemplateLabel(selectedTemplate)}`, 'info');
    },

    /**
     * الحصول على تسمية القالب
     */
    getTemplateLabel: function(template) {
        const labels = {
            'classic': 'كلاسيكي',
            'modern': 'عصري',
            'minimal': 'بسيط',
            'professional': 'احترافي',
            'colorful': 'ملون'
        };
        return labels[template] || 'غير محدد';
    },

    /**
     * معاينة تصميم الفاتورة
     */
    previewInvoiceDesign: function() {
        // إنشاء فاتورة تجريبية للمعاينة
        const sampleInvoice = {
            id: 'preview',
            number: 'INV-PREVIEW',
            date: new Date().toISOString().split('T')[0],
            dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            customerId: Object.keys(this.data.customers)[0] || 'sample',
            items: [
                {
                    productId: Object.keys(this.data.products)[0] || 'sample',
                    name: 'خدمة تجريبية',
                    quantity: 1,
                    price: 1000,
                    total: 1000
                }
            ],
            subtotal: 1000,
            tax: 150,
            total: 1150,
            status: 'draft',
            notes: 'هذه فاتورة تجريبية لمعاينة التصميم'
        };

        // تطبيق الإعدادات الحالية مؤقتاً
        const form = document.getElementById('invoiceDesignForm');
        const formData = new FormData(form);

        const tempSettings = {
            selectedTemplate: formData.get('selectedTemplate'),
            primaryColor: formData.get('primaryColor'),
            secondaryColor: formData.get('secondaryColor'),
            fontSize: formData.get('fontSize'),
            showLogo: formData.has('showLogo'),
            showWatermark: formData.has('showWatermark'),
            showBorder: formData.has('showBorder')
        };

        // عرض معاينة الفاتورة
        this.showInvoicePreview(sampleInvoice, tempSettings);
    },

    /**
     * عرض معاينة الفاتورة
     */
    showInvoicePreview: function(invoice, designSettings) {
        const customer = this.data.customers[invoice.customerId] || {
            name: 'عميل تجريبي',
            email: '<EMAIL>',
            phone: '+966501234567',
            address: 'الرياض، المملكة العربية السعودية'
        };

        const modalHTML = `
            <div class="modal fade" id="invoicePreviewModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-eye me-2"></i>معاينة تصميم الفاتورة
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                هذه معاينة للتصميم المحدد. يمكنك تعديل الإعدادات وإعادة المعاينة.
                            </div>
                            <div id="invoicePreviewContent">
                                ${this.generateInvoiceHTML(invoice, customer, designSettings)}
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" onclick="window.SalesComponent.saveInvoiceDesign(); bootstrap.Modal.getInstance(document.getElementById('invoicePreviewModal')).hide();">
                                <i class="fas fa-save me-1"></i>حفظ هذا التصميم
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('invoicePreviewModal'));
        modal.show();

        document.getElementById('invoicePreviewModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * إنشاء HTML الفاتورة مع التصميم المحدد
     */
    generateInvoiceHTML: function(invoice, customer, designSettings) {
        const settings = designSettings || this.data.settings;
        const template = settings.selectedTemplate || 'classic';
        const primaryColor = settings.primaryColor || '#007bff';
        const secondaryColor = settings.secondaryColor || '#6c757d';
        const fontSize = settings.fontSize || 'medium';
        const showLogo = settings.showLogo !== false;
        const showWatermark = settings.showWatermark === true;
        const showBorder = settings.showBorder !== false;

        // تحديد أحجام الخط
        const fontSizes = {
            small: { base: '12px', title: '18px', header: '14px' },
            medium: { base: '14px', title: '22px', header: '16px' },
            large: { base: '16px', title: '26px', header: '18px' }
        };

        const currentFontSize = fontSizes[fontSize];

        // إنشاء CSS مخصص للتصميم
        const customCSS = `
            <style>
                .invoice-preview {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    font-size: ${currentFontSize.base};
                    color: #333;
                    ${showBorder ? `border: 2px solid ${primaryColor}; padding: 20px;` : ''}
                    ${showWatermark ? `background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y="50" font-size="20" fill="${secondaryColor}" opacity="0.1">PREVIEW</text></svg>'); background-repeat: repeat;` : ''}
                }
                .invoice-header {
                    background: ${template === 'colorful' ? `linear-gradient(135deg, ${primaryColor}, ${secondaryColor})` : primaryColor};
                    color: white;
                    padding: 20px;
                    margin: -20px -20px 20px -20px;
                    ${template === 'minimal' ? 'background: transparent; color: ' + primaryColor + '; border-bottom: 2px solid ' + primaryColor + ';' : ''}
                }
                .invoice-title {
                    font-size: ${currentFontSize.title};
                    font-weight: bold;
                    margin: 0;
                }
                .invoice-number {
                    font-size: ${currentFontSize.header};
                    opacity: 0.9;
                }
                .company-info {
                    text-align: right;
                    ${template === 'modern' ? 'background: #f8f9fa; padding: 15px; border-radius: 8px;' : ''}
                }
                .customer-info {
                    ${template === 'professional' ? 'background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid ' + primaryColor + ';' : ''}
                }
                .invoice-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 20px 0;
                }
                .invoice-table th {
                    background: ${template === 'colorful' ? primaryColor : '#f8f9fa'};
                    color: ${template === 'colorful' ? 'white' : '#333'};
                    padding: 12px;
                    text-align: right;
                    font-size: ${currentFontSize.header};
                    ${template === 'minimal' ? 'border-bottom: 2px solid ' + primaryColor + '; background: transparent; color: ' + primaryColor + ';' : ''}
                }
                .invoice-table td {
                    padding: 10px 12px;
                    border-bottom: 1px solid #dee2e6;
                    text-align: right;
                }
                .invoice-total {
                    background: ${template === 'colorful' ? `linear-gradient(135deg, ${primaryColor}20, ${secondaryColor}20)` : '#f8f9fa'};
                    padding: 15px;
                    border-radius: 8px;
                    ${template === 'minimal' ? 'background: transparent; border: 1px solid ' + primaryColor + ';' : ''}
                }
                .total-row {
                    display: flex;
                    justify-content: space-between;
                    margin: 5px 0;
                    font-size: ${currentFontSize.header};
                }
                .total-final {
                    font-weight: bold;
                    font-size: ${currentFontSize.title};
                    color: ${primaryColor};
                    border-top: 2px solid ${primaryColor};
                    padding-top: 10px;
                }
                .logo-placeholder {
                    width: 80px;
                    height: 80px;
                    background: ${secondaryColor}30;
                    border-radius: 8px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: ${secondaryColor};
                    font-size: 12px;
                    text-align: center;
                }
            </style>
        `;

        return `
            ${customCSS}
            <div class="invoice-preview">
                <div class="invoice-header">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="invoice-title">فاتورة</h1>
                            <p class="invoice-number">رقم الفاتورة: ${invoice.number}</p>
                        </div>
                        <div class="col-md-4 text-end">
                            ${showLogo ? '<div class="logo-placeholder">شعار الشركة</div>' : ''}
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="company-info">
                            <h5 style="color: ${primaryColor};">${this.data.settings.companyName || 'قمة الوعد للسفريات'}</h5>
                            <p class="mb-1">${this.data.settings.companyAddress || 'المملكة العربية السعودية'}</p>
                            <p class="mb-1">هاتف: ${this.data.settings.companyPhone || '+966501234567'}</p>
                            <p class="mb-1">بريد إلكتروني: ${this.data.settings.companyEmail || '<EMAIL>'}</p>
                            ${this.data.settings.companyTaxNumber ? `<p class="mb-0">الرقم الضريبي: ${this.data.settings.companyTaxNumber}</p>` : ''}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="customer-info">
                            <h6 style="color: ${primaryColor};">بيانات العميل:</h6>
                            <p class="mb-1"><strong>${customer.name}</strong></p>
                            ${customer.email ? `<p class="mb-1">${customer.email}</p>` : ''}
                            ${customer.phone ? `<p class="mb-1">${customer.phone}</p>` : ''}
                            ${customer.address ? `<p class="mb-0">${customer.address}</p>` : ''}
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <p><strong>تاريخ الفاتورة:</strong> ${new Date(invoice.date).toLocaleDateString('ar-SA')}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>تاريخ الاستحقاق:</strong> ${new Date(invoice.dueDate).toLocaleDateString('ar-SA')}</p>
                    </div>
                </div>

                <table class="invoice-table">
                    <thead>
                        <tr>
                            <th>البند</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${invoice.items.map(item => `
                            <tr>
                                <td>${item.name}</td>
                                <td>${item.quantity}</td>
                                <td>${this.formatAmount(item.price)}</td>
                                <td>${this.formatAmount(item.total)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>

                <div class="row">
                    <div class="col-md-6">
                        ${invoice.notes ? `
                            <div>
                                <h6 style="color: ${primaryColor};">ملاحظات:</h6>
                                <p>${invoice.notes}</p>
                            </div>
                        ` : ''}
                    </div>
                    <div class="col-md-6">
                        <div class="invoice-total">
                            <div class="total-row">
                                <span>المجموع الفرعي:</span>
                                <span>${this.formatAmount(invoice.subtotal)}</span>
                            </div>
                            <div class="total-row">
                                <span>ضريبة القيمة المضافة (${(this.data.settings.taxRate * 100) || 15}%):</span>
                                <span>${this.formatAmount(invoice.tax)}</span>
                            </div>
                            <div class="total-row total-final">
                                <span>الإجمالي النهائي:</span>
                                <span>${this.formatAmount(invoice.total)}</span>
                            </div>
                        </div>
                    </div>
                </div>

                ${showWatermark ? '<div style="text-align: center; margin-top: 30px; opacity: 0.3; font-size: 48px; color: ' + secondaryColor + ';">معاينة</div>' : ''}
            </div>
        `;
    },

    /**
     * إعادة تعيين تصميم الفاتورة
     */
    resetInvoiceDesign: function() {
        if (confirm('هل تريد إعادة تعيين تصميم الفاتورة إلى الإعدادات الافتراضية؟')) {
            this.data.settings.selectedTemplate = 'classic';
            this.data.settings.primaryColor = '#007bff';
            this.data.settings.secondaryColor = '#6c757d';
            this.data.settings.fontSize = 'medium';
            this.data.settings.showLogo = true;
            this.data.settings.showWatermark = false;
            this.data.settings.showBorder = true;

            this.saveSalesData();
            this.refreshData();
            this.showMessage('تم إعادة تعيين تصميم الفاتورة بنجاح', 'success');
        }
    },

    /**
     * تحديث البيانات
     */
    refreshData: function() {
        this.loadSalesData();
        const content = document.getElementById('main-content');
        if (content) {
            content.innerHTML = this.renderCurrentView();
        }

        // تحديث التقارير إذا كان في قسم التقارير
        if (this.data.currentView === 'reports') {
            setTimeout(() => {
                this.updateReports();
            }, 100);
        }
    }
};

// تصدير المكون للاستخدام العام
window.SalesComponent = SalesComponent;
