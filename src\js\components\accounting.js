/**
 * ===================================
 * مكون النظام المحاسبي - Accounting Component
 * ===================================
 */

window.AccountingComponent = {
    // بيانات المكون
    data: {
        currentView: 'dashboard', // dashboard, entries, accounts, reports
        entries: [],
        accounts: [],
        selectedEntry: null,
        selectedAccount: null,
        filters: {
            dateFrom: '',
            dateTo: '',
            accountType: '',
            entryType: ''
        }
    },

    /**
     * عرض المكون الرئيسي
     */
    render: function(params = {}) {
        this.data.currentView = params.view || 'dashboard';
        
        return `
            <div class="accounting-container">
                ${this.renderHeader()}
                ${this.renderNavigation()}
                <div class="accounting-content">
                    ${this.renderCurrentView()}
                </div>
            </div>
        `;
    },

    /**
     * عرض رأس الصفحة
     */
    renderHeader: function() {
        return `
            <div class="accounting-header">
                <div class="header-title">
                    <h2><i class="fas fa-calculator me-2"></i>النظام المحاسبي</h2>
                    <p class="text-muted">إدارة شاملة للحسابات والقيود المالية</p>
                </div>
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="window.AccountingComponent.showNewEntryModal()">
                        <i class="fas fa-plus me-1"></i>قيد جديد
                    </button>
                    <button class="btn btn-outline-secondary" onclick="window.AccountingComponent.refreshData()">
                        <i class="fas fa-sync me-1"></i>تحديث
                    </button>
                </div>
            </div>
        `;
    },

    /**
     * عرض شريط التنقل
     */
    renderNavigation: function() {
        const navItems = [
            { key: 'dashboard', icon: 'fas fa-tachometer-alt', label: 'لوحة التحكم' },
            { key: 'entries', icon: 'fas fa-book', label: 'القيود المحاسبية' },
            { key: 'accounts', icon: 'fas fa-list', label: 'دليل الحسابات' },
            { key: 'reports', icon: 'fas fa-chart-bar', label: 'التقارير المالية' },
            { key: 'payments', icon: 'fas fa-money-bill-wave', label: 'المدفوعات' }
        ];

        return `
            <div class="accounting-nav">
                <ul class="nav nav-tabs">
                    ${navItems.map(item => `
                        <li class="nav-item">
                            <a class="nav-link ${this.data.currentView === item.key ? 'active' : ''}"
                               href="#" onclick="window.AccountingComponent.switchView('${item.key}')">
                                <i class="${item.icon} me-1"></i>${item.label}
                            </a>
                        </li>
                    `).join('')}
                </ul>
            </div>
        `;
    },

    /**
     * عرض المحتوى الحالي
     */
    renderCurrentView: function() {
        switch (this.data.currentView) {
            case 'dashboard':
                return this.renderDashboard();
            case 'entries':
                return this.renderEntries();
            case 'accounts':
                return this.renderAccounts();
            case 'reports':
                return this.renderReports();
            case 'payments':
                return this.renderPayments();
            default:
                return this.renderDashboard();
        }
    },

    /**
     * عرض لوحة التحكم المحاسبية
     */
    renderDashboard: function() {
        const balanceSheet = window.Accounting.generateBalanceSheet();
        const incomeStatement = window.Accounting.generateIncomeStatement();

        return `
            <div class="accounting-dashboard">
                <div class="row">
                    <!-- بطاقات الملخص -->
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">إجمالي الأصول</h6>
                                        <h4>${window.Accounting.formatAmount(balanceSheet.assets.total)}</h4>
                                    </div>
                                    <i class="fas fa-coins fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">إجمالي الإيرادات</h6>
                                        <h4>${window.Accounting.formatAmount(incomeStatement.revenue.total)}</h4>
                                    </div>
                                    <i class="fas fa-arrow-up fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">إجمالي المصروفات</h6>
                                        <h4>${window.Accounting.formatAmount(incomeStatement.expenses.total)}</h4>
                                    </div>
                                    <i class="fas fa-arrow-down fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card ${incomeStatement.netIncome >= 0 ? 'bg-info' : 'bg-danger'} text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">صافي الدخل</h6>
                                        <h4>${window.Accounting.formatAmount(incomeStatement.netIncome)}</h4>
                                    </div>
                                    <i class="fas fa-chart-line fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <!-- الميزانية العمومية المبسطة -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-balance-scale me-2"></i>الميزانية العمومية</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-6">
                                        <h6 class="text-primary">الأصول</h6>
                                        <ul class="list-unstyled">
                                            ${balanceSheet.assets.current.slice(0, 5).map(asset => `
                                                <li class="d-flex justify-content-between">
                                                    <span>${asset.name}</span>
                                                    <span>${window.Accounting.formatAmount(asset.balance)}</span>
                                                </li>
                                            `).join('')}
                                        </ul>
                                    </div>
                                    <div class="col-6">
                                        <h6 class="text-danger">الخصوم وحقوق الملكية</h6>
                                        <ul class="list-unstyled">
                                            ${balanceSheet.liabilities.current.slice(0, 3).map(liability => `
                                                <li class="d-flex justify-content-between">
                                                    <span>${liability.name}</span>
                                                    <span>${window.Accounting.formatAmount(liability.balance)}</span>
                                                </li>
                                            `).join('')}
                                            ${balanceSheet.equity.items.slice(0, 2).map(equity => `
                                                <li class="d-flex justify-content-between">
                                                    <span>${equity.name}</span>
                                                    <span>${window.Accounting.formatAmount(equity.balance)}</span>
                                                </li>
                                            `).join('')}
                                        </ul>
                                    </div>
                                </div>
                                <div class="text-center mt-3">
                                    <span class="badge ${balanceSheet.isBalanced ? 'bg-success' : 'bg-danger'}">
                                        ${balanceSheet.isBalanced ? 'الميزانية متوازنة' : 'الميزانية غير متوازنة'}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- آخر القيود -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-history me-2"></i>آخر القيود المحاسبية</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>رقم القيد</th>
                                                <th>التاريخ</th>
                                                <th>الوصف</th>
                                                <th>المبلغ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${window.Accounting.entries.slice(-5).reverse().map(entry => `
                                                <tr>
                                                    <td>${entry.entryNumber}</td>
                                                    <td>${new Date(entry.date).toLocaleDateString('ar-SA')}</td>
                                                    <td>${entry.description}</td>
                                                    <td>${window.Accounting.formatAmount(entry.totalDebit)}</td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض القيود المحاسبية المتكاملة
     */
    renderEntries: function() {
        const entries = window.Accounting.entries || [];

        return `
            <div class="accounting-entries">
                <!-- فلاتر وأدوات البحث -->
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label">البحث في القيود</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="entrySearch"
                                           placeholder="رقم القيد أو الوصف..."
                                           onkeyup="AccountingComponent.filterEntries()">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="entryDateFrom"
                                       onchange="AccountingComponent.filterEntries()">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="entryDateTo"
                                       onchange="AccountingComponent.filterEntries()">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">نوع القيد</label>
                                <select class="form-control" id="entryTypeFilter" onchange="AccountingComponent.filterEntries()">
                                    <option value="">جميع الأنواع</option>
                                    <option value="manual">يدوي</option>
                                    <option value="auto">تلقائي</option>
                                    <option value="booking">حجز</option>
                                    <option value="payment">دفع</option>
                                    <option value="receipt">استلام</option>
                                    <option value="expense">مصروف</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الإجراءات</label>
                                <div class="btn-group w-100">
                                    <button class="btn btn-primary" onclick="window.AccountingComponent.showNewEntryModal()">
                                        <i class="fas fa-plus me-1"></i>قيد جديد
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="window.AccountingComponent.exportEntries()">
                                        <i class="fas fa-download me-1"></i>تصدير
                                    </button>
                                    <button class="btn btn-outline-info" onclick="window.AccountingComponent.printEntries()">
                                        <i class="fas fa-print me-1"></i>طباعة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول القيود الرئيسي -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-book me-2"></i>القيود المحاسبية</h5>
                        <div class="header-stats">
                            <span class="badge bg-primary me-2">إجمالي القيود: <span id="totalEntriesCount">${entries.length}</span></span>
                            <span class="badge bg-success me-2">مرحلة: <span id="postedEntriesCount">${entries.filter(e => e.status === 'posted').length}</span></span>
                            <span class="badge bg-warning me-2">مسودة: <span id="draftEntriesCount">${entries.filter(e => e.status === 'draft').length}</span></span>
                            <span class="badge bg-info">معتمدة: <span id="approvedEntriesCount">${entries.filter(e => e.status === 'approved').length}</span></span>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="entriesTable">
                                <thead class="table-light">
                                    <tr>
                                        <th width="5%">
                                            <input type="checkbox" class="form-check-input" id="selectAllEntries"
                                                   onchange="AccountingComponent.toggleSelectAllEntries()">
                                        </th>
                                        <th width="8%" class="sortable" onclick="AccountingComponent.sortEntriesTable('entryNumber')">
                                            رقم القيد <i class="fas fa-sort"></i>
                                        </th>
                                        <th width="10%" class="sortable" onclick="AccountingComponent.sortEntriesTable('date')">
                                            التاريخ <i class="fas fa-sort"></i>
                                        </th>
                                        <th width="25%">الوصف</th>
                                        <th width="10%">المرجع</th>
                                        <th width="8%">النوع</th>
                                        <th width="10%" class="sortable" onclick="AccountingComponent.sortEntriesTable('totalDebit')">
                                            المدين <i class="fas fa-sort"></i>
                                        </th>
                                        <th width="10%" class="sortable" onclick="AccountingComponent.sortEntriesTable('totalCredit')">
                                            الدائن <i class="fas fa-sort"></i>
                                        </th>
                                        <th width="8%">الحالة</th>
                                        <th width="6%">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="entries-table-body">
                                    ${this.renderEntriesRows(entries)}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="selected-actions" id="selectedEntriesActions" style="display: none;">
                                    <button class="btn btn-sm btn-outline-success" onclick="AccountingComponent.bulkApproveEntries()">
                                        <i class="fas fa-check me-1"></i>اعتماد المحدد
                                    </button>
                                    <button class="btn btn-sm btn-outline-warning" onclick="AccountingComponent.bulkDraftEntries()">
                                        <i class="fas fa-edit me-1"></i>تحويل لمسودة
                                    </button>
                                    <button class="btn btn-sm btn-outline-primary" onclick="AccountingComponent.bulkExportEntries()">
                                        <i class="fas fa-download me-1"></i>تصدير المحدد
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="AccountingComponent.bulkDeleteEntries()">
                                        <i class="fas fa-trash me-1"></i>حذف المحدد
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="entries-summary">
                                    <small class="text-muted">
                                        إجمالي المدين: <span class="text-success fw-bold" id="totalDebitSum">${window.Accounting.formatAmount(entries.reduce((sum, e) => sum + e.totalDebit, 0))}</span> |
                                        إجمالي الدائن: <span class="text-danger fw-bold" id="totalCreditSum">${window.Accounting.formatAmount(entries.reduce((sum, e) => sum + e.totalCredit, 0))}</span>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                ${entries.length === 0 ? `
                    <div class="text-center py-5">
                        <i class="fas fa-book fa-4x text-muted mb-4"></i>
                        <h4 class="text-muted mb-3">لا توجد قيود محاسبية</h4>
                        <p class="text-muted mb-4">ابدأ بإنشاء أول قيد محاسبي لشركتك</p>
                        <button class="btn btn-primary btn-lg" onclick="AccountingComponent.showNewEntryModal()">
                            <i class="fas fa-plus me-2"></i>إنشاء أول قيد
                        </button>
                    </div>
                ` : ''}
            </div>
        `;
    },

    /**
     * عرض صفوف القيود
     */
    renderEntriesRows: function(entries) {
        return entries.map(entry => {
            const isBalanced = Math.abs(entry.totalDebit - entry.totalCredit) < 0.01;

            return `
                <tr class="entry-row ${!isBalanced ? 'table-warning' : ''}" data-entry-number="${entry.entryNumber}">
                    <td>
                        <input type="checkbox" class="form-check-input entry-checkbox"
                               value="${entry.entryNumber}" onchange="AccountingComponent.updateSelectedEntriesActions()">
                    </td>
                    <td>
                        <strong class="entry-number">${entry.entryNumber}</strong>
                        ${!isBalanced ? '<i class="fas fa-exclamation-triangle text-warning ms-1" title="قيد غير متوازن"></i>' : ''}
                    </td>
                    <td>
                        <span class="entry-date">${new Date(entry.date).toLocaleDateString('ar-SA')}</span>
                        <small class="d-block text-muted">${new Date(entry.createdAt).toLocaleTimeString('ar-SA')}</small>
                    </td>
                    <td>
                        <div class="entry-description">
                            <strong>${entry.description}</strong>
                            <small class="d-block text-muted">${entry.transactions.length} معاملة</small>
                        </div>
                    </td>
                    <td>
                        <span class="entry-reference">${entry.reference || '-'}</span>
                    </td>
                    <td>
                        <span class="badge ${this.getEntryTypeBadgeClass(entry.type)} entry-type">
                            ${this.getEntryTypeLabel(entry.type)}
                        </span>
                    </td>
                    <td>
                        <strong class="text-success entry-debit">${window.Accounting.formatAmount(entry.totalDebit)}</strong>
                    </td>
                    <td>
                        <strong class="text-danger entry-credit">${window.Accounting.formatAmount(entry.totalCredit)}</strong>
                    </td>
                    <td>
                        <span class="badge ${this.getStatusBadgeClass(entry.status)} entry-status">
                            ${this.getStatusLabel(entry.status)}
                        </span>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="AccountingComponent.viewEntry(${entry.entryNumber})"
                                    title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-secondary" onclick="AccountingComponent.editEntry(${entry.entryNumber})"
                                    title="تعديل" ${entry.status === 'approved' ? 'disabled' : ''}>
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-info" onclick="AccountingComponent.printEntry(${entry.entryNumber})"
                                    title="طباعة">
                                <i class="fas fa-print"></i>
                            </button>
                            ${entry.status !== 'approved' ?
                                `<button class="btn btn-outline-danger" onclick="AccountingComponent.deleteEntry(${entry.entryNumber})"
                                         title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>` : ''}
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
    },

    /**
     * فلترة القيود
     */
    filterEntries: function() {
        const searchTerm = document.getElementById('entrySearch')?.value.toLowerCase() || '';
        const dateFrom = document.getElementById('entryDateFrom')?.value || '';
        const dateTo = document.getElementById('entryDateTo')?.value || '';
        const typeFilter = document.getElementById('entryTypeFilter')?.value || '';

        const allEntries = window.Accounting.entries || [];

        const filteredEntries = allEntries.filter(entry => {
            const matchesSearch = !searchTerm ||
                entry.entryNumber.toString().includes(searchTerm) ||
                entry.description.toLowerCase().includes(searchTerm) ||
                (entry.reference && entry.reference.toLowerCase().includes(searchTerm));

            const entryDate = new Date(entry.date);
            const matchesDateFrom = !dateFrom || entryDate >= new Date(dateFrom);
            const matchesDateTo = !dateTo || entryDate <= new Date(dateTo);
            const matchesType = !typeFilter || entry.type === typeFilter;

            return matchesSearch && matchesDateFrom && matchesDateTo && matchesType;
        });

        // تحديث الجدول
        const tbody = document.getElementById('entries-table-body');
        if (tbody) {
            tbody.innerHTML = this.renderEntriesRows(filteredEntries);
        }

        // تحديث الإحصائيات
        this.updateEntriesStats(filteredEntries);
    },

    /**
     * تحديث إحصائيات القيود
     */
    updateEntriesStats: function(entries) {
        const totalCount = document.getElementById('totalEntriesCount');
        const postedCount = document.getElementById('postedEntriesCount');
        const draftCount = document.getElementById('draftEntriesCount');
        const approvedCount = document.getElementById('approvedEntriesCount');
        const totalDebitSum = document.getElementById('totalDebitSum');
        const totalCreditSum = document.getElementById('totalCreditSum');

        if (totalCount) totalCount.textContent = entries.length;
        if (postedCount) postedCount.textContent = entries.filter(e => e.status === 'posted').length;
        if (draftCount) draftCount.textContent = entries.filter(e => e.status === 'draft').length;
        if (approvedCount) approvedCount.textContent = entries.filter(e => e.status === 'approved').length;

        if (totalDebitSum) totalDebitSum.textContent = window.Accounting.formatAmount(entries.reduce((sum, e) => sum + e.totalDebit, 0));
        if (totalCreditSum) totalCreditSum.textContent = window.Accounting.formatAmount(entries.reduce((sum, e) => sum + e.totalCredit, 0));
    },

    /**
     * الحصول على فئة شارة نوع القيد
     */
    getEntryTypeBadgeClass: function(type) {
        const classes = {
            'manual': 'bg-primary',
            'auto': 'bg-info',
            'booking': 'bg-success',
            'payment': 'bg-warning',
            'receipt': 'bg-success',
            'expense': 'bg-danger'
        };
        return classes[type] || 'bg-secondary';
    },

    /**
     * عرض دليل الحسابات المتقدم
     */
    renderAccounts: function() {
        const accounts = Object.values(window.Accounting.chartOfAccounts)
            .sort((a, b) => a.code.localeCompare(b.code));

        return `
            <div class="accounting-accounts">
                <!-- فلاتر وأدوات البحث المتقدمة -->
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label">البحث في الحسابات</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="accountSearch"
                                           placeholder="رمز أو اسم الحساب..."
                                           onkeyup="AccountingComponent.filterAccounts()">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">نوع الحساب</label>
                                <select class="form-control" id="accountTypeFilter" onchange="AccountingComponent.filterAccounts()">
                                    <option value="">جميع الأنواع</option>
                                    <option value="asset">الأصول</option>
                                    <option value="liability">الخصوم</option>
                                    <option value="equity">حقوق الملكية</option>
                                    <option value="revenue">الإيرادات</option>
                                    <option value="expense">المصروفات</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">المستوى</label>
                                <select class="form-control" id="accountLevelFilter" onchange="AccountingComponent.filterAccounts()">
                                    <option value="">جميع المستويات</option>
                                    <option value="1">المستوى الأول</option>
                                    <option value="2">المستوى الثاني</option>
                                    <option value="3">المستوى الثالث</option>
                                    <option value="4">المستوى الرابع</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">الحالة</label>
                                <select class="form-control" id="accountStatusFilter" onchange="AccountingComponent.filterAccounts()">
                                    <option value="">جميع الحالات</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الإجراءات</label>
                                <div class="btn-group w-100">
                                    <button class="btn btn-primary" onclick="window.AccountingComponent.showNewAccountModal()">
                                        <i class="fas fa-plus me-1"></i>حساب جديد
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="window.AccountingComponent.exportAccounts()">
                                        <i class="fas fa-download me-1"></i>تصدير
                                    </button>
                                    <button class="btn btn-outline-info" onclick="window.AccountingComponent.printAccounts()">
                                        <i class="fas fa-print me-1"></i>طباعة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول الحسابات الرئيسي -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-list me-2"></i>دليل الحسابات</h5>
                        <div class="header-stats">
                            <span class="badge bg-primary me-2">إجمالي الحسابات: <span id="totalAccountsCount">${accounts.length}</span></span>
                            <span class="badge bg-success me-2">النشطة: <span id="activeAccountsCount">${accounts.filter(a => a.isActive).length}</span></span>
                            <span class="badge bg-secondary">غير النشطة: <span id="inactiveAccountsCount">${accounts.filter(a => !a.isActive).length}</span></span>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="accountsTable">
                                <thead class="table-light">
                                    <tr>
                                        <th width="5%">
                                            <input type="checkbox" class="form-check-input" id="selectAllAccounts"
                                                   onchange="window.AccountingComponent.toggleSelectAll()">
                                        </th>
                                        <th width="12%" class="sortable" onclick="window.AccountingComponent.sortTable('code')">
                                            رمز الحساب <i class="fas fa-sort"></i>
                                        </th>
                                        <th width="25%" class="sortable" onclick="window.AccountingComponent.sortTable('name')">
                                            اسم الحساب <i class="fas fa-sort"></i>
                                        </th>
                                        <th width="15%">النوع</th>
                                        <th width="8%">المستوى</th>
                                        <th width="12%">الحساب الأب</th>
                                        <th width="12%" class="sortable" onclick="window.AccountingComponent.sortTable('balance')">
                                            الرصيد <i class="fas fa-sort"></i>
                                        </th>
                                        <th width="8%">الحالة</th>
                                        <th width="13%">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="accounts-table-body">
                                    ${this.renderAccountsRows(accounts)}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="selected-actions" id="selectedActions" style="display: none;">
                                    <button class="btn btn-sm btn-outline-danger" onclick="window.AccountingComponent.bulkDeactivate()">
                                        <i class="fas fa-ban me-1"></i>إلغاء تفعيل المحدد
                                    </button>
                                    <button class="btn btn-sm btn-outline-success" onclick="window.AccountingComponent.bulkActivate()">
                                        <i class="fas fa-check me-1"></i>تفعيل المحدد
                                    </button>
                                    <button class="btn btn-sm btn-outline-primary" onclick="window.AccountingComponent.bulkExport()">
                                        <i class="fas fa-download me-1"></i>تصدير المحدد
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6 text-end">
                                <nav aria-label="تنقل الصفحات">
                                    <ul class="pagination pagination-sm mb-0" id="accountsPagination">
                                        <!-- سيتم ملؤها ديناميكياً -->
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض صفوف الحسابات
     */
    renderAccountsRows: function(accounts) {
        return accounts.map(account => {
            const parentAccount = account.parent ? window.Accounting.chartOfAccounts[account.parent] : null;
            const indentStyle = `padding-right: ${(account.level - 1) * 25}px;`;

            return `
                <tr class="account-row ${!account.isActive ? 'table-secondary' : ''}" data-account-code="${account.code}">
                    <td>
                        <input type="checkbox" class="form-check-input account-checkbox"
                               value="${account.code}" onchange="window.AccountingComponent.updateSelectedActions()">
                    </td>
                    <td>
                        <strong class="account-code">${account.code}</strong>
                        ${account.level > 1 ? `<small class="text-muted d-block">فرع من: ${account.parent}</small>` : ''}
                    </td>
                    <td style="${indentStyle}">
                        <div class="account-name-container">
                            <strong class="account-name">${account.name}</strong>
                            ${account.nameEn ? `<small class="text-muted d-block">${account.nameEn}</small>` : ''}
                            ${account.children && account.children.length > 0 ?
                                `<small class="text-info d-block"><i class="fas fa-sitemap me-1"></i>${account.children.length} حساب فرعي</small>` : ''}
                        </div>
                    </td>
                    <td>
                        <span class="badge ${this.getAccountTypeBadgeClass(account.type)} account-type">
                            ${this.getAccountTypeLabel(account.type)}
                        </span>
                        ${account.category ? `<small class="text-muted d-block">${account.category}</small>` : ''}
                    </td>
                    <td>
                        <span class="badge bg-light text-dark">المستوى ${account.level}</span>
                    </td>
                    <td>
                        ${parentAccount ?
                            `<small class="text-muted">${parentAccount.code}<br>${parentAccount.name}</small>` :
                            '<span class="text-muted">-</span>'}
                    </td>
                    <td>
                        <div class="balance-container">
                            ${account.balance !== undefined ?
                                `<strong class="account-balance ${this.getBalanceClass(account.balance, account.type)}">
                                    ${window.Accounting.formatAmount(Math.abs(account.balance))}
                                </strong>
                                <small class="d-block text-muted">${this.getBalanceDirection(account.balance, account.type)}</small>` :
                                '<span class="text-muted">-</span>'}
                        </div>
                    </td>
                    <td>
                        <span class="badge ${account.isActive ? 'bg-success' : 'bg-secondary'} account-status">
                            ${account.isActive ? 'نشط' : 'غير نشط'}
                        </span>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="window.AccountingComponent.viewAccountDetails('${account.code}')"
                                    title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-secondary" onclick="window.AccountingComponent.editAccount('${account.code}')"
                                    title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-info" onclick="window.AccountingComponent.viewAccountStatement('${account.code}')"
                                    title="كشف الحساب">
                                <i class="fas fa-file-alt"></i>
                            </button>
                            ${account.level > 2 ?
                                `<button class="btn btn-outline-danger" onclick="window.AccountingComponent.deleteAccount('${account.code}')"
                                         title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>` : ''}
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
    },

    /**
     * عرض التقارير المالية الشاملة
     */
    renderReports: function() {
        return `
            <div class="accounting-reports">
                <!-- أدوات التحكم في التقارير -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-cog me-2"></i>إعدادات التقارير</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="reportDateFrom"
                                       value="${new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0]}">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="reportDateTo"
                                       value="${new Date().toISOString().split('T')[0]}">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">العملة</label>
                                <select class="form-control" id="reportCurrency">
                                    <option value="SAR">ريال سعودي</option>
                                    <option value="USD">دولار أمريكي</option>
                                    <option value="EUR">يورو</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">تنسيق التصدير</label>
                                <select class="form-control" id="reportFormat">
                                    <option value="html">HTML</option>
                                    <option value="pdf">PDF</option>
                                    <option value="excel">Excel</option>
                                    <option value="csv">CSV</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- التقارير الأساسية -->
                <div class="row">
                    <div class="col-12">
                        <h4 class="mb-3"><i class="fas fa-chart-bar me-2"></i>التقارير الأساسية</h4>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4 mb-4">
                        <div class="card h-100 report-card">
                            <div class="card-body text-center">
                                <div class="report-icon mb-3">
                                    <i class="fas fa-balance-scale fa-4x text-primary"></i>
                                </div>
                                <h5 class="card-title">الميزانية العمومية</h5>
                                <p class="card-text text-muted">عرض الأصول والخصوم وحقوق الملكية في تاريخ محدد</p>
                                <div class="report-stats mb-3">
                                    <small class="text-muted">
                                        آخر تحديث: ${new Date().toLocaleDateString('ar-SA')}
                                    </small>
                                </div>
                                <div class="btn-group w-100">
                                    <button class="btn btn-primary" onclick="AccountingComponent.generateBalanceSheet()">
                                        <i class="fas fa-eye me-1"></i>عرض
                                    </button>
                                    <button class="btn btn-outline-primary" onclick="AccountingComponent.exportBalanceSheet()">
                                        <i class="fas fa-download me-1"></i>تصدير
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 mb-4">
                        <div class="card h-100 report-card">
                            <div class="card-body text-center">
                                <div class="report-icon mb-3">
                                    <i class="fas fa-chart-line fa-4x text-success"></i>
                                </div>
                                <h5 class="card-title">قائمة الدخل</h5>
                                <p class="card-text text-muted">عرض الإيرادات والمصروفات وصافي الدخل للفترة</p>
                                <div class="report-stats mb-3">
                                    <small class="text-success">
                                        صافي الدخل: ${window.Accounting.formatAmount(window.Accounting.state.totalRevenue - window.Accounting.state.totalExpenses)}
                                    </small>
                                </div>
                                <div class="btn-group w-100">
                                    <button class="btn btn-success" onclick="AccountingComponent.generateIncomeStatement()">
                                        <i class="fas fa-eye me-1"></i>عرض
                                    </button>
                                    <button class="btn btn-outline-success" onclick="AccountingComponent.exportIncomeStatement()">
                                        <i class="fas fa-download me-1"></i>تصدير
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 mb-4">
                        <div class="card h-100 report-card">
                            <div class="card-body text-center">
                                <div class="report-icon mb-3">
                                    <i class="fas fa-list-alt fa-4x text-info"></i>
                                </div>
                                <h5 class="card-title">ميزان المراجعة</h5>
                                <p class="card-text text-muted">عرض أرصدة جميع الحسابات والتحقق من التوازن</p>
                                <div class="report-stats mb-3">
                                    <small class="${window.Accounting.state.isBalanced ? 'text-success' : 'text-danger'}">
                                        ${window.Accounting.state.isBalanced ? 'متوازن ✓' : 'غير متوازن ✗'}
                                    </small>
                                </div>
                                <div class="btn-group w-100">
                                    <button class="btn btn-info" onclick="AccountingComponent.generateTrialBalance()">
                                        <i class="fas fa-eye me-1"></i>عرض
                                    </button>
                                    <button class="btn btn-outline-info" onclick="AccountingComponent.exportTrialBalance()">
                                        <i class="fas fa-download me-1"></i>تصدير
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تقارير التفاصيل -->
                <div class="row">
                    <div class="col-12">
                        <h4 class="mb-3"><i class="fas fa-file-alt me-2"></i>تقارير التفاصيل</h4>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3 mb-4">
                        <div class="card h-100 report-card">
                            <div class="card-body text-center">
                                <div class="report-icon mb-3">
                                    <i class="fas fa-users fa-3x text-warning"></i>
                                </div>
                                <h5 class="card-title">أرصدة الوكلاء</h5>
                                <p class="card-text text-muted">تفاصيل أرصدة وعمولات الوكلاء</p>
                                <button class="btn btn-warning w-100" onclick="AccountingComponent.generateAgentsReport()">
                                    <i class="fas fa-eye me-1"></i>عرض التقرير
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-4">
                        <div class="card h-100 report-card">
                            <div class="card-body text-center">
                                <div class="report-icon mb-3">
                                    <i class="fas fa-truck fa-3x text-secondary"></i>
                                </div>
                                <h5 class="card-title">أرصدة الموردين</h5>
                                <p class="card-text text-muted">تفاصيل أرصدة ومستحقات الموردين</p>
                                <button class="btn btn-secondary w-100" onclick="AccountingComponent.generateSuppliersReport()">
                                    <i class="fas fa-eye me-1"></i>عرض التقرير
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-4">
                        <div class="card h-100 report-card">
                            <div class="card-body text-center">
                                <div class="report-icon mb-3">
                                    <i class="fas fa-user-friends fa-3x text-primary"></i>
                                </div>
                                <h5 class="card-title">أرصدة العملاء</h5>
                                <p class="card-text text-muted">تفاصيل أرصدة وذمم العملاء</p>
                                <button class="btn btn-primary w-100" onclick="AccountingComponent.generateCustomersReport()">
                                    <i class="fas fa-eye me-1"></i>عرض التقرير
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-4">
                        <div class="card h-100 report-card">
                            <div class="card-body text-center">
                                <div class="report-icon mb-3">
                                    <i class="fas fa-money-bill-wave fa-3x text-success"></i>
                                </div>
                                <h5 class="card-title">التدفقات النقدية</h5>
                                <p class="card-text text-muted">تقرير التدفقات النقدية الداخلة والخارجة</p>
                                <button class="btn btn-success w-100" onclick="AccountingComponent.generateCashFlowReport()">
                                    <i class="fas fa-eye me-1"></i>عرض التقرير
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- التقارير التحليلية -->
                <div class="row">
                    <div class="col-12">
                        <h4 class="mb-3"><i class="fas fa-chart-pie me-2"></i>التقارير التحليلية</h4>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4 mb-4">
                        <div class="card h-100 report-card">
                            <div class="card-body text-center">
                                <div class="report-icon mb-3">
                                    <i class="fas fa-chart-pie fa-3x text-info"></i>
                                </div>
                                <h5 class="card-title">تحليل الإيرادات</h5>
                                <p class="card-text text-muted">تحليل الإيرادات حسب النوع والفترة</p>
                                <button class="btn btn-info w-100" onclick="AccountingComponent.generateRevenueAnalysis()">
                                    <i class="fas fa-chart-pie me-1"></i>عرض التحليل
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 mb-4">
                        <div class="card h-100 report-card">
                            <div class="card-body text-center">
                                <div class="report-icon mb-3">
                                    <i class="fas fa-chart-bar fa-3x text-danger"></i>
                                </div>
                                <h5 class="card-title">تحليل المصروفات</h5>
                                <p class="card-text text-muted">تحليل المصروفات حسب النوع والقسم</p>
                                <button class="btn btn-danger w-100" onclick="AccountingComponent.generateExpenseAnalysis()">
                                    <i class="fas fa-chart-bar me-1"></i>عرض التحليل
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 mb-4">
                        <div class="card h-100 report-card">
                            <div class="card-body text-center">
                                <div class="report-icon mb-3">
                                    <i class="fas fa-chart-area fa-3x text-purple"></i>
                                </div>
                                <h5 class="card-title">تحليل الربحية</h5>
                                <p class="card-text text-muted">تحليل الربحية حسب الخدمة والوكيل</p>
                                <button class="btn btn-purple w-100" onclick="AccountingComponent.generateProfitabilityAnalysis()">
                                    <i class="fas fa-chart-area me-1"></i>عرض التحليل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- التقارير المخصصة -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-cogs me-2"></i>التقارير المخصصة</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>إنشاء تقرير مخصص</h6>
                                        <p class="text-muted">قم بإنشاء تقرير مخصص حسب احتياجاتك</p>
                                        <button class="btn btn-outline-primary" onclick="AccountingComponent.showCustomReportBuilder()">
                                            <i class="fas fa-plus me-1"></i>إنشاء تقرير مخصص
                                        </button>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>التقارير المحفوظة</h6>
                                        <p class="text-muted">الوصول للتقارير المخصصة المحفوظة</p>
                                        <button class="btn btn-outline-secondary" onclick="AccountingComponent.showSavedReports()">
                                            <i class="fas fa-folder me-1"></i>التقارير المحفوظة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض المدفوعات والمقبوضات المتكاملة
     */
    renderPayments: function() {
        const payments = this.getPaymentsData();
        const receipts = this.getReceiptsData();

        return `
            <div class="accounting-payments">
                <!-- إحصائيات المدفوعات والمقبوضات -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">إجمالي المقبوضات</h6>
                                        <h4>${window.Accounting.formatAmount(receipts.reduce((sum, r) => sum + r.amount, 0))}</h4>
                                        <small>عدد المعاملات: ${receipts.length}</small>
                                    </div>
                                    <i class="fas fa-arrow-down fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">إجمالي المدفوعات</h6>
                                        <h4>${window.Accounting.formatAmount(payments.reduce((sum, p) => sum + p.amount, 0))}</h4>
                                        <small>عدد المعاملات: ${payments.length}</small>
                                    </div>
                                    <i class="fas fa-arrow-up fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">صافي التدفق النقدي</h6>
                                        <h4>${window.Accounting.formatAmount(receipts.reduce((sum, r) => sum + r.amount, 0) - payments.reduce((sum, p) => sum + p.amount, 0))}</h4>
                                        <small>الفرق بين المقبوضات والمدفوعات</small>
                                    </div>
                                    <i class="fas fa-exchange-alt fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">المعاملات المعلقة</h6>
                                        <h4>${[...payments, ...receipts].filter(t => t.status === 'pending').length}</h4>
                                        <small>تحتاج موافقة</small>
                                    </div>
                                    <i class="fas fa-clock fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- فلاتر البحث -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label">البحث</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="paymentSearch"
                                           placeholder="رقم المعاملة أو الاسم..."
                                           onkeyup="AccountingComponent.filterPayments()">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="paymentDateFrom"
                                       onchange="AccountingComponent.filterPayments()">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="paymentDateTo"
                                       onchange="AccountingComponent.filterPayments()">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">نوع المعاملة</label>
                                <select class="form-control" id="paymentTypeFilter" onchange="AccountingComponent.filterPayments()">
                                    <option value="">جميع الأنواع</option>
                                    <option value="receipt">مقبوضات</option>
                                    <option value="payment">مدفوعات</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الإجراءات</label>
                                <div class="btn-group w-100">
                                    <button class="btn btn-success" onclick="AccountingComponent.showReceiptModal()">
                                        <i class="fas fa-plus me-1"></i>مقبوض
                                    </button>
                                    <button class="btn btn-danger" onclick="AccountingComponent.showPaymentModal()">
                                        <i class="fas fa-plus me-1"></i>مدفوع
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="AccountingComponent.exportPayments()">
                                        <i class="fas fa-download me-1"></i>تصدير
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول المعاملات المدمج -->
                <div class="card">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="paymentsTab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="all-tab" data-bs-toggle="tab" data-bs-target="#all-payments"
                                        type="button" role="tab">
                                    <i class="fas fa-list me-1"></i>جميع المعاملات
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="receipts-tab" data-bs-toggle="tab" data-bs-target="#receipts-only"
                                        type="button" role="tab">
                                    <i class="fas fa-arrow-down text-success me-1"></i>المقبوضات فقط
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="payments-tab" data-bs-toggle="tab" data-bs-target="#payments-only"
                                        type="button" role="tab">
                                    <i class="fas fa-arrow-up text-danger me-1"></i>المدفوعات فقط
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="pending-tab" data-bs-toggle="tab" data-bs-target="#pending-payments"
                                        type="button" role="tab">
                                    <i class="fas fa-clock text-warning me-1"></i>المعلقة
                                </button>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body p-0">
                        <div class="tab-content" id="paymentsTabContent">
                            <!-- جميع المعاملات -->
                            <div class="tab-pane fade show active" id="all-payments" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th width="5%">
                                                    <input type="checkbox" class="form-check-input" id="selectAllPayments">
                                                </th>
                                                <th width="8%">رقم المعاملة</th>
                                                <th width="10%">التاريخ</th>
                                                <th width="8%">النوع</th>
                                                <th width="20%">الطرف</th>
                                                <th width="12%">المبلغ</th>
                                                <th width="10%">طريقة الدفع</th>
                                                <th width="8%">الحالة</th>
                                                <th width="15%">الوصف</th>
                                                <th width="4%">الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="all-payments-table">
                                            ${this.renderPaymentsRows([...receipts, ...payments])}
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- المقبوضات فقط -->
                            <div class="tab-pane fade" id="receipts-only" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th>رقم المقبوض</th>
                                                <th>التاريخ</th>
                                                <th>العميل/المصدر</th>
                                                <th>المبلغ</th>
                                                <th>طريقة الاستلام</th>
                                                <th>رقم المرجع</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="receipts-table">
                                            ${this.renderReceiptsRows(receipts)}
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- المدفوعات فقط -->
                            <div class="tab-pane fade" id="payments-only" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th>رقم المدفوع</th>
                                                <th>التاريخ</th>
                                                <th>المورد/المستفيد</th>
                                                <th>المبلغ</th>
                                                <th>طريقة الدفع</th>
                                                <th>رقم المرجع</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="payments-table">
                                            ${this.renderPaymentsOnlyRows(payments)}
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- المعاملات المعلقة -->
                            <div class="tab-pane fade" id="pending-payments" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th>رقم المعاملة</th>
                                                <th>التاريخ</th>
                                                <th>النوع</th>
                                                <th>الطرف</th>
                                                <th>المبلغ</th>
                                                <th>السبب</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="pending-payments-table">
                                            ${this.renderPendingPaymentsRows([...receipts, ...payments].filter(t => t.status === 'pending'))}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="selected-actions" id="selectedPaymentsActions" style="display: none;">
                                    <button class="btn btn-sm btn-outline-success" onclick="AccountingComponent.bulkApprovePayments()">
                                        <i class="fas fa-check me-1"></i>اعتماد المحدد
                                    </button>
                                    <button class="btn btn-sm btn-outline-warning" onclick="AccountingComponent.bulkPendingPayments()">
                                        <i class="fas fa-clock me-1"></i>تعليق المحدد
                                    </button>
                                    <button class="btn btn-sm btn-outline-primary" onclick="AccountingComponent.bulkExportPayments()">
                                        <i class="fas fa-download me-1"></i>تصدير المحدد
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6 text-end">
                                <small class="text-muted">
                                    إجمالي المعاملات المعروضة: <span class="fw-bold">${[...receipts, ...payments].length}</span>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * الحصول على بيانات المدفوعات
     */
    getPaymentsData: function() {
        // استخراج المدفوعات من القيود المحاسبية
        const payments = [];
        window.Accounting.entries.forEach(entry => {
            if (entry.type === 'payment') {
                entry.transactions.forEach(transaction => {
                    if (transaction.credit > 0 && (transaction.accountCode.startsWith('1111') || transaction.accountCode.startsWith('1112'))) {
                        payments.push({
                            id: `${entry.entryNumber}-${transaction.accountCode}`,
                            transactionNumber: entry.entryNumber,
                            date: entry.date,
                            type: 'payment',
                            party: this.getPartyName(transaction.accountCode, 'supplier'),
                            amount: transaction.credit,
                            paymentMethod: this.getPaymentMethod(transaction.accountCode),
                            reference: entry.reference || '',
                            status: entry.status || 'posted',
                            description: transaction.description || entry.description,
                            entryNumber: entry.entryNumber
                        });
                    }
                });
            }
        });
        return payments;
    },

    /**
     * الحصول على بيانات المقبوضات
     */
    getReceiptsData: function() {
        // استخراج المقبوضات من القيود المحاسبية
        const receipts = [];
        window.Accounting.entries.forEach(entry => {
            if (entry.type === 'receipt') {
                entry.transactions.forEach(transaction => {
                    if (transaction.debit > 0 && (transaction.accountCode.startsWith('1111') || transaction.accountCode.startsWith('1112'))) {
                        receipts.push({
                            id: `${entry.entryNumber}-${transaction.accountCode}`,
                            transactionNumber: entry.entryNumber,
                            date: entry.date,
                            type: 'receipt',
                            party: this.getPartyName(transaction.accountCode, 'customer'),
                            amount: transaction.debit,
                            paymentMethod: this.getPaymentMethod(transaction.accountCode),
                            reference: entry.reference || '',
                            status: entry.status || 'posted',
                            description: transaction.description || entry.description,
                            entryNumber: entry.entryNumber
                        });
                    }
                });
            }
        });
        return receipts;
    },

    /**
     * عرض صفوف المعاملات المدمجة
     */
    renderPaymentsRows: function(transactions) {
        return transactions.sort((a, b) => new Date(b.date) - new Date(a.date)).map(transaction => `
            <tr class="transaction-row ${transaction.type === 'receipt' ? 'table-success-light' : 'table-danger-light'}">
                <td>
                    <input type="checkbox" class="form-check-input payment-checkbox" value="${transaction.id}">
                </td>
                <td><strong>${transaction.transactionNumber}</strong></td>
                <td>${new Date(transaction.date).toLocaleDateString('ar-SA')}</td>
                <td>
                    <span class="badge ${transaction.type === 'receipt' ? 'bg-success' : 'bg-danger'}">
                        ${transaction.type === 'receipt' ? 'مقبوض' : 'مدفوع'}
                    </span>
                </td>
                <td>${transaction.party}</td>
                <td>
                    <strong class="${transaction.type === 'receipt' ? 'text-success' : 'text-danger'}">
                        ${window.Accounting.formatAmount(transaction.amount)}
                    </strong>
                </td>
                <td>${transaction.paymentMethod}</td>
                <td>
                    <span class="badge ${this.getPaymentStatusBadgeClass(transaction.status)}">
                        ${this.getPaymentStatusLabel(transaction.status)}
                    </span>
                </td>
                <td>
                    <small class="text-muted">${transaction.description}</small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="AccountingComponent.viewPaymentDetails('${transaction.id}')" title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-secondary" onclick="AccountingComponent.editPayment('${transaction.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="AccountingComponent.printPayment('${transaction.id}')" title="طباعة">
                            <i class="fas fa-print"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    },

    /**
     * عرض صفوف المقبوضات فقط
     */
    renderReceiptsRows: function(receipts) {
        return receipts.map(receipt => `
            <tr class="receipt-row">
                <td><strong>${receipt.transactionNumber}</strong></td>
                <td>${new Date(receipt.date).toLocaleDateString('ar-SA')}</td>
                <td>${receipt.party}</td>
                <td><strong class="text-success">${window.Accounting.formatAmount(receipt.amount)}</strong></td>
                <td>${receipt.paymentMethod}</td>
                <td>${receipt.reference}</td>
                <td>
                    <span class="badge ${this.getPaymentStatusBadgeClass(receipt.status)}">
                        ${this.getPaymentStatusLabel(receipt.status)}
                    </span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="AccountingComponent.viewReceiptDetails('${receipt.id}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="AccountingComponent.printReceipt('${receipt.id}')">
                            <i class="fas fa-print"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    },

    /**
     * عرض صفوف المدفوعات فقط
     */
    renderPaymentsOnlyRows: function(payments) {
        return payments.map(payment => `
            <tr class="payment-row">
                <td><strong>${payment.transactionNumber}</strong></td>
                <td>${new Date(payment.date).toLocaleDateString('ar-SA')}</td>
                <td>${payment.party}</td>
                <td><strong class="text-danger">${window.Accounting.formatAmount(payment.amount)}</strong></td>
                <td>${payment.paymentMethod}</td>
                <td>${payment.reference}</td>
                <td>
                    <span class="badge ${this.getPaymentStatusBadgeClass(payment.status)}">
                        ${this.getPaymentStatusLabel(payment.status)}
                    </span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="AccountingComponent.viewPaymentDetails('${payment.id}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="AccountingComponent.printPayment('${payment.id}')">
                            <i class="fas fa-print"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    },

    /**
     * عرض صفوف المعاملات المعلقة
     */
    renderPendingPaymentsRows: function(pendingTransactions) {
        return pendingTransactions.map(transaction => `
            <tr class="pending-transaction-row table-warning">
                <td><strong>${transaction.transactionNumber}</strong></td>
                <td>${new Date(transaction.date).toLocaleDateString('ar-SA')}</td>
                <td>
                    <span class="badge ${transaction.type === 'receipt' ? 'bg-success' : 'bg-danger'}">
                        ${transaction.type === 'receipt' ? 'مقبوض' : 'مدفوع'}
                    </span>
                </td>
                <td>${transaction.party}</td>
                <td>
                    <strong class="${transaction.type === 'receipt' ? 'text-success' : 'text-danger'}">
                        ${window.Accounting.formatAmount(transaction.amount)}
                    </strong>
                </td>
                <td><small class="text-muted">في انتظار الموافقة</small></td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-success" onclick="AccountingComponent.approvePayment('${transaction.id}')">
                            <i class="fas fa-check"></i> موافقة
                        </button>
                        <button class="btn btn-outline-danger" onclick="AccountingComponent.rejectPayment('${transaction.id}')">
                            <i class="fas fa-times"></i> رفض
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    },

    /**
     * عرض نافذة مقبوض جديد
     */
    showReceiptModal: function() {
        const customers = window.Database ? window.Database.findAll('customers') : [];

        const modalHTML = `
            <div class="modal fade" id="newReceiptModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-success text-white">
                            <h5 class="modal-title"><i class="fas fa-arrow-down me-2"></i>مقبوض جديد</h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="newReceiptForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">التاريخ *</label>
                                            <input type="date" class="form-control" name="date"
                                                   value="${new Date().toISOString().split('T')[0]}" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رقم المرجع</label>
                                            <input type="text" class="form-control" name="reference"
                                                   placeholder="رقم الإيصال أو المرجع">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label class="form-label">العميل/المصدر *</label>
                                            <select class="form-control" name="customer" required>
                                                <option value="">اختر العميل</option>
                                                <option value="cash">نقدي</option>
                                                ${customers.map(customer => `
                                                    <option value="${customer.id}">${customer.name}</option>
                                                `).join('')}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">المبلغ *</label>
                                            <input type="number" class="form-control" name="amount"
                                                   step="0.01" min="0" required placeholder="0.00">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">طريقة الاستلام *</label>
                                            <select class="form-control" name="paymentMethod" required>
                                                <option value="">اختر طريقة الاستلام</option>
                                                <option value="cash">نقدي</option>
                                                <option value="bank_transfer">تحويل بنكي</option>
                                                <option value="check">شيك</option>
                                                <option value="credit_card">بطاقة ائتمان</option>
                                                <option value="online">دفع إلكتروني</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الحساب المستلم</label>
                                            <select class="form-control" name="accountCode">
                                                <option value="1111">الصندوق</option>
                                                <option value="1112">البنك الأهلي</option>
                                                <option value="1113">البنك الراجحي</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الوصف</label>
                                    <textarea class="form-control" name="description" rows="2"
                                              placeholder="وصف المقبوض أو سبب الاستلام"></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">نوع الإيراد</label>
                                            <select class="form-control" name="revenueType">
                                                <option value="4110">مبيعات تذاكر الطيران</option>
                                                <option value="4120">مبيعات الفنادق</option>
                                                <option value="4130">مبيعات الحج والعمرة</option>
                                                <option value="4140">مبيعات التأشيرات</option>
                                                <option value="4150">إيرادات أخرى</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">حالة المقبوض</label>
                                            <select class="form-control" name="status">
                                                <option value="posted">مرحل</option>
                                                <option value="pending">في انتظار الموافقة</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-success" onclick="AccountingComponent.saveNewReceipt()">
                                <i class="fas fa-save me-1"></i>حفظ المقبوض
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('newReceiptModal'));
        modal.show();

        document.getElementById('newReceiptModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * عرض نافذة مدفوع جديد
     */
    showPaymentModal: function() {
        const suppliers = window.Database ? window.Database.findAll('suppliers') : [];

        const modalHTML = `
            <div class="modal fade" id="newPaymentModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title"><i class="fas fa-arrow-up me-2"></i>مدفوع جديد</h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="newPaymentForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">التاريخ *</label>
                                            <input type="date" class="form-control" name="date"
                                                   value="${new Date().toISOString().split('T')[0]}" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رقم المرجع</label>
                                            <input type="text" class="form-control" name="reference"
                                                   placeholder="رقم الشيك أو المرجع">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label class="form-label">المورد/المستفيد *</label>
                                            <select class="form-control" name="supplier" required>
                                                <option value="">اختر المورد</option>
                                                <option value="cash">نقدي</option>
                                                ${suppliers.map(supplier => `
                                                    <option value="${supplier.id}">${supplier.name}</option>
                                                `).join('')}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">المبلغ *</label>
                                            <input type="number" class="form-control" name="amount"
                                                   step="0.01" min="0" required placeholder="0.00">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">طريقة الدفع *</label>
                                            <select class="form-control" name="paymentMethod" required>
                                                <option value="">اختر طريقة الدفع</option>
                                                <option value="cash">نقدي</option>
                                                <option value="bank_transfer">تحويل بنكي</option>
                                                <option value="check">شيك</option>
                                                <option value="credit_card">بطاقة ائتمان</option>
                                                <option value="online">دفع إلكتروني</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الحساب المدفوع منه</label>
                                            <select class="form-control" name="accountCode">
                                                <option value="1111">الصندوق</option>
                                                <option value="1112">البنك الأهلي</option>
                                                <option value="1113">البنك الراجحي</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الوصف</label>
                                    <textarea class="form-control" name="description" rows="2"
                                              placeholder="وصف المدفوع أو سبب الدفع"></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">نوع المصروف</label>
                                            <select class="form-control" name="expenseType">
                                                <option value="5210">رواتب الموظفين</option>
                                                <option value="5220">إيجار المكتب</option>
                                                <option value="5230">عمولات الوكلاء</option>
                                                <option value="5240">مصروفات التشغيل</option>
                                                <option value="5250">مصروفات أخرى</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">حالة المدفوع</label>
                                            <select class="form-control" name="status">
                                                <option value="posted">مرحل</option>
                                                <option value="pending">في انتظار الموافقة</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-danger" onclick="AccountingComponent.saveNewPayment()">
                                <i class="fas fa-save me-1"></i>حفظ المدفوع
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('newPaymentModal'));
        modal.show();

        document.getElementById('newPaymentModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * حفظ المقبوض الجديد
     */
    saveNewReceipt: function() {
        const form = document.getElementById('newReceiptForm');
        const formData = new FormData(form);

        try {
            const receiptData = {
                date: formData.get('date'),
                customer: formData.get('customer'),
                amount: parseFloat(formData.get('amount')),
                paymentMethod: formData.get('paymentMethod'),
                accountCode: formData.get('accountCode') || '1111',
                revenueType: formData.get('revenueType') || '4150',
                description: formData.get('description') || 'مقبوض نقدي',
                reference: formData.get('reference'),
                status: formData.get('status') || 'posted'
            };

            // التحقق من صحة البيانات
            if (!receiptData.customer || !receiptData.amount || receiptData.amount <= 0) {
                throw new Error('يرجى ملء جميع الحقول المطلوبة');
            }

            // إنشاء قيد المقبوض
            const entryData = {
                date: receiptData.date,
                description: receiptData.description,
                reference: receiptData.reference,
                type: 'receipt',
                status: receiptData.status,
                transactions: [
                    {
                        accountCode: receiptData.accountCode,
                        description: receiptData.description,
                        debit: receiptData.amount,
                        credit: 0
                    },
                    {
                        accountCode: receiptData.revenueType,
                        description: receiptData.description,
                        debit: 0,
                        credit: receiptData.amount
                    }
                ]
            };

            // إضافة معاملة العميل إذا لم يكن نقدي
            if (receiptData.customer !== 'cash') {
                const customerAccountCode = `1120${receiptData.customer.slice(-3)}`;
                entryData.transactions[0] = {
                    accountCode: customerAccountCode,
                    description: receiptData.description,
                    debit: 0,
                    credit: receiptData.amount
                };
                entryData.transactions.push({
                    accountCode: receiptData.accountCode,
                    description: receiptData.description,
                    debit: receiptData.amount,
                    credit: 0
                });
            }

            // إنشاء القيد
            const entry = window.Accounting.createJournalEntry(entryData);

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('newReceiptModal'));
            modal.hide();

            // تحديث العرض
            this.refreshData();

            if (window.Notifications) {
                window.Notifications.success(`تم إنشاء المقبوض رقم ${entry.entryNumber} بنجاح`);
            }

        } catch (error) {
            console.error('خطأ في حفظ المقبوض:', error);
            if (window.Notifications) {
                window.Notifications.error('فشل في حفظ المقبوض: ' + error.message);
            }
        }
    },

    /**
     * حفظ المدفوع الجديد
     */
    saveNewPayment: function() {
        const form = document.getElementById('newPaymentForm');
        const formData = new FormData(form);

        try {
            const paymentData = {
                date: formData.get('date'),
                supplier: formData.get('supplier'),
                amount: parseFloat(formData.get('amount')),
                paymentMethod: formData.get('paymentMethod'),
                accountCode: formData.get('accountCode') || '1111',
                expenseType: formData.get('expenseType') || '5250',
                description: formData.get('description') || 'مدفوع نقدي',
                reference: formData.get('reference'),
                status: formData.get('status') || 'posted'
            };

            // التحقق من صحة البيانات
            if (!paymentData.supplier || !paymentData.amount || paymentData.amount <= 0) {
                throw new Error('يرجى ملء جميع الحقول المطلوبة');
            }

            // إنشاء قيد المدفوع
            const entryData = {
                date: paymentData.date,
                description: paymentData.description,
                reference: paymentData.reference,
                type: 'payment',
                status: paymentData.status,
                transactions: [
                    {
                        accountCode: paymentData.expenseType,
                        description: paymentData.description,
                        debit: paymentData.amount,
                        credit: 0
                    },
                    {
                        accountCode: paymentData.accountCode,
                        description: paymentData.description,
                        debit: 0,
                        credit: paymentData.amount
                    }
                ]
            };

            // إضافة معاملة المورد إذا لم يكن نقدي
            if (paymentData.supplier !== 'cash') {
                const supplierAccountCode = `2110${paymentData.supplier.slice(-3)}`;
                entryData.transactions[0] = {
                    accountCode: supplierAccountCode,
                    description: paymentData.description,
                    debit: paymentData.amount,
                    credit: 0
                };
                entryData.transactions.push({
                    accountCode: paymentData.accountCode,
                    description: paymentData.description,
                    debit: 0,
                    credit: paymentData.amount
                });
            }

            // إنشاء القيد
            const entry = window.Accounting.createJournalEntry(entryData);

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('newPaymentModal'));
            modal.hide();

            // تحديث العرض
            this.refreshData();

            if (window.Notifications) {
                window.Notifications.success(`تم إنشاء المدفوع رقم ${entry.entryNumber} بنجاح`);
            }

        } catch (error) {
            console.error('خطأ في حفظ المدفوع:', error);
            if (window.Notifications) {
                window.Notifications.error('فشل في حفظ المدفوع: ' + error.message);
            }
        }
    },

    /**
     * الحصول على اسم الطرف
     */
    getPartyName: function(accountCode, type) {
        if (accountCode.startsWith('1111') || accountCode.startsWith('1112')) {
            return 'نقدي';
        }

        if (type === 'customer' && window.Database) {
            const customers = window.Database.findAll('customers');
            const customer = customers.find(c => accountCode.includes(c.id.slice(-3)));
            return customer ? customer.name : 'عميل غير محدد';
        }

        if (type === 'supplier' && window.Database) {
            const suppliers = window.Database.findAll('suppliers');
            const supplier = suppliers.find(s => accountCode.includes(s.id.slice(-3)));
            return supplier ? supplier.name : 'مورد غير محدد';
        }

        return 'غير محدد';
    },

    /**
     * الحصول على طريقة الدفع
     */
    getPaymentMethod: function(accountCode) {
        if (accountCode.startsWith('1111')) return 'نقدي';
        if (accountCode.startsWith('1112')) return 'بنكي';
        return 'غير محدد';
    },

    /**
     * الحصول على فئة شارة حالة الدفع
     */
    getPaymentStatusBadgeClass: function(status) {
        const classes = {
            'posted': 'bg-success',
            'pending': 'bg-warning',
            'approved': 'bg-primary',
            'rejected': 'bg-danger'
        };
        return classes[status] || 'bg-secondary';
    },

    /**
     * الحصول على تسمية حالة الدفع
     */
    getPaymentStatusLabel: function(status) {
        const labels = {
            'posted': 'مرحل',
            'pending': 'معلق',
            'approved': 'معتمد',
            'rejected': 'مرفوض'
        };
        return labels[status] || status;
    },

    /**
     * تعديل الحساب
     */
    editAccount: function(accountCode) {
        const account = window.Accounting.chartOfAccounts[accountCode];
        if (!account) {
            if (window.Notifications) {
                window.Notifications.error('الحساب غير موجود');
            }
            return;
        }

        const modalHTML = `
            <div class="modal fade" id="editAccountModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تعديل الحساب: ${account.code} - ${account.name}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="editAccountForm">
                                <input type="hidden" name="originalCode" value="${account.code}">

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رمز الحساب *</label>
                                            <input type="text" class="form-control" name="code" value="${account.code}"
                                                   required maxlength="10" ${account.level <= 2 ? 'readonly' : ''}>
                                            <div class="form-text">
                                                ${account.level <= 2 ? 'لا يمكن تعديل رمز الحسابات الرئيسية' : 'يجب أن يكون رمز الحساب فريداً'}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">نوع الحساب *</label>
                                            <select class="form-control" name="type" required ${account.level <= 2 ? 'disabled' : ''}>
                                                <option value="asset" ${account.type === 'asset' ? 'selected' : ''}>أصل</option>
                                                <option value="liability" ${account.type === 'liability' ? 'selected' : ''}>خصم</option>
                                                <option value="equity" ${account.type === 'equity' ? 'selected' : ''}>حقوق ملكية</option>
                                                <option value="revenue" ${account.type === 'revenue' ? 'selected' : ''}>إيراد</option>
                                                <option value="expense" ${account.type === 'expense' ? 'selected' : ''}>مصروف</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label class="form-label">اسم الحساب (عربي) *</label>
                                            <input type="text" class="form-control" name="name" value="${account.name}" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">الحساب الأب</label>
                                            <select class="form-control" name="parent" ${account.level <= 2 ? 'disabled' : ''}>
                                                <option value="">حساب رئيسي</option>
                                                ${this.renderParentAccountOptions(account.type, account.parent)}
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label class="form-label">اسم الحساب (إنجليزي)</label>
                                            <input type="text" class="form-control" name="nameEn" value="${account.nameEn || ''}">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">التصنيف</label>
                                            <input type="text" class="form-control" name="category" value="${account.category || ''}">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الرصيد الحالي</label>
                                            <input type="number" class="form-control" name="balance"
                                                   value="${account.balance || 0}" step="0.01" readonly>
                                            <div class="form-text">الرصيد محسوب تلقائياً من المعاملات</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الحالة</label>
                                            <select class="form-control" name="isActive">
                                                <option value="true" ${account.isActive ? 'selected' : ''}>نشط</option>
                                                <option value="false" ${!account.isActive ? 'selected' : ''}>غير نشط</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الوصف</label>
                                    <textarea class="form-control" name="description" rows="2">${account.description || ''}</textarea>
                                </div>

                                ${account.children && account.children.length > 0 ? `
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>تنبيه:</strong> هذا الحساب يحتوي على ${account.children.length} حساب فرعي.
                                        تعديل النوع قد يؤثر على الحسابات الفرعية.
                                    </div>
                                ` : ''}
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="AccountingComponent.saveAccountEdit()">
                                <i class="fas fa-save me-1"></i>حفظ التعديلات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('editAccountModal'));
        modal.show();

        document.getElementById('editAccountModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * عرض خيارات الحساب الأب للتعديل
     */
    renderParentAccountOptions: function(accountType, currentParent) {
        const parentAccounts = Object.values(window.Accounting.chartOfAccounts)
            .filter(account => account.type === accountType && account.level < 3)
            .sort((a, b) => a.code.localeCompare(b.code));

        return parentAccounts.map(account => `
            <option value="${account.code}" ${account.code === currentParent ? 'selected' : ''}>
                ${account.code} - ${account.name}
            </option>
        `).join('');
    },

    /**
     * حفظ تعديل الحساب
     */
    saveAccountEdit: function() {
        const form = document.getElementById('editAccountForm');
        const formData = new FormData(form);

        try {
            const originalCode = formData.get('originalCode');
            const account = window.Accounting.chartOfAccounts[originalCode];

            if (!account) {
                throw new Error('الحساب الأصلي غير موجود');
            }

            const updatedData = {
                code: formData.get('code').trim(),
                name: formData.get('name').trim(),
                nameEn: formData.get('nameEn')?.trim() || '',
                type: formData.get('type'),
                category: formData.get('category')?.trim() || '',
                parent: formData.get('parent') || null,
                isActive: formData.get('isActive') === 'true',
                description: formData.get('description')?.trim() || ''
            };

            // التحقق من صحة البيانات
            if (!updatedData.code || !updatedData.name || !updatedData.type) {
                throw new Error('يرجى ملء جميع الحقول المطلوبة');
            }

            // التحقق من عدم تكرار رمز الحساب (إذا تم تغييره)
            if (updatedData.code !== originalCode && window.Accounting.chartOfAccounts[updatedData.code]) {
                throw new Error('رمز الحساب الجديد موجود مسبقاً');
            }

            // تحديث الحساب
            const updatedAccount = {
                ...account,
                ...updatedData,
                level: account.level, // المحافظة على المستوى
                balance: account.balance, // المحافظة على الرصيد
                children: account.children || []
            };

            // إذا تم تغيير رمز الحساب
            if (updatedData.code !== originalCode) {
                // حذف الحساب القديم
                delete window.Accounting.chartOfAccounts[originalCode];

                // تحديث المراجع في الحسابات الفرعية
                if (account.children) {
                    account.children.forEach(childCode => {
                        const childAccount = window.Accounting.chartOfAccounts[childCode];
                        if (childAccount && childAccount.parent === originalCode) {
                            childAccount.parent = updatedData.code;
                        }
                    });
                }

                // تحديث المراجع في الحساب الأب
                if (account.parent) {
                    const parentAccount = window.Accounting.chartOfAccounts[account.parent];
                    if (parentAccount && parentAccount.children) {
                        const index = parentAccount.children.indexOf(originalCode);
                        if (index > -1) {
                            parentAccount.children[index] = updatedData.code;
                        }
                    }
                }
            }

            // إضافة الحساب المحدث
            window.Accounting.chartOfAccounts[updatedData.code] = updatedAccount;

            // حفظ البيانات
            window.Accounting.saveAccountingData();

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('editAccountModal'));
            modal.hide();

            // تحديث العرض
            this.refreshData();

            if (window.Notifications) {
                window.Notifications.success(`تم تحديث الحساب ${updatedData.code} بنجاح`);
            }

        } catch (error) {
            console.error('خطأ في تعديل الحساب:', error);
            if (window.Notifications) {
                window.Notifications.error('فشل في تعديل الحساب: ' + error.message);
            }
        }
    },

    /**
     * حذف الحساب
     */
    deleteAccount: function(accountCode) {
        const account = window.Accounting.chartOfAccounts[accountCode];
        if (!account) {
            if (window.Notifications) {
                window.Notifications.error('الحساب غير موجود');
            }
            return;
        }

        // التحقق من وجود حسابات فرعية
        if (account.children && account.children.length > 0) {
            if (window.Notifications) {
                window.Notifications.error('لا يمكن حذف حساب يحتوي على حسابات فرعية');
            }
            return;
        }

        // التحقق من وجود معاملات
        const hasTransactions = this.getAccountTransactions(accountCode).length > 0;
        if (hasTransactions) {
            if (window.Notifications) {
                window.Notifications.error('لا يمكن حذف حساب يحتوي على معاملات');
            }
            return;
        }

        // التحقق من أن الحساب ليس من الحسابات الأساسية
        if (account.level <= 2) {
            if (window.Notifications) {
                window.Notifications.error('لا يمكن حذف الحسابات الأساسية');
            }
            return;
        }

        // تأكيد الحذف
        if (!confirm(`هل أنت متأكد من حذف الحساب "${account.code} - ${account.name}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            return;
        }

        try {
            // حذف الحساب من الحساب الأب
            if (account.parent) {
                const parentAccount = window.Accounting.chartOfAccounts[account.parent];
                if (parentAccount && parentAccount.children) {
                    const index = parentAccount.children.indexOf(accountCode);
                    if (index > -1) {
                        parentAccount.children.splice(index, 1);
                    }
                }
            }

            // حذف الحساب
            delete window.Accounting.chartOfAccounts[accountCode];

            // حفظ البيانات
            window.Accounting.saveAccountingData();

            // تحديث العرض
            this.refreshData();

            if (window.Notifications) {
                window.Notifications.success(`تم حذف الحساب ${accountCode} بنجاح`);
            }

        } catch (error) {
            console.error('خطأ في حذف الحساب:', error);
            if (window.Notifications) {
                window.Notifications.error('فشل في حذف الحساب: ' + error.message);
            }
        }
    },

    /**
     * عرض كشف الحساب التفصيلي
     */
    viewAccountStatement: function(accountCode) {
        const account = window.Accounting.chartOfAccounts[accountCode];
        if (!account) {
            if (window.Notifications) {
                window.Notifications.error('الحساب غير موجود');
            }
            return;
        }

        const transactions = this.getAccountTransactions(accountCode);
        const balance = window.Accounting.getAccountBalance(accountCode);

        const modalHTML = `
            <div class="modal fade" id="accountStatementModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">كشف حساب: ${account.code} - ${account.name}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <!-- فلاتر كشف الحساب -->
                            <div class="card mb-3">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label class="form-label">من تاريخ</label>
                                            <input type="date" class="form-control" id="statementDateFrom"
                                                   value="${new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0]}">
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">إلى تاريخ</label>
                                            <input type="date" class="form-control" id="statementDateTo"
                                                   value="${new Date().toISOString().split('T')[0]}">
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">نوع المعاملة</label>
                                            <select class="form-control" id="statementTransactionType">
                                                <option value="">جميع المعاملات</option>
                                                <option value="debit">مدين فقط</option>
                                                <option value="credit">دائن فقط</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">الإجراءات</label>
                                            <div class="btn-group w-100">
                                                <button class="btn btn-primary" onclick="AccountingComponent.filterAccountStatement('${accountCode}')">
                                                    <i class="fas fa-filter me-1"></i>فلترة
                                                </button>
                                                <button class="btn btn-outline-secondary" onclick="AccountingComponent.printAccountStatement('${accountCode}')">
                                                    <i class="fas fa-print me-1"></i>طباعة
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- معلومات الحساب -->
                            <div class="row mb-3">
                                <div class="col-md-8">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6 class="card-title">معلومات الحساب</h6>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <p><strong>رمز الحساب:</strong> ${account.code}</p>
                                                    <p><strong>اسم الحساب:</strong> ${account.name}</p>
                                                    <p><strong>نوع الحساب:</strong> <span class="badge ${this.getAccountTypeBadgeClass(account.type)}">${this.getAccountTypeLabel(account.type)}</span></p>
                                                </div>
                                                <div class="col-md-6">
                                                    <p><strong>المستوى:</strong> ${account.level}</p>
                                                    <p><strong>الحالة:</strong> <span class="badge ${account.isActive ? 'bg-success' : 'bg-secondary'}">${account.isActive ? 'نشط' : 'غير نشط'}</span></p>
                                                    <p><strong>عدد المعاملات:</strong> ${transactions.length}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-body text-center">
                                            <h6 class="card-title">الرصيد الحالي</h6>
                                            <h3 class="${this.getBalanceClass(balance, account.type)}">
                                                ${window.Accounting.formatAmount(Math.abs(balance))}
                                            </h3>
                                            <p class="text-muted">${this.getBalanceDirection(balance, account.type)}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- جدول المعاملات -->
                            <div class="card">
                                <div class="card-header">
                                    <h6><i class="fas fa-list me-2"></i>تفاصيل المعاملات</h6>
                                </div>
                                <div class="card-body p-0">
                                    <div class="table-responsive" id="statementTableContainer">
                                        ${this.renderAccountStatementTable(transactions, account)}
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <strong>إجمالي المدين: </strong>
                                            <span class="text-success">${window.Accounting.formatAmount(transactions.reduce((sum, t) => sum + (t.debit || 0), 0))}</span>
                                        </div>
                                        <div class="col-md-4">
                                            <strong>إجمالي الدائن: </strong>
                                            <span class="text-danger">${window.Accounting.formatAmount(transactions.reduce((sum, t) => sum + (t.credit || 0), 0))}</span>
                                        </div>
                                        <div class="col-md-4">
                                            <strong>الرصيد النهائي: </strong>
                                            <span class="${this.getBalanceClass(balance, account.type)}">${window.Accounting.formatAmount(Math.abs(balance))}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" onclick="AccountingComponent.exportAccountStatement('${accountCode}')">
                                <i class="fas fa-download me-1"></i>تصدير Excel
                            </button>
                            <button type="button" class="btn btn-outline-primary" onclick="AccountingComponent.printAccountStatement('${accountCode}')">
                                <i class="fas fa-print me-1"></i>طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('accountStatementModal'));
        modal.show();

        document.getElementById('accountStatementModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * عرض جدول كشف الحساب
     */
    renderAccountStatementTable: function(transactions, account) {
        if (transactions.length === 0) {
            return `
                <div class="text-center py-4">
                    <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد معاملات لهذا الحساب</p>
                </div>
            `;
        }

        return `
            <table class="table table-sm mb-0">
                <thead class="table-light">
                    <tr>
                        <th width="10%">التاريخ</th>
                        <th width="8%">رقم القيد</th>
                        <th width="25%">الوصف</th>
                        <th width="10%">المرجع</th>
                        <th width="12%">مدين</th>
                        <th width="12%">دائن</th>
                        <th width="12%">الرصيد الجاري</th>
                        <th width="11%">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    ${transactions.map(transaction => `
                        <tr>
                            <td>${new Date(transaction.date).toLocaleDateString('ar-SA')}</td>
                            <td>
                                <a href="#" onclick="AccountingComponent.viewEntry(${transaction.entryNumber})"
                                   class="text-decoration-none">
                                    <strong>${transaction.entryNumber}</strong>
                                </a>
                            </td>
                            <td>${transaction.description}</td>
                            <td>${transaction.reference || '-'}</td>
                            <td class="text-success">
                                ${transaction.debit ? window.Accounting.formatAmount(transaction.debit) : '-'}
                            </td>
                            <td class="text-danger">
                                ${transaction.credit ? window.Accounting.formatAmount(transaction.credit) : '-'}
                            </td>
                            <td class="${this.getBalanceClass(transaction.runningBalance, account.type)}">
                                ${window.Accounting.formatAmount(Math.abs(transaction.runningBalance || 0))}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="AccountingComponent.viewEntry(${transaction.entryNumber})"
                                            title="عرض القيد">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-info" onclick="AccountingComponent.printEntry(${transaction.entryNumber})"
                                            title="طباعة القيد">
                                        <i class="fas fa-print"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
    },

    /**
     * فلترة كشف الحساب
     */
    filterAccountStatement: function(accountCode) {
        const dateFrom = document.getElementById('statementDateFrom')?.value;
        const dateTo = document.getElementById('statementDateTo')?.value;
        const transactionType = document.getElementById('statementTransactionType')?.value;

        let transactions = this.getAccountTransactions(accountCode);
        const account = window.Accounting.chartOfAccounts[accountCode];

        // تطبيق الفلاتر
        if (dateFrom) {
            transactions = transactions.filter(t => new Date(t.date) >= new Date(dateFrom));
        }
        if (dateTo) {
            transactions = transactions.filter(t => new Date(t.date) <= new Date(dateTo));
        }
        if (transactionType === 'debit') {
            transactions = transactions.filter(t => t.debit > 0);
        } else if (transactionType === 'credit') {
            transactions = transactions.filter(t => t.credit > 0);
        }

        // إعادة حساب الرصيد الجاري للمعاملات المفلترة
        let runningBalance = 0;
        transactions.forEach(transaction => {
            const debit = transaction.debit || 0;
            const credit = transaction.credit || 0;

            if (account.type === 'asset' || account.type === 'expense') {
                runningBalance += debit - credit;
            } else {
                runningBalance += credit - debit;
            }
            transaction.runningBalance = runningBalance;
        });

        // تحديث الجدول
        const container = document.getElementById('statementTableContainer');
        if (container) {
            container.innerHTML = this.renderAccountStatementTable(transactions, account);
        }
    },

    /**
     * طباعة كشف الحساب
     */
    printAccountStatement: function(accountCode) {
        const account = window.Accounting.chartOfAccounts[accountCode];
        const transactions = this.getAccountTransactions(accountCode);
        const balance = window.Accounting.getAccountBalance(accountCode);

        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>كشف حساب - ${account.code} - ${account.name}</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        margin: 20px;
                        direction: rtl;
                        font-size: 12px;
                    }
                    .header {
                        text-align: center;
                        margin-bottom: 30px;
                        border-bottom: 2px solid #333;
                        padding-bottom: 20px;
                    }
                    .account-info {
                        background: #f8f9fa;
                        padding: 15px;
                        margin-bottom: 20px;
                        border-radius: 5px;
                    }
                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-bottom: 20px;
                    }
                    th, td {
                        border: 1px solid #ddd;
                        padding: 8px;
                        text-align: right;
                    }
                    th {
                        background-color: #f5f5f5;
                        font-weight: bold;
                    }
                    .debit { color: #28a745; font-weight: bold; }
                    .credit { color: #dc3545; font-weight: bold; }
                    .balance { font-weight: bold; }
                    .footer {
                        border-top: 2px solid #333;
                        padding-top: 15px;
                        margin-top: 20px;
                    }
                    .summary {
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 10px;
                    }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>كشف حساب</h1>
                    <h2>${account.code} - ${account.name}</h2>
                    <p>من ${new Date().toLocaleDateString('ar-SA')} إلى ${new Date().toLocaleDateString('ar-SA')}</p>
                </div>

                <div class="account-info">
                    <div style="display: flex; justify-content: space-between;">
                        <div>
                            <strong>نوع الحساب:</strong> ${this.getAccountTypeLabel(account.type)}<br>
                            <strong>المستوى:</strong> ${account.level}<br>
                            <strong>الحالة:</strong> ${account.isActive ? 'نشط' : 'غير نشط'}
                        </div>
                        <div>
                            <strong>الرصيد الحالي:</strong><br>
                            <span class="balance" style="font-size: 18px; color: ${balance >= 0 ? '#28a745' : '#dc3545'};">
                                ${window.Accounting.formatAmount(Math.abs(balance))}
                            </span><br>
                            <small>${this.getBalanceDirection(balance, account.type)}</small>
                        </div>
                    </div>
                </div>

                <table>
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>رقم القيد</th>
                            <th>الوصف</th>
                            <th>المرجع</th>
                            <th>مدين</th>
                            <th>دائن</th>
                            <th>الرصيد الجاري</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${transactions.map(transaction => `
                            <tr>
                                <td>${new Date(transaction.date).toLocaleDateString('ar-SA')}</td>
                                <td>${transaction.entryNumber}</td>
                                <td>${transaction.description}</td>
                                <td>${transaction.reference || '-'}</td>
                                <td class="debit">${transaction.debit ? window.Accounting.formatAmount(transaction.debit) : '-'}</td>
                                <td class="credit">${transaction.credit ? window.Accounting.formatAmount(transaction.credit) : '-'}</td>
                                <td class="balance">${window.Accounting.formatAmount(Math.abs(transaction.runningBalance || 0))}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>

                <div class="footer">
                    <div class="summary">
                        <div><strong>إجمالي المدين:</strong> <span class="debit">${window.Accounting.formatAmount(transactions.reduce((sum, t) => sum + (t.debit || 0), 0))}</span></div>
                        <div><strong>إجمالي الدائن:</strong> <span class="credit">${window.Accounting.formatAmount(transactions.reduce((sum, t) => sum + (t.credit || 0), 0))}</span></div>
                        <div><strong>الرصيد النهائي:</strong> <span class="balance">${window.Accounting.formatAmount(Math.abs(balance))}</span></div>
                    </div>
                    <div style="text-align: center; margin-top: 20px;">
                        <small>تم الطباعة في: ${new Date().toLocaleString('ar-SA')}</small>
                    </div>
                </div>
            </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    },

    /**
     * تبديل تحديد جميع الحسابات
     */
    toggleSelectAll: function() {
        const selectAllCheckbox = document.getElementById('selectAllAccounts');
        const accountCheckboxes = document.querySelectorAll('.account-checkbox');

        if (selectAllCheckbox && accountCheckboxes.length > 0) {
            const shouldCheck = selectAllCheckbox.checked;

            accountCheckboxes.forEach(checkbox => {
                checkbox.checked = shouldCheck;
            });

            // إزالة حالة indeterminate
            selectAllCheckbox.indeterminate = false;

            this.updateSelectedActions();

            // إضافة تأثير بصري
            if (shouldCheck) {
                accountCheckboxes.forEach((checkbox, index) => {
                    setTimeout(() => {
                        checkbox.closest('tr').classList.add('table-primary');
                        setTimeout(() => {
                            checkbox.closest('tr').classList.remove('table-primary');
                        }, 200);
                    }, index * 50);
                });
            }
        }
    },

    /**
     * تحديث إجراءات المحدد
     */
    updateSelectedActions: function() {
        const selectedCheckboxes = document.querySelectorAll('.account-checkbox:checked');
        const selectedActions = document.getElementById('selectedActions');

        if (selectedCheckboxes.length > 0) {
            if (selectedActions) {
                selectedActions.style.display = 'block';

                // تحديث عداد العناصر المحددة
                const countElement = selectedActions.querySelector('.selected-count');
                if (countElement) {
                    countElement.textContent = selectedCheckboxes.length;
                } else {
                    // إضافة عداد إذا لم يكن موجوداً
                    const countSpan = document.createElement('span');
                    countSpan.className = 'selected-count badge bg-primary me-2';
                    countSpan.textContent = selectedCheckboxes.length;
                    selectedActions.insertBefore(countSpan, selectedActions.firstChild);
                }
            }
        } else {
            if (selectedActions) {
                selectedActions.style.display = 'none';

                // إزالة العداد
                const countElement = selectedActions.querySelector('.selected-count');
                if (countElement) {
                    countElement.remove();
                }
            }
        }

        // تحديث حالة checkbox "تحديد الكل"
        const selectAllCheckbox = document.getElementById('selectAllAccounts');
        const allCheckboxes = document.querySelectorAll('.account-checkbox');

        if (selectAllCheckbox && allCheckboxes.length > 0) {
            if (selectedCheckboxes.length === 0) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = false;
            } else if (selectedCheckboxes.length === allCheckboxes.length) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = true;
            } else {
                selectAllCheckbox.indeterminate = true;
                selectAllCheckbox.checked = false;
            }
        }
    },

    /**
     * إلغاء تفعيل الحسابات المحددة
     */
    bulkDeactivate: function() {
        const selectedCheckboxes = document.querySelectorAll('.account-checkbox:checked');
        const selectedCodes = Array.from(selectedCheckboxes).map(cb => cb.value);

        if (selectedCodes.length === 0) {
            this.showMessage('يرجى تحديد حسابات للتعديل', 'warning');
            return;
        }

        // فحص الحسابات التي يمكن إلغاء تفعيلها
        const validAccounts = selectedCodes.filter(code => {
            const account = window.Accounting.chartOfAccounts[code];
            return account && account.level > 2 && account.isActive;
        });

        const protectedAccounts = selectedCodes.length - validAccounts.length;

        let confirmMessage = `هل أنت متأكد من إلغاء تفعيل ${validAccounts.length} حساب؟`;
        if (protectedAccounts > 0) {
            confirmMessage += `\n\nملاحظة: ${protectedAccounts} حساب محمي ولن يتم تعديله.`;
        }

        if (!confirm(confirmMessage)) {
            return;
        }

        try {
            let updatedCount = 0;
            let skippedCount = 0;
            const errors = [];

            selectedCodes.forEach(code => {
                const account = window.Accounting.chartOfAccounts[code];
                if (!account) {
                    errors.push(`الحساب ${code} غير موجود`);
                    return;
                }

                if (account.level <= 2) {
                    skippedCount++;
                    return;
                }

                if (!account.isActive) {
                    skippedCount++;
                    return;
                }

                // فحص وجود أرصدة
                const balance = Math.abs(account.balance || 0);
                if (balance > 0.01) {
                    if (!confirm(`الحساب ${account.code} - ${account.name} له رصيد ${window.Accounting.formatAmount(balance)}. هل تريد المتابعة؟`)) {
                        skippedCount++;
                        return;
                    }
                }

                account.isActive = false;
                account.deactivatedAt = new Date().toISOString();
                updatedCount++;
            });

            if (updatedCount > 0) {
                window.Accounting.saveAccountingData();
                this.refreshData();
            }

            // عرض النتائج
            let message = `تم إلغاء تفعيل ${updatedCount} حساب بنجاح`;
            if (skippedCount > 0) {
                message += `\nتم تخطي ${skippedCount} حساب`;
            }
            if (errors.length > 0) {
                message += `\nأخطاء: ${errors.join(', ')}`;
            }

            this.showMessage(message, updatedCount > 0 ? 'success' : 'info');

        } catch (error) {
            console.error('خطأ في إلغاء تفعيل الحسابات:', error);
            this.showMessage('فشل في إلغاء تفعيل الحسابات: ' + error.message, 'error');
        }
    },

    /**
     * تفعيل الحسابات المحددة
     */
    bulkActivate: function() {
        const selectedCheckboxes = document.querySelectorAll('.account-checkbox:checked');
        const selectedCodes = Array.from(selectedCheckboxes).map(cb => cb.value);

        if (selectedCodes.length === 0) {
            this.showMessage('يرجى تحديد حسابات للتعديل', 'warning');
            return;
        }

        // فحص الحسابات التي يمكن تفعيلها
        const inactiveAccounts = selectedCodes.filter(code => {
            const account = window.Accounting.chartOfAccounts[code];
            return account && !account.isActive;
        });

        const alreadyActive = selectedCodes.length - inactiveAccounts.length;

        let confirmMessage = `هل أنت متأكد من تفعيل ${inactiveAccounts.length} حساب؟`;
        if (alreadyActive > 0) {
            confirmMessage += `\n\nملاحظة: ${alreadyActive} حساب نشط بالفعل.`;
        }

        if (!confirm(confirmMessage)) {
            return;
        }

        try {
            let updatedCount = 0;
            let skippedCount = 0;
            const errors = [];

            selectedCodes.forEach(code => {
                const account = window.Accounting.chartOfAccounts[code];
                if (!account) {
                    errors.push(`الحساب ${code} غير موجود`);
                    return;
                }

                if (account.isActive) {
                    skippedCount++;
                    return;
                }

                account.isActive = true;
                account.activatedAt = new Date().toISOString();
                delete account.deactivatedAt;
                updatedCount++;
            });

            if (updatedCount > 0) {
                window.Accounting.saveAccountingData();
                this.refreshData();
            }

            // عرض النتائج
            let message = `تم تفعيل ${updatedCount} حساب بنجاح`;
            if (skippedCount > 0) {
                message += `\nتم تخطي ${skippedCount} حساب (نشط بالفعل)`;
            }
            if (errors.length > 0) {
                message += `\nأخطاء: ${errors.join(', ')}`;
            }

            this.showMessage(message, updatedCount > 0 ? 'success' : 'info');

        } catch (error) {
            console.error('خطأ في تفعيل الحسابات:', error);
            this.showMessage('فشل في تفعيل الحسابات: ' + error.message, 'error');
        }
    },

    /**
     * عرض رسالة للمستخدم
     */
    showMessage: function(message, type = 'info') {
        if (window.Notifications) {
            switch (type) {
                case 'success':
                    window.Notifications.success(message);
                    break;
                case 'error':
                    window.Notifications.error(message);
                    break;
                case 'warning':
                    window.Notifications.warning(message);
                    break;
                default:
                    window.Notifications.info(message);
            }
        } else {
            // fallback إلى alert إذا لم يكن نظام الإشعارات متاحاً
            alert(message);
        }
    },

    /**
     * تصدير الحسابات المحددة
     */
    bulkExport: function() {
        const selectedCheckboxes = document.querySelectorAll('.account-checkbox:checked');
        const selectedCodes = Array.from(selectedCheckboxes).map(cb => cb.value);

        if (selectedCodes.length === 0) {
            this.showMessage('يرجى تحديد حسابات للتصدير', 'warning');
            return;
        }

        try {
            const selectedAccounts = selectedCodes.map(code => window.Accounting.chartOfAccounts[code]).filter(Boolean);

            if (selectedAccounts.length === 0) {
                this.showMessage('لا توجد حسابات صحيحة للتصدير', 'warning');
                return;
            }

            // عرض خيارات التصدير
            this.showExportOptionsModal(selectedAccounts, 'الحسابات_المحددة');

        } catch (error) {
            console.error('خطأ في تصدير الحسابات:', error);
            this.showMessage('فشل في تصدير الحسابات: ' + error.message, 'error');
        }
    },

    /**
     * عرض نافذة خيارات التصدير
     */
    showExportOptionsModal: function(accounts, defaultFilename) {
        const modalHTML = `
            <div class="modal fade" id="exportOptionsModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">خيارات التصدير</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="exportOptionsForm">
                                <div class="mb-3">
                                    <label class="form-label">اسم الملف</label>
                                    <input type="text" class="form-control" name="filename"
                                           value="${defaultFilename}" required>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">تنسيق التصدير</label>
                                    <select class="form-control" name="format" required>
                                        <option value="excel">Excel (.xlsx)</option>
                                        <option value="csv">CSV (.csv)</option>
                                        <option value="json">JSON (.json)</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">البيانات المطلوب تصديرها</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="includeBasicInfo" checked>
                                        <label class="form-check-label">المعلومات الأساسية</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="includeBalances" checked>
                                        <label class="form-check-label">الأرصدة</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="includeTransactions">
                                        <label class="form-check-label">المعاملات (قد يكون الملف كبيراً)</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="includeInactive">
                                        <label class="form-check-label">تضمين الحسابات غير النشطة</label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <small class="text-muted">
                                        سيتم تصدير ${accounts.length} حساب
                                    </small>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="AccountingComponent.executeExport()">
                                <i class="fas fa-download me-1"></i>تصدير
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // حفظ البيانات للاستخدام لاحقاً
        this.exportData = accounts;

        const modal = new bootstrap.Modal(document.getElementById('exportOptionsModal'));
        modal.show();

        document.getElementById('exportOptionsModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * تنفيذ التصدير
     */
    executeExport: function() {
        const form = document.getElementById('exportOptionsForm');
        const formData = new FormData(form);

        const options = {
            filename: formData.get('filename'),
            format: formData.get('format'),
            includeBasicInfo: formData.has('includeBasicInfo'),
            includeBalances: formData.has('includeBalances'),
            includeTransactions: formData.has('includeTransactions'),
            includeInactive: formData.has('includeInactive')
        };

        try {
            let accounts = this.exportData;

            // فلترة الحسابات حسب الخيارات
            if (!options.includeInactive) {
                accounts = accounts.filter(account => account.isActive);
            }

            switch (options.format) {
                case 'excel':
                    this.exportAccountsToExcel(accounts, options.filename, options);
                    break;
                case 'csv':
                    this.exportAccountsToCSV(accounts, options.filename, options);
                    break;
                case 'json':
                    this.exportAccountsToJSON(accounts, options.filename, options);
                    break;
            }

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('exportOptionsModal'));
            modal.hide();

            this.showMessage(`تم تصدير ${accounts.length} حساب بنجاح`, 'success');

        } catch (error) {
            console.error('خطأ في تنفيذ التصدير:', error);
            this.showMessage('فشل في التصدير: ' + error.message, 'error');
        }
    },

    /**
     * تصدير جميع الحسابات
     */
    exportAccounts: function() {
        const accounts = Object.values(window.Accounting.chartOfAccounts);
        this.exportAccountsToExcel(accounts, 'دليل_الحسابات');
    },

    /**
     * تصدير الحسابات إلى Excel
     */
    exportAccountsToExcel: function(accounts, filename, options = {}) {
        try {
            // إعداد البيانات للتصدير
            const exportData = [];

            accounts.forEach(account => {
                const rowData = {};

                if (options.includeBasicInfo !== false) {
                    rowData['رمز الحساب'] = account.code;
                    rowData['اسم الحساب'] = account.name;
                    rowData['الاسم الإنجليزي'] = account.nameEn || '';
                    rowData['نوع الحساب'] = this.getAccountTypeLabel(account.type);
                    rowData['المستوى'] = account.level;
                    rowData['الحساب الأب'] = account.parent || '';
                    rowData['التصنيف'] = account.category || '';
                    rowData['الحالة'] = account.isActive ? 'نشط' : 'غير نشط';
                    rowData['الوصف'] = account.description || '';
                }

                if (options.includeBalances !== false) {
                    rowData['الرصيد'] = account.balance || 0;
                    rowData['اتجاه الرصيد'] = this.getBalanceDirection(account.balance || 0, account.type);
                }

                if (options.includeTransactions) {
                    const transactions = this.getAccountTransactions(account.code);
                    rowData['عدد المعاملات'] = transactions.length;
                    rowData['آخر معاملة'] = transactions.length > 0 ?
                        new Date(transactions[transactions.length - 1].date).toLocaleDateString('ar-SA') : 'لا توجد';
                }

                exportData.push(rowData);
            });

            // إنشاء ورقة العمل
            const ws = XLSX.utils.json_to_sheet(exportData);

            // إعداد عرض الأعمدة
            const colWidths = Object.keys(exportData[0] || {}).map(key => {
                switch (key) {
                    case 'رمز الحساب': return { wch: 12 };
                    case 'اسم الحساب': return { wch: 30 };
                    case 'الاسم الإنجليزي': return { wch: 25 };
                    case 'الوصف': return { wch: 30 };
                    default: return { wch: 15 };
                }
            });
            ws['!cols'] = colWidths;

            // إضافة معلومات إضافية في الأعلى
            const headerInfo = [
                [`تقرير دليل الحسابات`],
                [`تاريخ التصدير: ${new Date().toLocaleDateString('ar-SA')}`],
                [`عدد الحسابات: ${accounts.length}`],
                [''], // سطر فارغ
            ];

            XLSX.utils.sheet_add_aoa(ws, headerInfo, { origin: 'A1' });

            // تحديث نطاق البيانات
            const range = XLSX.utils.decode_range(ws['!ref']);
            range.s.r = 4; // البدء من الصف الخامس
            ws['!ref'] = XLSX.utils.encode_range(range);

            // إنشاء المصنف
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'دليل الحسابات');

            // تصدير الملف
            XLSX.writeFile(wb, `${filename}_${new Date().toISOString().split('T')[0]}.xlsx`);

            return true;

        } catch (error) {
            console.error('خطأ في تصدير الحسابات:', error);
            throw error;
        }
    },

    /**
     * تصدير الحسابات إلى CSV
     */
    exportAccountsToCSV: function(accounts, filename, options = {}) {
        try {
            const exportData = accounts.map(account => {
                const row = {};

                if (options.includeBasicInfo !== false) {
                    row['Account Code'] = account.code;
                    row['Account Name'] = account.name;
                    row['English Name'] = account.nameEn || '';
                    row['Account Type'] = account.type;
                    row['Level'] = account.level;
                    row['Parent Account'] = account.parent || '';
                    row['Category'] = account.category || '';
                    row['Status'] = account.isActive ? 'Active' : 'Inactive';
                    row['Description'] = account.description || '';
                }

                if (options.includeBalances !== false) {
                    row['Balance'] = account.balance || 0;
                }

                return row;
            });

            // تحويل إلى CSV
            const csv = this.convertToCSV(exportData);

            // تنزيل الملف
            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `${filename}_${new Date().toISOString().split('T')[0]}.csv`;
            link.click();

            return true;

        } catch (error) {
            console.error('خطأ في تصدير CSV:', error);
            throw error;
        }
    },

    /**
     * تصدير الحسابات إلى JSON
     */
    exportAccountsToJSON: function(accounts, filename, options = {}) {
        try {
            const exportData = {
                metadata: {
                    exportDate: new Date().toISOString(),
                    totalAccounts: accounts.length,
                    options: options
                },
                accounts: accounts.map(account => {
                    const accountData = { ...account };

                    if (options.includeTransactions) {
                        accountData.transactions = this.getAccountTransactions(account.code);
                    }

                    return accountData;
                })
            };

            const jsonString = JSON.stringify(exportData, null, 2);
            const blob = new Blob([jsonString], { type: 'application/json' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `${filename}_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            return true;

        } catch (error) {
            console.error('خطأ في تصدير JSON:', error);
            throw error;
        }
    },

    /**
     * تحويل البيانات إلى CSV
     */
    convertToCSV: function(data) {
        if (!data || data.length === 0) return '';

        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row =>
                headers.map(header => {
                    const value = row[header];
                    // تنظيف القيم وإضافة علامات اقتباس إذا لزم الأمر
                    if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
                        return `"${value.replace(/"/g, '""')}"`;
                    }
                    return value;
                }).join(',')
            )
        ].join('\n');

        return csvContent;
    },

    /**
     * عرض نافذة تعديل القيد
     */
    showEditEntryModal: function(entry) {
        const modalHTML = `
            <div class="modal fade" id="editEntryModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تعديل القيد رقم ${entry.entryNumber}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="editEntryForm">
                                <input type="hidden" name="entryNumber" value="${entry.entryNumber}">

                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label class="form-label">التاريخ *</label>
                                        <input type="date" class="form-control" name="date"
                                               value="${entry.date}" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">نوع القيد</label>
                                        <select class="form-control" name="type">
                                            <option value="manual" ${entry.type === 'manual' ? 'selected' : ''}>يدوي</option>
                                            <option value="auto" ${entry.type === 'auto' ? 'selected' : ''}>تلقائي</option>
                                            <option value="booking" ${entry.type === 'booking' ? 'selected' : ''}>حجز</option>
                                            <option value="payment" ${entry.type === 'payment' ? 'selected' : ''}>دفع</option>
                                            <option value="receipt" ${entry.type === 'receipt' ? 'selected' : ''}>استلام</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">المرجع</label>
                                        <input type="text" class="form-control" name="reference"
                                               value="${entry.reference || ''}" placeholder="رقم المرجع">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الوصف *</label>
                                    <textarea class="form-control" name="description" rows="2" required>${entry.description}</textarea>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">المعاملات</label>
                                    <div id="editTransactionsContainer">
                                        ${entry.transactions.map((transaction, index) => `
                                            <div class="transaction-row border rounded p-3 mb-2">
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <label class="form-label">الحساب</label>
                                                        <select class="form-control" name="transactions[${index}][accountCode]" required>
                                                            ${this.renderAccountOptions(transaction.accountCode)}
                                                        </select>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <label class="form-label">الوصف</label>
                                                        <input type="text" class="form-control" name="transactions[${index}][description]"
                                                               value="${transaction.description}">
                                                    </div>
                                                    <div class="col-md-2">
                                                        <label class="form-label">مدين</label>
                                                        <input type="number" class="form-control" name="transactions[${index}][debit]"
                                                               step="0.01" value="${transaction.debit || ''}"
                                                               onchange="AccountingComponent.updateTransactionTotals()">
                                                    </div>
                                                    <div class="col-md-2">
                                                        <label class="form-label">دائن</label>
                                                        <input type="number" class="form-control" name="transactions[${index}][credit]"
                                                               step="0.01" value="${transaction.credit || ''}"
                                                               onchange="AccountingComponent.updateTransactionTotals()">
                                                    </div>
                                                </div>
                                                <div class="row mt-2">
                                                    <div class="col-12 text-end">
                                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                                onclick="this.closest('.transaction-row').remove(); AccountingComponent.updateTransactionTotals();">
                                                            <i class="fas fa-trash"></i> حذف
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        `).join('')}
                                    </div>

                                    <button type="button" class="btn btn-outline-primary" onclick="AccountingComponent.addEditTransaction()">
                                        <i class="fas fa-plus me-1"></i>إضافة معاملة
                                    </button>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="alert alert-info">
                                            <strong>إجمالي المدين: </strong><span id="editTotalDebit">${window.Accounting.formatAmount(entry.totalDebit)}</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="alert alert-info">
                                            <strong>إجمالي الدائن: </strong><span id="editTotalCredit">${window.Accounting.formatAmount(entry.totalCredit)}</span>
                                        </div>
                                    </div>
                                </div>

                                <div id="editBalanceAlert" class="alert alert-warning" style="display: none;">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    القيد غير متوازن! يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن.
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="AccountingComponent.saveEditedEntry()">
                                <i class="fas fa-save me-1"></i>حفظ التعديلات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('editEntryModal'));
        modal.show();

        document.getElementById('editEntryModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });

        // تحديث الإجماليات
        this.updateTransactionTotals();
    },

    /**
     * عرض خيارات الحسابات
     */
    renderAccountOptions: function(selectedCode = '') {
        const accounts = Object.values(window.Accounting.chartOfAccounts)
            .filter(account => account.isActive)
            .sort((a, b) => a.code.localeCompare(b.code));

        return accounts.map(account => `
            <option value="${account.code}" ${account.code === selectedCode ? 'selected' : ''}>
                ${account.code} - ${account.name}
            </option>
        `).join('');
    },

    /**
     * إضافة معاملة جديدة في التعديل
     */
    addEditTransaction: function() {
        const container = document.getElementById('editTransactionsContainer');
        const transactionCount = container.children.length;

        const transactionHTML = `
            <div class="transaction-row border rounded p-3 mb-2">
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-label">الحساب</label>
                        <select class="form-control" name="transactions[${transactionCount}][accountCode]" required>
                            <option value="">اختر الحساب</option>
                            ${this.renderAccountOptions()}
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">الوصف</label>
                        <input type="text" class="form-control" name="transactions[${transactionCount}][description]">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">مدين</label>
                        <input type="number" class="form-control" name="transactions[${transactionCount}][debit]"
                               step="0.01" onchange="AccountingComponent.updateTransactionTotals()">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">دائن</label>
                        <input type="number" class="form-control" name="transactions[${transactionCount}][credit]"
                               step="0.01" onchange="AccountingComponent.updateTransactionTotals()">
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-12 text-end">
                        <button type="button" class="btn btn-sm btn-outline-danger"
                                onclick="this.closest('.transaction-row').remove(); AccountingComponent.updateTransactionTotals();">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </div>
                </div>
            </div>
        `;

        container.insertAdjacentHTML('beforeend', transactionHTML);
    },

    /**
     * تحديث إجماليات المعاملات
     */
    updateTransactionTotals: function() {
        const debitInputs = document.querySelectorAll('input[name*="[debit]"]');
        const creditInputs = document.querySelectorAll('input[name*="[credit]"]');

        let totalDebit = 0;
        let totalCredit = 0;

        debitInputs.forEach(input => {
            totalDebit += parseFloat(input.value) || 0;
        });

        creditInputs.forEach(input => {
            totalCredit += parseFloat(input.value) || 0;
        });

        // تحديث العرض
        const totalDebitElement = document.getElementById('editTotalDebit') || document.getElementById('totalDebit');
        const totalCreditElement = document.getElementById('editTotalCredit') || document.getElementById('totalCredit');
        const balanceAlert = document.getElementById('editBalanceAlert') || document.getElementById('balanceAlert');

        if (totalDebitElement) {
            totalDebitElement.textContent = window.Accounting.formatAmount(totalDebit);
        }

        if (totalCreditElement) {
            totalCreditElement.textContent = window.Accounting.formatAmount(totalCredit);
        }

        // فحص التوازن
        const isBalanced = Math.abs(totalDebit - totalCredit) < 0.01;
        if (balanceAlert) {
            balanceAlert.style.display = isBalanced ? 'none' : 'block';
        }

        return { totalDebit, totalCredit, isBalanced };
    },

    /**
     * حفظ القيد المعدل
     */
    saveEditedEntry: function() {
        const form = document.getElementById('editEntryForm');
        const formData = new FormData(form);

        try {
            const entryNumber = parseInt(formData.get('entryNumber'));
            const entry = window.Accounting.entries.find(e => e.entryNumber === entryNumber);

            if (!entry) {
                throw new Error('القيد غير موجود');
            }

            // جمع بيانات المعاملات
            const transactions = [];
            const transactionInputs = form.querySelectorAll('.transaction-row');

            transactionInputs.forEach((row, index) => {
                const accountCode = formData.get(`transactions[${index}][accountCode]`);
                const description = formData.get(`transactions[${index}][description]`) || '';
                const debit = parseFloat(formData.get(`transactions[${index}][debit]`)) || 0;
                const credit = parseFloat(formData.get(`transactions[${index}][credit]`)) || 0;

                if (accountCode && (debit > 0 || credit > 0)) {
                    transactions.push({
                        accountCode,
                        description,
                        debit,
                        credit
                    });
                }
            });

            if (transactions.length === 0) {
                throw new Error('يجب إضافة معاملة واحدة على الأقل');
            }

            // فحص التوازن
            const totalDebit = transactions.reduce((sum, t) => sum + t.debit, 0);
            const totalCredit = transactions.reduce((sum, t) => sum + t.credit, 0);

            if (Math.abs(totalDebit - totalCredit) > 0.01) {
                throw new Error('القيد غير متوازن');
            }

            // تحديث القيد
            entry.date = formData.get('date');
            entry.description = formData.get('description');
            entry.reference = formData.get('reference') || '';
            entry.type = formData.get('type');
            entry.transactions = transactions;
            entry.totalDebit = totalDebit;
            entry.totalCredit = totalCredit;
            entry.updatedAt = new Date().toISOString();

            // حفظ البيانات
            window.Accounting.saveAccountingData();
            window.Accounting.updateBalances();

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('editEntryModal'));
            modal.hide();

            // تحديث العرض
            this.refreshData();

            this.showMessage(`تم تحديث القيد رقم ${entryNumber} بنجاح`, 'success');

        } catch (error) {
            console.error('خطأ في تحديث القيد:', error);
            this.showMessage('فشل في تحديث القيد: ' + error.message, 'error');
        }
    },

    /**
     * عرض تفاصيل القيد
     */
    viewEntry: function(entryNumber) {
        const entry = window.Accounting.entries.find(e => e.entryNumber === entryNumber);
        if (!entry) {
            this.showMessage('القيد غير موجود', 'error');
            return;
        }

        const modalHTML = `
            <div class="modal fade" id="viewEntryModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تفاصيل القيد رقم ${entry.entryNumber}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <!-- معلومات القيد -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-info-circle me-2"></i>معلومات القيد</h6>
                                        </div>
                                        <div class="card-body">
                                            <table class="table table-sm">
                                                <tr><td><strong>رقم القيد:</strong></td><td>${entry.entryNumber}</td></tr>
                                                <tr><td><strong>التاريخ:</strong></td><td>${new Date(entry.date).toLocaleDateString('ar-SA')}</td></tr>
                                                <tr><td><strong>النوع:</strong></td><td><span class="badge ${this.getEntryTypeBadgeClass(entry.type)}">${this.getEntryTypeLabel(entry.type)}</span></td></tr>
                                                <tr><td><strong>الحالة:</strong></td><td><span class="badge ${this.getStatusBadgeClass(entry.status)}">${this.getStatusLabel(entry.status)}</span></td></tr>
                                                <tr><td><strong>المرجع:</strong></td><td>${entry.reference || '-'}</td></tr>
                                                <tr><td><strong>تاريخ الإنشاء:</strong></td><td>${new Date(entry.createdAt).toLocaleString('ar-SA')}</td></tr>
                                                ${entry.updatedAt ? `<tr><td><strong>آخر تحديث:</strong></td><td>${new Date(entry.updatedAt).toLocaleString('ar-SA')}</td></tr>` : ''}
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-calculator me-2"></i>الإجماليات</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row text-center">
                                                <div class="col-6">
                                                    <h4 class="text-success">${window.Accounting.formatAmount(entry.totalDebit)}</h4>
                                                    <small class="text-muted">إجمالي المدين</small>
                                                </div>
                                                <div class="col-6">
                                                    <h4 class="text-danger">${window.Accounting.formatAmount(entry.totalCredit)}</h4>
                                                    <small class="text-muted">إجمالي الدائن</small>
                                                </div>
                                            </div>
                                            <hr>
                                            <div class="text-center">
                                                <div class="alert ${Math.abs(entry.totalDebit - entry.totalCredit) < 0.01 ? 'alert-success' : 'alert-warning'} mb-0">
                                                    <i class="fas ${Math.abs(entry.totalDebit - entry.totalCredit) < 0.01 ? 'fa-check-circle' : 'fa-exclamation-triangle'} me-2"></i>
                                                    ${Math.abs(entry.totalDebit - entry.totalCredit) < 0.01 ? 'القيد متوازن' : 'القيد غير متوازن'}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- وصف القيد -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6><i class="fas fa-file-alt me-2"></i>وصف القيد</h6>
                                </div>
                                <div class="card-body">
                                    <p class="mb-0">${entry.description}</p>
                                </div>
                            </div>

                            <!-- جدول المعاملات -->
                            <div class="card">
                                <div class="card-header">
                                    <h6><i class="fas fa-list me-2"></i>تفاصيل المعاملات</h6>
                                </div>
                                <div class="card-body p-0">
                                    <div class="table-responsive">
                                        <table class="table table-sm mb-0">
                                            <thead class="table-light">
                                                <tr>
                                                    <th width="15%">رمز الحساب</th>
                                                    <th width="30%">اسم الحساب</th>
                                                    <th width="30%">الوصف</th>
                                                    <th width="12%">مدين</th>
                                                    <th width="12%">دائن</th>
                                                    <th width="1%"></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${entry.transactions.map(transaction => {
                                                    const account = window.Accounting.chartOfAccounts[transaction.accountCode];
                                                    return `
                                                        <tr>
                                                            <td>
                                                                <strong>${transaction.accountCode}</strong>
                                                            </td>
                                                            <td>
                                                                ${account ? account.name : '<span class="text-danger">حساب غير موجود</span>'}
                                                                ${account && !account.isActive ? '<small class="text-warning d-block">(غير نشط)</small>' : ''}
                                                            </td>
                                                            <td>${transaction.description}</td>
                                                            <td class="text-success">
                                                                ${transaction.debit ? window.Accounting.formatAmount(transaction.debit) : '-'}
                                                            </td>
                                                            <td class="text-danger">
                                                                ${transaction.credit ? window.Accounting.formatAmount(transaction.credit) : '-'}
                                                            </td>
                                                            <td>
                                                                ${account ?
                                                                    `<button class="btn btn-sm btn-outline-info" onclick="AccountingComponent.viewAccountDetails('${account.code}')" title="عرض الحساب">
                                                                        <i class="fas fa-eye"></i>
                                                                    </button>` : ''}
                                                            </td>
                                                        </tr>
                                                    `;
                                                }).join('')}
                                            </tbody>
                                            <tfoot class="table-light">
                                                <tr>
                                                    <td colspan="3"><strong>الإجمالي</strong></td>
                                                    <td class="text-success"><strong>${window.Accounting.formatAmount(entry.totalDebit)}</strong></td>
                                                    <td class="text-danger"><strong>${window.Accounting.formatAmount(entry.totalCredit)}</strong></td>
                                                    <td></td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            ${entry.status !== 'approved' ?
                                `<button type="button" class="btn btn-outline-primary" onclick="AccountingComponent.editEntry(${entry.entryNumber}); bootstrap.Modal.getInstance(document.getElementById('viewEntryModal')).hide();">
                                    <i class="fas fa-edit me-1"></i>تعديل
                                </button>` : ''}
                            <button type="button" class="btn btn-outline-info" onclick="AccountingComponent.printEntry(${entry.entryNumber})">
                                <i class="fas fa-print me-1"></i>طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('viewEntryModal'));
        modal.show();

        document.getElementById('viewEntryModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * عرض نافذة قيد جديد
     */
    showNewEntryModal: function() {
        const modalHTML = `
            <div class="modal fade" id="newEntryModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">قيد محاسبي جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="newEntryForm">
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label class="form-label">التاريخ *</label>
                                        <input type="date" class="form-control" name="date"
                                               value="${new Date().toISOString().split('T')[0]}" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">نوع القيد</label>
                                        <select class="form-control" name="type">
                                            <option value="manual">يدوي</option>
                                            <option value="auto">تلقائي</option>
                                            <option value="booking">حجز</option>
                                            <option value="payment">دفع</option>
                                            <option value="receipt">استلام</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">المرجع</label>
                                        <input type="text" class="form-control" name="reference"
                                               placeholder="رقم المرجع">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الوصف *</label>
                                    <textarea class="form-control" name="description" rows="2" required
                                              placeholder="وصف القيد المحاسبي"></textarea>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">المعاملات</label>
                                    <div id="transactionsContainer">
                                        <!-- سيتم إضافة المعاملات هنا -->
                                    </div>

                                    <button type="button" class="btn btn-outline-primary" onclick="AccountingComponent.addTransaction()">
                                        <i class="fas fa-plus me-1"></i>إضافة معاملة
                                    </button>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="alert alert-info">
                                            <strong>إجمالي المدين: </strong><span id="totalDebit">0.00</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="alert alert-info">
                                            <strong>إجمالي الدائن: </strong><span id="totalCredit">0.00</span>
                                        </div>
                                    </div>
                                </div>

                                <div id="balanceAlert" class="alert alert-warning" style="display: none;">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    القيد غير متوازن! يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن.
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="AccountingComponent.saveNewEntry()">
                                <i class="fas fa-save me-1"></i>حفظ القيد
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('newEntryModal'));
        modal.show();

        document.getElementById('newEntryModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });

        // إضافة معاملتين افتراضيتين
        this.addTransaction();
        this.addTransaction();
    },

    /**
     * إضافة معاملة جديدة
     */
    addTransaction: function() {
        const container = document.getElementById('transactionsContainer');
        const transactionCount = container.children.length;

        const transactionHTML = `
            <div class="transaction-row border rounded p-3 mb-2">
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-label">الحساب *</label>
                        <select class="form-control" name="transactions[${transactionCount}][accountCode]" required>
                            <option value="">اختر الحساب</option>
                            ${this.renderAccountOptions()}
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">الوصف</label>
                        <input type="text" class="form-control" name="transactions[${transactionCount}][description]"
                               placeholder="وصف المعاملة">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">مدين</label>
                        <input type="number" class="form-control" name="transactions[${transactionCount}][debit]"
                               step="0.01" placeholder="0.00" onchange="AccountingComponent.updateTransactionTotals()">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">دائن</label>
                        <input type="number" class="form-control" name="transactions[${transactionCount}][credit]"
                               step="0.01" placeholder="0.00" onchange="AccountingComponent.updateTransactionTotals()">
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-12 text-end">
                        <button type="button" class="btn btn-sm btn-outline-danger"
                                onclick="this.closest('.transaction-row').remove(); AccountingComponent.updateTransactionTotals();">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </div>
                </div>
            </div>
        `;

        container.insertAdjacentHTML('beforeend', transactionHTML);
    },

    /**
     * حفظ القيد الجديد
     */
    saveNewEntry: function() {
        const form = document.getElementById('newEntryForm');
        const formData = new FormData(form);

        try {
            // جمع بيانات المعاملات
            const transactions = [];
            const transactionInputs = form.querySelectorAll('.transaction-row');

            transactionInputs.forEach((row, index) => {
                const accountCode = formData.get(`transactions[${index}][accountCode]`);
                const description = formData.get(`transactions[${index}][description]`) || '';
                const debit = parseFloat(formData.get(`transactions[${index}][debit]`)) || 0;
                const credit = parseFloat(formData.get(`transactions[${index}][credit]`)) || 0;

                if (accountCode && (debit > 0 || credit > 0)) {
                    transactions.push({
                        accountCode,
                        description,
                        debit,
                        credit
                    });
                }
            });

            if (transactions.length === 0) {
                throw new Error('يجب إضافة معاملة واحدة على الأقل');
            }

            // فحص التوازن
            const totalDebit = transactions.reduce((sum, t) => sum + t.debit, 0);
            const totalCredit = transactions.reduce((sum, t) => sum + t.credit, 0);

            if (Math.abs(totalDebit - totalCredit) > 0.01) {
                throw new Error('القيد غير متوازن');
            }

            // إنشاء القيد
            const entryData = {
                date: formData.get('date'),
                description: formData.get('description'),
                reference: formData.get('reference') || '',
                type: formData.get('type'),
                status: 'draft',
                transactions: transactions
            };

            const entry = window.Accounting.createJournalEntry(entryData);

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('newEntryModal'));
            modal.hide();

            // تحديث العرض
            this.refreshData();

            this.showMessage(`تم إنشاء القيد رقم ${entry.entryNumber} بنجاح`, 'success');

        } catch (error) {
            console.error('خطأ في إنشاء القيد:', error);
            this.showMessage('فشل في إنشاء القيد: ' + error.message, 'error');
        }
    },

    /**
     * طباعة دليل الحسابات
     */
    printAccounts: function() {
        const accounts = Object.values(window.Accounting.chartOfAccounts)
            .sort((a, b) => a.code.localeCompare(b.code));

        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>دليل الحسابات</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        margin: 20px;
                        direction: rtl;
                        font-size: 11px;
                    }
                    .header {
                        text-align: center;
                        margin-bottom: 30px;
                        border-bottom: 2px solid #333;
                        padding-bottom: 20px;
                    }
                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-bottom: 20px;
                    }
                    th, td {
                        border: 1px solid #ddd;
                        padding: 6px;
                        text-align: right;
                    }
                    th {
                        background-color: #f5f5f5;
                        font-weight: bold;
                    }
                    .level-1 { font-weight: bold; background-color: #e3f2fd; }
                    .level-2 { font-weight: bold; background-color: #f3e5f5; padding-right: 20px; }
                    .level-3 { padding-right: 40px; }
                    .level-4 { padding-right: 60px; }
                    .inactive { color: #999; text-decoration: line-through; }
                    .balance-positive { color: #28a745; font-weight: bold; }
                    .balance-negative { color: #dc3545; font-weight: bold; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>دليل الحسابات</h1>
                    <p>شركة قمة الوعد للسفريات</p>
                    <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
                </div>

                <table>
                    <thead>
                        <tr>
                            <th width="12%">رمز الحساب</th>
                            <th width="35%">اسم الحساب</th>
                            <th width="15%">النوع</th>
                            <th width="8%">المستوى</th>
                            <th width="15%">الرصيد</th>
                            <th width="10%">الحالة</th>
                            <th width="5%">فرعية</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${accounts.map(account => `
                            <tr class="level-${account.level} ${!account.isActive ? 'inactive' : ''}">
                                <td><strong>${account.code}</strong></td>
                                <td>${account.name}</td>
                                <td>${this.getAccountTypeLabel(account.type)}</td>
                                <td>${account.level}</td>
                                <td class="${account.balance > 0 ? 'balance-positive' : account.balance < 0 ? 'balance-negative' : ''}">
                                    ${account.balance !== undefined ? window.Accounting.formatAmount(Math.abs(account.balance)) : '-'}
                                </td>
                                <td>${account.isActive ? 'نشط' : 'غير نشط'}</td>
                                <td>${account.children ? account.children.length : 0}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>

                <div style="margin-top: 30px; border-top: 1px solid #ddd; padding-top: 15px;">
                    <div style="display: flex; justify-content: space-between;">
                        <div><strong>إجمالي الحسابات:</strong> ${accounts.length}</div>
                        <div><strong>الحسابات النشطة:</strong> ${accounts.filter(a => a.isActive).length}</div>
                        <div><strong>الحسابات غير النشطة:</strong> ${accounts.filter(a => !a.isActive).length}</div>
                    </div>
                </div>
            </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    },

    /**
     * ترتيب جدول الحسابات
     */
    sortTable: function(column) {
        const accounts = Object.values(window.Accounting.chartOfAccounts);
        let sortedAccounts;

        // تحديد اتجاه الترتيب
        if (!this.sortDirection) this.sortDirection = {};
        this.sortDirection[column] = this.sortDirection[column] === 'asc' ? 'desc' : 'asc';
        const direction = this.sortDirection[column];

        // ترتيب الحسابات حسب العمود المحدد
        switch (column) {
            case 'code':
                sortedAccounts = accounts.sort((a, b) => {
                    const comparison = a.code.localeCompare(b.code);
                    return direction === 'asc' ? comparison : -comparison;
                });
                break;

            case 'name':
                sortedAccounts = accounts.sort((a, b) => {
                    const comparison = a.name.localeCompare(b.name);
                    return direction === 'asc' ? comparison : -comparison;
                });
                break;

            case 'balance':
                sortedAccounts = accounts.sort((a, b) => {
                    const balanceA = a.balance || 0;
                    const balanceB = b.balance || 0;
                    return direction === 'asc' ? balanceA - balanceB : balanceB - balanceA;
                });
                break;

            default:
                sortedAccounts = accounts.sort((a, b) => a.code.localeCompare(b.code));
        }

        // تحديث الجدول
        const tbody = document.getElementById('accounts-table-body');
        if (tbody) {
            tbody.innerHTML = this.renderAccountsRows(sortedAccounts);
        }

        // تحديث أيقونات الترتيب
        this.updateSortIcons(column, direction);
    },

    /**
     * تحديث أيقونات الترتيب
     */
    updateSortIcons: function(activeColumn, direction) {
        // إزالة جميع أيقونات الترتيب
        document.querySelectorAll('.sortable i').forEach(icon => {
            icon.className = 'fas fa-sort';
        });

        // إضافة أيقونة الترتيب للعمود النشط
        const activeHeader = document.querySelector(`.sortable[onclick*="${activeColumn}"] i`);
        if (activeHeader) {
            activeHeader.className = direction === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
        }
    },

    /**
     * البحث في الحسابات
     */
    searchAccounts: function(searchTerm) {
        // استخدام الفلترة الموجودة
        document.getElementById('accountSearch').value = searchTerm;
        this.filterAccounts();
    },

    /**
     * تصدير كشف الحساب
     */
    exportAccountStatement: function(accountCode) {
        const account = window.Accounting.chartOfAccounts[accountCode];
        const transactions = this.getAccountTransactions(accountCode);

        try {
            const exportData = transactions.map(transaction => ({
                'التاريخ': new Date(transaction.date).toLocaleDateString('ar-SA'),
                'رقم القيد': transaction.entryNumber,
                'الوصف': transaction.description,
                'المرجع': transaction.reference || '',
                'مدين': transaction.debit || 0,
                'دائن': transaction.credit || 0,
                'الرصيد الجاري': transaction.runningBalance || 0
            }));

            const ws = XLSX.utils.json_to_sheet(exportData);

            // إضافة معلومات الحساب في الأعلى
            XLSX.utils.sheet_add_aoa(ws, [
                [`كشف حساب: ${account.code} - ${account.name}`],
                [`نوع الحساب: ${this.getAccountTypeLabel(account.type)}`],
                [`الرصيد الحالي: ${window.Accounting.formatAmount(Math.abs(window.Accounting.getAccountBalance(accountCode)))}`],
                [''], // سطر فارغ
            ], { origin: 'A1' });

            // تحديث نطاق البيانات
            const range = XLSX.utils.decode_range(ws['!ref']);
            range.s.r = 4; // البدء من الصف الخامس
            ws['!ref'] = XLSX.utils.encode_range(range);

            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'كشف الحساب');

            XLSX.writeFile(wb, `كشف_حساب_${account.code}_${new Date().toISOString().split('T')[0]}.xlsx`);

            if (window.Notifications) {
                window.Notifications.success('تم تصدير كشف الحساب بنجاح');
            }

        } catch (error) {
            console.error('خطأ في تصدير كشف الحساب:', error);
            if (window.Notifications) {
                window.Notifications.error('فشل في تصدير كشف الحساب');
            }
        }
    },

    /**
     * تبديل تحديد جميع القيود
     */
    toggleSelectAllEntries: function() {
        const selectAllCheckbox = document.getElementById('selectAllEntries');
        const entryCheckboxes = document.querySelectorAll('.entry-checkbox');

        entryCheckboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
        });

        this.updateSelectedEntriesActions();
    },

    /**
     * تحديث إجراءات القيود المحددة
     */
    updateSelectedEntriesActions: function() {
        const selectedCheckboxes = document.querySelectorAll('.entry-checkbox:checked');
        const selectedActions = document.getElementById('selectedEntriesActions');

        if (selectedCheckboxes.length > 0) {
            selectedActions.style.display = 'block';
        } else {
            selectedActions.style.display = 'none';
        }
    },

    /**
     * اعتماد القيود المحددة
     */
    bulkApproveEntries: function() {
        const selectedCheckboxes = document.querySelectorAll('.entry-checkbox:checked');
        const selectedNumbers = Array.from(selectedCheckboxes).map(cb => parseInt(cb.value));

        if (selectedNumbers.length === 0) {
            if (window.Notifications) {
                window.Notifications.warning('يرجى تحديد قيود للاعتماد');
            }
            return;
        }

        if (!confirm(`هل أنت متأكد من اعتماد ${selectedNumbers.length} قيد؟`)) {
            return;
        }

        try {
            let approvedCount = 0;
            selectedNumbers.forEach(entryNumber => {
                const entry = window.Accounting.entries.find(e => e.entryNumber === entryNumber);
                if (entry && entry.status !== 'approved') {
                    entry.status = 'approved';
                    entry.approvedAt = new Date().toISOString();
                    entry.approvedBy = 'النظام'; // يمكن تحديث هذا بمعلومات المستخدم الحالي
                    approvedCount++;
                }
            });

            window.Accounting.saveAccountingData();
            this.refreshData();

            if (window.Notifications) {
                window.Notifications.success(`تم اعتماد ${approvedCount} قيد بنجاح`);
            }

        } catch (error) {
            console.error('خطأ في اعتماد القيود:', error);
            if (window.Notifications) {
                window.Notifications.error('فشل في اعتماد القيود');
            }
        }
    },

    /**
     * تحويل القيود المحددة لمسودة
     */
    bulkDraftEntries: function() {
        const selectedCheckboxes = document.querySelectorAll('.entry-checkbox:checked');
        const selectedNumbers = Array.from(selectedCheckboxes).map(cb => parseInt(cb.value));

        if (selectedNumbers.length === 0) {
            if (window.Notifications) {
                window.Notifications.warning('يرجى تحديد قيود للتعديل');
            }
            return;
        }

        if (!confirm(`هل أنت متأكد من تحويل ${selectedNumbers.length} قيد إلى مسودة؟`)) {
            return;
        }

        try {
            let draftedCount = 0;
            selectedNumbers.forEach(entryNumber => {
                const entry = window.Accounting.entries.find(e => e.entryNumber === entryNumber);
                if (entry && entry.status === 'approved') {
                    entry.status = 'draft';
                    delete entry.approvedAt;
                    delete entry.approvedBy;
                    draftedCount++;
                }
            });

            window.Accounting.saveAccountingData();
            this.refreshData();

            if (window.Notifications) {
                window.Notifications.success(`تم تحويل ${draftedCount} قيد إلى مسودة`);
            }

        } catch (error) {
            console.error('خطأ في تحويل القيود:', error);
            if (window.Notifications) {
                window.Notifications.error('فشل في تحويل القيود');
            }
        }
    },

    /**
     * تصدير القيود المحددة
     */
    bulkExportEntries: function() {
        const selectedCheckboxes = document.querySelectorAll('.entry-checkbox:checked');
        const selectedNumbers = Array.from(selectedCheckboxes).map(cb => parseInt(cb.value));

        if (selectedNumbers.length === 0) {
            if (window.Notifications) {
                window.Notifications.warning('يرجى تحديد قيود للتصدير');
            }
            return;
        }

        const selectedEntries = selectedNumbers.map(num =>
            window.Accounting.entries.find(e => e.entryNumber === num)
        ).filter(Boolean);

        this.exportEntriesToExcel(selectedEntries, 'القيود_المحددة');
    },

    /**
     * حذف القيود المحددة
     */
    bulkDeleteEntries: function() {
        const selectedCheckboxes = document.querySelectorAll('.entry-checkbox:checked');
        const selectedNumbers = Array.from(selectedCheckboxes).map(cb => parseInt(cb.value));

        if (selectedNumbers.length === 0) {
            if (window.Notifications) {
                window.Notifications.warning('يرجى تحديد قيود للحذف');
            }
            return;
        }

        // التحقق من وجود قيود معتمدة
        const approvedEntries = selectedNumbers.filter(num => {
            const entry = window.Accounting.entries.find(e => e.entryNumber === num);
            return entry && entry.status === 'approved';
        });

        if (approvedEntries.length > 0) {
            if (window.Notifications) {
                window.Notifications.error('لا يمكن حذف القيود المعتمدة');
            }
            return;
        }

        if (!confirm(`هل أنت متأكد من حذف ${selectedNumbers.length} قيد؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            return;
        }

        try {
            // حذف القيود
            window.Accounting.entries = window.Accounting.entries.filter(entry =>
                !selectedNumbers.includes(entry.entryNumber)
            );

            window.Accounting.saveAccountingData();
            window.Accounting.updateBalances();
            this.refreshData();

            if (window.Notifications) {
                window.Notifications.success(`تم حذف ${selectedNumbers.length} قيد بنجاح`);
            }

        } catch (error) {
            console.error('خطأ في حذف القيود:', error);
            if (window.Notifications) {
                window.Notifications.error('فشل في حذف القيود');
            }
        }
    },

    /**
     * تصدير جميع القيود
     */
    exportEntries: function() {
        const entries = window.Accounting.entries || [];
        this.exportEntriesToExcel(entries, 'القيود_المحاسبية');
    },

    /**
     * تصدير القيود إلى Excel
     */
    exportEntriesToExcel: function(entries, filename) {
        try {
            // إعداد البيانات للتصدير
            const exportData = [];

            entries.forEach(entry => {
                entry.transactions.forEach((transaction, index) => {
                    const account = window.Accounting.chartOfAccounts[transaction.accountCode];
                    exportData.push({
                        'رقم القيد': index === 0 ? entry.entryNumber : '',
                        'التاريخ': index === 0 ? new Date(entry.date).toLocaleDateString('ar-SA') : '',
                        'الوصف العام': index === 0 ? entry.description : '',
                        'المرجع': index === 0 ? entry.reference || '' : '',
                        'النوع': index === 0 ? this.getEntryTypeLabel(entry.type) : '',
                        'الحالة': index === 0 ? this.getStatusLabel(entry.status) : '',
                        'رمز الحساب': transaction.accountCode,
                        'اسم الحساب': account ? account.name : 'حساب غير موجود',
                        'وصف المعاملة': transaction.description,
                        'مدين': transaction.debit || 0,
                        'دائن': transaction.credit || 0
                    });
                });

                // إضافة سطر فارغ بين القيود
                exportData.push({});
            });

            // إنشاء ورقة العمل
            const ws = XLSX.utils.json_to_sheet(exportData);

            // إعداد عرض الأعمدة
            const colWidths = [
                { wch: 10 }, // رقم القيد
                { wch: 12 }, // التاريخ
                { wch: 30 }, // الوصف العام
                { wch: 15 }, // المرجع
                { wch: 12 }, // النوع
                { wch: 10 }, // الحالة
                { wch: 12 }, // رمز الحساب
                { wch: 25 }, // اسم الحساب
                { wch: 25 }, // وصف المعاملة
                { wch: 15 }, // مدين
                { wch: 15 }  // دائن
            ];
            ws['!cols'] = colWidths;

            // إنشاء المصنف
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'القيود المحاسبية');

            // تصدير الملف
            XLSX.writeFile(wb, `${filename}_${new Date().toISOString().split('T')[0]}.xlsx`);

            if (window.Notifications) {
                window.Notifications.success('تم تصدير القيود بنجاح');
            }

        } catch (error) {
            console.error('خطأ في تصدير القيود:', error);
            if (window.Notifications) {
                window.Notifications.error('فشل في تصدير القيود');
            }
        }
    },

    /**
     * طباعة القيود
     */
    printEntries: function() {
        const entries = window.Accounting.entries || [];

        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>القيود المحاسبية</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        margin: 20px;
                        direction: rtl;
                        font-size: 11px;
                    }
                    .header {
                        text-align: center;
                        margin-bottom: 30px;
                        border-bottom: 2px solid #333;
                        padding-bottom: 20px;
                    }
                    .entry {
                        margin-bottom: 30px;
                        border: 1px solid #ddd;
                        border-radius: 5px;
                        padding: 15px;
                    }
                    .entry-header {
                        background: #f8f9fa;
                        padding: 10px;
                        margin: -15px -15px 15px -15px;
                        border-radius: 5px 5px 0 0;
                        border-bottom: 1px solid #ddd;
                    }
                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-bottom: 10px;
                    }
                    th, td {
                        border: 1px solid #ddd;
                        padding: 6px;
                        text-align: right;
                    }
                    th {
                        background-color: #f5f5f5;
                        font-weight: bold;
                    }
                    .debit { color: #28a745; font-weight: bold; }
                    .credit { color: #dc3545; font-weight: bold; }
                    .total-row { background-color: #e9ecef; font-weight: bold; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>القيود المحاسبية</h1>
                    <p>شركة قمة الوعد للسفريات</p>
                    <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
                </div>

                ${entries.map(entry => `
                    <div class="entry">
                        <div class="entry-header">
                            <div style="display: flex; justify-content: space-between;">
                                <div>
                                    <strong>قيد رقم: ${entry.entryNumber}</strong> |
                                    <strong>التاريخ:</strong> ${new Date(entry.date).toLocaleDateString('ar-SA')}
                                </div>
                                <div>
                                    <strong>النوع:</strong> ${this.getEntryTypeLabel(entry.type)} |
                                    <strong>الحالة:</strong> ${this.getStatusLabel(entry.status)}
                                </div>
                            </div>
                            <div style="margin-top: 5px;">
                                <strong>الوصف:</strong> ${entry.description}
                                ${entry.reference ? ` | <strong>المرجع:</strong> ${entry.reference}` : ''}
                            </div>
                        </div>

                        <table>
                            <thead>
                                <tr>
                                    <th width="15%">رمز الحساب</th>
                                    <th width="35%">اسم الحساب</th>
                                    <th width="30%">الوصف</th>
                                    <th width="10%">مدين</th>
                                    <th width="10%">دائن</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${entry.transactions.map(transaction => {
                                    const account = window.Accounting.chartOfAccounts[transaction.accountCode];
                                    return `
                                        <tr>
                                            <td>${transaction.accountCode}</td>
                                            <td>${account ? account.name : 'حساب غير موجود'}</td>
                                            <td>${transaction.description}</td>
                                            <td class="debit">${transaction.debit ? window.Accounting.formatAmount(transaction.debit) : '-'}</td>
                                            <td class="credit">${transaction.credit ? window.Accounting.formatAmount(transaction.credit) : '-'}</td>
                                        </tr>
                                    `;
                                }).join('')}
                                <tr class="total-row">
                                    <td colspan="3">الإجمالي</td>
                                    <td class="debit">${window.Accounting.formatAmount(entry.totalDebit)}</td>
                                    <td class="credit">${window.Accounting.formatAmount(entry.totalCredit)}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                `).join('')}

                <div style="margin-top: 30px; border-top: 1px solid #ddd; padding-top: 15px; text-align: center;">
                    <strong>إجمالي القيود: ${entries.length}</strong> |
                    <strong>إجمالي المدين: ${window.Accounting.formatAmount(entries.reduce((sum, e) => sum + e.totalDebit, 0))}</strong> |
                    <strong>إجمالي الدائن: ${window.Accounting.formatAmount(entries.reduce((sum, e) => sum + e.totalCredit, 0))}</strong>
                </div>
            </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    },

    /**
     * ترتيب جدول القيود
     */
    sortEntriesTable: function(column) {
        // سيتم تنفيذها لاحقاً
        console.log('ترتيب القيود حسب:', column);
    },

    /**
     * تعديل القيد
     */
    editEntry: function(entryNumber) {
        const entry = window.Accounting.entries.find(e => e.entryNumber === entryNumber);
        if (!entry) {
            if (window.Notifications) {
                window.Notifications.error('القيد غير موجود');
            }
            return;
        }

        if (entry.status === 'approved') {
            if (window.Notifications) {
                window.Notifications.error('لا يمكن تعديل قيد معتمد');
            }
            return;
        }

        // عرض نافذة التعديل (مشابهة لنافذة الإنشاء)
        this.showEditEntryModal(entry);
    },

    /**
     * حذف القيد
     */
    deleteEntry: function(entryNumber) {
        const entry = window.Accounting.entries.find(e => e.entryNumber === entryNumber);
        if (!entry) {
            if (window.Notifications) {
                window.Notifications.error('القيد غير موجود');
            }
            return;
        }

        if (entry.status === 'approved') {
            if (window.Notifications) {
                window.Notifications.error('لا يمكن حذف قيد معتمد');
            }
            return;
        }

        if (!confirm(`هل أنت متأكد من حذف القيد رقم ${entryNumber}؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            return;
        }

        try {
            // حذف القيد
            window.Accounting.entries = window.Accounting.entries.filter(e => e.entryNumber !== entryNumber);

            window.Accounting.saveAccountingData();
            window.Accounting.updateBalances();
            this.refreshData();

            if (window.Notifications) {
                window.Notifications.success(`تم حذف القيد رقم ${entryNumber} بنجاح`);
            }

        } catch (error) {
            console.error('خطأ في حذف القيد:', error);
            if (window.Notifications) {
                window.Notifications.error('فشل في حذف القيد');
            }
        }
    },

    /**
     * طباعة القيد
     */
    printEntry: function(entryNumber) {
        const entry = window.Accounting.entries.find(e => e.entryNumber === entryNumber);
        if (!entry) {
            if (window.Notifications) {
                window.Notifications.error('القيد غير موجود');
            }
            return;
        }

        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>قيد محاسبي رقم ${entry.entryNumber}</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        margin: 20px;
                        direction: rtl;
                        font-size: 12px;
                    }
                    .header {
                        text-align: center;
                        margin-bottom: 30px;
                        border-bottom: 2px solid #333;
                        padding-bottom: 20px;
                    }
                    .entry-info {
                        background: #f8f9fa;
                        padding: 15px;
                        margin-bottom: 20px;
                        border-radius: 5px;
                    }
                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-bottom: 20px;
                    }
                    th, td {
                        border: 1px solid #ddd;
                        padding: 10px;
                        text-align: right;
                    }
                    th {
                        background-color: #f5f5f5;
                        font-weight: bold;
                    }
                    .debit { color: #28a745; font-weight: bold; }
                    .credit { color: #dc3545; font-weight: bold; }
                    .total-row { background-color: #e9ecef; font-weight: bold; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>قيد محاسبي</h1>
                    <h2>رقم ${entry.entryNumber}</h2>
                    <p>شركة قمة الوعد للسفريات</p>
                </div>

                <div class="entry-info">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                        <div><strong>التاريخ:</strong> ${new Date(entry.date).toLocaleDateString('ar-SA')}</div>
                        <div><strong>النوع:</strong> ${this.getEntryTypeLabel(entry.type)}</div>
                        <div><strong>الحالة:</strong> ${this.getStatusLabel(entry.status)}</div>
                    </div>
                    <div style="margin-bottom: 10px;">
                        <strong>الوصف:</strong> ${entry.description}
                    </div>
                    ${entry.reference ? `
                        <div><strong>المرجع:</strong> ${entry.reference}</div>
                    ` : ''}
                </div>

                <table>
                    <thead>
                        <tr>
                            <th width="15%">رمز الحساب</th>
                            <th width="30%">اسم الحساب</th>
                            <th width="35%">الوصف</th>
                            <th width="10%">مدين</th>
                            <th width="10%">دائن</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${entry.transactions.map(transaction => {
                            const account = window.Accounting.chartOfAccounts[transaction.accountCode];
                            return `
                                <tr>
                                    <td>${transaction.accountCode}</td>
                                    <td>${account ? account.name : 'حساب غير موجود'}</td>
                                    <td>${transaction.description}</td>
                                    <td class="debit">${transaction.debit ? window.Accounting.formatAmount(transaction.debit) : '-'}</td>
                                    <td class="credit">${transaction.credit ? window.Accounting.formatAmount(transaction.credit) : '-'}</td>
                                </tr>
                            `;
                        }).join('')}
                        <tr class="total-row">
                            <td colspan="3">الإجمالي</td>
                            <td class="debit">${window.Accounting.formatAmount(entry.totalDebit)}</td>
                            <td class="credit">${window.Accounting.formatAmount(entry.totalCredit)}</td>
                        </tr>
                    </tbody>
                </table>

                <div style="margin-top: 40px; text-align: center;">
                    <p>تم الطباعة في: ${new Date().toLocaleString('ar-SA')}</p>
                </div>
            </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    },

    /**
     * تبديل العرض
     */
    switchView: function(view) {
        this.data.currentView = view;
        const container = document.querySelector('.accounting-content');
        if (container) {
            container.innerHTML = this.renderCurrentView();
        }
    },

    /**
     * تحديث البيانات
     */
    refreshData: function() {
        window.Accounting.updateBalances();
        this.loadData();

        // إعادة عرض المحتوى الحالي
        const container = document.querySelector('.accounting-content');
        if (container) {
            container.innerHTML = this.renderCurrentView();
        }

        if (window.Notifications) {
            window.Notifications.success('تم تحديث البيانات بنجاح');
        }
    },

    /**
     * تحميل البيانات
     */
    loadData: function() {
        this.data.entries = window.Accounting.entries || [];
        this.data.accounts = Object.values(window.Accounting.chartOfAccounts);
    },

    /**
     * عرض نافذة قيد جديد
     */
    showNewEntryModal: function() {
        const modalHTML = `
            <div class="modal fade" id="newEntryModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">قيد محاسبي جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="newEntryForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">التاريخ</label>
                                            <input type="date" class="form-control" name="date"
                                                   value="${new Date().toISOString().split('T')[0]}" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">المرجع</label>
                                            <input type="text" class="form-control" name="reference"
                                                   placeholder="رقم المرجع (اختياري)">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الوصف</label>
                                    <textarea class="form-control" name="description" rows="2"
                                              placeholder="وصف القيد المحاسبي" required></textarea>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">المعاملات</label>
                                    <div id="transactionsContainer">
                                        <div class="transaction-row border rounded p-3 mb-2">
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <select class="form-control" name="accountCode" required>
                                                        <option value="">اختر الحساب</option>
                                                        ${this.renderAccountOptions()}
                                                    </select>
                                                </div>
                                                <div class="col-md-3">
                                                    <input type="number" class="form-control" name="debit"
                                                           placeholder="مدين" step="0.01" min="0">
                                                </div>
                                                <div class="col-md-3">
                                                    <input type="number" class="form-control" name="credit"
                                                           placeholder="دائن" step="0.01" min="0">
                                                </div>
                                                <div class="col-md-2">
                                                    <button type="button" class="btn btn-danger btn-sm"
                                                            onclick="this.closest('.transaction-row').remove()">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="row mt-2">
                                                <div class="col-12">
                                                    <input type="text" class="form-control form-control-sm"
                                                           name="transactionDescription" placeholder="وصف المعاملة">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-outline-primary btn-sm"
                                            onclick="AccountingComponent.addTransactionRow()">
                                        <i class="fas fa-plus me-1"></i>إضافة معاملة
                                    </button>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="alert alert-info">
                                            <strong>إجمالي المدين:</strong> <span id="totalDebit">0.00</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="alert alert-info">
                                            <strong>إجمالي الدائن:</strong> <span id="totalCredit">0.00</span>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="AccountingComponent.saveNewEntry()">
                                حفظ القيد
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إضافة النافذة للصفحة
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // عرض النافذة
        const modal = new bootstrap.Modal(document.getElementById('newEntryModal'));
        modal.show();

        // إزالة النافذة عند الإغلاق
        document.getElementById('newEntryModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });

        // إعداد حساب الإجماليات
        this.setupEntryCalculations();
    },

    /**
     * عرض خيارات الحسابات
     */
    renderAccountOptions: function() {
        return Object.values(window.Accounting.chartOfAccounts)
            .filter(account => account.isActive && account.level >= 3)
            .sort((a, b) => a.code.localeCompare(b.code))
            .map(account => `
                <option value="${account.code}">${account.code} - ${account.name}</option>
            `).join('');
    },

    /**
     * إضافة صف معاملة جديد
     */
    addTransactionRow: function() {
        const container = document.getElementById('transactionsContainer');
        const newRow = document.createElement('div');
        newRow.className = 'transaction-row border rounded p-3 mb-2';
        newRow.innerHTML = `
            <div class="row">
                <div class="col-md-4">
                    <select class="form-control" name="accountCode" required>
                        <option value="">اختر الحساب</option>
                        ${this.renderAccountOptions()}
                    </select>
                </div>
                <div class="col-md-3">
                    <input type="number" class="form-control" name="debit"
                           placeholder="مدين" step="0.01" min="0">
                </div>
                <div class="col-md-3">
                    <input type="number" class="form-control" name="credit"
                           placeholder="دائن" step="0.01" min="0">
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-danger btn-sm"
                            onclick="this.closest('.transaction-row').remove(); AccountingComponent.updateTotals();">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-12">
                    <input type="text" class="form-control form-control-sm"
                           name="transactionDescription" placeholder="وصف المعاملة">
                </div>
            </div>
        `;
        container.appendChild(newRow);
        this.setupEntryCalculations();
    },

    /**
     * إعداد حسابات القيد
     */
    setupEntryCalculations: function() {
        const container = document.getElementById('transactionsContainer');
        if (container) {
            container.addEventListener('input', () => {
                this.updateTotals();
            });
        }
    },

    /**
     * تحديث الإجماليات
     */
    updateTotals: function() {
        const debitInputs = document.querySelectorAll('input[name="debit"]');
        const creditInputs = document.querySelectorAll('input[name="credit"]');

        let totalDebit = 0;
        let totalCredit = 0;

        debitInputs.forEach(input => {
            totalDebit += parseFloat(input.value) || 0;
        });

        creditInputs.forEach(input => {
            totalCredit += parseFloat(input.value) || 0;
        });

        document.getElementById('totalDebit').textContent = totalDebit.toFixed(2);
        document.getElementById('totalCredit').textContent = totalCredit.toFixed(2);

        // تغيير لون التحذير إذا لم يكن متوازناً
        const isBalanced = Math.abs(totalDebit - totalCredit) < 0.01;
        const debitAlert = document.getElementById('totalDebit').closest('.alert');
        const creditAlert = document.getElementById('totalCredit').closest('.alert');

        if (isBalanced && totalDebit > 0) {
            debitAlert.className = 'alert alert-success';
            creditAlert.className = 'alert alert-success';
        } else {
            debitAlert.className = 'alert alert-warning';
            creditAlert.className = 'alert alert-warning';
        }
    },

    /**
     * حفظ القيد الجديد
     */
    saveNewEntry: function() {
        const form = document.getElementById('newEntryForm');
        const formData = new FormData(form);

        try {
            // جمع بيانات القيد
            const entryData = {
                date: formData.get('date'),
                description: formData.get('description'),
                reference: formData.get('reference'),
                type: 'manual',
                transactions: []
            };

            // جمع المعاملات
            const transactionRows = document.querySelectorAll('.transaction-row');
            transactionRows.forEach(row => {
                const accountCode = row.querySelector('select[name="accountCode"]').value;
                const debit = parseFloat(row.querySelector('input[name="debit"]').value) || 0;
                const credit = parseFloat(row.querySelector('input[name="credit"]').value) || 0;
                const description = row.querySelector('input[name="transactionDescription"]').value;

                if (accountCode && (debit > 0 || credit > 0)) {
                    entryData.transactions.push({
                        accountCode: accountCode,
                        description: description || entryData.description,
                        debit: debit,
                        credit: credit
                    });
                }
            });

            // التحقق من وجود معاملات
            if (entryData.transactions.length === 0) {
                throw new Error('يجب إضافة معاملة واحدة على الأقل');
            }

            // إنشاء القيد
            const entry = window.Accounting.createJournalEntry(entryData);

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('newEntryModal'));
            modal.hide();

            // تحديث العرض
            this.refreshData();

            if (window.Notifications) {
                window.Notifications.success(`تم إنشاء القيد رقم ${entry.entryNumber} بنجاح`);
            }

        } catch (error) {
            console.error('خطأ في حفظ القيد:', error);
            if (window.Notifications) {
                window.Notifications.error('فشل في حفظ القيد: ' + error.message);
            }
        }
    },

    /**
     * عرض تفاصيل القيد
     */
    viewEntry: function(entryNumber) {
        const entry = window.Accounting.entries.find(e => e.entryNumber === entryNumber);
        if (!entry) return;

        const modalHTML = `
            <div class="modal fade" id="viewEntryModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تفاصيل القيد رقم ${entry.entryNumber}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <strong>التاريخ:</strong> ${new Date(entry.date).toLocaleDateString('ar-SA')}
                                </div>
                                <div class="col-md-6">
                                    <strong>المرجع:</strong> ${entry.reference || '-'}
                                </div>
                            </div>
                            <div class="mb-3">
                                <strong>الوصف:</strong> ${entry.description}
                            </div>
                            <div class="mb-3">
                                <strong>النوع:</strong>
                                <span class="badge bg-secondary">${this.getEntryTypeLabel(entry.type)}</span>
                            </div>

                            <h6>المعاملات:</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>رمز الحساب</th>
                                            <th>اسم الحساب</th>
                                            <th>الوصف</th>
                                            <th>مدين</th>
                                            <th>دائن</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${entry.transactions.map(transaction => {
                                            const account = window.Accounting.chartOfAccounts[transaction.accountCode];
                                            return `
                                                <tr>
                                                    <td>${transaction.accountCode}</td>
                                                    <td>${account ? account.name : 'حساب غير موجود'}</td>
                                                    <td>${transaction.description}</td>
                                                    <td class="text-success">${transaction.debit ? window.Accounting.formatAmount(transaction.debit) : '-'}</td>
                                                    <td class="text-danger">${transaction.credit ? window.Accounting.formatAmount(transaction.credit) : '-'}</td>
                                                </tr>
                                            `;
                                        }).join('')}
                                    </tbody>
                                    <tfoot>
                                        <tr class="table-active">
                                            <th colspan="3">الإجمالي</th>
                                            <th class="text-success">${window.Accounting.formatAmount(entry.totalDebit)}</th>
                                            <th class="text-danger">${window.Accounting.formatAmount(entry.totalCredit)}</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" onclick="AccountingComponent.printEntry(${entryNumber})">
                                <i class="fas fa-print me-1"></i>طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('viewEntryModal'));
        modal.show();

        document.getElementById('viewEntryModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * إنشاء تقرير الميزانية العمومية
     */
    generateBalanceSheet: function() {
        const balanceSheet = window.Accounting.generateBalanceSheet();

        // عرض التقرير في نافذة جديدة
        const reportWindow = window.open('', '_blank');
        reportWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>${balanceSheet.title}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
                    .header { text-align: center; margin-bottom: 30px; }
                    .balance-sheet { display: flex; justify-content: space-between; }
                    .section { width: 48%; }
                    table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                    th { background-color: #f5f5f5; }
                    .total { font-weight: bold; background-color: #e9ecef; }
                    .balanced { color: green; }
                    .unbalanced { color: red; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>${balanceSheet.title}</h1>
                    <p>كما في تاريخ: ${new Date(balanceSheet.asOfDate).toLocaleDateString('ar-SA')}</p>
                    <p class="${balanceSheet.isBalanced ? 'balanced' : 'unbalanced'}">
                        ${balanceSheet.isBalanced ? 'الميزانية متوازنة ✓' : 'الميزانية غير متوازنة ✗'}
                    </p>
                </div>

                <div class="balance-sheet">
                    <div class="section">
                        <h3>الأصول</h3>
                        <table>
                            <thead>
                                <tr><th>الحساب</th><th>المبلغ</th></tr>
                            </thead>
                            <tbody>
                                ${balanceSheet.assets.current.map(asset => `
                                    <tr><td>${asset.name}</td><td>${window.Accounting.formatAmount(asset.balance)}</td></tr>
                                `).join('')}
                                ${balanceSheet.assets.fixed.map(asset => `
                                    <tr><td>${asset.name}</td><td>${window.Accounting.formatAmount(asset.balance)}</td></tr>
                                `).join('')}
                                <tr class="total">
                                    <td>إجمالي الأصول</td>
                                    <td>${window.Accounting.formatAmount(balanceSheet.assets.total)}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="section">
                        <h3>الخصوم وحقوق الملكية</h3>
                        <table>
                            <thead>
                                <tr><th>الحساب</th><th>المبلغ</th></tr>
                            </thead>
                            <tbody>
                                ${balanceSheet.liabilities.current.map(liability => `
                                    <tr><td>${liability.name}</td><td>${window.Accounting.formatAmount(liability.balance)}</td></tr>
                                `).join('')}
                                ${balanceSheet.equity.items.map(equity => `
                                    <tr><td>${equity.name}</td><td>${window.Accounting.formatAmount(equity.balance)}</td></tr>
                                `).join('')}
                                <tr class="total">
                                    <td>إجمالي الخصوم وحقوق الملكية</td>
                                    <td>${window.Accounting.formatAmount(balanceSheet.liabilities.total + balanceSheet.equity.total)}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </body>
            </html>
        `);
        reportWindow.document.close();
    },

    /**
     * الحصول على تسمية نوع القيد
     */
    getEntryTypeLabel: function(type) {
        const types = {
            'manual': 'يدوي',
            'auto': 'تلقائي',
            'booking': 'حجز',
            'payment': 'دفع',
            'receipt': 'استلام',
            'expense': 'مصروف'
        };
        return types[type] || type;
    },

    /**
     * الحصول على تسمية حالة القيد
     */
    getStatusLabel: function(status) {
        const statuses = {
            'draft': 'مسودة',
            'posted': 'مرحل',
            'approved': 'معتمد'
        };
        return statuses[status] || status;
    },

    /**
     * الحصول على فئة شارة الحالة
     */
    getStatusBadgeClass: function(status) {
        const classes = {
            'draft': 'bg-warning',
            'posted': 'bg-success',
            'approved': 'bg-primary'
        };
        return classes[status] || 'bg-secondary';
    },

    /**
     * الحصول على تسمية نوع الحساب
     */
    getAccountTypeLabel: function(type) {
        const types = {
            'asset': 'أصل',
            'liability': 'خصم',
            'equity': 'حقوق ملكية',
            'revenue': 'إيراد',
            'expense': 'مصروف'
        };
        return types[type] || type;
    },

    /**
     * الحصول على فئة شارة نوع الحساب
     */
    getAccountTypeBadgeClass: function(type) {
        const classes = {
            'asset': 'bg-primary',
            'liability': 'bg-danger',
            'equity': 'bg-info',
            'revenue': 'bg-success',
            'expense': 'bg-warning'
        };
        return classes[type] || 'bg-secondary';
    },

    /**
     * فلترة الحسابات
     */
    filterAccounts: function() {
        const searchTerm = document.getElementById('accountSearch')?.value.toLowerCase() || '';
        const typeFilter = document.getElementById('accountTypeFilter')?.value || '';
        const levelFilter = document.getElementById('accountLevelFilter')?.value || '';
        const statusFilter = document.getElementById('accountStatusFilter')?.value || '';

        const allAccounts = Object.values(window.Accounting.chartOfAccounts);

        const filteredAccounts = allAccounts.filter(account => {
            // البحث في رمز الحساب واسم الحساب والاسم الإنجليزي والوصف
            const matchesSearch = !searchTerm ||
                account.code.toLowerCase().includes(searchTerm) ||
                account.name.toLowerCase().includes(searchTerm) ||
                (account.nameEn && account.nameEn.toLowerCase().includes(searchTerm)) ||
                (account.description && account.description.toLowerCase().includes(searchTerm)) ||
                (account.category && account.category.toLowerCase().includes(searchTerm));

            const matchesType = !typeFilter || account.type === typeFilter;
            const matchesLevel = !levelFilter || account.level.toString() === levelFilter;
            const matchesStatus = !statusFilter ||
                (statusFilter === 'active' && account.isActive) ||
                (statusFilter === 'inactive' && !account.isActive);

            return matchesSearch && matchesType && matchesLevel && matchesStatus;
        });

        // ترتيب النتائج حسب رمز الحساب
        filteredAccounts.sort((a, b) => a.code.localeCompare(b.code));

        // تحديث الجدول
        const tbody = document.getElementById('accounts-table-body');
        if (tbody) {
            tbody.innerHTML = this.renderAccountsRows(filteredAccounts);
        }

        // تحديث الإحصائيات
        this.updateAccountsStats(filteredAccounts);

        // تحديث عداد النتائج
        this.updateSearchResults(filteredAccounts.length, allAccounts.length);
    },

    /**
     * تحديث عداد نتائج البحث
     */
    updateSearchResults: function(filteredCount, totalCount) {
        const searchInput = document.getElementById('accountSearch');
        if (searchInput && filteredCount !== totalCount) {
            // إضافة مؤشر نتائج البحث
            let resultIndicator = document.getElementById('searchResultIndicator');
            if (!resultIndicator) {
                resultIndicator = document.createElement('small');
                resultIndicator.id = 'searchResultIndicator';
                resultIndicator.className = 'text-muted d-block mt-1';
                searchInput.parentNode.appendChild(resultIndicator);
            }
            resultIndicator.textContent = `عرض ${filteredCount} من أصل ${totalCount} حساب`;
        } else {
            // إزالة مؤشر النتائج إذا لم يكن هناك فلترة
            const resultIndicator = document.getElementById('searchResultIndicator');
            if (resultIndicator) {
                resultIndicator.remove();
            }
        }
    },

    /**
     * تحديث إحصائيات الحسابات
     */
    updateAccountsStats: function(accounts) {
        const totalCount = document.getElementById('totalAccountsCount');
        const activeCount = document.getElementById('activeAccountsCount');
        const inactiveCount = document.getElementById('inactiveAccountsCount');

        if (totalCount) totalCount.textContent = accounts.length;
        if (activeCount) activeCount.textContent = accounts.filter(a => a.isActive).length;
        if (inactiveCount) inactiveCount.textContent = accounts.filter(a => !a.isActive).length;

        // تحديث إحصائيات إضافية
        this.updateAccountTypesStats(accounts);
        this.updateBalanceStats(accounts);
    },

    /**
     * تحديث إحصائيات أنواع الحسابات
     */
    updateAccountTypesStats: function(accounts) {
        const typeStats = {
            asset: 0,
            liability: 0,
            equity: 0,
            revenue: 0,
            expense: 0
        };

        accounts.forEach(account => {
            if (typeStats.hasOwnProperty(account.type)) {
                typeStats[account.type]++;
            }
        });

        // عرض الإحصائيات في tooltip أو عنصر منفصل
        const statsContainer = document.getElementById('accountTypesStats');
        if (statsContainer) {
            statsContainer.innerHTML = `
                <small class="text-muted">
                    أصول: ${typeStats.asset} | خصوم: ${typeStats.liability} |
                    حقوق ملكية: ${typeStats.equity} | إيرادات: ${typeStats.revenue} |
                    مصروفات: ${typeStats.expense}
                </small>
            `;
        }
    },

    /**
     * تحديث إحصائيات الأرصدة
     */
    updateBalanceStats: function(accounts) {
        let totalPositiveBalance = 0;
        let totalNegativeBalance = 0;
        let accountsWithBalance = 0;

        accounts.forEach(account => {
            const balance = account.balance || 0;
            if (Math.abs(balance) > 0.01) {
                accountsWithBalance++;
                if (balance > 0) {
                    totalPositiveBalance += balance;
                } else {
                    totalNegativeBalance += Math.abs(balance);
                }
            }
        });

        const balanceStatsContainer = document.getElementById('balanceStats');
        if (balanceStatsContainer) {
            balanceStatsContainer.innerHTML = `
                <small class="text-muted">
                    حسابات لها أرصدة: ${accountsWithBalance} |
                    إجمالي الأرصدة الموجبة: ${window.Accounting.formatAmount(totalPositiveBalance)} |
                    إجمالي الأرصدة السالبة: ${window.Accounting.formatAmount(totalNegativeBalance)}
                </small>
            `;
        }
    },

    /**
     * عرض نافذة حساب جديد
     */
    showNewAccountModal: function() {
        const modalHTML = `
            <div class="modal fade" id="newAccountModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">إضافة حساب جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="newAccountForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رمز الحساب *</label>
                                            <input type="text" class="form-control" name="code" required
                                                   placeholder="مثال: 1121" maxlength="10">
                                            <div class="form-text">يجب أن يكون رمز الحساب فريداً</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">نوع الحساب *</label>
                                            <select class="form-control" name="type" required onchange="AccountingComponent.updateParentOptions()">
                                                <option value="">اختر نوع الحساب</option>
                                                <option value="asset">أصل</option>
                                                <option value="liability">خصم</option>
                                                <option value="equity">حقوق ملكية</option>
                                                <option value="revenue">إيراد</option>
                                                <option value="expense">مصروف</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label class="form-label">اسم الحساب (عربي) *</label>
                                            <input type="text" class="form-control" name="name" required
                                                   placeholder="مثال: النقدية بالصندوق">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">الحساب الأب</label>
                                            <select class="form-control" name="parent" id="parentAccountSelect">
                                                <option value="">حساب رئيسي</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label class="form-label">اسم الحساب (إنجليزي)</label>
                                            <input type="text" class="form-control" name="nameEn"
                                                   placeholder="مثال: Cash in Hand">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">التصنيف</label>
                                            <input type="text" class="form-control" name="category"
                                                   placeholder="مثال: cash">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الرصيد الافتتاحي</label>
                                            <input type="number" class="form-control" name="openingBalance"
                                                   step="0.01" value="0" placeholder="0.00">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الحالة</label>
                                            <select class="form-control" name="isActive">
                                                <option value="true">نشط</option>
                                                <option value="false">غير نشط</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الوصف</label>
                                    <textarea class="form-control" name="description" rows="2"
                                              placeholder="وصف اختياري للحساب"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="AccountingComponent.saveNewAccount()">
                                <i class="fas fa-save me-1"></i>حفظ الحساب
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('newAccountModal'));
        modal.show();

        document.getElementById('newAccountModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });

        this.updateParentOptions();
    },

    /**
     * تحديث خيارات الحساب الأب
     */
    updateParentOptions: function() {
        const typeSelect = document.querySelector('select[name="type"]');
        const parentSelect = document.getElementById('parentAccountSelect');

        if (!typeSelect || !parentSelect) return;

        const selectedType = typeSelect.value;
        parentSelect.innerHTML = '<option value="">حساب رئيسي</option>';

        if (selectedType) {
            const parentAccounts = Object.values(window.Accounting.chartOfAccounts)
                .filter(account => account.type === selectedType && account.level < 3)
                .sort((a, b) => a.code.localeCompare(b.code));

            parentAccounts.forEach(account => {
                parentSelect.innerHTML += `
                    <option value="${account.code}">${account.code} - ${account.name}</option>
                `;
            });
        }
    },

    /**
     * حفظ الحساب الجديد
     */
    saveNewAccount: function() {
        const form = document.getElementById('newAccountForm');
        const formData = new FormData(form);

        try {
            const accountData = {
                code: formData.get('code').trim(),
                name: formData.get('name').trim(),
                nameEn: formData.get('nameEn')?.trim() || '',
                type: formData.get('type'),
                category: formData.get('category')?.trim() || '',
                parent: formData.get('parent') || null,
                isActive: formData.get('isActive') === 'true',
                balance: parseFloat(formData.get('openingBalance')) || 0,
                description: formData.get('description')?.trim() || ''
            };

            // التحقق من صحة البيانات
            if (!accountData.code || !accountData.name || !accountData.type) {
                throw new Error('يرجى ملء جميع الحقول المطلوبة');
            }

            // التحقق من عدم تكرار رمز الحساب
            if (window.Accounting.chartOfAccounts[accountData.code]) {
                throw new Error('رمز الحساب موجود مسبقاً');
            }

            // تحديد المستوى
            if (accountData.parent) {
                const parentAccount = window.Accounting.chartOfAccounts[accountData.parent];
                if (!parentAccount) {
                    throw new Error('الحساب الأب غير موجود');
                }
                accountData.level = parentAccount.level + 1;
            } else {
                accountData.level = 1;
            }

            // إضافة الحساب
            window.Accounting.chartOfAccounts[accountData.code] = {
                ...accountData,
                children: []
            };

            // تحديث الحساب الأب
            if (accountData.parent) {
                const parentAccount = window.Accounting.chartOfAccounts[accountData.parent];
                if (!parentAccount.children) parentAccount.children = [];
                parentAccount.children.push(accountData.code);
            }

            // حفظ البيانات
            window.Accounting.saveAccountingData();

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('newAccountModal'));
            modal.hide();

            // تحديث العرض
            this.refreshData();

            if (window.Notifications) {
                window.Notifications.success(`تم إضافة الحساب ${accountData.code} بنجاح`);
            }

        } catch (error) {
            console.error('خطأ في حفظ الحساب:', error);
            if (window.Notifications) {
                window.Notifications.error('فشل في حفظ الحساب: ' + error.message);
            }
        }
    },

    /**
     * عرض تفاصيل الحساب
     */
    viewAccountDetails: function(accountCode) {
        const account = window.Accounting.chartOfAccounts[accountCode];
        if (!account) return;

        // الحصول على المعاملات الخاصة بالحساب
        const transactions = this.getAccountTransactions(accountCode);
        const balance = window.Accounting.getAccountBalance(accountCode);

        const modalHTML = `
            <div class="modal fade" id="accountDetailsModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تفاصيل الحساب: ${account.code} - ${account.name}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <!-- معلومات الحساب -->
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-info-circle me-2"></i>معلومات الحساب</h6>
                                        </div>
                                        <div class="card-body">
                                            <table class="table table-sm">
                                                <tr><td><strong>رمز الحساب:</strong></td><td>${account.code}</td></tr>
                                                <tr><td><strong>اسم الحساب:</strong></td><td>${account.name}</td></tr>
                                                ${account.nameEn ? `<tr><td><strong>الاسم الإنجليزي:</strong></td><td>${account.nameEn}</td></tr>` : ''}
                                                <tr><td><strong>النوع:</strong></td><td><span class="badge ${this.getAccountTypeBadgeClass(account.type)}">${this.getAccountTypeLabel(account.type)}</span></td></tr>
                                                <tr><td><strong>المستوى:</strong></td><td>${account.level}</td></tr>
                                                ${account.parent ? `<tr><td><strong>الحساب الأب:</strong></td><td>${account.parent}</td></tr>` : ''}
                                                ${account.category ? `<tr><td><strong>التصنيف:</strong></td><td>${account.category}</td></tr>` : ''}
                                                <tr><td><strong>الحالة:</strong></td><td><span class="badge ${account.isActive ? 'bg-success' : 'bg-secondary'}">${account.isActive ? 'نشط' : 'غير نشط'}</span></td></tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <!-- الرصيد والإحصائيات -->
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-chart-line me-2"></i>الرصيد والإحصائيات</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="text-center mb-3">
                                                <h4 class="mb-1 ${this.getBalanceClass(balance, account.type)}">
                                                    ${window.Accounting.formatAmount(Math.abs(balance))}
                                                </h4>
                                                <small class="text-muted">${this.getBalanceDirection(balance, account.type)}</small>
                                            </div>
                                            <table class="table table-sm">
                                                <tr><td><strong>عدد المعاملات:</strong></td><td>${transactions.length}</td></tr>
                                                <tr><td><strong>إجمالي المدين:</strong></td><td class="text-success">${window.Accounting.formatAmount(transactions.reduce((sum, t) => sum + (t.debit || 0), 0))}</td></tr>
                                                <tr><td><strong>إجمالي الدائن:</strong></td><td class="text-danger">${window.Accounting.formatAmount(transactions.reduce((sum, t) => sum + (t.credit || 0), 0))}</td></tr>
                                                <tr><td><strong>آخر معاملة:</strong></td><td>${transactions.length > 0 ? new Date(transactions[transactions.length - 1].date).toLocaleDateString('ar-SA') : 'لا توجد معاملات'}</td></tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <!-- الحسابات الفرعية -->
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-sitemap me-2"></i>الحسابات الفرعية</h6>
                                        </div>
                                        <div class="card-body">
                                            ${account.children && account.children.length > 0 ? `
                                                <ul class="list-unstyled">
                                                    ${account.children.map(childCode => {
                                                        const childAccount = window.Accounting.chartOfAccounts[childCode];
                                                        return childAccount ? `
                                                            <li class="mb-2">
                                                                <strong>${childAccount.code}</strong><br>
                                                                <small>${childAccount.name}</small><br>
                                                                <small class="text-muted">الرصيد: ${window.Accounting.formatAmount(Math.abs(childAccount.balance || 0))}</small>
                                                            </li>
                                                        ` : '';
                                                    }).join('')}
                                                </ul>
                                            ` : '<p class="text-muted">لا توجد حسابات فرعية</p>'}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- جدول المعاملات -->
                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-list me-2"></i>آخر المعاملات</h6>
                                        </div>
                                        <div class="card-body">
                                            ${transactions.length > 0 ? `
                                                <div class="table-responsive">
                                                    <table class="table table-sm">
                                                        <thead>
                                                            <tr>
                                                                <th>التاريخ</th>
                                                                <th>رقم القيد</th>
                                                                <th>الوصف</th>
                                                                <th>مدين</th>
                                                                <th>دائن</th>
                                                                <th>الرصيد</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            ${transactions.slice(-10).map(transaction => `
                                                                <tr>
                                                                    <td>${new Date(transaction.date).toLocaleDateString('ar-SA')}</td>
                                                                    <td>${transaction.entryNumber}</td>
                                                                    <td>${transaction.description}</td>
                                                                    <td class="text-success">${transaction.debit ? window.Accounting.formatAmount(transaction.debit) : '-'}</td>
                                                                    <td class="text-danger">${transaction.credit ? window.Accounting.formatAmount(transaction.credit) : '-'}</td>
                                                                    <td>${window.Accounting.formatAmount(Math.abs(transaction.runningBalance || 0))}</td>
                                                                </tr>
                                                            `).join('')}
                                                        </tbody>
                                                    </table>
                                                </div>
                                            ` : '<p class="text-muted text-center">لا توجد معاملات لهذا الحساب</p>'}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" onclick="AccountingComponent.viewAccountStatement('${accountCode}')">
                                <i class="fas fa-file-alt me-1"></i>كشف الحساب
                            </button>
                            <button type="button" class="btn btn-outline-primary" onclick="AccountingComponent.editAccount('${accountCode}')">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('accountDetailsModal'));
        modal.show();

        document.getElementById('accountDetailsModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * الحصول على معاملات الحساب
     */
    getAccountTransactions: function(accountCode) {
        const transactions = [];
        let runningBalance = 0;

        window.Accounting.entries.forEach(entry => {
            entry.transactions.forEach(transaction => {
                if (transaction.accountCode === accountCode) {
                    const account = window.Accounting.chartOfAccounts[accountCode];
                    const debit = transaction.debit || 0;
                    const credit = transaction.credit || 0;

                    // حساب الرصيد الجاري
                    if (account.type === 'asset' || account.type === 'expense') {
                        runningBalance += debit - credit;
                    } else {
                        runningBalance += credit - debit;
                    }

                    transactions.push({
                        ...transaction,
                        date: entry.date,
                        entryNumber: entry.entryNumber,
                        runningBalance: runningBalance
                    });
                }
            });
        });

        return transactions.sort((a, b) => new Date(a.date) - new Date(b.date));
    },

    /**
     * الحصول على فئة لون الرصيد
     */
    getBalanceClass: function(balance, accountType) {
        if (balance === 0) return 'text-muted';

        if (accountType === 'asset' || accountType === 'expense') {
            return balance > 0 ? 'text-success' : 'text-danger';
        } else {
            return balance > 0 ? 'text-danger' : 'text-success';
        }
    },

    /**
     * الحصول على اتجاه الرصيد
     */
    getBalanceDirection: function(balance, accountType) {
        if (balance === 0) return 'رصيد صفر';

        if (accountType === 'asset' || accountType === 'expense') {
            return balance > 0 ? 'رصيد مدين' : 'رصيد دائن';
        } else {
            return balance > 0 ? 'رصيد دائن' : 'رصيد مدين';
        }
    },

    /**
     * تحديث البيانات
     */
    refreshData: function() {
        try {
            // إعادة تحميل البيانات من النظام المحاسبي
            if (window.Accounting) {
                window.Accounting.updateBalances();
            }

            // تحديث العرض الحالي
            const currentView = this.getCurrentView();
            if (currentView) {
                this.renderView(currentView);
            }

            // إزالة التحديدات
            this.clearSelections();

        } catch (error) {
            console.error('خطأ في تحديث البيانات:', error);
            this.showMessage('فشل في تحديث البيانات', 'error');
        }
    },

    /**
     * الحصول على العرض الحالي
     */
    getCurrentView: function() {
        // تحديد العرض الحالي بناءً على المحتوى المعروض
        const content = document.getElementById('main-content');
        if (!content) return null;

        if (content.querySelector('.accounting-accounts')) return 'accounts';
        if (content.querySelector('.accounting-entries')) return 'entries';
        if (content.querySelector('.accounting-reports')) return 'reports';
        if (content.querySelector('.accounting-payments')) return 'payments';
        if (content.querySelector('.accounting-dashboard')) return 'dashboard';

        return null;
    },

    /**
     * عرض طريقة العرض
     */
    renderView: function(view) {
        const content = document.getElementById('main-content');
        if (!content) return;

        switch (view) {
            case 'accounts':
                content.innerHTML = this.renderAccounts();
                break;
            case 'entries':
                content.innerHTML = this.renderEntries();
                break;
            case 'reports':
                content.innerHTML = this.renderReports();
                break;
            case 'payments':
                content.innerHTML = this.renderPayments();
                break;
            case 'dashboard':
                content.innerHTML = this.renderDashboard();
                break;
        }
    },

    /**
     * مسح التحديدات
     */
    clearSelections: function() {
        // مسح جميع checkboxes المحددة
        document.querySelectorAll('.account-checkbox:checked').forEach(checkbox => {
            checkbox.checked = false;
        });

        document.querySelectorAll('.entry-checkbox:checked').forEach(checkbox => {
            checkbox.checked = false;
        });

        // مسح checkbox "تحديد الكل"
        const selectAllAccounts = document.getElementById('selectAllAccounts');
        if (selectAllAccounts) {
            selectAllAccounts.checked = false;
            selectAllAccounts.indeterminate = false;
        }

        const selectAllEntries = document.getElementById('selectAllEntries');
        if (selectAllEntries) {
            selectAllEntries.checked = false;
            selectAllEntries.indeterminate = false;
        }

        // إخفاء أزرار الإجراءات المجمعة
        const selectedActions = document.getElementById('selectedActions');
        if (selectedActions) {
            selectedActions.style.display = 'none';
        }

        const selectedEntriesActions = document.getElementById('selectedEntriesActions');
        if (selectedEntriesActions) {
            selectedEntriesActions.style.display = 'none';
        }
    },

    /**
     * تحميل البيانات
     */
    loadData: function() {
        try {
            // تحميل البيانات من النظام المحاسبي
            if (window.Accounting) {
                this.accounts = window.Accounting.chartOfAccounts;
                this.entries = window.Accounting.entries;
                this.state = window.Accounting.state;
            }
        } catch (error) {
            console.error('خطأ في تحميل البيانات:', error);
        }
    },

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners: function() {
        // مستمع لتغييرات checkbox الحسابات
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('account-checkbox')) {
                this.updateSelectedActions();
            }
        });

        // مستمع لتغييرات checkbox القيود
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('entry-checkbox')) {
                this.updateSelectedEntriesActions();
            }
        });

        // مستمع للبحث في الحسابات
        document.addEventListener('input', (e) => {
            if (e.target.id === 'accountSearch') {
                // تأخير البحث لتحسين الأداء
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.filterAccounts();
                }, 300);
            }
        });

        // مستمع لتغيير الفلاتر
        document.addEventListener('change', (e) => {
            if (e.target.id === 'accountTypeFilter' ||
                e.target.id === 'accountLevelFilter' ||
                e.target.id === 'accountStatusFilter') {
                this.filterAccounts();
            }
        });
    },

    /**
     * تهيئة المكون
     */
    init: function() {
        this.loadData();
        this.setupEventListeners();
        console.log('✅ تم تهيئة مكون النظام المحاسبي');
    }
};
