/**
 * ===================================
 * مكون النظام المحاسبي - Accounting Component
 * ===================================
 */

window.AccountingComponent = {
    // بيانات المكون
    data: {
        currentView: 'dashboard', // dashboard, entries, accounts, reports
        entries: [],
        accounts: [],
        selectedEntry: null,
        selectedAccount: null,
        filters: {
            dateFrom: '',
            dateTo: '',
            accountType: '',
            entryType: ''
        }
    },

    /**
     * عرض المكون الرئيسي
     */
    render: function(params = {}) {
        this.data.currentView = params.view || 'dashboard';
        
        return `
            <div class="accounting-container">
                ${this.renderHeader()}
                ${this.renderNavigation()}
                <div class="accounting-content">
                    ${this.renderCurrentView()}
                </div>
            </div>
        `;
    },

    /**
     * عرض رأس الصفحة
     */
    renderHeader: function() {
        return `
            <div class="accounting-header">
                <div class="header-title">
                    <h2><i class="fas fa-calculator me-2"></i>النظام المحاسبي</h2>
                    <p class="text-muted">إدارة شاملة للحسابات والقيود المالية</p>
                </div>
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="AccountingComponent.showNewEntryModal()">
                        <i class="fas fa-plus me-1"></i>قيد جديد
                    </button>
                    <button class="btn btn-outline-secondary" onclick="AccountingComponent.refreshData()">
                        <i class="fas fa-sync me-1"></i>تحديث
                    </button>
                </div>
            </div>
        `;
    },

    /**
     * عرض شريط التنقل
     */
    renderNavigation: function() {
        const navItems = [
            { key: 'dashboard', icon: 'fas fa-tachometer-alt', label: 'لوحة التحكم' },
            { key: 'entries', icon: 'fas fa-book', label: 'القيود المحاسبية' },
            { key: 'accounts', icon: 'fas fa-list', label: 'دليل الحسابات' },
            { key: 'reports', icon: 'fas fa-chart-bar', label: 'التقارير المالية' },
            { key: 'payments', icon: 'fas fa-money-bill-wave', label: 'المدفوعات' }
        ];

        return `
            <div class="accounting-nav">
                <ul class="nav nav-tabs">
                    ${navItems.map(item => `
                        <li class="nav-item">
                            <a class="nav-link ${this.data.currentView === item.key ? 'active' : ''}" 
                               href="#" onclick="AccountingComponent.switchView('${item.key}')">
                                <i class="${item.icon} me-1"></i>${item.label}
                            </a>
                        </li>
                    `).join('')}
                </ul>
            </div>
        `;
    },

    /**
     * عرض المحتوى الحالي
     */
    renderCurrentView: function() {
        switch (this.data.currentView) {
            case 'dashboard':
                return this.renderDashboard();
            case 'entries':
                return this.renderEntries();
            case 'accounts':
                return this.renderAccounts();
            case 'reports':
                return this.renderReports();
            case 'payments':
                return this.renderPayments();
            default:
                return this.renderDashboard();
        }
    },

    /**
     * عرض لوحة التحكم المحاسبية
     */
    renderDashboard: function() {
        const balanceSheet = window.Accounting.generateBalanceSheet();
        const incomeStatement = window.Accounting.generateIncomeStatement();

        return `
            <div class="accounting-dashboard">
                <div class="row">
                    <!-- بطاقات الملخص -->
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">إجمالي الأصول</h6>
                                        <h4>${window.Accounting.formatAmount(balanceSheet.assets.total)}</h4>
                                    </div>
                                    <i class="fas fa-coins fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">إجمالي الإيرادات</h6>
                                        <h4>${window.Accounting.formatAmount(incomeStatement.revenue.total)}</h4>
                                    </div>
                                    <i class="fas fa-arrow-up fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">إجمالي المصروفات</h6>
                                        <h4>${window.Accounting.formatAmount(incomeStatement.expenses.total)}</h4>
                                    </div>
                                    <i class="fas fa-arrow-down fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card ${incomeStatement.netIncome >= 0 ? 'bg-info' : 'bg-danger'} text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">صافي الدخل</h6>
                                        <h4>${window.Accounting.formatAmount(incomeStatement.netIncome)}</h4>
                                    </div>
                                    <i class="fas fa-chart-line fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <!-- الميزانية العمومية المبسطة -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-balance-scale me-2"></i>الميزانية العمومية</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-6">
                                        <h6 class="text-primary">الأصول</h6>
                                        <ul class="list-unstyled">
                                            ${balanceSheet.assets.current.slice(0, 5).map(asset => `
                                                <li class="d-flex justify-content-between">
                                                    <span>${asset.name}</span>
                                                    <span>${window.Accounting.formatAmount(asset.balance)}</span>
                                                </li>
                                            `).join('')}
                                        </ul>
                                    </div>
                                    <div class="col-6">
                                        <h6 class="text-danger">الخصوم وحقوق الملكية</h6>
                                        <ul class="list-unstyled">
                                            ${balanceSheet.liabilities.current.slice(0, 3).map(liability => `
                                                <li class="d-flex justify-content-between">
                                                    <span>${liability.name}</span>
                                                    <span>${window.Accounting.formatAmount(liability.balance)}</span>
                                                </li>
                                            `).join('')}
                                            ${balanceSheet.equity.items.slice(0, 2).map(equity => `
                                                <li class="d-flex justify-content-between">
                                                    <span>${equity.name}</span>
                                                    <span>${window.Accounting.formatAmount(equity.balance)}</span>
                                                </li>
                                            `).join('')}
                                        </ul>
                                    </div>
                                </div>
                                <div class="text-center mt-3">
                                    <span class="badge ${balanceSheet.isBalanced ? 'bg-success' : 'bg-danger'}">
                                        ${balanceSheet.isBalanced ? 'الميزانية متوازنة' : 'الميزانية غير متوازنة'}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- آخر القيود -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-history me-2"></i>آخر القيود المحاسبية</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>رقم القيد</th>
                                                <th>التاريخ</th>
                                                <th>الوصف</th>
                                                <th>المبلغ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${window.Accounting.entries.slice(-5).reverse().map(entry => `
                                                <tr>
                                                    <td>${entry.entryNumber}</td>
                                                    <td>${new Date(entry.date).toLocaleDateString('ar-SA')}</td>
                                                    <td>${entry.description}</td>
                                                    <td>${window.Accounting.formatAmount(entry.totalDebit)}</td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض القيود المحاسبية
     */
    renderEntries: function() {
        const entries = window.Accounting.entries || [];

        return `
            <div class="accounting-entries">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-book me-2"></i>القيود المحاسبية</h5>
                        <div class="header-actions">
                            <button class="btn btn-sm btn-primary" onclick="AccountingComponent.showNewEntryModal()">
                                <i class="fas fa-plus me-1"></i>قيد جديد
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" onclick="AccountingComponent.showFilters()">
                                <i class="fas fa-filter me-1"></i>فلترة
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم القيد</th>
                                        <th>التاريخ</th>
                                        <th>الوصف</th>
                                        <th>المرجع</th>
                                        <th>النوع</th>
                                        <th>المدين</th>
                                        <th>الدائن</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${entries.map(entry => `
                                        <tr>
                                            <td><strong>${entry.entryNumber}</strong></td>
                                            <td>${new Date(entry.date).toLocaleDateString('ar-SA')}</td>
                                            <td>${entry.description}</td>
                                            <td>${entry.reference || '-'}</td>
                                            <td>
                                                <span class="badge bg-secondary">${this.getEntryTypeLabel(entry.type)}</span>
                                            </td>
                                            <td class="text-success">${window.Accounting.formatAmount(entry.totalDebit)}</td>
                                            <td class="text-danger">${window.Accounting.formatAmount(entry.totalCredit)}</td>
                                            <td>
                                                <span class="badge ${this.getStatusBadgeClass(entry.status)}">${this.getStatusLabel(entry.status)}</span>
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary" onclick="AccountingComponent.viewEntry(${entry.entryNumber})">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-secondary" onclick="AccountingComponent.editEntry(${entry.entryNumber})">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                        
                        ${entries.length === 0 ? `
                            <div class="text-center py-4">
                                <i class="fas fa-book fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد قيود محاسبية</p>
                                <button class="btn btn-primary" onclick="AccountingComponent.showNewEntryModal()">
                                    إنشاء أول قيد
                                </button>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض دليل الحسابات
     */
    renderAccounts: function() {
        const accounts = Object.values(window.Accounting.chartOfAccounts)
            .filter(account => account.level <= 3) // عرض الحسابات الرئيسية والفرعية فقط
            .sort((a, b) => a.code.localeCompare(b.code));

        return `
            <div class="accounting-accounts">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-list me-2"></i>دليل الحسابات</h5>
                        <div class="header-actions">
                            <button class="btn btn-sm btn-primary" onclick="AccountingComponent.showNewAccountModal()">
                                <i class="fas fa-plus me-1"></i>حساب جديد
                            </button>
                            <div class="input-group input-group-sm" style="width: 250px;">
                                <input type="text" class="form-control" placeholder="البحث في الحسابات..." 
                                       onkeyup="AccountingComponent.searchAccounts(this.value)">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رمز الحساب</th>
                                        <th>اسم الحساب</th>
                                        <th>النوع</th>
                                        <th>المستوى</th>
                                        <th>الرصيد</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="accounts-table-body">
                                    ${accounts.map(account => `
                                        <tr style="padding-right: ${(account.level - 1) * 20}px;">
                                            <td><strong>${account.code}</strong></td>
                                            <td>${account.name}</td>
                                            <td>
                                                <span class="badge ${this.getAccountTypeBadgeClass(account.type)}">${this.getAccountTypeLabel(account.type)}</span>
                                            </td>
                                            <td>${account.level}</td>
                                            <td class="${account.balance > 0 ? 'text-success' : account.balance < 0 ? 'text-danger' : ''}">
                                                ${account.balance !== undefined ? window.Accounting.formatAmount(account.balance) : '-'}
                                            </td>
                                            <td>
                                                <span class="badge ${account.isActive ? 'bg-success' : 'bg-secondary'}">
                                                    ${account.isActive ? 'نشط' : 'غير نشط'}
                                                </span>
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary" onclick="AccountingComponent.viewAccountDetails('${account.code}')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-secondary" onclick="AccountingComponent.editAccount('${account.code}')">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض التقارير المالية
     */
    renderReports: function() {
        return `
            <div class="accounting-reports">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-balance-scale fa-3x text-primary mb-3"></i>
                                <h5>الميزانية العمومية</h5>
                                <p class="text-muted">عرض الأصول والخصوم وحقوق الملكية</p>
                                <button class="btn btn-primary" onclick="AccountingComponent.generateBalanceSheet()">
                                    عرض التقرير
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-line fa-3x text-success mb-3"></i>
                                <h5>قائمة الدخل</h5>
                                <p class="text-muted">عرض الإيرادات والمصروفات وصافي الدخل</p>
                                <button class="btn btn-success" onclick="AccountingComponent.generateIncomeStatement()">
                                    عرض التقرير
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-list-alt fa-3x text-info mb-3"></i>
                                <h5>ميزان المراجعة</h5>
                                <p class="text-muted">عرض أرصدة جميع الحسابات</p>
                                <button class="btn btn-info" onclick="AccountingComponent.generateTrialBalance()">
                                    عرض التقرير
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-3x text-warning mb-3"></i>
                                <h5>تقرير أرصدة الوكلاء</h5>
                                <p class="text-muted">عرض أرصدة وعمولات الوكلاء</p>
                                <button class="btn btn-warning" onclick="AccountingComponent.generateAgentsReport()">
                                    عرض التقرير
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-truck fa-3x text-secondary mb-3"></i>
                                <h5>تقرير أرصدة الموردين</h5>
                                <p class="text-muted">عرض أرصدة ومستحقات الموردين</p>
                                <button class="btn btn-secondary" onclick="AccountingComponent.generateSuppliersReport()">
                                    عرض التقرير
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض المدفوعات
     */
    renderPayments: function() {
        return `
            <div class="accounting-payments">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-arrow-up text-success me-2"></i>المقبوضات</h5>
                            </div>
                            <div class="card-body">
                                <button class="btn btn-success mb-3" onclick="AccountingComponent.showReceiptModal()">
                                    <i class="fas fa-plus me-1"></i>إضافة مقبوض
                                </button>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>التاريخ</th>
                                                <th>العميل</th>
                                                <th>المبلغ</th>
                                                <th>الطريقة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- سيتم ملؤها ديناميكياً -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-arrow-down text-danger me-2"></i>المدفوعات</h5>
                            </div>
                            <div class="card-body">
                                <button class="btn btn-danger mb-3" onclick="AccountingComponent.showPaymentModal()">
                                    <i class="fas fa-plus me-1"></i>إضافة مدفوع
                                </button>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>التاريخ</th>
                                                <th>المورد</th>
                                                <th>المبلغ</th>
                                                <th>الطريقة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- سيتم ملؤها ديناميكياً -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * تبديل العرض
     */
    switchView: function(view) {
        this.data.currentView = view;
        const container = document.querySelector('.accounting-content');
        if (container) {
            container.innerHTML = this.renderCurrentView();
        }
    },

    /**
     * تحديث البيانات
     */
    refreshData: function() {
        window.Accounting.updateBalances();
        this.loadData();

        // إعادة عرض المحتوى الحالي
        const container = document.querySelector('.accounting-content');
        if (container) {
            container.innerHTML = this.renderCurrentView();
        }

        if (window.Notifications) {
            window.Notifications.success('تم تحديث البيانات بنجاح');
        }
    },

    /**
     * تحميل البيانات
     */
    loadData: function() {
        this.data.entries = window.Accounting.entries || [];
        this.data.accounts = Object.values(window.Accounting.chartOfAccounts);
    },

    /**
     * عرض نافذة قيد جديد
     */
    showNewEntryModal: function() {
        const modalHTML = `
            <div class="modal fade" id="newEntryModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">قيد محاسبي جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="newEntryForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">التاريخ</label>
                                            <input type="date" class="form-control" name="date"
                                                   value="${new Date().toISOString().split('T')[0]}" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">المرجع</label>
                                            <input type="text" class="form-control" name="reference"
                                                   placeholder="رقم المرجع (اختياري)">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الوصف</label>
                                    <textarea class="form-control" name="description" rows="2"
                                              placeholder="وصف القيد المحاسبي" required></textarea>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">المعاملات</label>
                                    <div id="transactionsContainer">
                                        <div class="transaction-row border rounded p-3 mb-2">
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <select class="form-control" name="accountCode" required>
                                                        <option value="">اختر الحساب</option>
                                                        ${this.renderAccountOptions()}
                                                    </select>
                                                </div>
                                                <div class="col-md-3">
                                                    <input type="number" class="form-control" name="debit"
                                                           placeholder="مدين" step="0.01" min="0">
                                                </div>
                                                <div class="col-md-3">
                                                    <input type="number" class="form-control" name="credit"
                                                           placeholder="دائن" step="0.01" min="0">
                                                </div>
                                                <div class="col-md-2">
                                                    <button type="button" class="btn btn-danger btn-sm"
                                                            onclick="this.closest('.transaction-row').remove()">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="row mt-2">
                                                <div class="col-12">
                                                    <input type="text" class="form-control form-control-sm"
                                                           name="transactionDescription" placeholder="وصف المعاملة">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-outline-primary btn-sm"
                                            onclick="AccountingComponent.addTransactionRow()">
                                        <i class="fas fa-plus me-1"></i>إضافة معاملة
                                    </button>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="alert alert-info">
                                            <strong>إجمالي المدين:</strong> <span id="totalDebit">0.00</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="alert alert-info">
                                            <strong>إجمالي الدائن:</strong> <span id="totalCredit">0.00</span>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="AccountingComponent.saveNewEntry()">
                                حفظ القيد
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إضافة النافذة للصفحة
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // عرض النافذة
        const modal = new bootstrap.Modal(document.getElementById('newEntryModal'));
        modal.show();

        // إزالة النافذة عند الإغلاق
        document.getElementById('newEntryModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });

        // إعداد حساب الإجماليات
        this.setupEntryCalculations();
    },

    /**
     * عرض خيارات الحسابات
     */
    renderAccountOptions: function() {
        return Object.values(window.Accounting.chartOfAccounts)
            .filter(account => account.isActive && account.level >= 3)
            .sort((a, b) => a.code.localeCompare(b.code))
            .map(account => `
                <option value="${account.code}">${account.code} - ${account.name}</option>
            `).join('');
    },

    /**
     * إضافة صف معاملة جديد
     */
    addTransactionRow: function() {
        const container = document.getElementById('transactionsContainer');
        const newRow = document.createElement('div');
        newRow.className = 'transaction-row border rounded p-3 mb-2';
        newRow.innerHTML = `
            <div class="row">
                <div class="col-md-4">
                    <select class="form-control" name="accountCode" required>
                        <option value="">اختر الحساب</option>
                        ${this.renderAccountOptions()}
                    </select>
                </div>
                <div class="col-md-3">
                    <input type="number" class="form-control" name="debit"
                           placeholder="مدين" step="0.01" min="0">
                </div>
                <div class="col-md-3">
                    <input type="number" class="form-control" name="credit"
                           placeholder="دائن" step="0.01" min="0">
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-danger btn-sm"
                            onclick="this.closest('.transaction-row').remove(); AccountingComponent.updateTotals();">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-12">
                    <input type="text" class="form-control form-control-sm"
                           name="transactionDescription" placeholder="وصف المعاملة">
                </div>
            </div>
        `;
        container.appendChild(newRow);
        this.setupEntryCalculations();
    },

    /**
     * إعداد حسابات القيد
     */
    setupEntryCalculations: function() {
        const container = document.getElementById('transactionsContainer');
        if (container) {
            container.addEventListener('input', () => {
                this.updateTotals();
            });
        }
    },

    /**
     * تحديث الإجماليات
     */
    updateTotals: function() {
        const debitInputs = document.querySelectorAll('input[name="debit"]');
        const creditInputs = document.querySelectorAll('input[name="credit"]');

        let totalDebit = 0;
        let totalCredit = 0;

        debitInputs.forEach(input => {
            totalDebit += parseFloat(input.value) || 0;
        });

        creditInputs.forEach(input => {
            totalCredit += parseFloat(input.value) || 0;
        });

        document.getElementById('totalDebit').textContent = totalDebit.toFixed(2);
        document.getElementById('totalCredit').textContent = totalCredit.toFixed(2);

        // تغيير لون التحذير إذا لم يكن متوازناً
        const isBalanced = Math.abs(totalDebit - totalCredit) < 0.01;
        const debitAlert = document.getElementById('totalDebit').closest('.alert');
        const creditAlert = document.getElementById('totalCredit').closest('.alert');

        if (isBalanced && totalDebit > 0) {
            debitAlert.className = 'alert alert-success';
            creditAlert.className = 'alert alert-success';
        } else {
            debitAlert.className = 'alert alert-warning';
            creditAlert.className = 'alert alert-warning';
        }
    },

    /**
     * حفظ القيد الجديد
     */
    saveNewEntry: function() {
        const form = document.getElementById('newEntryForm');
        const formData = new FormData(form);

        try {
            // جمع بيانات القيد
            const entryData = {
                date: formData.get('date'),
                description: formData.get('description'),
                reference: formData.get('reference'),
                type: 'manual',
                transactions: []
            };

            // جمع المعاملات
            const transactionRows = document.querySelectorAll('.transaction-row');
            transactionRows.forEach(row => {
                const accountCode = row.querySelector('select[name="accountCode"]').value;
                const debit = parseFloat(row.querySelector('input[name="debit"]').value) || 0;
                const credit = parseFloat(row.querySelector('input[name="credit"]').value) || 0;
                const description = row.querySelector('input[name="transactionDescription"]').value;

                if (accountCode && (debit > 0 || credit > 0)) {
                    entryData.transactions.push({
                        accountCode: accountCode,
                        description: description || entryData.description,
                        debit: debit,
                        credit: credit
                    });
                }
            });

            // التحقق من وجود معاملات
            if (entryData.transactions.length === 0) {
                throw new Error('يجب إضافة معاملة واحدة على الأقل');
            }

            // إنشاء القيد
            const entry = window.Accounting.createJournalEntry(entryData);

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('newEntryModal'));
            modal.hide();

            // تحديث العرض
            this.refreshData();

            if (window.Notifications) {
                window.Notifications.success(`تم إنشاء القيد رقم ${entry.entryNumber} بنجاح`);
            }

        } catch (error) {
            console.error('خطأ في حفظ القيد:', error);
            if (window.Notifications) {
                window.Notifications.error('فشل في حفظ القيد: ' + error.message);
            }
        }
    },

    /**
     * عرض تفاصيل القيد
     */
    viewEntry: function(entryNumber) {
        const entry = window.Accounting.entries.find(e => e.entryNumber === entryNumber);
        if (!entry) return;

        const modalHTML = `
            <div class="modal fade" id="viewEntryModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تفاصيل القيد رقم ${entry.entryNumber}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <strong>التاريخ:</strong> ${new Date(entry.date).toLocaleDateString('ar-SA')}
                                </div>
                                <div class="col-md-6">
                                    <strong>المرجع:</strong> ${entry.reference || '-'}
                                </div>
                            </div>
                            <div class="mb-3">
                                <strong>الوصف:</strong> ${entry.description}
                            </div>
                            <div class="mb-3">
                                <strong>النوع:</strong>
                                <span class="badge bg-secondary">${this.getEntryTypeLabel(entry.type)}</span>
                            </div>

                            <h6>المعاملات:</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>رمز الحساب</th>
                                            <th>اسم الحساب</th>
                                            <th>الوصف</th>
                                            <th>مدين</th>
                                            <th>دائن</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${entry.transactions.map(transaction => {
                                            const account = window.Accounting.chartOfAccounts[transaction.accountCode];
                                            return `
                                                <tr>
                                                    <td>${transaction.accountCode}</td>
                                                    <td>${account ? account.name : 'حساب غير موجود'}</td>
                                                    <td>${transaction.description}</td>
                                                    <td class="text-success">${transaction.debit ? window.Accounting.formatAmount(transaction.debit) : '-'}</td>
                                                    <td class="text-danger">${transaction.credit ? window.Accounting.formatAmount(transaction.credit) : '-'}</td>
                                                </tr>
                                            `;
                                        }).join('')}
                                    </tbody>
                                    <tfoot>
                                        <tr class="table-active">
                                            <th colspan="3">الإجمالي</th>
                                            <th class="text-success">${window.Accounting.formatAmount(entry.totalDebit)}</th>
                                            <th class="text-danger">${window.Accounting.formatAmount(entry.totalCredit)}</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" onclick="AccountingComponent.printEntry(${entryNumber})">
                                <i class="fas fa-print me-1"></i>طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('viewEntryModal'));
        modal.show();

        document.getElementById('viewEntryModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * إنشاء تقرير الميزانية العمومية
     */
    generateBalanceSheet: function() {
        const balanceSheet = window.Accounting.generateBalanceSheet();

        // عرض التقرير في نافذة جديدة
        const reportWindow = window.open('', '_blank');
        reportWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>${balanceSheet.title}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
                    .header { text-align: center; margin-bottom: 30px; }
                    .balance-sheet { display: flex; justify-content: space-between; }
                    .section { width: 48%; }
                    table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                    th { background-color: #f5f5f5; }
                    .total { font-weight: bold; background-color: #e9ecef; }
                    .balanced { color: green; }
                    .unbalanced { color: red; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>${balanceSheet.title}</h1>
                    <p>كما في تاريخ: ${new Date(balanceSheet.asOfDate).toLocaleDateString('ar-SA')}</p>
                    <p class="${balanceSheet.isBalanced ? 'balanced' : 'unbalanced'}">
                        ${balanceSheet.isBalanced ? 'الميزانية متوازنة ✓' : 'الميزانية غير متوازنة ✗'}
                    </p>
                </div>

                <div class="balance-sheet">
                    <div class="section">
                        <h3>الأصول</h3>
                        <table>
                            <thead>
                                <tr><th>الحساب</th><th>المبلغ</th></tr>
                            </thead>
                            <tbody>
                                ${balanceSheet.assets.current.map(asset => `
                                    <tr><td>${asset.name}</td><td>${window.Accounting.formatAmount(asset.balance)}</td></tr>
                                `).join('')}
                                ${balanceSheet.assets.fixed.map(asset => `
                                    <tr><td>${asset.name}</td><td>${window.Accounting.formatAmount(asset.balance)}</td></tr>
                                `).join('')}
                                <tr class="total">
                                    <td>إجمالي الأصول</td>
                                    <td>${window.Accounting.formatAmount(balanceSheet.assets.total)}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="section">
                        <h3>الخصوم وحقوق الملكية</h3>
                        <table>
                            <thead>
                                <tr><th>الحساب</th><th>المبلغ</th></tr>
                            </thead>
                            <tbody>
                                ${balanceSheet.liabilities.current.map(liability => `
                                    <tr><td>${liability.name}</td><td>${window.Accounting.formatAmount(liability.balance)}</td></tr>
                                `).join('')}
                                ${balanceSheet.equity.items.map(equity => `
                                    <tr><td>${equity.name}</td><td>${window.Accounting.formatAmount(equity.balance)}</td></tr>
                                `).join('')}
                                <tr class="total">
                                    <td>إجمالي الخصوم وحقوق الملكية</td>
                                    <td>${window.Accounting.formatAmount(balanceSheet.liabilities.total + balanceSheet.equity.total)}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </body>
            </html>
        `);
        reportWindow.document.close();
    },

    /**
     * الحصول على تسمية نوع القيد
     */
    getEntryTypeLabel: function(type) {
        const types = {
            'manual': 'يدوي',
            'auto': 'تلقائي',
            'booking': 'حجز',
            'payment': 'دفع',
            'receipt': 'استلام',
            'expense': 'مصروف'
        };
        return types[type] || type;
    },

    /**
     * الحصول على تسمية حالة القيد
     */
    getStatusLabel: function(status) {
        const statuses = {
            'draft': 'مسودة',
            'posted': 'مرحل',
            'approved': 'معتمد'
        };
        return statuses[status] || status;
    },

    /**
     * الحصول على فئة شارة الحالة
     */
    getStatusBadgeClass: function(status) {
        const classes = {
            'draft': 'bg-warning',
            'posted': 'bg-success',
            'approved': 'bg-primary'
        };
        return classes[status] || 'bg-secondary';
    },

    /**
     * الحصول على تسمية نوع الحساب
     */
    getAccountTypeLabel: function(type) {
        const types = {
            'asset': 'أصل',
            'liability': 'خصم',
            'equity': 'حقوق ملكية',
            'revenue': 'إيراد',
            'expense': 'مصروف'
        };
        return types[type] || type;
    },

    /**
     * الحصول على فئة شارة نوع الحساب
     */
    getAccountTypeBadgeClass: function(type) {
        const classes = {
            'asset': 'bg-primary',
            'liability': 'bg-danger',
            'equity': 'bg-info',
            'revenue': 'bg-success',
            'expense': 'bg-warning'
        };
        return classes[type] || 'bg-secondary';
    },

    /**
     * تهيئة المكون
     */
    init: function() {
        this.loadData();
        console.log('✅ تم تهيئة مكون النظام المحاسبي');
    }
};
