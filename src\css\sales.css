/**
 * أنماط نظام المبيعات
 * Sales System Styles
 */

/* المتغيرات */
:root {
    --sales-primary: #28a745;
    --sales-secondary: #6c757d;
    --sales-success: #20c997;
    --sales-info: #17a2b8;
    --sales-warning: #ffc107;
    --sales-danger: #dc3545;
    --sales-light: #f8f9fa;
    --sales-dark: #343a40;
}

/* نافذة المبيعات الرئيسية */
.sales-management {
    padding: 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

/* بطاقات الإحصائيات */
.sales-dashboard .card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.sales-dashboard .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.sales-dashboard .card-body {
    padding: 1.5rem;
}

.sales-dashboard .card h4 {
    font-size: 2rem;
    font-weight: bold;
    margin: 0;
}

.sales-dashboard .card h6 {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-bottom: 0.5rem;
}

/* تبويبات التنقل */
.sales-management .nav-pills .nav-link {
    border-radius: 25px;
    padding: 12px 20px;
    margin: 0 5px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.sales-management .nav-pills .nav-link:hover {
    background-color: rgba(40, 167, 69, 0.1);
    border-color: var(--sales-primary);
    transform: translateY(-2px);
}

.sales-management .nav-pills .nav-link.active {
    background-color: var(--sales-primary);
    border-color: var(--sales-primary);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

/* نموذج الفاتورة */
.invoice-form .card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.invoice-form .card-header {
    background: linear-gradient(45deg, var(--sales-primary), var(--sales-success));
    color: white;
    border-radius: 10px 10px 0 0 !important;
    padding: 15px 20px;
}

.invoice-form .card-header h6 {
    margin: 0;
    font-weight: 600;
}

.invoice-form .form-control {
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.invoice-form .form-control:focus {
    border-color: var(--sales-primary);
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

/* جدول عناصر الفاتورة */
.invoice-items-table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.invoice-items-table th {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    color: var(--sales-dark);
    font-weight: 600;
    padding: 15px 10px;
    border: none;
}

.invoice-items-table td {
    padding: 12px 10px;
    vertical-align: middle;
    border-color: #f0f0f0;
}

.invoice-items-table .form-control {
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 8px 12px;
}

/* ملخص الفاتورة */
.invoice-summary {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #dee2e6;
}

.invoice-summary .d-flex {
    margin-bottom: 10px;
    font-size: 1rem;
}

.invoice-summary .fw-bold {
    font-size: 1.2rem;
    color: var(--sales-primary);
}

/* أزرار الإجراءات */
.action-buttons .btn {
    border-radius: 25px;
    padding: 10px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
    margin: 0 5px;
}

.action-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.action-buttons .btn-primary {
    background: linear-gradient(45deg, var(--sales-primary), var(--sales-success));
    border: none;
}

.action-buttons .btn-outline-primary {
    border-color: var(--sales-primary);
    color: var(--sales-primary);
}

.action-buttons .btn-outline-primary:hover {
    background-color: var(--sales-primary);
    border-color: var(--sales-primary);
}

/* قائمة الفواتير الأخيرة */
.recent-invoices .list-group-item {
    border: none;
    border-radius: 8px;
    margin-bottom: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.recent-invoices .list-group-item:hover {
    transform: translateX(5px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.recent-invoices .badge {
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 12px;
}

/* حالات الفواتير */
.invoice-status-draft {
    background-color: var(--sales-secondary);
}

.invoice-status-sent {
    background-color: var(--sales-info);
}

.invoice-status-paid {
    background-color: var(--sales-success);
}

.invoice-status-overdue {
    background-color: var(--sales-danger);
}

.invoice-status-cancelled {
    background-color: var(--sales-dark);
}

/* رسائل التنبيه */
.sales-alert {
    border-radius: 10px;
    border: none;
    padding: 15px 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.sales-alert.alert-success {
    background: linear-gradient(45deg, #d4edda, #c3e6cb);
    color: #155724;
}

.sales-alert.alert-danger {
    background: linear-gradient(45deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

.sales-alert.alert-info {
    background: linear-gradient(45deg, #d1ecf1, #bee5eb);
    color: #0c5460;
}

/* تأثيرات الحركة */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.sales-management .card {
    animation: slideInUp 0.5s ease-out;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .sales-management {
        padding: 10px;
    }
    
    .sales-management .nav-pills .nav-link {
        padding: 8px 12px;
        margin: 2px;
        font-size: 0.9rem;
    }
    
    .invoice-form .card-body {
        padding: 15px;
    }
    
    .action-buttons .btn {
        margin: 5px 0;
        width: 100%;
    }
    
    .invoice-items-table {
        font-size: 0.9rem;
    }
    
    .invoice-summary {
        padding: 15px;
    }
}

/* تحسينات للطباعة */
@media print {
    .sales-management .nav-pills,
    .action-buttons,
    .btn {
        display: none !important;
    }
    
    .sales-management {
        background: white;
        padding: 0;
    }
    
    .card {
        border: 1px solid #000;
        box-shadow: none;
    }
}
