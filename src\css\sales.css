/**
 * أنماط نظام المبيعات
 * Sales System Styles
 */

/* المتغيرات */
:root {
    --sales-primary: #28a745;
    --sales-secondary: #6c757d;
    --sales-success: #20c997;
    --sales-info: #17a2b8;
    --sales-warning: #ffc107;
    --sales-danger: #dc3545;
    --sales-light: #f8f9fa;
    --sales-dark: #343a40;
}

/* نافذة المبيعات الرئيسية */
.sales-management {
    padding: 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

/* بطاقات الإحصائيات */
.sales-dashboard .card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.sales-dashboard .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.sales-dashboard .card-body {
    padding: 1.5rem;
}

.sales-dashboard .card h4 {
    font-size: 2rem;
    font-weight: bold;
    margin: 0;
}

.sales-dashboard .card h6 {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-bottom: 0.5rem;
}

/* تبويبات التنقل */
.sales-management .nav-pills .nav-link {
    border-radius: 25px;
    padding: 12px 20px;
    margin: 0 5px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.sales-management .nav-pills .nav-link:hover {
    background-color: rgba(40, 167, 69, 0.1);
    border-color: var(--sales-primary);
    transform: translateY(-2px);
}

.sales-management .nav-pills .nav-link.active {
    background-color: var(--sales-primary);
    border-color: var(--sales-primary);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

/* نموذج الفاتورة */
.invoice-form .card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.invoice-form .card-header {
    background: linear-gradient(45deg, var(--sales-primary), var(--sales-success));
    color: white;
    border-radius: 10px 10px 0 0 !important;
    padding: 15px 20px;
}

.invoice-form .card-header h6 {
    margin: 0;
    font-weight: 600;
}

.invoice-form .form-control {
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.invoice-form .form-control:focus {
    border-color: var(--sales-primary);
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

/* جدول عناصر الفاتورة */
.invoice-items-table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.invoice-items-table th {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    color: var(--sales-dark);
    font-weight: 600;
    padding: 15px 10px;
    border: none;
}

.invoice-items-table td {
    padding: 12px 10px;
    vertical-align: middle;
    border-color: #f0f0f0;
}

.invoice-items-table .form-control {
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 8px 12px;
}

/* ملخص الفاتورة */
.invoice-summary {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #dee2e6;
}

.invoice-summary .d-flex {
    margin-bottom: 10px;
    font-size: 1rem;
}

.invoice-summary .fw-bold {
    font-size: 1.2rem;
    color: var(--sales-primary);
}

/* أزرار الإجراءات */
.action-buttons .btn {
    border-radius: 25px;
    padding: 10px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
    margin: 0 5px;
}

.action-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.action-buttons .btn-primary {
    background: linear-gradient(45deg, var(--sales-primary), var(--sales-success));
    border: none;
}

.action-buttons .btn-outline-primary {
    border-color: var(--sales-primary);
    color: var(--sales-primary);
}

.action-buttons .btn-outline-primary:hover {
    background-color: var(--sales-primary);
    border-color: var(--sales-primary);
}

/* قائمة الفواتير الأخيرة */
.recent-invoices .list-group-item {
    border: none;
    border-radius: 8px;
    margin-bottom: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.recent-invoices .list-group-item:hover {
    transform: translateX(5px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.recent-invoices .badge {
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 12px;
}

/* حالات الفواتير */
.invoice-status-draft {
    background-color: var(--sales-secondary);
}

.invoice-status-sent {
    background-color: var(--sales-info);
}

.invoice-status-paid {
    background-color: var(--sales-success);
}

.invoice-status-overdue {
    background-color: var(--sales-danger);
}

.invoice-status-cancelled {
    background-color: var(--sales-dark);
}

/* رسائل التنبيه */
.sales-alert {
    border-radius: 10px;
    border: none;
    padding: 15px 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.sales-alert.alert-success {
    background: linear-gradient(45deg, #d4edda, #c3e6cb);
    color: #155724;
}

.sales-alert.alert-danger {
    background: linear-gradient(45deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

.sales-alert.alert-info {
    background: linear-gradient(45deg, #d1ecf1, #bee5eb);
    color: #0c5460;
}

/* تأثيرات الحركة */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.sales-management .card {
    animation: slideInUp 0.5s ease-out;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .sales-management {
        padding: 10px;
    }
    
    .sales-management .nav-pills .nav-link {
        padding: 8px 12px;
        margin: 2px;
        font-size: 0.9rem;
    }
    
    .invoice-form .card-body {
        padding: 15px;
    }
    
    .action-buttons .btn {
        margin: 5px 0;
        width: 100%;
    }
    
    .invoice-items-table {
        font-size: 0.9rem;
    }
    
    .invoice-summary {
        padding: 15px;
    }
}

/* أنماط إدارة الفواتير المتقدمة */
.invoices-management .invoice-row {
    transition: all 0.3s ease;
}

.invoices-management .invoice-row:hover {
    background-color: rgba(40, 167, 69, 0.05);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.invoices-management .invoice-checkbox:checked {
    background-color: var(--sales-primary);
    border-color: var(--sales-primary);
}

.invoices-management .invoice-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.invoices-management .invoice-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* فلاتر البحث المتقدمة */
.invoices-management .bg-light {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border-radius: 10px;
    border: 1px solid #dee2e6;
}

.invoices-management .form-label.small {
    font-weight: 600;
    color: var(--sales-dark);
    margin-bottom: 5px;
}

/* إحصائيات سريعة */
.invoices-management .bg-light.rounded {
    transition: all 0.3s ease;
    cursor: pointer;
}

.invoices-management .bg-light.rounded:hover {
    background: linear-gradient(45deg, #e9ecef, #f8f9fa) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* أزرار الإجراءات المجمعة */
#bulkActionsContainer {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* أزرار العرض */
.invoices-management .btn-group .btn.active {
    background-color: var(--sales-primary);
    border-color: var(--sales-primary);
    color: white;
}

/* جدول الفواتير المتطور */
.invoices-management .table th {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border: none;
    font-weight: 600;
    color: var(--sales-dark);
    position: sticky;
    top: 0;
    z-index: 10;
}

.invoices-management .table td {
    border-color: #f0f0f0;
    vertical-align: middle;
    padding: 15px 10px;
}

/* حالات الفواتير */
.badge.bg-draft {
    background-color: var(--sales-secondary) !important;
}

.badge.bg-sent {
    background-color: var(--sales-info) !important;
}

.badge.bg-paid {
    background-color: var(--sales-success) !important;
}

.badge.bg-overdue {
    background-color: var(--sales-danger) !important;
}

.badge.bg-cancelled {
    background-color: var(--sales-dark) !important;
}

/* أزرار الإجراءات السريعة */
.btn-xs {
    padding: 2px 6px;
    font-size: 0.75rem;
    border-radius: 4px;
}

/* نافذة البحث المتقدم */
#advancedSearchModal .modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

#advancedSearchModal .modal-header {
    background: linear-gradient(45deg, var(--sales-primary), var(--sales-success));
    color: white;
    border-radius: 15px 15px 0 0;
}

/* نافذة إرسال البريد */
#sendEmailModal .modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

#sendEmailModal .modal-header {
    background: linear-gradient(45deg, #17a2b8, #20c997);
    color: white;
    border-radius: 15px 15px 0 0;
}

/* تأثيرات التحميل */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid var(--sales-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .invoices-management .bg-light .row {
        margin: 0;
    }

    .invoices-management .bg-light .col-md-3,
    .invoices-management .bg-light .col-md-2,
    .invoices-management .bg-light .col-md-1 {
        padding: 5px;
        margin-bottom: 10px;
    }

    .invoices-management .table-responsive {
        font-size: 0.9rem;
    }

    .invoices-management .btn-group {
        flex-direction: column;
    }

    .invoices-management .btn-group .btn {
        margin: 2px 0;
    }

    #bulkActionsContainer {
        margin-top: 10px;
    }

    .invoice-card .card-body {
        padding: 15px;
    }
}

/* أنماط إدارة العملاء */
.customers-management .customer-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.customers-management .customer-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.customers-management .customer-card .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
}

.customers-management .customer-info p {
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.customers-management .customer-stats .border {
    border-color: #e9ecef !important;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.customers-management .customer-stats .border:hover {
    background: #e9ecef;
    transform: scale(1.05);
}

.customers-management .btn-group .btn {
    border-radius: 0;
}

.customers-management .btn-group .btn:first-child {
    border-radius: 0.375rem 0 0 0.375rem;
}

.customers-management .btn-group .btn:last-child {
    border-radius: 0 0.375rem 0.375rem 0;
}

/* نوافذ العملاء */
#newCustomerModal .modal-content,
#editCustomerModal .modal-content,
#viewCustomerModal .modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

#newCustomerModal .modal-header,
#editCustomerModal .modal-header {
    background: linear-gradient(45deg, var(--sales-primary), var(--sales-success));
    color: white;
    border-radius: 15px 15px 0 0;
}

#viewCustomerModal .modal-header {
    background: linear-gradient(45deg, #17a2b8, #20c997);
    color: white;
    border-radius: 15px 15px 0 0;
}

/* فلاتر العملاء */
.customers-management .bg-light .row {
    align-items: end;
}

.customers-management .input-group .btn {
    border-color: #ced4da;
}

/* إحصائيات العملاء */
.customers-management .bg-light.rounded {
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid #e9ecef;
}

.customers-management .bg-light.rounded:hover {
    background: linear-gradient(45deg, #e9ecef, #f8f9fa) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .customers-management .customer-card {
        margin-bottom: 15px;
    }

    .customers-management .btn-group {
        flex-direction: column;
    }

    .customers-management .btn-group .btn {
        margin: 2px 0;
        border-radius: 0.375rem !important;
    }

    .customers-management .customer-stats .row {
        margin: 0;
    }

    .customers-management .customer-stats .col-6 {
        padding: 5px;
    }
}

/* تحسينات للطباعة */
@media print {
    .sales-management .nav-pills,
    .action-buttons,
    .btn,
    .bg-light,
    #bulkActionsContainer,
    .card-header {
        display: none !important;
    }

    .sales-management {
        background: white;
        padding: 0;
    }

    .card {
        border: 1px solid #000;
        box-shadow: none;
    }

    .table {
        border-collapse: collapse;
    }

    .table th,
    .table td {
        border: 1px solid #000;
        padding: 8px;
    }

    .customer-card,
    .product-card {
        break-inside: avoid;
        margin-bottom: 20px;
    }
}

/* أنماط التقارير */
.reports-management .card {
    transition: all 0.3s ease;
}

.reports-management .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.reports-management .bg-primary,
.reports-management .bg-success,
.reports-management .bg-info,
.reports-management .bg-warning {
    background: linear-gradient(135deg, var(--bs-primary), var(--bs-primary-dark)) !important;
}

.reports-management .bg-success {
    background: linear-gradient(135deg, var(--bs-success), var(--bs-success-dark)) !important;
}

.reports-management .bg-info {
    background: linear-gradient(135deg, var(--bs-info), var(--bs-info-dark)) !important;
}

.reports-management .bg-warning {
    background: linear-gradient(135deg, var(--bs-warning), var(--bs-warning-dark)) !important;
}

.reports-management .border {
    border-color: #e9ecef !important;
    transition: all 0.3s ease;
}

.reports-management .border:hover {
    background: #f8f9fa;
    transform: scale(1.02);
}

/* نوافذ التقارير */
#paymentStatusModal .modal-content,
#monthlyTrendsModal .modal-content,
#topCustomersModal .modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

#paymentStatusModal .modal-header {
    background: linear-gradient(45deg, #17a2b8, #20c997);
    color: white;
    border-radius: 15px 15px 0 0;
}

#monthlyTrendsModal .modal-header {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    border-radius: 15px 15px 0 0;
}

#topCustomersModal .modal-header {
    background: linear-gradient(45deg, #ffc107, #fd7e14);
    color: white;
    border-radius: 15px 15px 0 0;
}

/* أنماط الإعدادات */
.settings-management .card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.settings-management .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.settings-management .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
}

.settings-management .form-control:focus {
    border-color: var(--sales-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--sales-primary-rgb), 0.25);
}

.settings-management .btn-primary {
    background: linear-gradient(45deg, var(--sales-primary), var(--sales-success));
    border: none;
}

.settings-management .btn-primary:hover {
    background: linear-gradient(45deg, var(--sales-success), var(--sales-primary));
    transform: translateY(-1px);
}

/* نافذة استعادة النسخة الاحتياطية */
#restoreBackupModal .modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

#restoreBackupModal .modal-header {
    background: linear-gradient(45deg, #ffc107, #fd7e14);
    color: white;
    border-radius: 15px 15px 0 0;
}

/* إحصائيات البيانات */
.settings-management .border.rounded {
    transition: all 0.3s ease;
    cursor: pointer;
}

.settings-management .border.rounded:hover {
    background: #e9ecef !important;
    transform: scale(1.05);
}

/* أنماط تصميمات الفواتير */
.settings-management .form-control-color {
    width: 100%;
    height: 38px;
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    cursor: pointer;
}

.settings-management .form-control-color:focus {
    border-color: var(--sales-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--sales-primary-rgb), 0.25);
}

/* شبكة القوالب */
.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.template-card {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    position: relative;
    overflow: hidden;
}

.template-card:hover {
    border-color: var(--sales-primary);
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.template-card.selected {
    border-color: var(--sales-primary);
    background: linear-gradient(135deg, var(--sales-primary)05, var(--sales-primary)10);
    box-shadow: 0 8px 25px rgba(var(--sales-primary-rgb), 0.2);
}

.template-card.selected::before {
    content: '✓';
    position: absolute;
    top: 10px;
    right: 10px;
    background: var(--sales-primary);
    color: white;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}

.template-preview {
    height: 120px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
}

.mini-invoice {
    width: 80%;
    height: 80%;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.mini-header {
    border-radius: 4px 4px 0 0;
}

.mini-content {
    background: white;
}

.mini-line {
    border-radius: 2px;
}

.mini-table {
    border-radius: 2px;
}

.template-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.template-description {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 12px;
    line-height: 1.4;
}

.template-features {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.feature-tag {
    background: var(--sales-primary)15;
    color: var(--sales-primary);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* أنماط متقدمة للنماذج */
.settings-management .form-range {
    margin: 10px 0;
}

.settings-management .form-range::-webkit-slider-thumb {
    background: var(--sales-primary);
    border: 2px solid white;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.settings-management .form-range::-moz-range-thumb {
    background: var(--sales-primary);
    border: 2px solid white;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .template-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .template-card {
        padding: 12px;
    }

    .template-preview {
        height: 100px;
    }

    .template-name {
        font-size: 1rem;
    }

    .template-description {
        font-size: 0.8rem;
    }

    .feature-tag {
        font-size: 0.7rem;
        padding: 3px 6px;
    }
}

/* معاينة تصميم الفاتورة */
#invoicePreviewModal .modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

#invoicePreviewModal .modal-header {
    background: linear-gradient(45deg, #6f42c1, #e83e8c);
    color: white;
    border-radius: 15px 15px 0 0;
}

#invoicePreviewModal .modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

/* أنماط معاينة الفاتورة */
.invoice-preview {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    background: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.invoice-preview .invoice-header {
    position: relative;
}

.invoice-preview .logo-placeholder {
    font-size: 10px;
    line-height: 1.2;
}

.invoice-preview .invoice-table {
    font-size: 0.9rem;
}

.invoice-preview .invoice-table th,
.invoice-preview .invoice-table td {
    padding: 8px 12px;
}

.invoice-preview .total-row {
    font-size: 0.9rem;
}

.invoice-preview .total-final {
    font-size: 1.1rem;
}

/* أنماط اختيار الألوان */
.settings-management .color-input-group {
    display: flex;
    gap: 10px;
    align-items: center;
}

.settings-management .color-preview {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 2px solid #dee2e6;
    cursor: pointer;
    transition: all 0.3s ease;
}

.settings-management .color-preview:hover {
    transform: scale(1.1);
    border-color: var(--sales-primary);
}

/* أنماط قوالب الفواتير */
.template-preview {
    border: 2px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.template-preview:hover {
    border-color: var(--sales-primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.template-preview.selected {
    border-color: var(--sales-primary);
    background: var(--sales-primary)10;
}

.template-preview h6 {
    color: var(--sales-primary);
    margin-bottom: 10px;
}

.template-preview .template-sample {
    height: 100px;
    background: #f8f9fa;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 0.8rem;
}

/* تحسينات للشاشات الصغيرة للتقارير والإعدادات */
@media (max-width: 768px) {
    .reports-management .card-body,
    .settings-management .card-body {
        padding: 1rem;
    }

    .reports-management .btn-group,
    .settings-management .btn-group {
        flex-direction: column;
    }

    .reports-management .btn-group .btn,
    .settings-management .btn-group .btn {
        margin: 2px 0;
        border-radius: 0.375rem !important;
    }

    .reports-management .row.text-center .col-md-3,
    .settings-management .row.text-center .col-6 {
        margin-bottom: 15px;
    }

    /* تحسينات تصميمات الفواتير للشاشات الصغيرة */
    .settings-management .color-input-group {
        flex-direction: column;
        gap: 5px;
    }

    .settings-management .form-control-color {
        height: 45px;
    }

    #invoicePreviewModal .modal-body {
        max-height: 60vh;
        padding: 10px;
    }

    .invoice-preview {
        font-size: 0.8rem;
    }

    .invoice-preview .invoice-table {
        font-size: 0.7rem;
    }

    .template-preview {
        padding: 10px;
        margin: 5px 0;
    }

    .template-preview .template-sample {
        height: 80px;
        font-size: 0.7rem;
    }
}

/* أنماط إدارة المنتجات */
.products-management .product-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.products-management .product-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.products-management .product-card .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
}

.products-management .product-pricing {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.products-management .product-stats .border {
    border-color: #e9ecef !important;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.products-management .product-stats .border:hover {
    background: #e9ecef;
    transform: scale(1.05);
}

/* نوافذ المنتجات */
#newProductModal .modal-content,
#editProductModal .modal-content,
#viewProductModal .modal-content,
#productSalesReportModal .modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

#newProductModal .modal-header,
#editProductModal .modal-header {
    background: linear-gradient(45deg, var(--sales-primary), var(--sales-success));
    color: white;
    border-radius: 15px 15px 0 0;
}

#viewProductModal .modal-header {
    background: linear-gradient(45deg, #17a2b8, #20c997);
    color: white;
    border-radius: 15px 15px 0 0;
}

#productSalesReportModal .modal-header {
    background: linear-gradient(45deg, #fd7e14, #ffc107);
    color: white;
    border-radius: 15px 15px 0 0;
}

/* بطاقات الفئات */
.products-management .badge {
    font-size: 0.75rem;
    padding: 0.5em 0.75em;
}

/* تحسينات للشاشات الصغيرة للمنتجات */
@media (max-width: 768px) {
    .products-management .product-card {
        margin-bottom: 15px;
    }

    .products-management .product-pricing {
        padding: 10px;
    }

    .products-management .product-pricing .row {
        margin: 0;
    }

    .products-management .product-pricing .col-6 {
        padding: 5px;
    }

    .products-management .btn-group {
        flex-direction: column;
    }

    .products-management .btn-group .btn {
        margin: 2px 0;
        border-radius: 0.375rem !important;
    }
}
