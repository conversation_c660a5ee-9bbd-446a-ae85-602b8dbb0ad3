/**
 * تنسيقات نافذة المبيعات المكتملة
 * Complete Sales Window Styles
 */

/* متغيرات CSS للألوان والقياسات */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    --border-radius: 15px;
    --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* تنسيقات عامة */
body {
    font-family: var(--font-family);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* تحسينات البطاقات */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-bottom: none;
    padding: 1.5rem;
}

.card-body {
    padding: 2rem;
}

/* تحسينات الأزرار */
.btn {
    border-radius: 10px;
    padding: 12px 24px;
    font-weight: 600;
    transition: var(--transition);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
}

.btn-success {
    background: linear-gradient(45deg, #28a745, #20c997);
}

.btn-warning {
    background: linear-gradient(45deg, #ffc107, #fd7e14);
}

.btn-danger {
    background: linear-gradient(45deg, #dc3545, #c82333);
}

.btn-info {
    background: linear-gradient(45deg, #17a2b8, #138496);
}

/* تحسينات النماذج */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 16px;
    transition: var(--transition);
    font-size: 16px;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: translateY(-2px);
}

.input-group-text {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    border-radius: 10px 0 0 10px;
}

/* تحسينات الجداول */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    padding: 1.5rem 1rem;
    font-weight: 600;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.1);
    transform: scale(1.02);
}

.table tbody td {
    padding: 1.2rem 1rem;
    vertical-align: middle;
    border-color: #e9ecef;
}

/* تحسينات النوافذ المنبثقة */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-bottom: none;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-footer {
    border-top: none;
    padding: 1.5rem;
}

/* تحسينات الشارات */
.badge {
    padding: 8px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.85em;
}

/* تحسينات شريط التقدم */
.progress {
    height: 10px;
    border-radius: 20px;
    background-color: #e9ecef;
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border-radius: 20px;
    transition: width 0.8s ease;
}

/* تحسينات القوائم المنسدلة */
.dropdown-menu {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 12px 20px;
    transition: var(--transition);
}

.dropdown-item:hover {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    transform: translateX(5px);
}

/* تحسينات التنبيهات */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--box-shadow);
}

.alert-success {
    background: linear-gradient(45deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-warning {
    background: linear-gradient(45deg, #fff3cd, #ffeaa7);
    color: #856404;
}

.alert-danger {
    background: linear-gradient(45deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

.alert-info {
    background: linear-gradient(45deg, #d1ecf1, #bee5eb);
    color: #0c5460;
}

/* تحسينات لوحة التحكم */
.dashboard-card {
    background: linear-gradient(135deg, white 0%, #f8f9fa 100%);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.dashboard-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.stats-card {
    text-align: center;
    padding: 2rem;
    border-radius: var(--border-radius);
    color: white;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.2));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stats-card:hover::before {
    opacity: 1;
}

.stats-card h3 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-card p {
    font-size: 1.1rem;
    margin-bottom: 0;
    opacity: 0.9;
}

/* تحسينات محول العملات */
.currency-converter {
    background: linear-gradient(135deg, #f8f9fa 0%, white 100%);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--box-shadow);
}

.currency-result {
    background: linear-gradient(45deg, var(--info-color), #138496);
    color: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    text-align: center;
    font-size: 1.2rem;
    font-weight: 600;
}

/* تحسينات الإجراءات السريعة */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.quick-action-btn {
    background: linear-gradient(135deg, white 0%, #f8f9fa 100%);
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    transition: var(--transition);
    text-decoration: none;
    color: var(--dark-color);
}

.quick-action-btn:hover {
    border-color: var(--primary-color);
    transform: translateY(-5px);
    box-shadow: var(--box-shadow);
    color: var(--primary-color);
}

.quick-action-btn i {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    display: block;
}

/* تحسينات التنقل */
.navbar {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    box-shadow: var(--box-shadow);
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-link {
    color: white !important;
    font-weight: 500;
    padding: 0.8rem 1.5rem !important;
    border-radius: 10px;
    transition: var(--transition);
    margin: 0 0.2rem;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* تحسينات الفلاتر */
.filter-section {
    background: linear-gradient(135deg, #f8f9fa 0%, white 100%);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--box-shadow);
}

.filter-section .form-control {
    margin-bottom: 1rem;
}

/* تحسينات الطباعة */
@media print {
    body {
        background: white !important;
    }
    
    .no-print {
        display: none !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
    
    .btn {
        display: none !important;
    }
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 14px;
    }
    
    .table {
        font-size: 14px;
    }
    
    .quick-actions {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }
    
    .quick-action-btn {
        padding: 1.5rem;
    }
    
    .quick-action-btn i {
        font-size: 2rem;
    }
}

/* تأثيرات الحركة */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

/* تحسينات إضافية */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.drag-over {
    border: 2px dashed var(--primary-color) !important;
    background-color: rgba(102, 126, 234, 0.1) !important;
}

.tooltip-inner {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border-radius: 8px;
}

.tooltip.bs-tooltip-top .tooltip-arrow::before {
    border-top-color: var(--primary-color);
}

/* تحسينات النصوص */
.text-gradient {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.section-title {
    position: relative;
    padding-bottom: 1rem;
    margin-bottom: 2rem;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 4px;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}
