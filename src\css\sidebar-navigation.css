/**
 * لوحة التنقل الجانبية المتطورة
 * Advanced Sidebar Navigation
 */

:root {
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 70px;
    --sidebar-bg: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    --sidebar-text: #ecf0f1;
    --sidebar-text-muted: #bdc3c7;
    --sidebar-hover: rgba(52, 152, 219, 0.1);
    --sidebar-active: #3498db;
    --sidebar-border: rgba(255, 255, 255, 0.1);
    --sidebar-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
    --transition-speed: 0.3s;
}

/* تخطيط الصفحة الرئيسي */
.main-layout {
    display: flex;
    min-height: 100vh;
    transition: all var(--transition-speed) ease;
}

/* اللوحة الجانبية */
.sidebar {
    width: var(--sidebar-width);
    min-height: 100vh;
    background: var(--sidebar-bg);
    color: var(--sidebar-text);
    position: fixed;
    top: 0;
    right: 0;
    z-index: 1050;
    transition: all var(--transition-speed) ease;
    box-shadow: var(--sidebar-shadow);
    overflow-y: auto;
    overflow-x: hidden;
    transform: translateX(100%);
    display: none;
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

/* رأس اللوحة الجانبية */
.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid var(--sidebar-border);
    text-align: center;
    position: relative;
}

.sidebar-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    color: var(--sidebar-text);
    text-decoration: none;
    transition: all var(--transition-speed) ease;
}

.sidebar-logo i {
    font-size: 2rem;
    color: var(--sidebar-active);
}

.sidebar-logo-text {
    font-size: 1.2rem;
    font-weight: 600;
    transition: all var(--transition-speed) ease;
}

.sidebar.collapsed .sidebar-logo-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

/* زر الإغلاق */
.sidebar-close {
    position: absolute;
    top: 15px;
    left: 15px;
    width: 35px;
    height: 35px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-speed) ease;
    font-size: 16px;
}

.sidebar-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

/* طبقة التغطية */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    opacity: 0;
    transition: opacity var(--transition-speed) ease;
    display: none;
}

/* قائمة التنقل */
.sidebar-nav {
    padding: 20px 0;
}

.nav-section {
    margin-bottom: 30px;
}

.nav-section-title {
    padding: 0 20px 10px;
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--sidebar-text-muted);
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all var(--transition-speed) ease;
}

.sidebar.collapsed .nav-section-title {
    opacity: 0;
    height: 0;
    padding: 0;
    margin: 0;
    overflow: hidden;
}

/* عناصر التنقل */
.nav-item {
    margin-bottom: 5px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--sidebar-text);
    text-decoration: none;
    transition: all var(--transition-speed) ease;
    position: relative;
    border-right: 3px solid transparent;
}

.nav-link:hover {
    background: var(--sidebar-hover);
    color: var(--sidebar-active);
    border-right-color: var(--sidebar-active);
}

.nav-link.active {
    background: var(--sidebar-hover);
    color: var(--sidebar-active);
    border-right-color: var(--sidebar-active);
}

.nav-link i {
    width: 20px;
    text-align: center;
    margin-left: 15px;
    font-size: 1.1rem;
    transition: all var(--transition-speed) ease;
}

.nav-link-text {
    transition: all var(--transition-speed) ease;
    white-space: nowrap;
}

.sidebar.collapsed .nav-link-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar.collapsed .nav-link {
    padding: 12px;
    justify-content: center;
}

/* عناصر التنقل مع قوائم فرعية */
.nav-item.has-submenu > .nav-link::after {
    content: '\f107';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    margin-right: auto;
    transition: transform var(--transition-speed) ease;
}

.nav-item.has-submenu.open > .nav-link::after {
    transform: rotate(180deg);
}

.sidebar.collapsed .nav-item.has-submenu > .nav-link::after {
    display: none;
}

/* القوائم الفرعية */
.submenu {
    max-height: 0;
    overflow: hidden;
    transition: max-height var(--transition-speed) ease;
    background: rgba(0, 0, 0, 0.2);
}

.nav-item.has-submenu.open .submenu {
    max-height: 500px;
}

.submenu .nav-link {
    padding: 10px 20px 10px 55px;
    font-size: 0.9rem;
    border-right: none;
}

.submenu .nav-link:hover {
    background: rgba(52, 152, 219, 0.2);
    border-right: 3px solid var(--sidebar-active);
}

.sidebar.collapsed .submenu {
    display: none;
}

/* شارات الإشعارات */
.nav-badge {
    background: #e74c3c;
    color: white;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    margin-right: auto;
    min-width: 18px;
    text-align: center;
    transition: all var(--transition-speed) ease;
}

.sidebar.collapsed .nav-badge {
    position: absolute;
    top: 8px;
    left: 8px;
    margin: 0;
}

/* المحتوى الرئيسي */
.main-content {
    flex: 1;
    margin-right: var(--sidebar-width);
    transition: margin-right var(--transition-speed) ease;
    min-height: 100vh;
    background: #f8f9fa;
}

.sidebar.collapsed + .main-content {
    margin-right: var(--sidebar-collapsed-width);
}

/* شريط التنقل العلوي المبسط */
.top-navbar {
    background: white;
    padding: 15px 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
}

.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.top-navbar-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 15px;
    background: #f8f9fa;
    border-radius: 25px;
    text-decoration: none;
    color: #2c3e50;
    transition: all 0.3s ease;
}

.user-menu:hover {
    background: #e9ecef;
    color: #2c3e50;
}

/* تأثيرات الحركة */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.sidebar {
    animation: slideInRight 0.5s ease-out;
}

/* تلميحات الأدوات للوضع المطوي */
.sidebar.collapsed .nav-link {
    position: relative;
}

.sidebar.collapsed .nav-link:hover::before {
    content: attr(data-tooltip);
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    background: #2c3e50;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.85rem;
    white-space: nowrap;
    z-index: 1000;
    margin-left: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.sidebar.collapsed .nav-link:hover::after {
    content: '';
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    border: 6px solid transparent;
    border-left-color: #2c3e50;
    margin-left: 4px;
}

/* التجاوب للشاشات المختلفة */
@media (max-width: 1200px) {
    .sidebar {
        width: 300px;
    }
}

@media (max-width: 768px) {
    .sidebar {
        width: 280px;
        transform: translateX(100%);
    }

    .sidebar-nav {
        padding: 15px 0;
    }

    .nav-link {
        padding: 15px 20px;
        font-size: 16px;
    }

    .nav-link i {
        font-size: 18px;
        margin-left: 12px;
    }
}

@media (max-width: 576px) {
    .sidebar {
        width: 260px;
    }

    .sidebar-header {
        padding: 15px;
    }

    .sidebar-logo-text {
        font-size: 1rem;
    }

    .nav-section-title {
        font-size: 0.8rem;
        padding: 0 15px 8px;
    }

    .nav-link {
        padding: 12px 15px;
        font-size: 15px;
    }

    .submenu .nav-link {
        padding: 10px 15px 10px 45px;
        font-size: 14px;
    }
}

/* تحسينات إضافية */
.nav-link:focus {
    outline: 2px solid var(--sidebar-active);
    outline-offset: -2px;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid var(--sidebar-border);
    margin-top: auto;
    text-align: center;
}

.sidebar-footer .version-info {
    font-size: 0.8rem;
    color: var(--sidebar-text-muted);
    transition: all var(--transition-speed) ease;
}

.sidebar.collapsed .sidebar-footer .version-info {
    opacity: 0;
    height: 0;
    padding: 0;
    overflow: hidden;
}
